# SPARKS CRM 2.0
Backend REST api for CRM 2.0


## Core libs and DB
1. [Django](https://www.djangoproject.com/)
2. [Django Rest Framework](https://www.django-rest-framework.org/)
3. [PostgreSQL](https://www.postgresql.org/)


All requirements you can find in `requirements.txt`

## Getting started

### Setup Env Vars

create `.env` file and add variables like in `.env.example`

### To run locally

Install `python:3.10`, `pip3`, `virtualenv`

Using [pipenv](https://github.com/pypa/pipenv) run `pipenv shell` and `pipenv install` to create virtual environment and install dependencies

```sh
$ python -m venv venv
$ source venv/bin/activate
$ pip  install -r requirements.txt
```
add required environment variables from .env.example

Go to `src` directory and run

```sh
$ python manage.py migrate
$ python manage.py test
$ python manage.py runserver
```

ATTENTION! load fixtures after first migrate
```sh
$ python manage.py loaddata accounts_data.json
$ python manage.py loaddata only_fans_models_data.json
```

Admin credentials
```sh
$ USERNAME <EMAIL>
$ PASSWORD admin
```
if you need test coverage

```sh
$ coverage erase
$ coverage python manage.py test
$ coverage report
```

you can also get test coverage with one command
```sh
$ coverage erase && coverage run manage.py test && coverage report
```

sort imports 
```sh
$  isort .
```
check flake
```sh
$  flake8 --show-source
```

### Add cache to endpoint [Optional]

[Cache Documentation](src/base/crm_cache/README.md)

### To run via docker

Install `Docker` and `docker-compose`

Run
```sh
$ make build
$ make up
$ make logs
```
more teams in Makefile

### Write tests
must be inherited from **BaseCRMTest** from **base.tests** module

if you need to disable services when developing locally using docker, create a docker-compose.override.yml file, an example is in the repository


Open `http://localhost:8000` to view it in the browser

## [Django admin](https://docs.djangoproject.com/en/4.1/ref/contrib/admin/) web interface (user should be `is_staff` or `is_superuser`)
`http://localhost:8000/admin`


## [Browsable API](https://www.django-rest-framework.org/topics/browsable-api/)
`http://localhost:8000/api/v1/`


## Swagger and Redoc
`http://localhost:8000/api/docs/swagger-ui/`
`http://localhost:8000/api/docs/redoc/`

## YAML schema
`http://localhost:8000/api/docs/schema/`

## Flower (Celery monitoring)
`http://0.0.0.0:5555/`

Use your user credentials to login into the swagger
