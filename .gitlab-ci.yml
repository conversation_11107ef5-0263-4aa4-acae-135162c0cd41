stages:
  - test
  - build-dev
  - deploy-dev
  - build-main
  - deploy-main

.test:
  script:
    - "#=============== BUILD AND TEST ${CI_COMMIT_SHORT_SHA} =================#"
    - make build
    - make test

.deploy:
  script:
    - cd /home/<USER>/crm_backend/
    - docker compose down
    - docker compose up -d

test:
  extends: .test
  stage: test
  before_script:
    - "#=============== TESTING =================#"
    - "#=============== COPY .ENV FILE =================#"
    - echo "$DOTENV"
    - cat "$DOTENV"
    - cp "$DOTENV" .env
    - "#=============== COPIED .ENV FILE =================#"
  tags:
    - dev
  coverage: '/TOTAL.*\s+(\d+%)$/'
  except:
    - main

build-main:
  script:
    - "#================Go to project directory=================#"
    - cd /home/<USER>/crm_backend/
    - make clean
    - eval "$(ssh-agent -s)"
    - ssh-add ~/.ssh/id_rsa
    - GIT_SSH_COMMAND='ssh -o StrictHostKeyChecking=no' git checkout main
    - GIT_SSH_COMMAND='ssh -o StrictHostKeyChecking=no' git pull origin main
    - "#=============== Building images =================#"
    - make build
    - make static
  dependencies:
    - test
  stage: build-main
  tags:
    - prod
  only:
    - main

deploy-main:
  extends:
    - .deploy
  dependencies:
    - build-main
  when: manual
  stage: deploy-main
  tags:
    - prod
  only:
    - main


build-dev:
  script:
    - "#================Go to project directory=================#"
    - cd /home/<USER>/crm_backend/
    - make clean
    - eval "$(ssh-agent -s)"
    - ssh-add ~/.ssh/id_rsa
    - GIT_SSH_COMMAND='ssh -o StrictHostKeyChecking=no' git checkout dev
    - GIT_SSH_COMMAND='ssh -o StrictHostKeyChecking=no' git pull origin dev
    - "#=============== Building images =================#"
    - make build
    - make static
    - make migrate
  dependencies:
    - test
  stage: build-dev
  tags:
    - dev
  only:
    - dev

deploy-dev:
  extends: .deploy
  dependencies:
    - build-dev
  stage: deploy-dev
  when: manual
  tags:
    - dev
  only:
    - dev
