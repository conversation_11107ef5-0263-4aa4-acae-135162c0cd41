services:
  api:
    image: "sparks_crm"
    container_name: "sparks_crm"
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "5"
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      - "./src:/opt/app"
    env_file:
      - .env
    ports:
      - "127.0.0.1:8000:8000"
    environment:
      - POSTGRES_TEST_DB=postgres
      - POSTGRES_TEST_USER=postgres
      - POSTGRES_TEST_PASSWORD=postgres
      - POSTGRES_TEST_HOST=db
    command: gunicorn core.wsgi:application --bind 0.0.0.0:8000  -w 13 --timeout 300 --max-requests 20000 --max-requests-jitter 2000 --capture-output
    depends_on:
      - redis
    restart: unless-stopped

  fast_api:
    container_name: "fast_api_socket"
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "5"
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      - "./src:/opt/app"
    ports:
      - "127.0.0.1:5000:5000"
    command: uvicorn fast_api_socket.main:app --workers 6 --reload --log-level=info --host 0.0.0.0 --port 5000
    depends_on:
      - redis
    env_file:
      - .env
    restart: unless-stopped

  redis:
    image: redis:latest
    command: redis-server --appendonly yes --replica-read-only no --requirepass ${REDIS_PASSWORD}
    hostname: redis
    ports:
      - "127.0.0.1:6379:6379"
    volumes:
      - redis_data:/data
    env_file:
      - .env
    restart: always

  db:
    image: postgres
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    environment:
      - "POSTGRES_HOST_AUTH_METHOD=trust"
    restart: unless-stopped

  worker:
    build:
      context: .
    hostname: worker
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "5"
    entrypoint:  "/bin/sh -c 'celery -A core.celery worker -l info -Q cleaner,socket_events,services_queue,notifications --concurrency=6'"
    depends_on:
      - api
      - redis
    volumes:
      - "./src:/opt/app"
    env_file:
      - .env
    restart: unless-stopped

  celery-beat:
    build:
      context: .
    entrypoint: "/bin/sh -c 'celery -A core.celery beat -l info'"
    depends_on:
      - api
      - redis
    volumes:
      - "./src:/opt/app"
    env_file:
      - .env
    restart: unless-stopped

  # ----------------------------
  # Monitoring Services
  # ----------------------------

  flower:
    image: mher/flower:latest
    command: celery flower --url_prefix=special/flower
    ports:
      - "127.0.0.1:5555:5555"
    depends_on:
      - worker
    env_file:
      - .env
    restart: unless-stopped

  prometheus:
    image: prom/prometheus
    volumes:
      - ./metrics/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - "--config.file=/etc/prometheus/prometheus.yml"
    depends_on:
      - api
      - flower
    restart: unless-stopped

  grafana:
    image: grafana/grafana-oss:latest
    ports:
      - "127.0.0.1:3000:3000"
    depends_on:
      - prometheus
    environment:
      - GF_SECURITY_ADMIN_USER=$GRAFANA_USER
      - GF_SECURITY_ADMIN_PASSWORD=$GRAFANA_PASSWORD
      - GF_SERVER_ROOT_URL=https://ofcrm.biz/special/grafana/
      - GF_SERVER_SERVE_FROM_SUB_PATH=true
      - GF_SERVER_DOMAIN=ofcrm.biz
    volumes:
      - grafana_data:/var/lib/grafana
    restart: unless-stopped

  node-exporter:
    image: prom/node-exporter
    volumes:
      - "/proc:/host/proc:ro"
      - "/sys:/host/sys:ro"
      - "/:/rootfs:ro"
    command:
      - '--path.procfs=/host/proc'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.ignored-mount-points'
      - '^/(sys|proc|dev|host|etc)($$|/)'
    restart: unless-stopped

  # ----------------------------
  # DB Backup
  # ----------------------------

  pgbackups:
      image: prodrigestivill/postgres-backup-local
      restart: always
      volumes:
          - ./pgbackups:/backups
      env_file:
          - .env
      environment:
          - POSTGRES_EXTRA_OPTS=-Z1 --schema=public --blobs
          - SCHEDULE=@daily
          - BACKUP_ON_START=TRUE
          - BACKUP_KEEP_DAYS=7
          - BACKUP_KEEP_WEEKS=4
          - BACKUP_KEEP_MONTHS=6
          - HEALTHCHECK_PORT=8080

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:
