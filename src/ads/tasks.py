from datetime import timedel<PERSON>

from django.db import transaction
from django.db.models import F
from django.utils import timezone

from ads.models import (
    Ads,
    AdsSale,
    AdsTracking,
    LinkLogging,
)
from ads.services import (
    AdsAnalyticsService,
    AdsService,
    AdsTrackingDailyDataService,
    PromotionCountService,
)
from core.celery import BaseTaskWithRetry
from core.celery import app as celery_app
from only_fans_db.models import (
    CampaignLinks,
    Promos,
    Sales,
    TrialLinks,
)
from refresh_fans_models.models import RefreshTask
from refresh_fans_models.models import Sale as RefreshSale


@celery_app.task(bind=BaseTaskWithRetry)
def update_ads_stats(self):
    ads_list = list(
        Ads.objects.filter(
            trial_link__isnull=False
        ).prefetch_related(
            'sales'
        ).select_related(
            'parent',
            'child',
            'status'
        )
    )

    if not ads_list:

        return "No ads to update"

    refreshed_sales = list(
        RefreshSale.objects.filter(
            is_calculated=False, refresh_task__status=RefreshTask.StatusChoices.SUCCESS
        )
    )
    ads_sales = AdsSale.objects.in_bulk(
        id_list=[refresh_sale.trans_id for refresh_sale in refreshed_sales],
        field_name='trans_id'
    )
    refreshed_sales_ids = [
        refresh_sale.trans_id
        for refresh_sale in refreshed_sales
        if refresh_sale.trans_id not in ads_sales
    ]

    sales = list(
        Sales.objects.filter(
            amount__isnull=False,
            fan__isnull=False,
            model__isnull=False,
            trans_id__in=refreshed_sales_ids
        ).order_by(
            'trans_date'
        ).only(
            'trans_id', 'trans_date', 'amount', 'fan', 'model', 'type'
        )
    )

    trial_links = TrialLinks.objects.all()
    campaign_links = CampaignLinks.objects.all()

    with transaction.atomic():
        number_of_updated_records = AdsService.update_list_of_ads(
            ads_list=ads_list,
            sales=sales,
            trial_links=trial_links,
            campaign_links=campaign_links,
        )

        for refreshed_sale in refreshed_sales:
            refreshed_sale.is_calculated = True

        # RefreshSale.objects.bulk_update(refreshed_sales, ['is_calculated'],  batch_size=100)

    return f"Updated {number_of_updated_records} of ads"


@celery_app.task(bind=BaseTaskWithRetry)
def create_ads_tracking_daily_datas(self):
    all_active_ads_tracking_list = list(
        AdsTracking.objects.filter(
            track_end__isnull=True,
            ads__isnull=False
        ).select_related('ads')
    )
    create_date = timezone.now().date() - timezone.timedelta(days=1)
    updated_objects, created_objects = AdsTrackingDailyDataService.create_ads_tracking_daily_data_for_ads_tracking_list(
        all_active_ads_tracking_list,
        specific_date=create_date,
    )

    return (
        f"Created {len(created_objects)} AdsTrackingDailyData objects and updated {len(updated_objects)} "
        f"AdsTrackingDailyData objects for {create_date} date"
    )


@celery_app.task(bind=BaseTaskWithRetry)
def write_link_logging(self, link_logging_data: list[dict]):
    link_logging_objects = [
        LinkLogging(
            model_id=data['model_id'],
            fan_id=data['fan_id'],
            join_date=data['join_date'],
            link_url=data['link_url'],
            type=data['type']
        )
        for data in link_logging_data
    ]

    objects_created = LinkLogging.objects.bulk_create(link_logging_objects, ignore_conflicts=True)

    return f"Created {objects_created} LinkLogging objects"


@celery_app.task(bind=BaseTaskWithRetry)
def write_promotions_count(self):
    created, updated = PromotionCountService().write_promotions_count()

    return f"Created {created} PromotionCount objects and updated {updated} PromotionCount objects"


@celery_app.task(bind=BaseTaskWithRetry)
def update_ads_friends_status(self):
    promos = list(Promos.objects.filter(
        check_date__gte=timezone.now() - timezone.timedelta(hours=10)
    ).order_by('promo_id'))
    updated_count = AdsService.update_ads_friends_status(promos)

    return f"Ads friends status updated for {updated_count} records"


@celery_app.task(bind=BaseTaskWithRetry)
def write_ads_models_fans_income_costs(self):
    service = AdsAnalyticsService()
    created_objects = service.write_models_ads_fans_income_costs()

    return f"Created {len(created_objects)} AdsModelsFansIncomeCost objects"


@celery_app.task(bind=BaseTaskWithRetry)
def write_ads_daily_data(self):
    service = AdsAnalyticsService()
    created_objects = service.write_ads_daily_data()

    write_ads_models_fans_income_costs.delay()

    return f"Created {len(created_objects)} AdsDailyData records"


@celery_app.task(bind=BaseTaskWithRetry)
def switch_ads_new_donor_promos(self):
    ads_list = Ads.objects.annotate(
        new_donor_filled_date_60_days=F('new_donor_filled_date') + timedelta(days=60),
    ).filter(
        new_donor_filled_date__isnull=False,
        promo__switch_to_promo__isnull=False,
        new_donor_filled_date_60_days__lte=timezone.now().date()
    )

    updated_ads_numbers = []

    for ads in ads_list:
        ads.promo = ads.promo.switch_to_promo
        ads.new_donor_filled_date = None

        ads._change_reason = 'Switch to new promo'

        ads.save()

        updated_ads_numbers.append(ads.ads_number)

    return f"Updated ads numbers: {updated_ads_numbers}"
