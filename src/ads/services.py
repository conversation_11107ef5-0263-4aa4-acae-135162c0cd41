import logging
from calendar import monthrange
from collections import OrderedDict, defaultdict
from dataclasses import dataclass
from dataclasses import field as dc_field
from datetime import date as date_type
from datetime import datetime, timedelta
from decimal import Decimal

from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.contrib.auth import get_user_model
from django.db import transaction
from django.db.models import (
    Avg,
    F,
    Q,
    QuerySet,
    Sum,
    Window,
)
from django.db.models.functions import RowNumber
from django.utils import timezone
from django.utils.dateparse import parse_date
from rest_framework import serializers
from rest_framework.exceptions import PermissionDenied

from accounts.serializers import PureUserSerializer
from ads.exceptions import (
    AdsDraftAlreadyAcceptedError,
    AdsDraftError,
    AdsDraftMarketerWithoutParentError,
    AdsDraftTelegramServiceError,
    AdsDraftTelegramServiceReviewerError,
    AdsDraftTelegramServiceSendReviewResultError,
)
from ads.models import (
    <PERSON>s,
    AdsDailyData,
    AdsDraft,
    AdsSale,
    AdsSwapLog,
    AdsTracking,
    AdsTrackingDailyData,
    Donor,
    ModelAdsFansIncomeCost,
    PaymentMethod,
    PlatformType,
    Promo,
    PromotionCount,
    Status,
)
from ads.tools import execute_task_write_link_logging
from base.crm_cache.service import RedisCacheService
from base.services import TelegramBotService
from base.signals import post_bulk_update
from base.tasks import send_bug_notification
from base.tools import sort_data_by_field_in_list_of_dicts
from finance.dtos import IncomeFansDTO
from only_fans_db.models import (
    CampaignLinks,
    CampaignUsers,
    Fans,
    FansModels,
    Promos,
    PromotionsDetails,
    Sales,
    TrialLinks,
)
from only_fans_models.models import OnlyFansModel
from only_fans_models.serializers import OnlyFansModelsSerializer

User = get_user_model()
logger = logging.getLogger("ads")


@dataclass
class Calculator:
    """
    The Calculator class provides functionality for calculating revenue,
    profit, and return on marketing investment (ROMI) based on
    trial link, cost_result, fans count, and claims count.

    Attrs:
        trial_link (str | None): The trial link associated with the calculation.
        cost_result (Decimal | None): The cost result associated with the calculation.
        fans_count (int | None): The number of fans associated with the calculation.
        revenue (Decimal): The revenue calculated based on the given inputs. Default is Decimal(0).
        claims_count (int): The number of claims associated with the calculation. Default is 0.

    Methods:
        - fans_delta(): Returns the difference between claims_count and fans_count.
        - profit(): Returns the profit calculated based on the revenue and cost result.
        - romi(): Returns the return on marketing investment (ROMI) calculated based on the profit and cost result.
        - get_dict(): Returns a dictionary representation of the Calculator object.
    """

    trial_link: str | None
    cost_result: Decimal | None
    fans_count: int | None
    revenue: Decimal = Decimal(0)
    claims_count: int = 0
    ads_sales: list[AdsSale] | None = None
    _new_sales: list[Sales] | None = None

    def __post_init__(self):
        if self.ads_sales:
            self.revenue = sum(sale.amount for sale in self.ads_sales)  # noqa

    @property
    def fans_delta(self) -> int | None:
        if self.fans_count is not None:
            return self.fans_count - self.claims_count

    @property
    def profit(self) -> Decimal | None:
        if self.cost_result is not None:
            return self.revenue - self.cost_result

    @property
    def romi(self) -> Decimal | None:
        if self.cost_result is not None and self.cost_result > 0:
            return (self.profit / self.cost_result * 100).quantize(Decimal('1.00'))

    @property
    def new_sales(self) -> list[Sales]:
        return self._new_sales or []

    @new_sales.setter
    def new_sales(self, value: list[Sales] | Sales) -> None:
        if self._new_sales is None:
            self._new_sales = []

        if isinstance(value, list):
            self._new_sales.extend(value)
        elif isinstance(value, Sales):
            self._new_sales.append(value)

    @property
    def spenders_data(self) -> dict[str, int]:
        spenders = {}

        if self.ads_sales:
            for ads_sale in self.ads_sales:
                spender_amount = spenders.setdefault(ads_sale.fan_id, 0)
                spenders[ads_sale.fan_id] = spender_amount + ads_sale.amount

        for new_sale in self.new_sales:
            spender_amount = spenders.setdefault(new_sale.fan_id, 0)
            spenders[new_sale.fan_id] = spender_amount + Decimal(new_sale.amount)

        spenders_low = 0
        spenders_high = 0

        for fan_id, amount in spenders.items():
            if 100 <= amount < 500:
                spenders_low += 1
            elif amount >= 500:
                spenders_high += 1

        return {
            'spenders': len(spenders),
            'spenders_low': spenders_low,
            'spenders_high': spenders_high,
        }

    def get_dict(self):
        return {
            'revenue': self.revenue,
            'profit': self.profit,
            'romi': self.romi,
            'claims_count': self.claims_count,
            'fans_delta': self.fans_delta,
            **self.spenders_data,
        }


@dataclass
class ModelDebt:
    only_fans_model: OnlyFansModel
    _amount: int = 0
    _fans_amount: int = 0

    @property
    def amount(self) -> int:
        """
        Property to get the financial record amount.

        Returns:
            int: The financial record amount.
        """
        return round(self._amount)

    def add_amount(self, value: int | float | Decimal):
        """
        Method to increment the value of the financial record.

        Parameters:
            value (int | float | Decimal): The value to be added to the current record amount.
        """
        self._amount += value

    @property
    def fans_amount(self) -> int:
        """
        Property to get the financial record amount.

        Returns:
            int: The financial record amount.
        """
        return round(self._fans_amount)

    def add_fans_amount(self, value: int | float | Decimal):
        """
        Method to increment the value of the financial record.

        Parameters:
            value (int | float | Decimal): The value to be added to the current record amount.
        """
        self._fans_amount += value


@dataclass
class MonthAmountRecord:
    """
    Class representing a financial record for a specific month,
    which could be a debt or a purchase.

    Attributes:
        month (datetime.date): The month for which the record is noted.
        _amount (int): The amount of the financial record for the specific month.
    """

    month: datetime.date
    _amount: int = 0
    _fans_amount: int = 0
    models_debts: dict[OnlyFansModel, ModelDebt] = dc_field(default_factory=dict)

    @property
    def amount(self) -> int:
        """
        Property to get the financial record amount.

        Returns:
            int: The financial record amount.
        """
        return round(self._amount)

    def add_amount(self, value: int | float | Decimal):
        """
        Method to increment the value of the financial record.

        Parameters:
            value (int | float | Decimal): The value to be added to the current record amount.
        """
        self._amount += value

    @property
    def fans_amount(self) -> int:
        """
        Property to get the financial record amount.

        Returns:
            int: The financial record amount.
        """
        return round(self._fans_amount)

    def add_fans_amount(self, value: int | float | Decimal):
        """
        Method to increment the value of the financial record.

        Parameters:
            value (int | float | Decimal): The value to be added to the current record amount.
        """
        self._fans_amount += value

    def add_model_debt(
        self, only_fans_model: OnlyFansModel, amount: int, fans_amount: int
    ):
        """
        Method to add a model debt to the financial record.

        Parameters:
            only_fans_model (OnlyFansModel): The model for which the debt is added.
            amount (int): The amount of the debt.
            fans_amount (int): Debt fans amount
        """
        if not only_fans_model:
            return

        if not self.models_debts.get(only_fans_model):
            self.models_debts[only_fans_model] = ModelDebt(
                only_fans_model=only_fans_model,
            )

        self.models_debts[only_fans_model].add_amount(amount)
        self.models_debts[only_fans_model].add_fans_amount(fans_amount)


class AdsService:
    """
    Ads service
    """

    MODEL = Ads
    CACHE_SALES_PREFIX = 'buying_sales'
    CACHE_SALES_TTL = 135 * 60
    PARSE_DATE = datetime(year=2023, day=27, month=11, hour=10, minute=25)
    FINANCE_DATA_INTERVALS = [0, 1, 15, 30, 60, 90, 120, 150]
    DEBT_QUERYSET_FILTER_STATUSES = ['progress']
    TRIAL_LINK_IDENTIFIER = '/trial/'

    def __init__(self, instance: MODEL):
        self.instance = instance

    @staticmethod
    def get_min_max_dates_from_trial_dates(
        creation_date: datetime,
        fan_model_data: dict[str, list],
        is_trial: bool,
        link_url: str,
    ) -> tuple[datetime, datetime | None]:
        """
        Get the minimum and maximum dates from the trial dates.

        Parameters:
        - creation_date (datetime): The creation date of the trial.
        - fan_model_data (dict): The data of the fan model.
        - link_url (str): The URL of the link
        """
        min_date = creation_date
        max_date = None
        trial_dates = fan_model_data.get('trial_dates', [])
        key = 'trial_link' if is_trial else 'campaign_link_url'
        compare_value = creation_date if is_trial else link_url

        if trial_dates:

            for index, trial_date in enumerate(trial_dates):
                if key in trial_date and trial_date[key] == compare_value:

                    if index > 0:
                        max_date = trial_dates[index - 1]["join_date"]
                    min_date = trial_date["join_date"]

                    break

        return min_date, max_date

    @classmethod
    def get_revenue_and_claims_count_for_a_single_trial_link(
        cls,
        link_from_db: TrialLinks | CampaignLinks,
        calculator: Calculator,
        grouped_sales: dict[tuple[str, str], list],
        grouped_fans_models: dict[tuple[str, str], dict[str, list]],
        exclude_sales: list[str] = None,
    ) -> Calculator:
        """
        Calculate revenue based on grouped data

        Args:
            link_from_db (TrialLinks | CampaignLinks): link
            calculator (Calculator): Calculator
            grouped_sales (dict[tuple[str, str], list]): Grouped sales data
            grouped_fans_models (dict[tuple[str, str], dict[str, list]]): Grouped fans models
            exclude_sales (list[str], optional): Exclude sales. Defaults to None.
        """
        revenue = calculator.revenue
        creation_date = link_from_db.creation_date
        link_url = link_from_db.link_url

        for key, sales in grouped_sales.items():
            fan_model_data = grouped_fans_models.get(key)

            if not fan_model_data:
                continue

            if isinstance(
                link_from_db, TrialLinks
            ) and creation_date not in fan_model_data.get('trial_links', []):
                continue

            elif isinstance(
                link_from_db, CampaignLinks
            ) and link_url not in fan_model_data.get('campaign_urls', []):
                continue

            min_date, max_date = cls.get_min_max_dates_from_trial_dates(
                creation_date=creation_date,
                fan_model_data=fan_model_data,
                is_trial=isinstance(link_from_db, TrialLinks),
                link_url=link_url,
            )

            for sale in sales:

                if exclude_sales and sale.trans_id in exclude_sales:
                    continue

                if max_date is not None and sale.trans_date > max_date:
                    break

                if sale.trans_date < min_date:
                    continue

                revenue += Decimal(sale.amount)
                calculator.new_sales = sale

        calculator.revenue = Decimal(revenue).quantize(Decimal('1.00'))
        calculator.claims_count = link_from_db.claims_count

        return calculator

    @classmethod
    def generate_cache_key(cls, trial_link: TrialLinks | CampaignLinks) -> str:
        """
        Generate cache key

        Args:
            trial_link (TrialLinks | CampaignLinks): Trial link
        """
        return f"{cls.CACHE_SALES_PREFIX}_{trial_link.link_url.strip()}"

    @staticmethod
    def group_sales_data(
        sales: list[Sales],
    ) -> tuple[dict[tuple[str, str], list], list, list, dict]:
        """
        Group sales data by model and fan

        Args:
            sales: (list[Sales]): Sales data
        """
        grouped_sales = {}
        fans_ids = set()
        sales_ids = set()
        data_for_synchro_api = {}

        for sale in sales:
            grouped_sales.setdefault((sale.model_id, sale.fan_id), []).append(sale)
            fans_ids.add(sale.fan_id)
            sales_ids.add(sale.trans_id)
            data_for_synchro_api.setdefault(str(sale.model_id), set()).add(
                str(sale.fan_id)
            )

        return grouped_sales, list(fans_ids), list(sales_ids), data_for_synchro_api

    @staticmethod
    def convert_trial_date_join_date_to_datetime(
        trial_dates: list[dict],
    ) -> list[dict[str, str | datetime]]:
        for data in trial_dates:
            data['join_date'] = datetime.fromisoformat(data['join_date']).replace(
                tzinfo=None
            )

            if 'trial_link' in data:
                data['trial_link'] = datetime.fromisoformat(data['trial_link']).replace(
                    tzinfo=None
                )

            if 'campaign' in data:
                data['campaign'] = datetime.fromisoformat(data['campaign']).replace(
                    tzinfo=None
                )

        return trial_dates

    @classmethod
    def filter_valid_campaign_links_in_trial_dates(
        cls,
        trial_dates: list[dict[str, datetime]],
        fan_model_key: tuple[int, int],
        grouped_campaign_users: dict[tuple[int, int], list[CampaignUsers]],
    ) -> list[dict[str, datetime]]:
        result_trial_dates = []

        for trial_date in trial_dates:
            if 'campaign' in trial_date:
                if not grouped_campaign_users:
                    continue

                campaign_users = grouped_campaign_users.get(fan_model_key)

                if campaign_users:
                    for campaign_user in campaign_users:
                        if (
                            trial_date['join_date']
                            >= campaign_user.campaign_link.creation_date
                        ):
                            result_trial_dates.append(
                                {
                                    'campaign_link_url': campaign_user.campaign_link.link_url,
                                    'campaign': campaign_user.campaign_link.creation_date,
                                    'join_date': trial_date['join_date'],
                                }
                            )
                            break
            else:
                result_trial_dates.append(trial_date)

        return result_trial_dates

    @staticmethod
    def group_campaign_users_data(
        campaign_users: QuerySet[CampaignUsers],
    ) -> dict[tuple[int, int], list]:
        """
        Group campaign users data by model and fan

        Args:
            campaign_users: (list[CampaignUsers]): CampaignUsers list
        """
        grouped_campaign_users = {}

        for campaign_user in campaign_users:
            grouped_campaign_users.setdefault(
                (campaign_user.model_id, campaign_user.fan_id), []
            ).append(campaign_user)

        if grouped_campaign_users:
            for value in grouped_campaign_users.values():
                value.sort(key=lambda x: x.campaign_link.creation_date, reverse=True)

        return grouped_campaign_users

    @staticmethod
    def check_null_trial_dates(fans_ids: list):
        fans_from_db = Fans.objects.filter(
            fan_id__in=fans_ids, is_exist=False
        ).values_list('fan_id', flat=True)
        alive_fans_with_null_trial_dates = set(fans_ids) - set(fans_from_db)

        if alive_fans_with_null_trial_dates:
            (
                send_bug_notification.delay(
                    f"Alive fans with null trial dates: {list(alive_fans_with_null_trial_dates)[:25]} and more..."
                )
            )

    @classmethod
    def get_fans_models_by_fans_ids(
        cls, fans_ids: list, model_id: int | None = None
    ) -> dict[tuple[str, str], dict[str, list]]:
        """
        Get fans_models by fans_ids

        Args:
            fans_ids (list): List of fans_ids]
            model_id (int, optional): model id
        """
        if not model_id:
            fans_models = list(
                FansModels.objects.filter(fan_id__in=fans_ids, model_id__isnull=False)
            )
            campaign_users = CampaignUsers.objects.filter(
                fan_id__in=fans_ids,
                model_id__isnull=False,
            ).select_related('campaign_link')
        else:
            fans_models = list(
                FansModels.objects.filter(fan_id__in=fans_ids, model_id=model_id)
            )
            campaign_users = CampaignUsers.objects.filter(
                fan_id__in=fans_ids,
                model_id=model_id,
            ).select_related('campaign_link')

        grouped_campaign_users = None

        if campaign_users:
            grouped_campaign_users = cls.group_campaign_users_data(campaign_users)

        result = {}
        link_logging_data = []
        null_trial_dates = []

        for fan_model in fans_models:
            key = (fan_model.model_id, fan_model.fan_id)
            result.setdefault(key, {})

            if fan_model.trial_dates:
                filtered_trial_date = list(
                    filter(
                        lambda x: (
                            x.get('trial_link') is not None
                            or x.get('campaign') is not None
                        )
                        and x.get('join_date') is not None,
                        fan_model.trial_dates,
                    )
                )

                if filtered_trial_date:
                    filtered_trial_date = cls.convert_trial_date_join_date_to_datetime(
                        filtered_trial_date,
                    )
                    trial_date_with_valid_campaign_links = (
                        cls.filter_valid_campaign_links_in_trial_dates(
                            filtered_trial_date, key, grouped_campaign_users
                        )
                    )

                    if trial_date_with_valid_campaign_links:
                        sorted_trial_dates = sorted(
                            trial_date_with_valid_campaign_links,
                            key=lambda x: x['join_date'],
                            reverse=True,
                        )
                        result[key].setdefault('trial_dates', []).extend(
                            sorted_trial_dates
                        )
                        trial_links_from_trial_dates = [
                            x['trial_link']
                            for x in sorted_trial_dates
                            if 'trial_link' in x
                        ]
                        campaign_urls_from_trial_dates = [
                            x['campaign_link_url']
                            for x in sorted_trial_dates
                            if 'campaign' in x
                        ]

                        result[key].setdefault('trial_links', []).extend(
                            trial_links_from_trial_dates
                        )
                        result[key].setdefault('campaign_urls', []).extend(
                            campaign_urls_from_trial_dates
                        )

                        link_logging_data.extend(
                            [
                                {
                                    'model_id': fan_model.model_id,
                                    'fan_id': fan_model.fan_id,
                                    'join_date': x['join_date'],
                                    'link_url': x['campaign_link_url'],
                                    'type': 'campaign',
                                }
                                for x in sorted_trial_dates
                                if 'campaign' in x
                            ]
                        )

                        continue
            else:
                null_trial_dates.append(key[1])

            if fan_model.trial_links:
                filtered_trial_links = list(filter(None, fan_model.trial_links))

                if filtered_trial_links:
                    result[key].setdefault('trial_links', []).append(
                        filtered_trial_links[-1]
                    )

                    continue

        if link_logging_data:
            execute_task_write_link_logging(link_logging_data)

        # THIS CHECK OFF
        # cls.check_null_trial_dates(null_trial_dates)

        return result

    @classmethod
    def check_is_trial_link(cls, url: str) -> bool:
        """
        Check that url is trial link

        Args:
            url (str): Url of trial link
        Returns:
            bool: True if url is trial link, False otherwise.
        """
        return cls.TRIAL_LINK_IDENTIFIER in url.strip()

    @classmethod
    def get_trial_link_from_db(cls, url: str) -> TrialLinks | CampaignLinks | None:
        """
        This method retrieves a trial link or a campaign link object
        from the database based on the provided URL.

        Parameters:
        - url: str: The URL for which the link object needs to be retrieved.

        Returns:
        - TrialLinks | CampaignLinks: The trial link or
        campaign link object retrieved from the database.
        """
        is_trial_link = cls.check_is_trial_link(url)

        if is_trial_link:
            link_from_db = TrialLinks.objects.filter(link_url=url).first()
        else:
            link_from_db = CampaignLinks.objects.filter(link_url=url).first()

        if link_from_db is None:

            if is_trial_link:
                link_from_db = CampaignLinks.objects.filter(link_url=url).first()
            else:
                link_from_db = TrialLinks.objects.filter(link_url=url).first()

        return link_from_db

    @classmethod
    def calculation(
        cls,
        trial_link: str | None,
        cost_result: Decimal | None,
        fans_count: int | None,
        saved_revenue: Decimal | None = None,
        ads_sales: list[AdsSale] | None = None,
    ) -> Calculator:
        """
        Calculation for ads


        Args:
            trial_link (str | None): Trial link
            cost_result (Decimal | None): Cost result of trial link
            saved_revenue (Decimal | None): Saved revenue if Ads was parsed
            fans_count (int | None): Fans count
            ads_sales (list[AdsSale] | None): Ads sales
        """
        calculator = Calculator(
            trial_link=trial_link,
            cost_result=cost_result,
            revenue=saved_revenue or Decimal(0),
            fans_count=fans_count,
            ads_sales=ads_sales,
        )

        if not trial_link:
            return calculator

        link_from_of_db = cls.get_trial_link_from_db(trial_link)

        if not link_from_of_db:
            return calculator

        creation_date = link_from_of_db.creation_date
        model_id = link_from_of_db.model_id
        start_sales_date = (
            creation_date if saved_revenue is None else cls.PARSE_DATE or creation_date
        )
        sales = (
            Sales.objects.filter(
                trans_date__gte=start_sales_date,
                model_id=model_id,
                trans_date__isnull=False,
                amount__isnull=False,
                fan__isnull=False,
            )
            .order_by('trans_date')
            .only('trans_id', 'trans_date', 'amount', 'fan', 'model', 'type')
        )

        if ads_sales:
            sales = sales.exclude(
                trans_id__in=[ads_sale.trans_id for ads_sale in ads_sales]
            )

        (
            grouped_sales,
            fans_ids,
            sales_ids,
            fans_models_refresh_data,
        ) = cls.group_sales_data(sales)

        fans_models = cls.get_fans_models_by_fans_ids(fans_ids, int(model_id))

        calculator = cls.get_revenue_and_claims_count_for_a_single_trial_link(
            link_from_db=link_from_of_db,
            calculator=calculator,
            grouped_sales=grouped_sales,
            grouped_fans_models=fans_models,
        )

        return calculator

    def update_calculated_fields(self):
        """
        Update calculated fields
        """
        calculator = self.calculation(
            ads_sales=list(self.instance.sales.all()),
            trial_link=self.instance.trial_link,
            cost_result=self.instance.phantom_cost or self.instance.cost_result,
            fans_count=self.instance.fans_count,
            saved_revenue=self.instance.saved_revenue,
        )
        updated = False

        AdsSale.objects.bulk_create(
            [
                AdsSale(
                    ads=self.instance,
                    trans_id=sale.trans_id,
                    trans_date=sale.trans_date,
                    amount=sale.amount,
                    fan_id=sale.fan_id,
                    model_id=sale.model_id,
                    type=sale.type,
                )
                for sale in calculator.new_sales
            ],
            batch_size=500,
            ignore_conflicts=True,
        )

        for field, value in calculator.get_dict().items():
            if not getattr(self.instance, field) == value:

                if not updated:
                    updated = True

                setattr(self.instance, field, value)

        updated = self._update_ad_status() or updated
        updated = self._update_ads_debt() or updated
        updated = self._update_date_counter_extra(self.instance) or updated

        if updated:
            self.instance.save()

    @staticmethod
    def _get_creation_date_or_url(
        trial_date: dict,
    ) -> tuple[datetime | None, str | None]:
        """
        Static method to retrieve the creation dates from the provided dictionary.

        Parameters:
            trial_date (dict): A dictionary containing the creation date or url.

        Returns:
            tuple: A tuple containing the filtered_trial_link_creation_date
            and the filtered_campaign_link_creation_date.

        """
        filtered_trial_link_creation_date = None
        filtered_campaign_link_url = None

        if 'trial_link' in trial_date:

            filtered_trial_link_creation_date = trial_date['trial_link']

        elif 'campaign' in trial_date:

            filtered_campaign_link_url = trial_date['campaign_link_url']

        return filtered_trial_link_creation_date, filtered_campaign_link_url

    @classmethod
    def group_sales_by_creation_date_and_url(
        cls,
        grouped_sales: dict[tuple[str, str], list[Sales]],
        fans_models: dict[tuple[str, str], dict[str, list]],
    ) -> tuple[dict[datetime, list[Sales]], dict[str, list[Sales]]]:
        """
        Group sales by creation date

        Args:
            grouped_sales (dict[tuple[str, str], list[Sales]]): Grouped sales
            fans_models (ddict[tuple[str, str], dict[str, list]]): Grouped fans models
        """
        trial_links_creation_date_sales = {}
        campaign_links_url_sales = {}

        for key, sales in grouped_sales.items():
            fan_model_data = fans_models.get(key)

            if not fan_model_data:
                continue

            for sale in sales:
                trial_dates = fan_model_data.get('trial_dates')
                trial_links = fan_model_data.get('trial_links')
                filtered_trial_link_creation_date = None
                filtered_campaign_link_url = None

                if trial_dates:
                    for trial_date in trial_dates:
                        if sale.trans_date >= trial_date['join_date']:
                            (
                                filtered_trial_link_creation_date,
                                filtered_campaign_link_url,
                            ) = cls._get_creation_date_or_url(trial_date)

                            break

                elif trial_links:
                    filtered_trial_link_creation_date = trial_links[0]

                if filtered_trial_link_creation_date:
                    trial_links_creation_date_sales.setdefault(
                        filtered_trial_link_creation_date, []
                    ).append(sale)
                elif filtered_campaign_link_url:
                    campaign_links_url_sales.setdefault(
                        filtered_campaign_link_url, []
                    ).append(sale)

        return trial_links_creation_date_sales, campaign_links_url_sales

    def _add_spenders(self, new_sales: list[Sales]):
        """
        Create new sales for ads and update spenders

        Args:
            new_sales (list[Sales]): Sales
        """
        spenders = {}
        instance_sales = self.instance.sales.all()

        if instance_sales:
            for ads_sale in instance_sales:
                spender_amount = spenders.setdefault(ads_sale.fan_id, 0)
                spenders[ads_sale.fan_id] = spender_amount + ads_sale.amount

        if new_sales:
            new_ads_sales = []

            for new_sale in new_sales:
                spender_amount = spenders.setdefault(new_sale.fan_id, 0)
                spenders[new_sale.fan_id] = spender_amount + Decimal(new_sale.amount)
                new_ads_sales.append(
                    AdsSale(
                        ads=self.instance,
                        trans_id=new_sale.trans_id,
                        trans_date=new_sale.trans_date,
                        amount=new_sale.amount,
                        fan_id=new_sale.fan_id,
                        model_id=new_sale.model_id,
                        type=new_sale.type,
                    )
                )

            AdsSale.objects.bulk_create(
                new_ads_sales, batch_size=500, ignore_conflicts=True
            )

        spenders_low = 0
        spenders_high = 0

        for fan_id, amount in spenders.items():
            if 100 <= amount < 500:
                spenders_low += 1
            elif amount >= 500:
                spenders_high += 1

        self.instance.spenders = len(spenders)
        self.instance.spenders_low = spenders_low
        self.instance.spenders_high = spenders_high

    def add_stats_data_to_ads(
        self,
        sales: list[Sales],
        exclude_sales_ids: list[str] = None,
    ):
        """
        Add new revenue

        Args:
            sales (list[Sales]): Sales
            exclude_sales_ids (list[str]): List of sales ids to exclude from revenue calculation
                (default: None)
        """
        revenue = Decimal(0)
        new_sales = []

        for sale in sales:

            if exclude_sales_ids and sale.trans_id in exclude_sales_ids:
                continue

            revenue += Decimal(sale.amount).quantize(Decimal('1.00'))
            new_sales.append(sale)

        if self.instance.revenue is not None:
            self.instance.revenue += revenue
        else:
            self.instance.revenue = revenue

        cost_attr = 'phantom_cost' if self.instance.phantom_cost else 'cost_result'
        if getattr(self.instance, cost_attr, None):
            self.instance.profit = self.instance.revenue - getattr(
                self.instance, cost_attr
            )
            self.instance.romi = (
                self.instance.profit / getattr(self.instance, cost_attr) * 100
            ).quantize(Decimal('1.00'))

        self._add_spenders(new_sales)

        return self.instance

    @classmethod
    def _filter_trial_link_from_db(
        cls,
        url: str,
        url_trial_links: dict[str, TrialLinks],
        url_campaign_links: [str, CampaignLinks],
    ) -> TrialLinks | CampaignLinks | None:
        """
        Filters a given URL from the database of trial and campaign links.

        Parameters:
        - url (str): The URL to be filtered.
        - url_trial_links (dict[str, TrialLinks]): The dictionary of trial links where the URL will be searched.
        - url_campaign_links ([str, CampaignLinks]): The list of campaign links where the URL will be searched.

        Returns:
        - TrialLinks | CampaignLinks | None: The filtered link from the database. R
        returns None if no matching link is found.
        """
        is_trial_link = cls.check_is_trial_link(url)

        if is_trial_link:
            link_from_db = url_trial_links.get(url)

            if not link_from_db:
                link_from_db = url_campaign_links.get(url)

        else:
            link_from_db = url_campaign_links.get(url)

            if not link_from_db:
                link_from_db = url_trial_links.get(url)

        return link_from_db

    def _update_ad_status(self) -> bool:
        """
        Update ad status according to the current status and the number of claims.

        Parameters:
        - ad (Ads): The ad object to update.

        Returns:
        - bool: True if the ad status was updated, False otherwise.
        """
        need_to_update_in_the_db = False

        if self.instance.claims_count > 0:
            if self.instance.status:
                if self.instance.status.name.lower().strip() == 'draft':
                    self.instance.status = Status.objects.get(name='progress')
                    need_to_update_in_the_db = True

                if (
                    self.instance.status.name.lower().strip() == 'progress'
                    and self.instance.fans_count
                    and self.instance.claims_count >= self.instance.fans_count
                ):
                    self.instance.status = Status.objects.get(name='done')
                    need_to_update_in_the_db = True

        return need_to_update_in_the_db

    def _update_ads_debt(self):
        """
        Update ads debt
        """
        need_to_update_in_the_db = False

        new_debt = self.instance.fans_delta or 0 * self.instance.get_cost_per_fan()

        if self.instance.debt != new_debt:
            self.instance.debt = new_debt
            need_to_update_in_the_db = True

            logger.info(f'Update debt for ad #{self.instance.ads_number} to {new_debt}')

        return need_to_update_in_the_db

    @classmethod
    def _update_date_counter(
        cls, ad: Ads, link_from_db: TrialLinks | CampaignLinks
    ) -> Ads:
        """
        Updates the date_counter field of the given ad based on the link_from_db.claims_count
        """
        if (
            ad.date_counter is None
            and not ad.claims_count
            and link_from_db.claims_count
        ):
            ad.date_counter = timezone.now().date()

            logger.info(
                f'Update date_counter for ad #{ad.ads_number} to {ad.date_counter}'
            )

        try:
            if (
                ad.date_counter
                and not ad.date_counter_end
                and link_from_db.claims_count >= ad.fans_count
                and not ad.is_child
                and not ad.is_parent
                and not ad.refund_cost
                and (ad.status and ad.status.name != 'problem')
            ):
                ad.date_counter_end = timezone.now().date()
                ad.days_completion = (ad.date_counter_end - ad.date_counter).days

                logger.info(
                    f'Update date_counter_end for ad #{ad.ads_number} to {ad.date_counter_end}'
                )
        except TypeError:
            logger.error(f'Error updating date_counter for ad #{ad.ads_number}')

        return ad

    @classmethod
    def _update_ad(
        cls,
        ad: Ads,
        link_sales: list[Sales] | None,
        link_from_db: TrialLinks | CampaignLinks,
    ) -> bool:
        """
        Updates the given ad object with data from various sources.

        Parameters:
        - ad (Ads): The ad object to update.
        - link_sales (list[Sales] | None): The sales data linked to the ad. If None, no sales data is available.
        - link_from_db (TrialLinks | CampaignLinks): The link object from the database.

        Returns:
        - bool: True if the ad was updated, False otherwise.
        """
        need_to_update_in_the_db = False

        ad_service = cls(ad)

        if link_sales:
            ad = ad_service.add_stats_data_to_ads(
                sales=link_sales,
            )
            ad.arpu = ad.get_arpu()
            need_to_update_in_the_db = True

        if ad.claims_count != link_from_db.claims_count:
            ad = cls._update_date_counter(ad, link_from_db)
            ad.claims_count = link_from_db.claims_count
            ad.arpu = ad.get_arpu()
            need_to_update_in_the_db = True

        if ad.fans_count is not None:
            fans_delta = ad.fans_count - ad.claims_count
            today_date = timezone.now().date()
            days_diff = (today_date - ad.date).days

            fans_per_day = (
                Decimal(ad.claims_count / days_diff).quantize(Decimal('1.00'))
                if days_diff > 0
                else Decimal(0)
            )
            days_remains = (
                round(fans_delta / fans_per_day)
                if fans_per_day != 0 and fans_delta > 0
                else 0
            )
            probable_end_date = (
                today_date + timedelta(days=days_remains) if days_remains > 0 else None
            )

            if (
                ad.fans_delta != fans_delta
                or ad.fans_per_day != fans_per_day
                or ad.days_remains != days_remains
                or ad.probable_end_date != probable_end_date
            ):
                ad.fans_delta = fans_delta
                ad.fans_per_day = fans_per_day
                ad.days_remains = days_remains
                ad.probable_end_date = probable_end_date
                ad.debt = ad.fans_delta * ad.get_cost_per_fan()
                need_to_update_in_the_db = True

        need_to_update_in_the_db = (
            ad_service._update_ad_status() or need_to_update_in_the_db
        )

        return need_to_update_in_the_db

    @classmethod
    def generate_update_list(
        cls,
        ads_list: list[Ads],
        trial_links_creation_date_sales: dict[datetime, list[Sales]],
        campaign_links_url_sales: dict[str, list[Sales]],
        url_trial_links: dict[str, TrialLinks],
        url_campaign_links: dict[str, CampaignLinks],
    ) -> list[Ads]:
        """
        Generate update list.

        This method generates a list of ads that need to be updated based on the input parameters.
        The generated list contains only the ads that have changes or updates.

        Parameters:
            ads_list (list[Ads]): A list of ads.
            trial_links_creation_date_sales (dict[datetime, list[Sales]]): A dictionary that maps
            creation dates of trial links to a list of sales.
            campaign_links_url_sales: dict[str, list[Sales]]: A dictionary that maps
            url of campaign links to a list of sales.
            url_trial_links (dict[str, TrialLinks]): A dictionary that maps trial links to trial link objects.
            url_campaign_links (dict[str, CampaignLinks]): A dictionary that maps trial links to campaign link objects.

        Returns:
            list[Ads]: A list of ads that need to be updated.

        """
        update_list = []

        for ad in ads_list:
            link_from_db = cls._filter_trial_link_from_db(
                url=ad.trial_link,
                url_trial_links=url_trial_links,
                url_campaign_links=url_campaign_links,
            )

            if not link_from_db:
                continue

            link_sales = None

            if isinstance(link_from_db, TrialLinks):
                link_sales = trial_links_creation_date_sales.get(
                    link_from_db.creation_date
                )
            elif isinstance(link_from_db, CampaignLinks):
                link_sales = campaign_links_url_sales.get(link_from_db.link_url)

            add_to_update_list = cls._update_ad(
                ad=ad,
                link_sales=link_sales,
                link_from_db=link_from_db,
            )

            if add_to_update_list:
                update_list.append(ad)

        return update_list

    @classmethod
    def update_list_of_ads(
        cls,
        ads_list: list[Ads],
        sales: list[Sales],
        trial_links: list[TrialLinks],
        campaign_links: list[CampaignLinks],
    ) -> int:
        """
        Update list of ads with bulk_update(save not triggered)

        Args:
            ads_list (list[Ads]) list of Ads
            sales (list[Sales]) list of Sales
            trial_links (list[TrialLinks]) list of Trial Links,
            campaign_links (list[CampaignLinks]) list of Campaign Links,
        """
        grouped_sales, fans_ids, sales_ids, data_for_synchro_api = cls.group_sales_data(
            sales
        )

        fans_models = cls.get_fans_models_by_fans_ids(fans_ids)

        url_trial_links = {}

        for trial_link in trial_links:
            url_trial_links[trial_link.link_url.strip()] = trial_link

        url_campaign_links = {}

        for campaign_link in campaign_links:
            url_campaign_links[campaign_link.link_url.strip()] = campaign_link

        (
            trial_links_creation_date_sales,
            campaign_links_url_sales,
        ) = cls.group_sales_by_creation_date_and_url(
            grouped_sales=grouped_sales, fans_models=fans_models
        )

        update_list = cls.generate_update_list(
            ads_list=ads_list,
            trial_links_creation_date_sales=trial_links_creation_date_sales,
            campaign_links_url_sales=campaign_links_url_sales,
            url_trial_links=url_trial_links,
            url_campaign_links=url_campaign_links,
        )

        if update_list:
            result = cls.MODEL.objects.bulk_update(
                update_list,
                fields=[
                    'revenue',
                    'profit',
                    'romi',
                    'claims_count',
                    'fans_delta',
                    'fans_per_day',
                    'days_remains',
                    'probable_end_date',
                    'spenders',
                    'spenders_high',
                    'spenders_low',
                    'date_counter',
                    'status',
                    'date_counter_end',
                    'days_completion',
                    'debt',
                    'arpu',
                ],
                batch_size=500,
            )

            post_bulk_update.send(Ads, instances=update_list)

            return result

        return 0

    @staticmethod
    def _update_date_counter_extra(ads: MODEL) -> bool:
        if (
            ads.trial_link
            and ads.claims_count > 0
            and not ads.date_counter_extra
            and not ads.date_counter
        ):
            ads.date_counter_extra = timezone.now().date()

            return True
        return False

    @classmethod
    def create(cls, validated_data: dict, user: User) -> MODEL:
        """
        Create and return a new `Ads` instance, given the validated data.

        Args:
            validated_data (dict): Validated data for creating Ads instance.
            user (User): User who created the Ads instance.
        """
        trial_link = validated_data.get('trial_link')
        fans_count = validated_data.get('fans_count')

        cost = validated_data.get('cost')
        refund_cost = validated_data.get('refund_cost')
        phantom_cost = validated_data.get('phantom_cost')
        cost_result = cost

        if refund_cost and cost_result:
            cost_result -= refund_cost

        calculator = cls.calculation(
            trial_link=trial_link,
            cost_result=phantom_cost or cost_result,
            fans_count=fans_count,
        )
        department = Ads.ROLE_DEPARTMENT_MAP.get(user.role.name, None)

        ads_instance = cls.MODEL(
            **validated_data, **calculator.get_dict(), department=department
        )
        cls(ads_instance)._update_ad_status()
        cls(ads_instance)._update_ads_debt()
        cls._update_date_counter_extra(ads_instance)

        if ads_instance.promo:
            ads_instance.new_donor_filled_date = cls._get_new_donor_filled_date(
                old_promo=None,
                new_promo=ads_instance.promo
            )

        ads_instance.save()

        AdsSale.objects.bulk_create(
            [
                AdsSale(
                    ads=ads_instance,
                    trans_id=sale.trans_id,
                    trans_date=sale.trans_date,
                    amount=sale.amount,
                    fan_id=sale.fan_id,
                    model_id=sale.model_id,
                    type=sale.type,
                )
                for sale in calculator.new_sales
            ],
            batch_size=500,
            ignore_conflicts=True,
        )

        return ads_instance

    @classmethod
    def get_catalog(cls) -> dict:
        """
        Get catalog data for related fields
        """
        from ads.serializers import (
            DonorListSerializer,
            PaymentMethodSerializer,
            PlatformTypeSerializer,
            PromoSerializer,
        )

        only_fans_model_queryset = OnlyFansModel.objects.all()
        only_fans_models = OnlyFansModelsSerializer(
            only_fans_model_queryset, many=True
        ).data

        donor_queryset = Donor.objects.select_related('black_list')
        donors = DonorListSerializer(donor_queryset, many=True).data

        payment_method_queryset = PaymentMethod.objects.all()
        payment_methods = PaymentMethodSerializer(
            payment_method_queryset, many=True
        ).data

        platform_type_queryset = PlatformType.objects.all()
        platform_types = PlatformTypeSerializer(platform_type_queryset, many=True).data

        marketer_queryset = get_user_model().objects.filter(role__name='marketer')
        marketers = PureUserSerializer(marketer_queryset, many=True).data

        promo_queryset = Promo.objects.all()
        promos = PromoSerializer(promo_queryset, many=True).data

        return {
            'only_fans_models': only_fans_models,
            'donors': donors,
            'payment_methods': payment_methods,
            'platform_types': platform_types,
            'marketers': marketers,
            'promos': promos,
        }

    @staticmethod
    def check_one_of_the_fields_is_not_empty(ads: Ads) -> bool:
        """
        Check one of the fields is not empty

        Args:
            ads: Ads instance
        """
        attributes = ['donor', 'claims_count', 'fans_count', 'romi']

        if not any(getattr(ads, attribute) for attribute in attributes):
            return False

        return True

    @classmethod
    def get_ads_summary_data(cls, rows_list: list[Ads]) -> list[dict]:
        """
        Get summary data for ads

        Args:
            rows_list: list of Ads instances
        """
        from ads.serializers import NestedAdsSummarySerializer

        grouped_data = {}

        for ads in rows_list:
            if cls.check_one_of_the_fields_is_not_empty(ads):
                grouped_data.setdefault(ads.date, []).append(
                    NestedAdsSummarySerializer(ads).data
                )

        return [{'date': date, 'items': items} for date, items in grouped_data.items()]

    @classmethod
    def get_total_spend_data(
        cls,
        ads_list: list[Ads],
        date_filter_parameter: str,
        by_phantom_cost: bool,
        by_friends_stories: bool
    ) -> list[dict]:
        """
        Get total spend data for a list of ads.

        Args:
            ads_list (list[Ads]): A list of Ads objects
            date_filter_parameter (str): Define to group data which date field
            by_phantom_cost (bool): Use phantom_cost value instead of result_cost
            by_friends_stories (bool): Use friends_stories value instead of result_cost

        Returns:
            list[dict]: A list of dictionaries representing the total spend data.
            Each dictionary has two keys, 'date' and
            'promos'. 'date' represents the date of the ads,
            and 'promos' represents a list of promo data associated with the ads.
        """
        grouped_data = {}

        for ads in ads_list:
            if date_filter_parameter == "date_counter":
                ads.date_counter = ads.date_counter or ads.date_counter_extra

                if (
                    not ads.date_counter
                    and by_friends_stories
                    and hasattr(ads, 'promo')
                    and ads.promo
                    and ads.promo.name.lower() in {
                        'friends for model',
                        'stories for models',
                        'new donor friends',
                        'new donor stories'
                    }
                ):
                    ads.date_counter = ads.date

            group_field_value = getattr(ads, date_filter_parameter, None)

            if group_field_value is None:
                continue

            if date_filter_parameter == 'refund_date':
                ads.cost_result = ads.refund_cost
            elif date_filter_parameter == 'buy_date':
                ads.cost_result = ads.cost

            if by_phantom_cost and ads.phantom_cost:
                ads.cost_result = ads.phantom_cost

            grouped_data.setdefault(group_field_value, {}).setdefault(
                ads.only_fans_model.nickname,
                {'order': ads.only_fans_model.index_number, 'cost': Decimal('0')},
            )
            grouped_data[group_field_value][ads.only_fans_model.nickname][
                'cost'
            ] += Decimal(ads.cost_result)

        return [
            {
                'date': date,
                'promos': sorted(
                    [
                        {
                            'nickname': nickname,
                            'order': data['order'],
                            'cost': data['cost'],
                        }
                        for nickname, data in items.items()
                    ],
                    key=lambda x: (x['order'] is None, x['order']),
                ),
            }
            for date, items in grouped_data.items()
        ]

    @staticmethod
    def get_total_spend_sum(
        min_date: datetime,
        max_date: datetime,
        promo_ids: list[str] | None,
        date_filter_parameter: str,
        by_phantom_cost: bool,
        by_friends_stories: bool,
    ) -> Decimal:
        """
        Get the total spend sum for a given date range and date filter parameter.

        Args:
            min_date (datetime): The minimum date for the range.
            max_date (datetime): The maximum date for the range.
            promo_ids (list[str] | None): The list of promo ids to filter the data. Defaults to None.
            date_filter_parameter (str): The date filter parameter.
            by_phantom_cost (bool): Use phantom cost instead of cost_result
            by_friends_stories (bool): Use friends_stories instead of cost_result
        """
        date_filter = {
            f"{date_filter_parameter}__isnull": False,
            f"{date_filter_parameter}__gte": min_date,
            f"{date_filter_parameter}__lte": max_date,
        }

        if date_filter_parameter == 'date_counter' and by_friends_stories:
            queryset = Ads.objects.filter(
                Q(
                    date_counter__isnull=False,
                    date_counter__gte=min_date,
                    date_counter__lte=max_date
                )
                | Q(
                    date_counter__isnull=True,
                    date_counter_extra__gte=min_date,
                    date_counter_extra__lte=max_date
                )
                | Q(
                    date_counter__isnull=True,
                    date_counter_extra__isnull=True,
                    promo__name__in=[
                        'Friends for model',
                        'Stories for models',
                        'New donor friends',
                        'New donor stories'
                    ],
                    date__gte=min_date,
                    date__lte=max_date
                ),
                only_fans_model__index_number__isnull=False,
                department=Ads.DepartmentChoices.MARKETING
            ).distinct()
        elif date_filter_parameter == 'date_counter':
            queryset = Ads.objects.filter(
                Q(
                    Q(
                        date_counter__isnull=False,
                        date_counter__gte=min_date,
                        date_counter__lte=max_date
                    )
                    | Q(
                        date_counter__isnull=True,
                        date_counter_extra__isnull=False,
                        date_counter_extra__gte=min_date,
                        date_counter_extra__lte=max_date
                    )
                ),
                only_fans_model__index_number__isnull=False,
                department=Ads.DepartmentChoices.MARKETING
            ).distinct()
        else:
            queryset = Ads.objects.filter(
                **date_filter,
                only_fans_model__index_number__isnull=False,
                department=Ads.DepartmentChoices.MARKETING
            ).distinct()

        if promo_ids:
            queryset = queryset.filter(
                promo_id__in=promo_ids,
            )

        if date_filter_parameter == 'refund_date':
            total_sum = queryset.aggregate(Sum('refund_cost'))['refund_cost__sum'] or Decimal(0)
        elif date_filter_parameter == 'buy_date':
            total_sum = queryset.aggregate(Sum('cost'))['cost__sum'] or Decimal(0)
        elif by_phantom_cost:
            cost_result_sum = queryset.filter(phantom_cost__isnull=True).aggregate(
                Sum('cost_result')
            )['cost_result__sum'] or Decimal(0)

            phantom_cost_sum = queryset.filter(phantom_cost__isnull=False).aggregate(
                Sum('phantom_cost')
            )['phantom_cost__sum'] or Decimal(0)

            total_sum = Decimal(cost_result_sum) + Decimal(phantom_cost_sum)
        else:
            total_sum = queryset.aggregate(Sum('cost_result'))['cost_result__sum'] or Decimal(0)

        return total_sum

    @classmethod
    def get_total_spend_table_data_string(
        cls,
        ads_list: list[Ads],
        by_phantom_cost: bool,
        by_friends_stories: bool,
        min_date: str = None,
        max_date: str = None,
        promo_ids: list[str] | None = None,
        date_filter_parameter: str = 'date',

    ) -> str:
        """
        Get the table string representation of the total spend data for a given list of ads.

        Attrs:
        - ads_list: List of Ads objects.
        - min_date: Optional. Minimum date for the table. Defaults to the earliest date available in the data.
        - max_date: Optional. Maximum date for the table. Defaults to the latest date available in the data.
        - promo_ids: Optional. List of promo ids to filter the data. Defaults to None.
        - date_filter_parameter: Optional. Define to group data which date field. Defaults to 'date'.
        - by_phantom_cost: bool. Use phantom cost instead of cost_result
        - by_friends_stories: bool. Use friends_stories instead of cost_result
        """
        data = cls.get_total_spend_data(
            ads_list=ads_list,
            date_filter_parameter=date_filter_parameter,
            by_phantom_cost=by_phantom_cost,
            by_friends_stories=by_friends_stories,
        )

        if not data:
            return ''

        min_date = parse_date(min_date) if min_date else data[0]["date"]
        max_date = parse_date(max_date) if max_date else data[-1]["date"]
        date_range = [
            min_date + timedelta(days=x) for x in range((max_date - min_date).days + 1)
        ]

        nicknames = (
            OnlyFansModel.objects.filter(index_number__isnull=False)
            .values_list('nickname', flat=True)
            .order_by('index_number')
        )

        table = OrderedDict(
            (date, OrderedDict((nickname, 0) for nickname in nicknames))
            for date in date_range
        )

        for entry in data:
            date = entry['date']

            for promo in entry['promos']:
                table[date][promo['nickname']] = promo['cost']

        table_string = 'Date\t' + '\t'.join(nicknames) + '\n'

        for date, costs in table.items():
            line = [str(costs[nickname]).replace('.', ',') for nickname in nicknames]
            table_string += f'{date}\t' + '\t'.join(line) + '\n'

        total_sum = cls.get_total_spend_sum(
            min_date=min_date,
            max_date=max_date,
            promo_ids=promo_ids,
            date_filter_parameter=date_filter_parameter,
            by_phantom_cost=by_phantom_cost,
            by_friends_stories=by_friends_stories,
        )
        table_string += f'\nTotal sum:\t{total_sum}\n'
        table_string += 'Applied filters:\n'
        table_string += f'  date filter parameter: {date_filter_parameter}\n'
        table_string += f'  by phantom cost: {by_phantom_cost}\n'
        table_string += f'  by friends stories: {by_friends_stories}\n'
        table_string += f'  min date: {min_date.strftime("%d.%m.%Y")}\n'
        table_string += f'  max date: {max_date.strftime("%d.%m.%Y")}\n'
        table_string += f'  promos: {promo_ids or "all"}\n'

        return table_string

    @classmethod
    def get_ads_list_data_with_empty_dates(
        cls,
        ads_list: list[Ads],
        min_date: str = None,
        max_date: str = None,
        ordering: str = None,
    ):
        """
        Add empty ads to the result list

        Args:
            ads_list (list[Ads]): A list of Ads objects.
            min_date (str, optional): Minimum date for the data. Defaults to the earliest date available in the data.
            max_date (str, optional): Maximum date for the data. Defaults to the latest date available in the data.
            ordering (str, optional): Ordering for the data. Defaults to None.
        """
        if not ads_list:
            if not min_date or not max_date:
                return []

        min_date_parsed = (
            parse_date(min_date)
            if min_date
            else min(ads_list, key=lambda x: x.date).date
        )
        max_date_parsed = (
            parse_date(max_date)
            if max_date
            else max(ads_list, key=lambda x: x.date).date
        )

        date_list = [
            min_date_parsed + timedelta(days=x)
            for x in range((max_date_parsed - min_date_parsed).days + 1)
        ]

        if ordering and (min_date or max_date) and '-date' in ordering:
            date_list.reverse()

        grouped_data_by_dates = {}

        for ad in ads_list:
            grouped_data_by_dates.setdefault(ad.date, []).append(ad)

        result = []

        for date in date_list:
            if date in grouped_data_by_dates:
                result.extend(grouped_data_by_dates[date])
            else:
                fake_ad = Ads(date=date)
                fake_ad.id = None
                result.append(fake_ad)

        return result

    @classmethod
    def get_sorted_finance_data_intervals(cls) -> list[int]:
        """
        Get the sorted finance data intervals.

        Returns:
            list[int]: The sorted finance data intervals.
        """
        return sorted(cls.FINANCE_DATA_INTERVALS)

    @classmethod
    def get_future_total_purchase(cls, ads_list: list[Ads]) -> dict[str, list | str]:
        """
        Class method to calculate the future total purchase based on a list of ads.

        Parameters:
        - ads_list (list[Ads]): A list of ads.

        Returns:
        - dict[str, list | str]: A dictionary containing the following information:
            - 'points': A list of dictionaries representing the interval and cost for each interval.
            - 'yesterday_differ': A string indicating the difference between
            the current cost and the cost from the previous day. Possible values are 'positive', 'negative', or 'equal'.
        """
        sorted_intervals = cls.get_sorted_finance_data_intervals()

        interval_cutoffs = {
            interval: timezone.now().date() - timedelta(days=interval)
            for interval in sorted_intervals
        }
        cost_results = {interval: Decimal(0) for interval in sorted_intervals}

        for ad in ads_list:
            for interval, cutoff_date in interval_cutoffs.items():
                if ad.date > cutoff_date > ad.buy_date:
                    cost_results[interval] += ad.cost_result

        yesterday_cost = cost_results.pop(1, 0)

        return {
            'points': [
                {'interval': interval, 'cost_result': cost_result}
                for interval, cost_result in cost_results.items()
            ],
            'yesterday_differ': (
                'positive'
                if cost_results[0] > yesterday_cost
                else 'negative'
                if cost_results[0] < yesterday_cost
                else 'equal'
            ),
        }

    @classmethod
    def get_average_purchase_on_model(cls, ads_list: list[Ads]) -> dict:
        """
        Get the average purchase on specific models.

        Parameters:
        - ads_list (list[Ads]): A list of Ads objects.

        Returns:
        - dict: A dictionary containing the average purchase on different models
        and the difference compared to yesterday's purchase.
        """
        sorted_intervals = cls.get_sorted_finance_data_intervals()

        interval_cutoffs = {
            interval: timezone.now().date() - timedelta(days=interval)
            for interval in sorted_intervals
        }
        interval_cost = {interval: Decimal(0) for interval in sorted_intervals}
        interval_models = {interval: set() for interval in sorted_intervals}

        for ad in ads_list:
            for interval, cutoff_date in interval_cutoffs.items():
                if ad.date > cutoff_date > ad.buy_date:
                    interval_cost[interval] += ad.cost_result
                if (
                    ad.date > cutoff_date
                    and ad.platform_type is not None
                    and ad.platform_type.name == 'Guaranteed'
                ):
                    interval_models[interval].add(ad.only_fans_model_id)

        yesterday_interval_models = interval_models.pop(1, set())
        yesterday_interval_cost = interval_cost.pop(1, 0)

        if yesterday_interval_models:
            yesterday_purchase = yesterday_interval_cost / len(
                yesterday_interval_models
            )
        else:
            yesterday_purchase = 0

        today_purchase = (
            interval_cost[0] / len(interval_models[0]) if interval_models[0] else 0
        )

        return {
            'points': [
                {
                    'interval': interval,
                    'cost_result': round(cost_result / len(interval_models[interval])),
                }
                if interval_models[interval]
                else {'interval': interval, 'cost_result': 0}
                for interval, cost_result in interval_cost.items()
            ],
            'yesterday_differ': (
                'positive'
                if today_purchase > yesterday_purchase
                else 'negative'
                if today_purchase < yesterday_purchase
                else 'equal'
            ),
        }

    @classmethod
    def generate_month_list(cls, ads_list: list[Ads]) -> list[datetime.date]:
        """
        Generates a list of unique month values based on the dates of the ads provided.

        Parameters:
        - cls (class object): The class itself.
        - ads_list (list[Ads]): A list of Ads objects containing the dates.

        Returns:
        - month_list (list[datetime.date]): A list of unique month values as date objects.
        """
        month_list = []
        min_date = ads_list[0].date.replace(day=1)
        max_date = ads_list[-1].date.replace(day=1)

        month = min_date

        while month <= max_date:
            month_list.append(month)
            month = month + relativedelta(months=1)

        return month_list

    @classmethod
    def get_debts(cls, ads_list: list[Ads]) -> list[MonthAmountRecord]:
        """
        Get the debts for a given list of Ads.

        Parameters:
        - ads_list: A list of Ads objects.

        Return type:
        - list[MonthAmountRecord]: A list of MonthAmountRecord objects representing the debts for each month.
        """
        if not ads_list:
            return []

        month_list = cls.generate_month_list(ads_list)
        month_debts = {month: MonthAmountRecord(month=month) for month in month_list}

        for ads in ads_list:

            if (
                not getattr(ads, 'fans_count', None)
                or not getattr(ads, 'cost_result', None)
                or not getattr(ads, 'fans_delta', None)
            ):
                continue

            debt = ads.fans_delta * ads.cost_per_fan
            month_debt_date = ads.date.replace(day=1)
            month_debts[month_debt_date].add_amount(debt)
            month_debts[month_debt_date].add_fans_amount(ads.fans_delta)
            month_debts[month_debt_date].add_model_debt(
                only_fans_model=ads.only_fans_model,
                amount=debt,
                fans_amount=ads.fans_delta,
            )

        return list(month_debts.values())

    @classmethod
    def get_debts_summary(cls, ads_list: list[Ads]) -> list[dict[str, datetime | list]]:
        """
        Get the debts summary for a given list of Ads.

        Parameters:
        - ads_list: A list of Ads objects.

        Return type:
        - dict[str, list[dict]]: A dictionary containing the debts summary for each month.
        """
        month_list = cls.generate_month_list(ads_list)
        month_debts = {month: {} for month in month_list}

        for ads in ads_list:

            if (
                not getattr(ads, 'fans_count', None)
                or not getattr(ads, 'cost_result', None)
                or not getattr(ads, 'fans_delta', None)
            ):
                continue

            fan_price = ads.cost_result / ads.fans_count
            debt = ads.fans_delta * fan_price
            month = ads.date.replace(day=1)

            month_debts[month].setdefault(ads.only_fans_model, Decimal(0))
            month_debts[month][ads.only_fans_model] += debt

        result = {}

        for month, debts in month_debts.items():
            result.setdefault(month, [])

            for model, value in debts.items():
                result[month].append(
                    {
                        'model': model.nickname,
                        'value': value,
                        'index': model.index_number,
                    }
                )

        return [{'month': month, 'debts': debts} for month, debts in result.items()]

    @classmethod
    def get_debts_summary_string(cls, ads_list: list[Ads]) -> str:
        """
        Get the debts summary string representation.

        Parameters:
        - ads: A list of Ads objects.

        Return type:
        - str: A string containing the debts summary.
        """
        return cls._get_table_data_string(cls.get_debts_summary(ads_list), 'debts')

    @classmethod
    def _get_table_data_string(
        cls,
        table_data: list[dict[str, datetime | list[dict]]],
        specific_data_field: str,
    ) -> str:
        """
        Get the table data string representation.

        Parameters:
        - table_data (list[dict]): A list of dictionaries containing the table data.

        Return type:
        - str: A string containing the table data.
        """
        date_range = sorted([entry['month'] for entry in table_data])
        nicknames = (
            OnlyFansModel.objects.filter()
            .values_list('nickname', flat=True)
            .order_by('nickname')
        )

        table = OrderedDict(
            (nickname, OrderedDict((month, 0) for month in date_range))
            for nickname in nicknames
        )

        for entry in table_data:
            month = entry['month']

            for specific_data in entry[specific_data_field]:
                table[specific_data['model']][month] = specific_data['value']

        table_string = (
            'Model\t' + '\t'.join(date.strftime('%m.%Y') for date in date_range) + '\n'
        )

        for nickname, debts in table.items():
            line = [str(debts[month]) for month in date_range]
            table_string += nickname + '\t' + '\t'.join(line) + '\n'

        return table_string

    @classmethod
    def get_future_purchases(cls, ads_list: list[Ads]):
        """
        Get the future purchases for a given list of Ads.

        Parameters:
        - ads_list: A list of Ads objects.

        Return type:
        - list[MonthAmountRecord]: A list of MonthAmountRecord objects representing the future purchases for each month.
        """
        if not ads_list:
            return []

        month_list = cls.generate_month_list(ads_list)
        month_future_purchases = {
            month: MonthAmountRecord(month=month) for month in month_list
        }

        for ads in ads_list:
            if not getattr(ads, 'cost_result', None):
                continue

            month_future_purchases[ads.date.replace(day=1)].add_amount(ads.cost_result)

        return list(month_future_purchases.values())

    @classmethod
    def get_future_purchases_string(cls, ads_list: list[Ads]) -> str:
        """
        Get the future purchases string for a given list of Ads.

        Parameters:
        - ads_list: A list of Ads objects.

        Return type:
        - str: A string containing the future purchases for each month.
        """
        future_purchases = cls.get_future_purchases(ads_list)

        if not future_purchases:
            return ""

        future_purchases_string = ""

        for purchase in future_purchases:
            future_purchases_string += (
                f"{purchase.month.strftime('%m.%Y')}\t{purchase.amount}\n"
            )

        total = sum(purchase.amount for purchase in future_purchases)
        future_purchases_string += f"total\t{total}\n"

        return future_purchases_string

    @classmethod
    def get_debts_string(cls, ads_list: list[Ads]) -> str:
        """
        Get the debts string for a given list of Ads.

        Parameters:
        - ads_list: A list of Ads objects.

        Return type:
        - str: A string containing the debts for each month.
        """
        debts = cls.get_debts(ads_list)
        debts_string = ""

        for debt in debts:
            debts_string += f"{debt.month.strftime('%m.%Y')}\t{debt.amount}\n"

        total = sum(debt.amount for debt in debts)
        debts_string += f"total\t{total}\n"

        return debts_string

    @staticmethod
    def get_model_currency_and_total(
        model_future_purchases: dict[str, Decimal],
        model_revenues: dict[str, float],
        model_debts: dict[str, dict[str, Decimal]],
    ) -> tuple[dict[str, Decimal], dict[str, Decimal]]:
        """
        Get the model currency and total for a given list of Ads.

        Parameters:
        - model_future_purchases: A dictionary containing the future purchases for each model.
        - model_revenues: A dictionary containing the revenues for each model.
        - model_debts: A dictionary containing the debts for each model.

        Returns:
        - tuple[dict[str, Decimal], dict[str, Decimal]]: A tuple containing the model currency and total dictionaries.
        """
        model_currency = {}
        model_total = {}

        for model, cost_result in model_future_purchases.items():
            revenue = model_revenues.get(model.lower(), 0)

            model_currency[model] = (
                model_future_purchases[model] / Decimal(revenue) * 100 if revenue else 0
            )
            debt = model_debts.get(model, {}).get('debt', 0)
            model_total[model] = cost_result + debt

        return model_currency, model_total

    @classmethod
    def get_today_report_data(cls) -> list[dict]:
        """
        Get the today's report data.

        Returns:
        - list[dict]: A list of dictionaries containing the today's report data.
        """
        today_date = timezone.now().date()

        model_future_purchase = (
            Ads.objects.filter(
                date__gte=today_date,
                cost_result__isnull=False,
                department=Ads.DepartmentChoices.MARKETING,
            )
            .values(model=F('only_fans_model__username_of'))
            .annotate(total_cost_result=Sum('cost_result'))
            .values('model', 'total_cost_result')
        )
        model_future_purchase = {
            data['model']: data['total_cost_result'] for data in model_future_purchase
        }

        model_debt = (
            Ads.objects.filter(
                status__name__in=cls.DEBT_QUERYSET_FILTER_STATUSES,
                fans_delta__isnull=False,
                fans_delta__gt=0,
                cost_result__isnull=False,
                fans_count__gt=0,
                date__lte=today_date,
                department=Ads.DepartmentChoices.MARKETING,
            )
            .values(model=F('only_fans_model__username_of'))
            .annotate(
                debt_fans=Sum('fans_delta'),
                debt=Sum('fans_delta') * (Sum('cost_result') / Sum('fans_count')),
            )
            .values('model', 'debt_fans', 'debt')
        )
        model_debt = {
            data['model']: {'debt': data['debt'], 'debt_fans': data['debt_fans']}
            for data in model_debt
        }

        model_revenue = (
            Sales.objects.filter(
                amount__isnull=False,
                trans_date__gte=today_date - timedelta(days=30),
            )
            .values('model')
            .annotate(revenue=Sum('amount'))
            .values('model__username', 'revenue')
        )
        model_revenue = {
            data['model__username']: data['revenue'] for data in model_revenue
        }

        model_currency, model_total = cls.get_model_currency_and_total(
            model_future_purchases=model_future_purchase,
            model_revenues=model_revenue,
            model_debts=model_debt,
        )

        return [
            {
                'model': model,
                'future_purchase': round(model_future_purchase[model]),
                'currency': round(model_currency[model], 2),
                'debt_fans': round(model_debt.get(model, {}).get('debt_fans', 0)),
                'debt': round(model_debt.get(model, {}).get('debt', 0)),
                'total': round(model_total[model]),
            }
            for model in model_total.keys()
        ]

    @classmethod
    def get_today_report_data_string(cls) -> dict[str, str]:
        """
        Get the today's report data string representation

        Returns:
        - dict[str, str]: A dictionary containing the today's report data string representation.
        """
        string = ''

        data = cls.get_today_report_data()

        if not data:
            return {'copy_data': string}

        string += '\t'.join(data[0].keys()) + '\n'

        for entry in data:
            string += '\t'.join(str(value) for value in entry.values()) + '\n'

        return {'copy_data': string}

    @staticmethod
    def __update_data_for_categories_summary(
        data: dict, entity: Donor | User, future_purchase: int | Decimal, debt
    ) -> None:
        """
        Update data in donors_data or marketers_data
        """
        if entity:
            name = entity.name if isinstance(entity, Donor) else entity.full_name

            data.setdefault(name, {'future_purchase': 0, 'debt': 0, 'total': 0})
            data[name]['future_purchase'] += future_purchase
            data[name]['debt'] += debt
            data[name]['total'] += future_purchase + debt

    @classmethod
    def get_categories_summary_data(cls) -> dict[str, dict[str, list[dict]]]:
        """
        Get the categories summary data.

        Parameters:
        - ads_list (list[Ads]): The list of Ads objects.

        Returns:
        - dict[str, list[dict]]: The categories summary data.
        """
        today_date = timezone.now().date()

        ads_list = (
            Ads.objects.filter(
                Q(donor__isnull=False) | Q(marketer__isnull=False),
                Q(date__gt=today_date)
                | Q(
                    fans_delta__isnull=False, fans_delta__gt=0, fans_count__isnull=False
                ),
                cost_result__isnull=False,
                cost_result__gt=0,
                department=Ads.DepartmentChoices.MARKETING,
            )
            .select_related('donor', 'marketer', 'status')
            .order_by('date')
            .only(
                'date',
                'cost_result',
                'fans_count',
                'fans_delta',
                'donor__name',
                'marketer__first_name',
                'marketer__last_name',
                'marketer__email',
                'status__name',
            )
        )

        donors_data = {}
        marketers_data = {}

        for ads in ads_list:
            donor = getattr(ads, 'donor', None)
            marketer = getattr(ads, 'marketer', None)
            fans_delta = getattr(ads, 'fans_delta', None)
            fans_count = getattr(ads, 'fans_count', None)
            cost_result = getattr(ads, 'cost_result', None)
            date = getattr(ads, 'date', None)
            status = getattr(ads, 'status', None)
            future_purchase = 0
            debt = 0

            if date > today_date and cost_result:
                future_purchase = cost_result

            if (
                fans_delta
                and fans_delta > 0
                and fans_count
                and cost_result
                and date <= today_date
                and status
                and status.name == 'progress'
            ):
                debt = fans_delta * cost_result / fans_count

            if donor:
                cls.__update_data_for_categories_summary(
                    donors_data, donor, future_purchase, debt
                )

            if marketer:
                cls.__update_data_for_categories_summary(
                    marketers_data, marketer, future_purchase, debt
                )

        return {
            'future_purchases': {
                'by_donor': sort_data_by_field_in_list_of_dicts(
                    [
                        {'name': name, 'value': round(data['future_purchase'])}
                        for name, data in donors_data.items()
                        if round(data['future_purchase'])
                    ],
                    '-value',
                ),
                'by_marketer': sort_data_by_field_in_list_of_dicts(
                    [
                        {'name': name, 'value': round(data['future_purchase'])}
                        for name, data in marketers_data.items()
                        if round(data['future_purchase'])
                    ],
                    '-value',
                ),
            },
            'debts': {
                'by_donor': sort_data_by_field_in_list_of_dicts(
                    [
                        {'name': name, 'value': round(data['debt'])}
                        for name, data in donors_data.items()
                        if round(data['debt'])
                    ],
                    '-value',
                ),
                'by_marketer': sort_data_by_field_in_list_of_dicts(
                    [
                        {'name': name, 'value': round(data['debt'])}
                        for name, data in marketers_data.items()
                        if round(data['debt'])
                    ],
                    '-value',
                ),
            },
            'total': {
                'by_donor': sort_data_by_field_in_list_of_dicts(
                    [
                        {'name': name, 'value': round(data['total'])}
                        for name, data in donors_data.items()
                        if round(data['total'])
                    ],
                    '-value',
                ),
                'by_marketer': sort_data_by_field_in_list_of_dicts(
                    [
                        {'name': name, 'value': round(data['total'])}
                        for name, data in marketers_data.items()
                        if round(data['total'])
                    ],
                    '-value',
                ),
            },
        }

    @classmethod
    def get_categories_summary_data_string(cls) -> dict[str, dict[str, str]]:
        """
        Get the categories summary data string representation

        Returns:
        - dict[str, dict[str, str]]: A dictionary containing the categories summary data string representation.
        """
        data = cls.get_categories_summary_data()

        result = {}
        for key, value in data.items():
            result[key] = {}

            for inner_key, inner_value in value.items():
                result[key][inner_key] = '\n'.join(
                    f"{value['name']}\t{value['value']}" for value in inner_value
                )

                for entry in inner_value:
                    result[key][inner_key] += f"{entry['name']}\t{entry['value']}\n"

        return result

    @staticmethod
    def _fill_model_forecast_data(
        model_fans_forecast_of_months: dict[str, dict],
        ads: Ads,
        max_month_delta: int = None,
    ) -> None:
        """
        Fill model forecast data

        Args:
            model_fans_forecast_of_months (dict[str, dict]): The model fans forecast of months.
            ads (Ads): The Ads object.
            max_month_delta (int): If not set - will be used fact max month, if set - will be used
            today date + max_month_delta(in months)

        Returns:
            None
        """
        today_date = timezone.now().date()
        model_nickname = ads.only_fans_model.nickname

        model_fans_forecast_of_months.setdefault(model_nickname, {})
        month_list = []

        # generate months list
        min_month = today_date.replace(day=1)
        max_month = ads.probable_end_date.replace(day=1)

        if max_month_delta is not None:
            month_with_max_delta = min_month + relativedelta(months=max_month_delta)

            if month_with_max_delta < max_month:
                max_month = month_with_max_delta

        month = min_month

        while month <= max_month:
            month_list.append(month)
            month = month + relativedelta(months=1)

        for month in month_list:
            model_fans_forecast_of_months[model_nickname].setdefault(month, 0)

            number_of_days_in_month = monthrange(month.year, month.month)[1]

            if month == min_month:
                number_of_days_in_month = number_of_days_in_month - today_date.day

            elif (
                month == max_month
                and max_month_delta is None
                and ads.probable_end_date.replace(day=1) == max_month
            ):
                number_of_days_in_month = ads.probable_end_date.day

            model_fans_forecast_of_months[model_nickname][month] += (
                ads.fans_per_day * number_of_days_in_month
            )

    @classmethod
    def get_forecast_data(cls, ads_list: list[Ads]) -> list[dict]:
        """
        Get the forecast data.

        Parameters:
        - ads_list (list[Ads]): The list of Ads objects.

        Returns:
        - dict[str, list[dict]]: The forecast data.
        """
        today_date = timezone.now().date()

        model_revenue = (
            Sales.objects.filter(
                amount__isnull=False,
                trans_date__gte=today_date - timedelta(days=30),
            )
            .values('model')
            .annotate(revenue=Sum('amount'))
            .values('model__username', 'revenue')
        )

        model_revenue = {
            data['model__username']: data['revenue'] for data in model_revenue
        }
        all_ads_models = set()
        model_count_active_ads = {}
        model_future_purchases = {}
        model_debts = {}
        model_fans_per_day = {}
        model_fans_delta = {}
        model_fans_forecast_of_months = {}
        model_fans_count_claims_count = {}

        for ads in ads_list:
            model_nickname = ads.only_fans_model.nickname
            all_ads_models.add(model_nickname)

            if ads.fans_delta is not None and ads.fans_delta > 0:
                model_fans_delta.setdefault(model_nickname, 0)
                model_fans_delta[model_nickname] += ads.fans_delta

            if ads.status and ads.status.name == 'progress':
                model_count_active_ads.setdefault(model_nickname, 0)
                model_count_active_ads[model_nickname] += 1

                model_fans_per_day.setdefault(model_nickname, 0)
                model_fans_per_day[model_nickname] += (
                    ads.fans_per_day if ads.fans_per_day else 0
                )

                if ads.probable_end_date and ads.probable_end_date:
                    cls._fill_model_forecast_data(
                        model_fans_forecast_of_months, ads, max_month_delta=6
                    )

                if ads.fans_count:
                    model_fans_count_claims_count.setdefault(
                        model_nickname, {'claims_count': 0, 'fans_count': 0}
                    )
                    model_fans_count_claims_count[model_nickname]['claims_count'] += (
                        ads.claims_count or 0
                    )
                    model_fans_count_claims_count[model_nickname][
                        'fans_count'
                    ] += ads.fans_count

            if (
                ads.fans_delta is not None
                and ads.fans_delta > 0
                and ads.status
                and ads.status.name == 'progress'
                and ads.fans_delta > 0
                and ads.date <= today_date
            ):
                model_debts.setdefault(model_nickname, {'debt': 0, 'debt_fans': 0, 'debt_no_last_month': 0})
                model_debts[model_nickname]['debt_fans'] += ads.fans_delta

                debt_sum = ads.fans_delta * ads.cost_result / ads.fans_count

                model_debts[model_nickname]['debt'] += debt_sum

                if ads.date.replace(day=1) != today_date.replace(day=1):
                    model_debts[model_nickname]['debt_no_last_month'] += debt_sum

            if ads.date > today_date:
                model_future_purchases.setdefault(model_nickname, 0)
                model_future_purchases[model_nickname] += ads.cost_result

        model_currency, model_total = cls.get_model_currency_and_total(
            model_future_purchases=model_future_purchases,
            model_revenues=model_revenue,
            model_debts=model_debts,
        )

        return [
            {
                'model': model,
                'future_purchase': round(model_future_purchases.get(model, 0)),
                'currency': round(model_currency.get(model, 0)),
                'debt_fans': round(model_debts.get(model, {}).get('debt_fans', 0)),
                'debt': round(model_debts.get(model, {}).get('debt', 0)),
                'debt_no_last_month': round(model_debts.get(model, {}).get('debt_no_last_month', 0)),
                'total': round(model_total.get(model, 0)),
                'fans_per_day': model_fans_per_day.get(model, 0),
                'forecast_of_days': model_fans_delta.get(model, 0),
                'fans_forecast_of_months': [
                    {'month': month, 'amount': round(amount)}
                    for month, amount in model_fans_forecast_of_months.get(
                        model, {}
                    ).items()
                ],
                'count_active_promo': model_count_active_ads.get(model, 0),
                'completion': round(
                    model_fans_count_claims_count.get(model, {}).get('claims_count', 0)
                    / model_fans_count_claims_count.get(model, {}).get('fans_count')
                    * 100
                )
                if model_fans_count_claims_count.get(model, {}).get('fans_count')
                else 0,
            }
            for model in sorted(list(all_ads_models))
        ]

    @classmethod
    def get_forecast_table_view_data(cls, ads_list: list[Ads]) -> list:
        """
        Get the forecast table view data.

        Parameters:
        - ads_list (list[Ads]): The list of Ads objects.

        Returns:
        - dict: The forecast table view data.
        """
        today_date = timezone.now().date()
        models_list = OnlyFansModel.objects.filter(
            index_number__isnull=False
        ).values_list('nickname', 'index_number')
        model_index = {nickname: index for nickname, index in models_list}

        models_forecast_of_months = {}

        for ads in ads_list:
            if (
                not ads.status.name == 'progress'
                or ads.probable_end_date
                and ads.probable_end_date <= today_date
            ):
                continue

            cls._fill_model_forecast_data(
                models_forecast_of_months, ads, max_month_delta=6
            )

        month_forecast_grouped_by_month = {}

        for model, model_forecast in models_forecast_of_months.items():
            for month, amount in model_forecast.items():

                if not amount:
                    continue

                month_forecast_grouped_by_month.setdefault(month, {})
                month_forecast_grouped_by_month[month].setdefault(
                    model, {'value': 0, 'index': 0}
                )

                try:
                    month_forecast_grouped_by_month[month][model]['value'] = amount
                    month_forecast_grouped_by_month[month][model][
                        'index'
                    ] = model_index[model]
                except KeyError:
                    continue

        return sort_data_by_field_in_list_of_dicts(
            [
                {
                    'month': month,
                    'forecast': [
                        {'model': model, 'value': data['value'], 'index': data['index']}
                        for model, data in forecast_data.items()
                    ],
                }
                for month, forecast_data in month_forecast_grouped_by_month.items()
            ],
            'month',
        )

    @classmethod
    def get_forecast_table_view_data_string(cls, ads_list: list[Ads]) -> str:
        """
        Get the forecast table view data as a string.

        Parameters:
        - ads_list (list[Ads]): The list of Ads objects.

        Returns:
        - str: The forecast table view data as a string.
        """
        return cls._get_table_data_string(
            cls.get_forecast_table_view_data(ads_list), 'forecast'
        )

    @classmethod
    def create_swap(cls, validated_data: dict, parent: MODEL, user: User) -> MODEL:
        """
        Create a swap for parent Ads. In the result will
        be created child ads and recalculated parent

        Parameters:
        - validated_data (dict): The validated data.
        - parent (MODEL): The parent instance.
        - user (User): The user instance.

        Returns:
        - MODEL: The child Ads instance.
        """
        with transaction.atomic():
            parent = cls.MODEL.objects.select_for_update(nowait=True).get(id=parent.id)
            parent.phantom_cost = round(
                parent.cost / parent.fans_count * parent.claims_count
            )
            parent_old_fans_count = parent.fans_count
            parent.fans_count = parent.claims_count
            parent.status = Status.objects.get_or_create(name='done')[0]
            parent._change_reason = 'Swap'
            parent.save()

            child_phantom_cost = parent.cost - parent.phantom_cost
            child_fans_count = parent_old_fans_count - parent.claims_count
            child = cls.create(
                validated_data={
                    **validated_data,
                    'parent': parent,
                    'phantom_cost': child_phantom_cost,
                    'fans_count': child_fans_count,
                    'cost': 1,
                    'cwvt': 1,
                    'status': Status.objects.get_or_create(name='swap')[0],
                    'donor': parent.donor,
                },
                user=user,
            )

            cls(parent).update_calculated_fields()

            AdsSwapLog.objects.create(
                parent_model=parent.only_fans_model,
                child_model=child.only_fans_model,
                fans_transferred=child.fans_count,
                donor=parent.donor,
                parent_ads_number=parent.ads_number,
                child_ads_number=child.ads_number,
            )

            return child

    def delete_swap_child_ads(self):
        assert self.instance.parent is not None, 'Instance must be a child'

        with transaction.atomic():
            self.instance.parent.fans_count += self.instance.fans_count
            self.instance.parent.phantom_cost = None
            self.instance.parent.status = Status.objects.get_or_create(name='progress')[
                0
            ]
            self.instance.parent.save()
            AdsService(self.instance.parent).update_calculated_fields()
            self.instance.delete()

    @classmethod
    def update_ads_friends_status(cls, promos: list[Promos]) -> int:
        """
        Update ads friend statuses according to the Promos and invalidating cache
        """
        logger = logging.getLogger('friend_promos')
        logger.info('Updating ads friends status...')
        promo_ids = {promo.promo_id: promo.status for promo in promos}
        ads = Ads.objects.filter(ads_number__in=promo_ids.keys()).only(
            'id', 'ads_number', 'friends_status'
        )

        ads_update_list = []
        for ad in ads:
            current_status = promo_ids.get(ad.ads_number)

            if current_status is not None and current_status != ad.friends_status:
                logger.info(
                    f'Ad {ad.ads_number} friends status updated to {current_status} from {ad.friends_status}'
                )
                ad.friends_status = current_status
                ads_update_list.append(ad)

        Ads.objects.bulk_update(ads_update_list, ['friends_status'], batch_size=100)

        # invalidate cache
        if ads_update_list:
            logger.info('Invalidating cache buying-ads-list')
            cache_service = RedisCacheService()
            cache_service.delete_keys_with_prefix('buying-ads-list')
        logger.info(f'Updated {len(ads_update_list)} ads statuses')

        return len(ads_update_list)

    @staticmethod
    def _get_cost_value_for_income_fans(ads: Ads) -> Decimal:
        """
        Get the cost value for income fans.

        Parameters:
        - ads (Ads): The Ads object.

        Returns:
        - Decimal: The cost value for income fans (phantom_costor cost_result)
        """
        if ads.phantom_cost:
            return ads.phantom_cost

        if ads.cost_result:
            return ads.cost_result

        return Decimal('0')

    @classmethod
    def get_income_fans_dtos(cls, ads_list: list[Ads]) -> list[IncomeFansDTO]:
        """
        Get the IncomeFansDTOs for a given list of Ads.

        Parameters:
        - ads_list: A list of Ads objects.

        Return type:
        - list[IncomeFansDTO]: A list of IncomeFansDTO objects representing the income fans amount for each month.
        """
        if not ads_list:
            return []

        month_list = cls.generate_month_list(ads_list)
        month_income_fans = {
            month: IncomeFansDTO(month_date=month) for month in month_list
        }

        for ads in ads_list:
            if not getattr(ads, 'fans_count', None) and not getattr(
                ads, 'claims_count', None
            ):
                continue

            cost = cls._get_cost_value_for_income_fans(ads)
            fan_price = cost / ads.fans_count
            income_amount = ads.claims_count * fan_price
            month_income_fans[ads.date.replace(day=1)].add_amount(income_amount)

        return list(month_income_fans.values())

    @classmethod
    def get_calendar_data(
        cls,
        date_after: date_type,
        date_before: date_type,
        only_fans_models_ids: list[str] | None = None,
        user:  User | None = None
    ):
        """
        Get the Ads for a given date range and list of OnlyFansModel IDs.

        Parameters:
        - date_after: The start date of the range.
        - date_before: The end date of the range.
        - only_fans_models_ids: A list of OnlyFansModel IDs.
        - user

        Return type:
        - list[Ads]
        """
        queryset = Ads.objects.filter(
            calendar_start_date__lte=date_before,
            calendar_end_date__gte=date_after,
        ).order_by(
            'calendar_start_date'
        ).select_related(
            'only_fans_model', 'status'
        ).distinct()

        if only_fans_models_ids:
            queryset = queryset.filter(only_fans_model_id__in=only_fans_models_ids)

        if user and user.role:
            match user.role.name:
                case 'team_lead':
                    queryset = queryset.filter(
                        only_fans_model__team_lead=user
                    )
                case 'senior_operator':
                    queryset = queryset.filter(
                        only_fans_model__team_lead=user.parent
                    )

        return list(queryset)

    @staticmethod
    def _get_new_donor_filled_date(old_promo: Promo | None, new_promo: Promo | None) -> date_type | None:
        if not new_promo:
            return None

        if new_promo != old_promo and new_promo:
            if new_promo.switch_to_promo:
                return timezone.now().date()

            return None

    def update(self, validated_data: dict):
        with transaction.atomic():
            self.instance = Ads.objects.select_for_update().get(id=self.instance.id)

            old_trial_link = self.instance.trial_link
            new_trial_link = validated_data.get("trial_link") or None

            if "trial_link" in validated_data and new_trial_link != old_trial_link:
                validated_data["date_counter"] = None
                validated_data["date_counter_extra"] = None

            if "promo" in validated_data and validated_data["promo"]:
                self.instance.new_donor_filled_date = self._get_new_donor_filled_date(
                    old_promo=self.instance.promo,
                    new_promo=validated_data["promo"]
                )

            for attr, value in validated_data.items():
                setattr(self.instance, attr, value)

            self.instance.save()

            self.update_calculated_fields()

        return self.instance

    def delete_ads(self):
        with transaction.atomic():
            if hasattr(self.instance, 'draft') and self.instance.draft:
                self.instance.draft.delete()
            self.instance.delete()


# -----------------------------------------SMM----------------------------------------
class AdsTrackingService:
    def __init__(self, instance: AdsTracking):
        self.instance = instance

    def end_tracking(self) -> None:
        if self.instance.track_end is not None:
            raise serializers.ValidationError("AdsTracking is already ended")

        with transaction.atomic():
            self.instance.track_end = timezone.now()
            current_date = self.instance.track_end.date()
            (
                ads_tracking_daily_data_object,
                _,
            ) = AdsTrackingDailyDataService.create_ads_tracking_daily_data_object(
                self.instance, current_date
            )
            ads_tracking_daily_data_object.save()

            self.instance.save()

    @classmethod
    def create(cls, validated_data: dict) -> AdsTracking:
        """
        Creates a new AdsTracking instance.

        Args:
            validated_data (dict): A dictionary containing validated data for creating a new AdsTracking instance.

        Returns:
            AdsTracking: The newly created AdsTracking instance.

        Raises:
            serializers.ValidationError: If AdsTracking with the same ads already exists and is currently active.

        """
        ads = validated_data.pop('ads')
        creator = validated_data.pop('creator')

        with transaction.atomic():
            ads_tracking, created = AdsTracking.objects.get_or_create(
                ads=ads, created_by=creator
            )

            if not created and ads_tracking.track_end is None:
                raise serializers.ValidationError(
                    {'ads': ["AdsTracking with such ads already exists"]}
                )

            if not created:
                ads_tracking.track_end = None

            for key, value in validated_data.items():
                setattr(ads_tracking, key, value)

            ads_tracking.save()

            AdsTrackingDailyDataService.create_ads_tracking_daily_data_for_new_ads_tracking(
                ads_tracking=ads_tracking
            )

        return ads_tracking


class AdsTrackingDailyDataService:
    """
    Service for creating AdsTrackingDailyData instances.
    """

    @classmethod
    def create_ads_tracking_daily_data_for_new_ads_tracking(
        cls, ads_tracking: AdsTracking
    ):
        track_start_date = ads_tracking.track_start.date()

        if AdsTrackingDailyData.objects.filter(
            date=track_start_date, ads_tracking=ads_tracking
        ).exists():
            return

        revenue_start = ads_tracking.ads.revenue or 0
        claims_count_start = ads_tracking.ads.claims_count or 0

        return AdsTrackingDailyData.objects.create(
            date=track_start_date,
            ads_tracking=ads_tracking,
            revenue_start=revenue_start,
            claims_count_start=claims_count_start,
        )

    @classmethod
    def create_ads_tracking_daily_data_object(
        cls, ads_tracking: AdsTracking, specific_date: date_type
    ) -> tuple[AdsTrackingDailyData, bool]:
        created = False

        revenue_end = ads_tracking.ads.revenue or 0
        claims_count_end = ads_tracking.ads.claims_count or 0

        latest_ads_tracking_daily_data = AdsTrackingDailyData.objects.filter(
            ads_tracking=ads_tracking
        ).latest('date')

        if latest_ads_tracking_daily_data.date == specific_date:
            ads_tracking_daily_data = latest_ads_tracking_daily_data
        else:
            updated_latest_ads_tracking_daily_data = False

            if latest_ads_tracking_daily_data.revenue_end is None:
                latest_ads_tracking_daily_data.revenue_end = (
                    latest_ads_tracking_daily_data.revenue_start
                )
                updated_latest_ads_tracking_daily_data = True

            if latest_ads_tracking_daily_data.claims_count_end is None:
                latest_ads_tracking_daily_data.claims_count_end = (
                    latest_ads_tracking_daily_data.claims_count_start
                )
                updated_latest_ads_tracking_daily_data = True

            if updated_latest_ads_tracking_daily_data:
                latest_ads_tracking_daily_data.save()

            ads_tracking_daily_data = AdsTrackingDailyData(
                date=specific_date,
                ads_tracking=ads_tracking,
                revenue_start=latest_ads_tracking_daily_data.revenue_end,
                claims_count_start=latest_ads_tracking_daily_data.claims_count_end,
            )
            created = True

        ads_tracking_daily_data.revenue_end = revenue_end
        ads_tracking_daily_data.claims_count_end = claims_count_end
        ads_tracking_daily_data.total_day_revenue = (
            revenue_end - ads_tracking_daily_data.revenue_start
        )
        ads_tracking_daily_data.total_day_claims_count = (
            claims_count_end - ads_tracking_daily_data.claims_count_start
        )

        return ads_tracking_daily_data, created

    @classmethod
    def create_ads_tracking_daily_data_for_ads_tracking_list(
        cls, ads_tracking_list: list[AdsTracking], specific_date: datetime
    ) -> tuple[list[AdsTrackingDailyData], list[AdsTrackingDailyData]]:
        update_list = []
        create_list = []

        for ads_tracking in ads_tracking_list:
            (
                ads_tracking_daily_data,
                created,
            ) = cls.create_ads_tracking_daily_data_object(
                ads_tracking=ads_tracking, specific_date=specific_date
            )

            if ads_tracking_daily_data:
                if created:
                    create_list.append(ads_tracking_daily_data)
                else:
                    update_list.append(ads_tracking_daily_data)

        if update_list:
            AdsTrackingDailyData.objects.bulk_update(
                update_list,
                [
                    'revenue_end',
                    'claims_count_end',
                    'total_day_revenue',
                    'total_day_claims_count',
                ],
            )

        if create_list:
            AdsTrackingDailyData.objects.bulk_create(create_list)

        return update_list, create_list


class PromotionCountService:
    """
    Service for PromotionCount instances
    """

    DEFAULT_NUMBER_LAST_PROMOTIONS_PER_MODEL = 2

    @staticmethod
    def get_latest_promotions_details_from_of_db(
        number_last_promotions_per_model=DEFAULT_NUMBER_LAST_PROMOTIONS_PER_MODEL,
    ) -> dict[int, list[PromotionsDetails]]:
        # crutch
        filter_model = [
            144398306,
            321238063,
            263901728,
            287167268,
            279132197,
            147679291,
            251383712,
            164804782,
            275137747,
            219491871,
            226212434,
            237258293,
            323502545,
            325123363,
            187850153,
            242818367,
            266818868,
            173833496,
            267795782,
            269229044,
            292787632,
            315102418,
            342724494,
            350052481,
            366124087,
        ]

        ranked_promotions = PromotionsDetails.objects.filter(
            model_id__in=filter_model, type_subscription='new_subscribers'
        ).annotate(
            rank=Window(
                expression=RowNumber(),
                partition_by=[F('model_id')],
                order_by=F('promotion_date').desc(),
            )
        )

        latest_promotions = ranked_promotions.filter(
            rank__lte=number_last_promotions_per_model
        )
        latest_promotions_grouped_by_model = {}

        for promotion in latest_promotions:
            latest_promotions_grouped_by_model.setdefault(
                promotion.model_id, []
            ).append(promotion)

        return latest_promotions_grouped_by_model

    @staticmethod
    def create_and_update_promotions_count(
        promotions_count_to_create: list[PromotionCount],
        promotions_count_to_update: list[PromotionCount],
    ) -> tuple[int, int]:
        number_created_objects = 0
        number_updated_objects = 0

        if promotions_count_to_create:
            created_objects = PromotionCount.objects.bulk_create(
                promotions_count_to_create, batch_size=500
            )
            number_created_objects = len(created_objects)

        if promotions_count_to_update:
            number_updated_objects = PromotionCount.objects.bulk_update(
                promotions_count_to_update,
                ['total_count', 'updated_count'],
                batch_size=500,
            )

        return number_created_objects, number_updated_objects

    def write_promotions_count(
        self, number_last_promotions_per_model=DEFAULT_NUMBER_LAST_PROMOTIONS_PER_MODEL
    ) -> tuple[int, int]:
        promotions_count_to_create: list[PromotionCount] = []
        promotions_count_to_update: list[PromotionCount] = []

        latest_promotions = self.get_latest_promotions_details_from_of_db(
            number_last_promotions_per_model
        )
        latest_promotions_ids = [
            promotion.promotion_id
            for latest_model_promotions in latest_promotions.values()
            for promotion in latest_model_promotions
        ]

        promotions_count = PromotionCount.objects.filter(
            of_db_promotion_id__in=latest_promotions_ids
        )
        promotions_count = {
            promotion_count.of_db_promotion_id: promotion_count
            for promotion_count in promotions_count
        }

        for model_id, promotions in latest_promotions.items():
            sorted_promotions = sorted(promotions, key=lambda x: x.promotion_date)
            campaign_users_fan_ids = CampaignUsers.objects.filter(
                model_id=model_id,
            ).values_list('fan_id', flat=True)
            fans_models = AdsService.get_fans_models_by_fans_ids(
                fans_ids=campaign_users_fan_ids, model_id=model_id
            )

            filtered_fans_models = {}

            for fan_model, data in fans_models.items():
                if data.get('campaign_urls') and data.get('trial_dates'):
                    filtered_fans_models[fan_model] = data

            next_promotion = (
                sorted_promotions[1] if len(sorted_promotions) > 1 else None
            )

            for promotion in sorted_promotions:
                claims_count = promotion.claims_count
                link_subs = 0
                min_range = promotion.promotion_date
                max_range = next_promotion.promotion_date if next_promotion else None

                for fan_model, data in filtered_fans_models.items():
                    for trial_date in data.get('trial_dates'):
                        if (
                            'campaign' in trial_date
                            and min_range <= trial_date['join_date']
                        ):
                            if (
                                max_range is not None
                                and trial_date['join_date'] > max_range
                            ):
                                continue

                            link_subs += 1
                            break

                total_count = claims_count or 0
                updated_count = total_count - link_subs

                if promotion_count := promotions_count.get(promotion.promotion_id):
                    promotion_count.total_count = total_count
                    promotion_count.updated_count = updated_count

                    promotions_count_to_update.append(promotion_count)
                else:
                    promotions_count_to_create.append(
                        PromotionCount(
                            model_id=model_id,
                            promotion_name=promotion.promotion_name,
                            promotion_date=promotion.promotion_date,
                            total_count=total_count,
                            updated_count=updated_count,
                            of_db_promotion_id=promotion.promotion_id,
                        )
                    )

        (
            number_created_objects,
            numbers_updated_objects,
        ) = self.create_and_update_promotions_count(
            promotions_count_to_create=promotions_count_to_create,
            promotions_count_to_update=promotions_count_to_update,
        )

        return number_created_objects, numbers_updated_objects


class DonorService:
    """
    Service for Donor instances
    """

    def update_donor_speed_completion(self, donor: Donor) -> Donor:
        speed_completion = Ads.objects.filter(
            donor=donor, days_completion__isnull=False
        ).aggregate(Avg('days_completion'))['days_completion__avg']

        if speed_completion is None:
            return donor

        speed_completion = round(speed_completion)

        if speed_completion != donor.speed_completion:
            donor.speed_completion = round(speed_completion)
            donor.save()

            logger.info(
                f'Donor {donor.id} speed completion updated to {speed_completion}'
            )

        return donor


# ---------------------------------------------Ads Analytics----------------------------------------
class AdsAnalyticsService:
    """
    Service for creating AdsAnalytics instances.
    """

    START_DATE = date_type(2024, 1, 1)

    def write_ads_daily_data(self, current_date: date_type = None):
        """
        Create AdsDailyData objects for each Ads instance.
        """
        logger.info("Creating AdsDailyData objects...")

        if not current_date:
            current_date = timezone.now().date()

        ads = Ads.objects.filter(
            date__gte=self.START_DATE,
        ).only('id', 'claims_count', 'revenue')

        daily_data_list = []
        for ad in ads:
            daily_data = AdsDailyData(
                date=current_date,
                ads=ad,
                revenue=ad.revenue,
                claims_count=ad.claims_count,
            )
            daily_data_list.append(daily_data)

        if daily_data_list:
            AdsDailyData.objects.bulk_create(
                daily_data_list,
                batch_size=1000,
                update_conflicts=True,
                unique_fields=["ads", "date"],
                update_fields=["revenue", "claims_count"],
            )

            logger.info(f"Created {len(daily_data_list)} AdsDailyData objects")
        else:
            logger.info("No AdsDailyData objects to create")

        return daily_data_list

    def write_models_ads_fans_income_costs(self, current_date: date_type = None):
        """
        Create ModelAdsFansIncomeCost objects based on AdsDailyData for each OnlyFansModel's ads.
        """
        logger.info("Creating ModelAdsFansIncomeCost objects...")

        if not current_date:
            current_date = timezone.now().date()

        start_date = current_date.replace(day=1)
        end_date = start_date + relativedelta(months=1, days=-1)

        only_fans_models = OnlyFansModel.objects.filter(ads__isnull=False).distinct()

        objects_list = []
        for only_fans_model in only_fans_models:
            ads_daily_data = (
                AdsDailyData.objects.filter(
                    ads__only_fans_model=only_fans_model,
                    date__gte=start_date,
                    date__lte=end_date,
                )
                .order_by('date')
                .select_related('ads')
            )

            if not ads_daily_data:
                continue

            grouped_ads_daily_data = defaultdict(list)
            for ads_daily in ads_daily_data:
                grouped_ads_daily_data[ads_daily.ads].append(ads_daily)

            start_fans_count = 0
            end_fans_count = 0
            fans_income_cost = 0
            for ads, data in grouped_ads_daily_data.items():
                start_fans_count += data[0].claims_count
                end_fans_count += data[-1].claims_count
                fans_income_cost += ads.cost_per_fan * (
                    data[-1].claims_count - data[0].claims_count
                )

            claims_count_diff = end_fans_count - start_fans_count
            avg_fans_income_cost = (
                fans_income_cost / claims_count_diff if claims_count_diff else 0
            )

            objects_list.append(
                ModelAdsFansIncomeCost(
                    date=start_date,
                    only_fans_model=only_fans_model,
                    start_fans_count=start_fans_count,
                    end_fans_count=end_fans_count,
                    fans_income_cost=fans_income_cost,
                    avg_fans_income_cost=avg_fans_income_cost,
                )
            )

        if objects_list:
            ModelAdsFansIncomeCost.objects.bulk_create(
                objects_list,
                batch_size=1000,
                update_conflicts=True,
                unique_fields=['date', 'only_fans_model'],
                update_fields=[
                    'start_fans_count',
                    'end_fans_count',
                    'fans_income_cost',
                    'avg_fans_income_cost',
                ],
            )

            logger.info(f"Created {len(objects_list)} ModelAdsFansIncomeCost objects")
        else:
            logger.info("No ModelAdsFansIncomeCost objects to create")

        return objects_list


# ----------------------------------------------AdsDraft---------------------------------------------
class AdsDraftService:
    """
    Service for AdsDraft instances
    """
    def __init__(self):
        self.telegram_bot_service = AdsDraftTelegramBotService()

    def create_draft(self, marketer: User, **validated_data) -> AdsDraft:
        """
        Create AdsDraft instance

        Args:
            marketer (User): The user who created the draft.
            **validated_data: The validated data for creating the AdsDraft instance.

        Returns:
            AdsDraft: The created AdsDraft instance.
        """
        if not hasattr(marketer, 'parent') or not marketer.parent:
            raise AdsDraftMarketerWithoutParentError(
                "Marketer should have parent to create draft"
            )
        with transaction.atomic():
            draft = AdsDraft.objects.create(marketer=marketer, reviewer=marketer.parent, **validated_data)

            try:
                tg_message_data = self.telegram_bot_service.send_draft_review_message(draft)
            except Exception as e:
                logger.error(e, exc_info=True)

                raise AdsDraftError(str(e))

            draft.tg_message = tg_message_data
            draft.save()

        return draft

    def update_draft(self, instance: AdsDraft, user: User, **validated_data) -> AdsDraft:
        """
        Update AdsDraft instance

        Args:
            instance (AdsDraft): The AdsDraft instance to update.
            user (User): The user who updated the draft.
            **validated_data: The validated data for updating the AdsDraft instance.

        Returns:
            AdsDraft: The updated AdsDraft instance.
        """
        status_changed = False
        review_comment = validated_data.get('review_comment')

        for key, value in validated_data.items():
            if key == 'status' and value != instance.status:
                if user.role.name != 'hom':
                    raise PermissionDenied(
                        {'status': "You don't have permission to change status for draft"}
                    )

                status_changed = True

            setattr(instance, key, value)

        if status_changed:
            match instance.status:
                case AdsDraft.StatusChoices.ACCEPT:
                    instance = self.accept_draft(instance, user, review_comment)
                case AdsDraft.StatusChoices.DECLINE:
                    instance = self.decline_draft(instance, user, review_comment)
        else:
            instance.save()

        return instance

    def accept_draft(
        self,
        instance: AdsDraft,
        reviewer: User,
        review_comment: str | None = None,
    ) -> AdsDraft:
        """
        Accept AdsDraft instance

        Args:
            instance (AdsDraft): The AdsDraft instance to accept.
            reviewer (User): The user who reviewed the draft.
            review_comment (str | None): The review comment. Defaults to None.

        Returns:
            AdsDraft: The accepted AdsDraft instance.
        """
        if instance.status == AdsDraft.StatusChoices.ACCEPT and instance.ads:
            logger.info(f"AdsDraft {instance.id} already accepted")

            raise AdsDraftAlreadyAcceptedError("AdsDraft already accepted")

        with transaction.atomic():
            ads = Ads.objects.create(
                date=instance.date,
                only_fans_model=instance.only_fans_model,
                donor=instance.donor,
                cost=instance.cost,
                fans_count=instance.fans_count,
                marketer=instance.marketer,
                promo=instance.promo,
            )

            instance.ads = ads
            instance.status = AdsDraft.StatusChoices.ACCEPT
            instance.review_date = timezone.now()
            instance.review_comment = review_comment

            if reviewer.is_authenticated:
                instance.reviewer = reviewer

                try:
                    self.telegram_bot_service.edit_review_message(instance)
                except Exception as e:
                    logger.error(f'Failed to remove reply markup: {e}', exc_info=True)

                    pass

            instance.save()

        try:
            self.telegram_bot_service.send_review_result_message(instance)
        except Exception as e:
            logger.error(f'Failed to send review result message: {e}', exc_info=True)

            pass

        return instance

    def decline_draft(
        self,
        instance: AdsDraft,
        reviewer: User,
        review_comment: str | None = None,
    ) -> AdsDraft:
        """
        Decline AdsDraft instance

        Args:
            instance (AdsDraft): The AdsDraft instance to decline.
            reviewer (User): The user who reviewed the draft.
            review_comment(str | None): The review comment. Defaults to None.

        Returns:
            AdsDraft: The declined AdsDraft instance.
        """
        with transaction.atomic():
            if instance.ads:
                instance.ads.delete()
                instance.ads = None

            instance.status = AdsDraft.StatusChoices.DECLINE
            instance.review_date = timezone.now()
            instance.review_comment = review_comment

            if reviewer.is_authenticated:
                instance.reviewer = reviewer

                try:
                    self.telegram_bot_service.edit_review_message(instance)
                except Exception as e:
                    logger.error(f'Failed to remove reply markup: {e}', exc_info=True)

                    pass

            instance.save()

        try:
            self.telegram_bot_service.send_review_result_message(instance)
        except Exception as e:
            logger.error(f'Failed to send review result message: {e}', exc_info=True)

            pass

        return instance

    def delete_draft(self, instance: AdsDraft) -> None:
        """
        Delete AdsDraft instance

        Args:
            instance (AdsDraft): The AdsDraft instance to delete.

        Returns:
            None
        """
        with transaction.atomic():
            try:
                self.telegram_bot_service.edit_review_message(instance)
            except Exception as e:
                logger.error(f'Failed to remove reply markup: {e}', exc_info=True)

                pass

            if instance.ads:
                instance.ads.delete()

            instance.delete()


class AdsDraftTelegramBotService(TelegramBotService):
    def __init__(self, token: str = settings.TRACKER_BOT_TOKEN):
        if not token:
            raise ValueError('TRACKER_BOT_TOKEN is not set')

        super().__init__(token)

    def _get_draft_review_message_text(self, draft: AdsDraft) -> str:
        return (
            f'New draft for review from <b>{draft.marketer.full_name}</b>\n\n'
            f'<b>Model:</b> {draft.only_fans_model.nickname}\n'
            f'<b>Date:</b> {draft.date}\n'
            f'<b>Donor:</b> {draft.donor.name}\n'
            f'<b>Donor debt:</b> {draft.donor_debt or "-"}$\n'
            f'<b>Promo:</b> {draft.promo.name}\n'
            f'<b>Cost:</b> {draft.cost}\n'
            f'<b>ARPU:</b> {draft.arpu or "-"}\n'
            f'<b>Fans:</b> {draft.fans_count}\n'
            f'<b>Cost per fan:</b> {round(draft.cost_per_fan, 3)}\n'
        )

    @staticmethod
    def _get_draft_review_message_reply_markup(draft: AdsDraft) -> dict:
        return {
            'inline_keyboard': [
                [
                    {
                        'text': 'Accept',
                        'callback_data': f'draft_a_{draft.id}'
                    },
                    {
                        'text': 'Decline',
                        'callback_data': f'draft_d_{draft.id}'
                    }
                ]
            ]
        }

    def send_draft_review_message(self, draft: AdsDraft) -> dict:
        """
        Send draft review message to reviewer

        Args:
            draft (AdsDraft): The AdsDraft instance to send the message for.

        Returns:
            int: The message id of the sent message.
        """
        if not draft.reviewer.telegram_id:
            raise AdsDraftTelegramServiceReviewerError(
                f'Failed to send draft review message: reviewer {draft.reviewer} has no telegram id'
            )

        try:
            response = self.send_message(
                chat_id=draft.reviewer.telegram_id,
                text=self._get_draft_review_message_text(draft),
                reply_markup=self._get_draft_review_message_reply_markup(draft)
            )

            return response['result']
        except Exception as e:
            logger.error(f'Failed to send draft review message: {e}', exc_info=True)

            raise AdsDraftTelegramServiceError(f'Failed to send draft review message: {e}')

    @staticmethod
    def _get_edit_review_message_text(draft: AdsDraft) -> str:
        return f"{draft.tg_message['text']}\n\n{'✅' if draft.status == AdsDraft.StatusChoices.ACCEPT else '❌'}"

    def edit_review_message(self, draft: AdsDraft) -> None:
        """
        Edit review message

        Args:
            draft (AdsDraft): The AdsDraft instance to edit.

        Returns:
            None
        """
        if not draft.tg_message:
            raise AdsDraftTelegramServiceError(
                f'Failed to edit message, AdsDrfat: {draft.id} missing tg_message'
            )

        try:
            chat_id = draft.tg_message['chat']['id']
            message_id = draft.tg_message['message_id']
            text = self._get_edit_review_message_text(draft)

            self.edit_message(
                chat_id=chat_id,
                message_id=message_id,
                text=text,
            )
        except Exception as e:
            logger.error(f'Failed to remove reply markup: {e}', exc_info=True)

            raise AdsDraftTelegramServiceError(f'Failed to remove reply markup: {e}')

    @staticmethod
    def _get_review_result_message_text(draft: AdsDraft) -> str:
        status_result = "declined❌" if draft.status == AdsDraft.StatusChoices.DECLINE else "approved✅"

        return (
            f"Draft:\n\n"
            f"<b>Model:</b> {draft.only_fans_model.nickname}\n"
            f"<b>Date:</b> {draft.date}\n"
            f"<b>Donor:</b> {draft.donor.name if draft.donor else '-'}\n"
            f"<b>Cost:</b> {draft.cost}$\n\n"
            f"Has been {status_result}\n"
            f"Comment: {draft.review_comment or '-'}"
        )

    def send_review_result_message(self, draft: AdsDraft) -> None:
        """
        Send review result message to reviewer

        Args:
            draft (AdsDraft): The AdsDraft instance to send the message for.

        Returns:
            None
        """
        if not draft.marketer.telegram_id:
            raise AdsDraftTelegramServiceSendReviewResultError(
                f'Failed to send review result message: marketer {draft.marketer} has no telegram id'
            )

        try:
            chat_id = draft.marketer.telegram_id
            text = self._get_review_result_message_text(draft)

            self.send_message(
                chat_id=chat_id,
                text=text,
            )
        except Exception as e:
            logger.error(f'Failed to send review result message: {e}', exc_info=True)

            raise AdsDraftTelegramServiceSendReviewResultError(f'Failed to send review result message: {e}')
