from django.urls import include, path
from rest_framework import routers

from ads.views import (
    AdsDraftViewSet,
    AdsTrackingViewSet,
    AdsViewSet,
    BlackListViewSet,
    DonorOptionViewSet,
    DonorTypeViewSet,
    DonorViewSet,
    FriendsAdsListView,
    PaymentMethodViewSet,
    PlatformTypeViewSet,
    ProblemStatusViewSet,
    PromoViewSet,
    StatusViewSet,
)

router = routers.DefaultRouter()
router.register('payment-methods', PaymentMethodViewSet, basename='payment_methods')
router.register('platform-types', PlatformTypeViewSet, basename='platform_types')
router.register('promos', PromoViewSet, basename='promos')
router.register('donors', DonorViewSet, basename='donors')
router.register('donor-types', DonorTypeViewSet, basename='donor-types')
router.register('donor-options', DonorOptionViewSet, basename='donor-options')
router.register('statuses', StatusViewSet, basename='statuses')
router.register('problem-statuses', ProblemStatusViewSet, basename='problem-statuses')
router.register('black-list', BlackListViewSet, basename='black_list')
router.register('tracking', AdsTrackingViewSet, basename='tracking')
router.register('drafts', AdsDraftViewSet, basename='ads-drafts')
router.register('', AdsViewSet, basename='ads')


urlpatterns = [
    path('friends/', FriendsAdsListView.as_view(), name='ads-friends'),
    path('', include(router.urls))
]
