from freezegun import freeze_time

from ads.models import Ads, Promo
from ads.tasks import switch_ads_new_donor_promos
from base.tests.mixins import BaseCRMTest


class TestAdsTasks(BaseCRMTest):

    @freeze_time('2025-06-01')
    def test_switch_ads_new_donor_promos(self):
        promo_switch_to = Promo.objects.create(name='promo_switch_to')
        promo_switch_from = Promo.objects.create(name='promo_switch_from', switch_to_promo=promo_switch_to)

        ads_not_update = Ads.objects.create(
            date='2025-05-01',
            cost=1000,
            fans_count=500,
            only_fans_model=self.create_only_fans_model(1),
            trial_link='<EMAIL>',
            promo=promo_switch_from,
            new_donor_filled_date='2025-05-01'
        )
        ads_update = Ads.objects.create(
            date='2025-05-01',
            cost=1000,
            fans_count=500,
            only_fans_model=self.create_only_fans_model(2),
            trial_link='<EMAIL>',
            promo=promo_switch_from,
            new_donor_filled_date='2025-04-01'
        )

        result = switch_ads_new_donor_promos()
        self.assertIn(str(ads_update.ads_number), result)
        self.assertNotIn(str(ads_not_update.ads_number), result)

        ads_update.refresh_from_db()
        self.assertEqual(ads_update.promo, promo_switch_to)
        self.assertIsNone(ads_update.new_donor_filled_date)
