from unittest.mock import call, patch

from django.contrib.auth import get_user_model
from django.utils import timezone

from ads.models import (
    Ads,
    BlackList,
    Donor,
    PaymentMethod,
    PlatformType,
    Promo,
)
from base.tests.mixins import BaseCRMTest
from only_fans_models.models import OnlyFansModel


class TestBuyingCacheInvalidationTest(BaseCRMTest):
    @patch('ads.signals.RedisCacheService')
    def test_invalidate_cache_buying(self, mock_cache_service):
        only_fans_model = OnlyFansModel(
            model_id=1,
            nickname='OnlyFansModel cache',
            username_of='model_1',
        )
        donor = Donor(name="cache_donor")
        payment_method = PaymentMethod(name="cache_payment_method")
        platform_type = PlatformType(name="cache_platform_type")
        promo = Promo(name="cache_promo")
        black_list = BlackList.objects.create(
            donor=donor,
            marketer=self.user_marketer
        )
        marketer = get_user_model()(
            email='<EMAIL>',
            first_name='marketer_cache',
            last_name='marketer_cache',
            role=self.marketer_role
        )
        team_lead = get_user_model()(
            email='<EMAIL>',
            first_name='team_lead_cache',
            last_name='team_lead_cache',
            role=self.team_lead_role
        )

        prefixes = [
            'buying-catalog',
            'buying-donors',
            'buying-payment-methods',
            'buying-platform-types',
            'buying-promos',
            'buying-black-list'
        ]
        models = [
            only_fans_model,
            donor,
            payment_method,
            platform_type,
            promo,
            black_list,
            marketer
        ]

        for instance, prefix in zip(
                models,
                prefixes
        ):
            instance.save()
            mock_cache_service().delete_keys_with_prefix.assert_called_with(prefix)
            mock_cache_service().delete_keys_with_prefix.reset_mock()

        team_lead.save()
        mock_cache_service().delete_keys_with_prefix.assert_not_called()

        for instance, prefix in zip(
                models,
                prefixes
        ):
            instance.delete()
            mock_cache_service().delete_keys_with_prefix.assert_called_with(prefix)
            mock_cache_service().delete_keys_with_prefix.reset_mock()

        team_lead.delete()
        mock_cache_service().delete_keys_with_prefix.assert_not_called()

    @patch('ads.signals.RedisCacheService')
    def test_invalidate_cache_buying_ads(self, mock_cache_service):
        ads = Ads(
            date=timezone.now(),
            only_fans_model=self.create_only_fans_model(1),
            cost=100
        )

        ads.save()

        expected_calls = [
            call().delete_keys_with_prefix('buying-ads-models-summary'),
            call().delete_keys_with_prefix('buying-ads-summary'),
            call().delete_keys_with_prefix('buying-ads-negative-romi'),
            call().delete_keys_with_prefix('buying-ads-list'),
        ]
        mock_cache_service.assert_has_calls(expected_calls, any_order=True)
        mock_cache_service().delete_keys_with_prefix.reset_mock()

        ads.delete()
        expected_calls_after_delete = [
            call().delete_keys_with_prefix('buying-ads-models-summary'),
            call().delete_keys_with_prefix('buying-ads-summary'),
            call().delete_keys_with_prefix('buying-ads-negative-romi'),
            call().delete_keys_with_prefix('buying-ads-list'),
        ]
        mock_cache_service.assert_has_calls(expected_calls_after_delete, any_order=True)
