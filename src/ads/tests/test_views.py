import datetime
from datetime import timedelta
from decimal import Decimal
from unittest.mock import Mock, patch

from dateutil.relativedelta import relativedelta
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.utils import timezone

from ads import services
from ads.models import (
    Ads,
    AdsDraft,
    AdsTracking,
    AdsTrackingDailyData,
    BlackList,
    Donor,
    PlatformType,
    Promo,
    Status,
)
from base.tests.mixins import BaseCRMTest


class TestAdsViews(BaseCRMTest):
    def setUp(self):
        super().setUp()
        self.client.force_authenticate(user=self.user_hom)

    def test_payment_method_list_permissions(self):
        self.client.force_authenticate(user=self.user_team_lead)
        response = self.client.get(reverse('payment_methods-list'))
        self.assertEqual(response.status_code, 403)

        self.client.force_authenticate(user=self.user_operator)
        response = self.client.get(reverse('payment_methods-list'))
        self.assertEqual(response.status_code, 403)

        self.client.force_authenticate(user=self.user_hom)
        response = self.client.get(reverse('payment_methods-list'))
        self.assertEqual(response.status_code, 200)

        self.client.force_authenticate(user=self.user_marketer)
        response = self.client.get(reverse('payment_methods-list'))
        self.assertEqual(response.status_code, 200)

    def test_payment_method_unique_constraints(self):
        response = self.client.post(reverse('payment_methods-list'), data={'name': 'Test payment'})
        self.assertEqual(response.status_code, 201)

        response = self.client.post(reverse('payment_methods-list'), data={'name': 'Test payment'})
        self.assertEqual(response.status_code, 400)

        response = self.client.post(reverse('payment_methods-list'), data={'name': 'test payment'})
        self.assertEqual(response.status_code, 400)

    def test_platform_type_list_permissions(self):
        self.client.force_authenticate(user=self.user_team_lead)
        response = self.client.get(reverse('platform_types-list'))
        self.assertEqual(response.status_code, 403)

        self.client.force_authenticate(user=self.user_operator)
        response = self.client.get(reverse('platform_types-list'))
        self.assertEqual(response.status_code, 403)

        self.client.force_authenticate(user=self.user_hom)
        response = self.client.get(reverse('platform_types-list'))
        self.assertEqual(response.status_code, 200)

        self.client.force_authenticate(user=self.user_marketer)
        response = self.client.get(reverse('platform_types-list'))
        self.assertEqual(response.status_code, 200)

    def test_platform_type_unique_constraints(self):
        response = self.client.post(reverse('platform_types-list'), data={'name': 'Test platform'})
        self.assertEqual(response.status_code, 201)

        response = self.client.post(reverse('platform_types-list'), data={'name': 'Test platform'})
        self.assertEqual(response.status_code, 400)

        response = self.client.post(reverse('platform_types-list'), data={'name': 'test platform'})
        self.assertEqual(response.status_code, 400)

    def test_promo_list_permissions(self):
        self.client.force_authenticate(user=self.user_team_lead)
        response = self.client.get(reverse('promos-list'))
        self.assertEqual(response.status_code, 403)

        self.client.force_authenticate(user=self.user_operator)
        response = self.client.get(reverse('promos-list'))
        self.assertEqual(response.status_code, 403)

        self.client.force_authenticate(user=self.user_hom)
        response = self.client.get(reverse('promos-list'))
        self.assertEqual(response.status_code, 200)

        self.client.force_authenticate(user=self.user_marketer)
        response = self.client.get(reverse('promos-list'))
        self.assertEqual(response.status_code, 200)

    def test_promo_unique_constraints(self):
        response = self.client.post(reverse('promos-list'), data={'name': 'Test promo'})
        self.assertEqual(response.status_code, 201)

        response = self.client.post(reverse('promos-list'), data={'name': 'Test promo'})
        self.assertEqual(response.status_code, 400)

        response = self.client.post(reverse('promos-list'), data={'name': 'test promo'})
        self.assertEqual(response.status_code, 400)

    def test_donor_list_permissions(self):
        self.client.force_authenticate(user=self.user_team_lead)
        response = self.client.get(reverse('donors-list'))
        self.assertEqual(response.status_code, 403)

        self.client.force_authenticate(user=self.user_operator)
        response = self.client.get(reverse('donors-list'))
        self.assertEqual(response.status_code, 403)

        self.client.force_authenticate(user=self.user_hom)
        response = self.client.get(reverse('donors-list'))
        self.assertEqual(response.status_code, 200)

        self.client.force_authenticate(user=self.user_marketer)
        response = self.client.get(reverse('donors-list'))
        self.assertEqual(response.status_code, 200)

    def test_donor_unique_constraints(self):
        response = self.client.post(reverse('donors-list'), data={'name': 'Test donor'})
        self.assertEqual(response.status_code, 201)

        response = self.client.post(reverse('donors-list'), data={'name': 'Test donor'})
        self.assertEqual(response.status_code, 400)

        response = self.client.post(reverse('donors-list'), data={'name': 'test donor'})
        self.assertEqual(response.status_code, 400)

    def test_status_list_permissions(self):
        self.client.force_authenticate(user=self.user_team_lead)
        response = self.client.get(reverse('statuses-list'))
        self.assertEqual(response.status_code, 403)

        self.client.force_authenticate(user=self.user_operator)
        response = self.client.get(reverse('statuses-list'))
        self.assertEqual(response.status_code, 403)

        self.client.force_authenticate(user=self.user_hom)
        response = self.client.get(reverse('statuses-list'))
        self.assertEqual(response.status_code, 200)

        self.client.force_authenticate(user=self.user_marketer)
        response = self.client.get(reverse('statuses-list'))
        self.assertEqual(response.status_code, 200)

    def test_status_unique_constraints(self):
        response = self.client.post(reverse('statuses-list'), data={'name': 'Test status'})
        self.assertEqual(response.status_code, 201)

        response = self.client.post(reverse('statuses-list'), data={'name': 'Test status'})
        self.assertEqual(response.status_code, 400)

        response = self.client.post(reverse('statuses-list'), data={'name': 'test status'})
        self.assertEqual(response.status_code, 400)

    def test_black_list_list_permissions(self):
        self.client.force_authenticate(user=self.user_team_lead)
        response = self.client.get(reverse('black_list-list'))
        self.assertEqual(response.status_code, 403)

        self.client.force_authenticate(user=self.user_operator)
        response = self.client.get(reverse('black_list-list'))
        self.assertEqual(response.status_code, 403)

        self.client.force_authenticate(user=self.user_hof)
        response = self.client.get(reverse('black_list-list'))
        self.assertEqual(response.status_code, 403)

        self.client.force_authenticate(user=self.user_financier)
        response = self.client.get(reverse('black_list-list'))
        self.assertEqual(response.status_code, 403)

        self.client.force_authenticate(user=self.user_hom)
        response = self.client.get(reverse('black_list-list'))
        self.assertEqual(response.status_code, 200)

        self.client.force_authenticate(user=self.user_marketer)
        response = self.client.get(reverse('black_list-list'))
        self.assertEqual(response.status_code, 200)

        self.client.force_authenticate(user=self.user_superuser)
        response = self.client.get(reverse('black_list-list'))
        self.assertEqual(response.status_code, 200)

    def test_is_blacklist_field_is_listed_in_donor(self):
        response = self.client.post(reverse('donors-list'), data={'name': 'Test blacklist donor'})
        donor_name = response.json()['name']
        self.client.post(
            reverse('black_list-list'),
            data={
                'donor': donor_name,
                'comment': 'Test comment',
                'marketer': self.user_marketer.id}
        )
        response = self.client.get(reverse('donors-list'))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()['results'][0]['is_blacklist'], True)

    def test_ads_list_permissions(self):
        self.client.force_authenticate(user=self.user_team_lead)
        response = self.client.get(reverse('ads-list'))
        self.assertEqual(response.status_code, 403)

        self.client.force_authenticate(user=self.user_operator)
        response = self.client.get(reverse('ads-list'))
        self.assertEqual(response.status_code, 403)

        self.client.force_authenticate(user=self.user_hom)
        response = self.client.get(reverse('ads-list'))
        self.assertEqual(response.status_code, 200)

        self.client.force_authenticate(user=self.user_marketer)
        response = self.client.get(reverse('ads-list'))
        self.assertEqual(response.status_code, 200)

        self.client.force_authenticate(user=self.user_superuser)
        response = self.client.get(reverse('ads-list'))
        self.assertEqual(response.status_code, 200)

        self.client.force_authenticate(user=self.user_hof)
        response = self.client.get(reverse('ads-list'))
        self.assertEqual(response.status_code, 200)

        self.client.force_authenticate(user=self.user_financier)
        response = self.client.get(reverse('ads-list'))
        self.assertEqual(response.status_code, 200)

    def test_not_unique_together_date_and_model(self):
        only_fans_model = self.create_only_fans_model(1)
        response = self.client.post(
            reverse('ads-list'),
            data={
                'only_fans_model': only_fans_model.id,
                'date': '2023-10-01'
            }
        )
        self.assertEqual(response.status_code, 201)

        response = self.client.post(
            reverse('ads-list'),
            data={
                'only_fans_model': only_fans_model.id,
                'date': '2023-10-01'
            }
        )
        self.assertEqual(response.status_code, 201)

        self.assertEqual(Ads.objects.filter(only_fans_model=only_fans_model, date='2023-10-01').count(), 2)

    def test_ads_catalog_listed_correct(self):
        response = self.client.get(reverse('ads-catalog'))

        self.assertEqual(response.status_code, 200)
        self.assertIn('only_fans_models', response.json())
        self.assertIn('donors', response.json())
        self.assertIn('payment_methods', response.json())
        self.assertIn('platform_types', response.json())
        self.assertIn('promos', response.json())
        self.assertIn('marketers', response.json())

    def test_ads_models_summary_listed_correct(self):
        only_fans_model = self.create_only_fans_model(1)

        response = self.client.post(
            reverse('ads-list'),
            data={
                'only_fans_model': only_fans_model.id,
                'date': '2021-01-01'
            }
        )
        self.assertEqual(response.status_code, 201)

        response = self.client.get(reverse('ads-models-summary'))
        self.assertEqual(response.status_code, 200)
        self.assertIn('only_fans_model_nickname', response.json()['results'][0])
        self.assertIn('donor_name', response.json()['results'][0])
        self.assertIn('total_fans_count', response.json()['results'][0])
        self.assertIn('total_claims_count', response.json()['results'][0])
        self.assertIn('total_romi', response.json()['results'][0])
        self.assertIn('promo_count', response.json()['results'][0])

    def test_ads_summary_listed_correct(self):
        only_fans_model = self.create_only_fans_model(1)

        self.client.post(
            reverse('ads-list'),
            data={
                'only_fans_model': only_fans_model.id,
                'date': '2021-01-01',
                'fans_count': 10
            }
        )
        self.client.post(
            reverse('ads-list'),
            data={
                'only_fans_model': only_fans_model.id,
                'date': '2021-01-02',
                'fans_count': 25
            }
        )

        response = self.client.get(reverse('ads-summary'))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()['results']), 2)
        self.assertIn('id', response.json()['results'][0]['items'][0])
        self.assertIn('only_fans_model', response.json()['results'][0]['items'][0])
        self.assertIn('donor', response.json()['results'][0]['items'][0])
        self.assertIn('claims_count', response.json()['results'][0]['items'][0])
        self.assertIn('fans_count', response.json()['results'][0]['items'][0])

    def test_ads_negative_romi_listed_correct(self):
        only_fans_model = self.create_only_fans_model(1)

        Ads.objects.create(
            only_fans_model=only_fans_model,
            date='2021-01-01',
            fans_count=10,
            romi=-15
        )
        Ads.objects.create(
            only_fans_model=only_fans_model,
            date='2021-01-02',
            fans_count=25,
            romi=-25
        )

        response = self.client.get(reverse('ads-negative-romi'))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()['results']), 2)
        self.assertIn('id', response.json()['results'][0])
        self.assertIn('marketer', response.json()['results'][0])
        self.assertIn('only_fans_model', response.json()['results'][0])
        self.assertIn('donor', response.json()['results'][0])
        self.assertIn('claims_count', response.json()['results'][0])
        self.assertIn('romi', response.json()['results'][0])
        self.assertIn('cost_result', response.json()['results'][0])
        self.assertIn('date', response.json()['results'][0])
        self.assertIn('buy_date', response.json()['results'][0])

    def test_donor_could_be_created_through_black_list(self):
        response = self.client.post(
            reverse('black_list-list'),
            data={
                'donor': 'Test create donor',
                'marketer': self.user_marketer.id,
            }
        )
        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.json()['donor']['name'], 'Test create donor')

        response = self.client.post(
            reverse('black_list-list'),
            data={
                'donor': 'Test create donor',
                'marketer': self.user_marketer.id,
            }
        )
        self.assertEqual(response.status_code, 400)

    def test_update_ads_with_saved_revenue(self):
        only_fans_model = self.create_only_fans_model(1)

        ads = Ads.objects.create(
            only_fans_model=only_fans_model,
            date=timezone.now().date(),
            saved_revenue=100,
        )

        update_data = [
            {
                'field_name': 'comment',
                'value': 'Test comment',
                'status_code': 200,
            },
            {
                'field_name': 'status',
                'value': 'Test status',
                'status_code': 200,
            },
            {
                'field_name': 'trial_link',
                'value': 'http://test.com',
                'status_code': 200,
            },
            {
                'field_name': 'trial_link',
                'value': 'http://link_was_set.com',
                'status_code': 400,
            },
            {
                'field_name': 'date',
                'value': timezone.now().date() + timedelta(days=1),
                'status_code': 400,
            },
            {
                'field_name': 'only_fans_model',
                'value': self.create_only_fans_model(2).id,
                'status_code': 400,
            }
        ]

        for data in update_data:
            response = self.client.patch(
                reverse(
                    'ads-detail',
                    args=(str(ads.id),)
                ),
                data={
                    data['field_name']: data['value']
                }
            )
            self.assertEqual(response.status_code, data['status_code'])

        ads.saved_revenue = 0
        ads.save()

        update_data_without_saved_revenue = [
            {
                'field_name': 'trial_link',
                'value': 'http://zerosavedrevenue.com',
                'status_code': 200,
            },
            {
                'field_name': 'date',
                'value': timezone.now().date() + timedelta(days=1),
                'status_code': 200,
            },
            {
                'field_name': 'only_fans_model',
                'value': self.create_only_fans_model(3).id,
                'status_code': 200,
            }
        ]

        for data in update_data_without_saved_revenue:
            response = self.client.patch(
                reverse(
                    'ads-detail',
                    args=(str(ads.id),)
                ),
                data={
                    data['field_name']: data['value']
                }
            )
            self.assertEqual(response.status_code, data['status_code'])

    def test_create_ads_tracking(self):
        self.client.force_authenticate(user=self.user_smm)
        ads = Ads.objects.create(
            only_fans_model=self.create_only_fans_model(1),
            date='2023-01-01',
            trial_link='https://trial_link.com'
        )
        data = {
                'ads': ads.id
            }
        response = self.client.post(
            reverse('tracking-list'),
            data=data
        )
        self.assertEqual(response.status_code, 201)

        ads_tracking = AdsTracking.objects.get(id=response.json()['id'])
        ads_tracking_daily_data = AdsTrackingDailyData.objects.get(ads_tracking=ads_tracking)
        self.assertEqual(ads_tracking.ads.id, ads.id)
        self.assertIsNone(ads_tracking.track_end)
        self.assertIsNotNone(ads_tracking.track_start)
        self.assertEqual(ads_tracking_daily_data.ads_tracking.id, ads_tracking.id)
        self.assertEqual(ads_tracking_daily_data.revenue_start, 0)
        self.assertEqual(ads_tracking_daily_data.revenue_end, None)
        self.assertEqual(ads_tracking_daily_data.claims_count_start, 0)
        self.assertEqual(ads_tracking_daily_data.claims_count_end, None)
        self.assertEqual(ads_tracking_daily_data.total_day_revenue, None)
        self.assertEqual(ads_tracking_daily_data.total_day_claims_count, None)

        response = self.client.post(
            reverse('tracking-list'),
            data=data
        )
        self.assertEqual(response.status_code, 400)

    def test_end_ads_tracking(self):
        self.client.force_authenticate(user=self.user_smm)
        ads = Ads.objects.create(
            only_fans_model=self.create_only_fans_model(1),
            date='2023-01-01',
            trial_link='https://trial_link.com'
        )
        data = {
            'ads': ads.id
        }
        response = self.client.post(
            reverse('tracking-list'),
            data=data
        )
        self.assertEqual(response.status_code, 201)
        ads_tracking = AdsTracking.objects.get(id=response.json()['id'])
        response = self.client.post(reverse('tracking-end-tracking', args=(str(ads_tracking.id),)))
        self.assertEqual(response.status_code, 200)
        ads_tracking.refresh_from_db()
        self.assertIsNotNone(ads_tracking.track_end)
        ads_tracking_daily_data = AdsTrackingDailyData.objects.get(ads_tracking=ads_tracking)
        self.assertEqual(ads_tracking_daily_data.revenue_end, 0)
        self.assertEqual(ads_tracking_daily_data.claims_count_end, 0)
        self.assertEqual(ads_tracking_daily_data.total_day_revenue, 0)
        self.assertEqual(ads_tracking_daily_data.total_day_claims_count, 0)

    def test_daily_data_listed_in_retrieve_ads_tracking(self):
        self.client.force_authenticate(user=self.user_smm)
        ads = Ads.objects.create(
            only_fans_model=self.create_only_fans_model(1),
            date='2023-01-01',
            trial_link='https://trial_link.com',
            revenue=100,
            claims_count=10,
        )
        data = {
            'ads': ads.id
        }
        response = self.client.post(
            reverse('tracking-list'),
            data=data
        )
        self.assertEqual(response.status_code, 201)
        ads_tracking_id = response.json()['id']
        response = self.client.get(reverse('tracking-detail', args=(str(ads_tracking_id),)))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()['daily_data']), 1)
        self.assertEqual(response.json()['daily_data'][0]['revenue_start'], '100.00')
        self.assertEqual(response.json()['daily_data'][0]['revenue_end'], None)
        self.assertEqual(response.json()['daily_data'][0]['claims_count_start'], 10)
        self.assertEqual(response.json()['daily_data'][0]['claims_count_end'], None)

    def test_ads_tracking_history_filters(self):
        self.client.force_authenticate(user=self.user_smm)
        today_date = timezone.now().date()
        yesterday_date = today_date - timedelta(days=1)
        only_fans_model = self.create_only_fans_model(1)
        ads = Ads.objects.create(
            only_fans_model=only_fans_model,
            date='2023-01-01',
            trial_link='https://trial_link.com',
            revenue=100,
            claims_count=10,
            platform_type=PlatformType.objects.create(name='Twitter')
        )
        data = {
            'ads': ads.id
        }
        response = self.client.post(
            reverse('tracking-list'),
            data=data
        )
        self.assertEqual(response.status_code, 201)
        tracking = AdsTracking.objects.get(id=response.json()['id'])
        tracking.track_start = timezone.now() - timedelta(days=1)
        tracking.track_end = timezone.now() + timedelta(days=1)
        tracking.save()

        new_ads = Ads.objects.create(
            only_fans_model=self.create_only_fans_model(2),
            date='2023-01-02',
            trial_link='https://new_trial_link.com',
            revenue=200,
            claims_count=20,
            platform_type=PlatformType.objects.create(name='Facebook')
        )
        new_data = {
            'ads': new_ads.id
        }
        response = self.client.post(
            reverse('tracking-list'),
            data=new_data
        )
        self.assertEqual(response.status_code, 201)
        new_tracking = AdsTracking.objects.get(id=response.json()['id'])
        new_tracking.track_end = timezone.now() + timedelta(days=1)
        new_tracking.save()

        response = self.client.get(reverse('tracking-history'))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()), 2)

        response = self.client.get(reverse('tracking-history'), data={'only_fans_model': only_fans_model.id})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()), 1)
        self.assertEqual(response.json()[0]['only_fans_model_nickname'], only_fans_model.nickname)

        response = self.client.get(
            reverse('tracking-history'), data={'platform_type': PlatformType.objects.get(name='Facebook').id}
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()), 1)
        self.assertEqual(response.json()[0]['platform_type_name'], 'Facebook')

        response = self.client.get(
            reverse('tracking-history'), data={'date_after': yesterday_date, 'date_before': today_date}
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()), 2)

        response = self.client.get(
            reverse('tracking-history'), data={'date_after': yesterday_date, 'date_before': yesterday_date}
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()), 1)
        self.assertEqual(response.json()[0]['track_start'], str(yesterday_date))

        response = self.client.get(
            reverse('tracking-history'), data={'date_after': today_date, 'date_before': today_date}
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()), 1)
        self.assertEqual(response.json()[0]['track_start'], str(today_date))

    def test_ads_retrieve_date_filters(self):
        self.client.force_authenticate(user=self.user_smm)
        only_fans_model = self.create_only_fans_model(1)
        ads = Ads.objects.create(
            only_fans_model=only_fans_model,
            date='2023-01-01',
            trial_link='https://trial_link.com',
            revenue=100,
            claims_count=10,
            platform_type=PlatformType.objects.create(name='Twitter')
        )
        data = {
            'ads': ads.id
        }
        response = self.client.post(
            reverse('tracking-list'),
            data=data
        )
        self.assertEqual(response.status_code, 201)

        retrieve_response = self.client.get(
            reverse('tracking-detail', args=(str(response.json()['id']),)),
            data={'date_before': timezone.now().date(), 'date_after':  timezone.now().date()}
        )
        self.assertEqual(retrieve_response.status_code, 200)
        self.assertEqual(len(retrieve_response.json()['daily_data']), 1)

        retrieve_response = self.client.get(
            reverse('tracking-detail', args=(str(response.json()['id']),)),
            data={'date_after': timezone.now().date() + timedelta(days=1)}
        )
        self.assertEqual(retrieve_response.status_code, 200)
        self.assertEqual(len(retrieve_response.json()['daily_data']), 0)

        retrieve_response = self.client.get(
            reverse('tracking-detail', args=(str(response.json()['id']),)),
            data={'date_before': timezone.now().date() - timedelta(days=1)}
        )
        self.assertEqual(retrieve_response.status_code, 200)
        self.assertEqual(len(retrieve_response.json()['daily_data']), 0)

    def test_ads_refund_fields_could_be_updated(self):
        only_fans_model = self.create_only_fans_model(1)
        ads = Ads.objects.create(
            only_fans_model=only_fans_model,
            date=timezone.now().date(),
            saved_revenue=100,
            cost=1000
        )
        refund_date = timezone.now().date()
        update_data = [
            {
                'field_name': 'refund_date',
                'value': refund_date,
                'status_code': 200,
            },
            {
                'field_name': 'refund_cost',
                'value': 1000,
                'status_code': 200,
            },
            {
                'field_name': 'new_fans_count',
                'value': 100,
                'status_code': 200,
            },
            {
                'field_name': 'refund_payment',
                'value': 'Refund payment here',
                'status_code': 200,
            }
        ]

        for data in update_data:
            response = self.client.patch(
                reverse(
                    'ads-detail',
                    args=(str(ads.id),)
                ),
                data={
                    data['field_name']: data['value']
                }
            )
            self.assertEqual(response.status_code, data['status_code'])

        ads.refresh_from_db()
        self.assertEqual(ads.refund_date, refund_date)
        self.assertEqual(ads.refund_cost, Decimal('1000.00'))
        self.assertEqual(ads.new_fans_count, 100)
        self.assertEqual(ads.refund_payment, 'Refund payment here')

    def test_ads_list_filter_by_status_name(self):
        draft_status = Status.objects.create(name='draft')
        ads_with_status_draft = Ads.objects.create(
            only_fans_model=self.create_only_fans_model(1),
            date='2023-01-01',
            status=draft_status
        )
        other_status = Status.objects.create(name='other')
        ads_with_status_active = Ads.objects.create(
            only_fans_model=self.create_only_fans_model(2),
            date='2023-01-02',
            status=other_status
        )

        response = self.client.get(reverse('ads-list'), data={'status': 'draft'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()['results']), 1)
        self.assertEqual(response.json()['results'][0]['id'], str(ads_with_status_draft.id))

        response = self.client.get(reverse('ads-list'), data={'status': 'other'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()['results']), 1)
        self.assertEqual(response.json()['results'][0]['id'], str(ads_with_status_active.id))

        response = self.client.get(reverse('ads-list'), data={'status': 'draft,other'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()['results']), 2)

    def test_tracking_list_department_scope_filter(self):
        created_ads = [
            Ads.objects.create(
                only_fans_model=self.create_only_fans_model(i),
                date='2023-01-01',
                trial_link=f'https://trial_link_smm{i}.com',
                revenue=100,
                claims_count=10,
            )
            for i in range(10)
        ]
        self.client.force_authenticate(user=self.user_smm)

        for ads in created_ads[:3]:
            data = {
                'ads': ads.id
            }
            response = self.client.post(
                reverse('tracking-list'),
                data=data
            )
            self.assertEqual(response.status_code, 201)

        new_user_smm = get_user_model().objects.create(
            email='<EMAIL>',
            first_name='first_name',
            last_name='last_name',
            role=self.smm_role,
        )
        self.client.force_authenticate(user=new_user_smm)

        for ads in created_ads[3:6]:
            data = {
                'ads': ads.id
            }
            response = self.client.post(
                reverse('tracking-list'),
                data=data
            )
            self.assertEqual(response.status_code, 201)

        self.client.force_authenticate(user=self.user_marketer)

        for ads in created_ads[6:8]:
            data = {
                'ads': ads.id
            }
            response = self.client.post(
                reverse('tracking-list'),
                data=data
            )
            self.assertEqual(response.status_code, 201)

        new_user_marketer = get_user_model().objects.create(
            email='<EMAIL>',
            first_name='first_name',
            last_name='last_name',
            role=self.marketer_role,
        )
        self.client.force_authenticate(user=new_user_marketer)

        for ads in created_ads[8:]:
            data = {
                'ads': ads.id
            }
            response = self.client.post(
                reverse('tracking-list'),
                data=data
            )
            self.assertEqual(response.status_code, 201)

        marketer_list_response = self.client.get(reverse('tracking-list'))
        self.assertEqual(marketer_list_response.status_code, 200)
        self.assertEqual(len(marketer_list_response.json()), 2)

        # with ?department_scope=true
        self.client.force_authenticate(user=self.user_smm)
        smm_list_response = self.client.get(reverse('tracking-list'), data={'department_scope': 'true'})
        self.assertEqual(smm_list_response.status_code, 200)
        self.assertEqual(len(smm_list_response.json()), 6)

    def test_donor_research(self):
        Donor.objects.create(
            name="@donor"
        )
        for user in get_user_model().objects.all():
            if user.role and user.role.name != 'researcher':
                response = self.client.get(
                    reverse('donors-research'),
                    data={
                        'name': 'donor'
                    }
                )
                self.assertEqual(response.status_code, 403)

        self.client.force_authenticate(
            user=self.user_researcher
        )
        response = self.client.get(
            reverse('donors-research'),
            data={
                'name': 'donor'
            }
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()['exists'], True)

        response = self.client.get(
            reverse('donors-research'),
            data={
                'name': '@donor'
            }
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()['exists'], True)

        response = self.client.get(
            reverse('donors-research'),
            data={
                'name': 'fake'
            }
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()['exists'], False)

    def test_ads_number_field(self):
        ads = Ads.objects.create(
            only_fans_model=self.create_only_fans_model(1),
            date='2023-01-01',
            trial_link=f'https://trial_link{1}.com',
            revenue=100,
            claims_count=10,
        )
        self.assertIsNotNone(ads.ads_number)

    def test_ads_copy_action(self):
        ads = Ads.objects.create(
            only_fans_model=self.create_only_fans_model(1),
            date='2023-01-01',
            trial_link=f'https://trial_link{1}.com',
            revenue=100,
            claims_count=10,
        )
        copy_data = {
            'only_fans_model': self.create_only_fans_model(2).id,
            'date': '2023-01-01'
        }

        response = self.client.post(reverse('ads-copy', args=(ads.id,)), data=copy_data)
        self.assertEqual(response.status_code, 201)

        new_ads = Ads.objects.get(id=response.json()['id'])
        self.assertNotEquals(new_ads.id, ads.id)
        self.assertEqual(new_ads.date.strftime('%Y-%m-%d'), copy_data['date'])
        self.assertEqual(new_ads.only_fans_model.id, copy_data['only_fans_model'])

    def test_ads_can_be_edited_with_hom_as_marketer(self):
        self.client.force_authenticate(user=self.user_marketer)
        ads = Ads.objects.create(
            only_fans_model=self.create_only_fans_model(1),
            date=timezone.now().date(),
            marketer=self.user_marketer
        )
        self.user_marketer.role = self.hom_role
        self.user_marketer.save()
        self.client.force_authenticate(user=self.user_hom)
        response = self.client.patch(reverse('ads-detail', args=(ads.id, )), data={'cost': 1000})
        self.assertEqual(response.status_code, 200)
        ads.refresh_from_db()
        self.assertEqual(ads.cost, 1000)

    def test_ads_blocked_ads_is_restricted_to_update(self):
        self.client.force_authenticate(user=self.user_marketer)

        ads = Ads.objects.create(
            only_fans_model=self.create_only_fans_model(1),
            date=timezone.now().date() - relativedelta(months=2, days=1),
            marketer=self.user_marketer,
            cost=100
        )
        response = self.client.patch(reverse('ads-detail', args=(ads.id,)), data={'cost': 10000})
        self.assertEqual(response.status_code, 400)
        response = self.client.get(reverse('ads-detail', args=(ads.id,)))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()['is_blocked'], True)

        # from 30.07.2025 also bloacked for hom
        self.client.force_authenticate(user=self.user_hom)
        response = self.client.patch(reverse('ads-detail', args=(ads.id,)), data={'cost': 10000})
        self.assertEqual(response.status_code, 400)

        response = self.client.get(reverse('ads-detail', args=(ads.id,)))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()['is_blocked'], True)

    def test_update_ads_date(self):
        ads = Ads.objects.create(
            only_fans_model=self.create_only_fans_model(1),
            date=timezone.now().date(),
            marketer=self.user_marketer,
            cost=100
        )
        self.client.force_authenticate(user=self.user_marketer)
        date = (ads.date - relativedelta(months=2, days=1)).strftime('%Y-%m-%d')
        response = self.client.patch(
            reverse('ads-detail', args=(ads.id,)),
            data={'date': date}
        )
        self.assertEqual(response.status_code, 400)
        self.assertIn('date', response.json())

        # from 30.07.2025 also bloacked for hom
        self.client.force_authenticate(user=self.user_hom)
        response = self.client.patch(
            reverse('ads-detail', args=(ads.id,)),
            data={'date': date}
        )
        self.assertEqual(response.status_code, 400)

        date = (ads.date - relativedelta(months=1)).strftime('%Y-%m-%d')
        response = self.client.patch(
            reverse('ads-detail', args=(ads.id,)),
            data={'date': date}
        )
        self.assertEqual(response.status_code, 200)

    def test_update_donors_duplicates(self):
        donor = Donor.objects.create(
            name='@donor'
        )
        ads_data = {
            'only_fans_model':  str(self.create_only_fans_model(1).id),
            'date': '2023-01-01',
            'donor': ' @donor   ',
            'cost': 1000
        }
        response = self.client.post(reverse('ads-list'), data=ads_data)
        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.json()['donor']['id'], str(donor.id))

    def test_black_list_record_could_be_updated(self):
        donor = Donor.objects.create(
            name='@donor'
        )
        black_list = BlackList.objects.create(
            donor=donor,
            marketer=self.user_marketer
        )
        response = self.client.patch(
            reverse('black_list-detail', args=(black_list.id,)),
            data={
                'donor': donor.name,
                'comment': 'comment_here'
            }
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()['comment'], 'comment_here')
        self.assertEqual(response.json()['donor']['id'], str(donor.id))

    # TODO TEMPORARY OFF TEST
    # def test_refund_date_and_buy_date_terminated(self):
    #     today_date = timezone.now().date()
    #     ads_data = {
    #         'only_fans_model': str(self.create_only_fans_model(1).id),
    #         'date': today_date,
    #         'trial_link': 'https://link.here',
    #         'cost': 1000,
    #         'revenue': 100,
    #         'claims_count': 10
    #     }
    #
    #     for field_ in ('refund_date', 'buy_date'):
    #         ads_data[field_] = (
    #                 today_date - relativedelta(days=AdsValidator.DATE_NOT_TERMINATED_LIMIT + 1)
    #         ).strftime('%Y-%m-%d')
    #         response = self.client.post(reverse('ads-list'), data=ads_data)
    #         self.assertEqual(response.status_code, 400)
    #         self.assertIn(field_, response.json())
    #
    #     # check works if instance's field is empty
    #     ads_data = {
    #         'only_fans_model': str(self.create_only_fans_model(2).id),
    #         'date': today_date,
    #         'trial_link': 'https://linknew.here',
    #         'cost': 1000,
    #         'revenue': 100,
    #         'claims_count': 10,
    #         'refund_date': (today_date - relativedelta(days=AdsValidator.DATE_NOT_TERMINATED_LIMIT - 1)).strftime('%Y-%m-%d'),
    #         'buy_date': (today_date - relativedelta(days=AdsValidator.DATE_NOT_TERMINATED_LIMIT - 1)).strftime('%Y-%m-%d')
    #     }
    #     response = self.client.post(reverse('ads-list'), data=ads_data)
    #     self.assertEqual(response.status_code, 201)
    #
    #     for field_ in ('refund_date', 'buy_date'):
    #         ads_data[field_] = (
    #                 today_date - relativedelta(days=AdsValidator.DATE_NOT_TERMINATED_LIMIT + 1)
    #         ).strftime('%Y-%m-%d')
    #         ads_id = response.json()['id']
    #         url = reverse('ads-detail', args=(ads_id, ))
    #         response = self.client.put(url, data=ads_data)
    #         self.assertEqual(response.status_code, 400)
    #         self.assertIn(field_, response.json())
    #
    #         ads_data[field_] = (
    #                 today_date - relativedelta(days=AdsValidator.DATE_NOT_TERMINATED_LIMIT - 1)
    #         ).strftime('%Y-%m-%d')
    #         response = self.client.put(url, data=ads_data)
    #         self.assertEqual(response.status_code, 200)
    #
    #         ads_data[field_] = (
    #                 today_date - relativedelta(days=AdsValidator.DATE_NOT_TERMINATED_LIMIT - 5)
    #         ).strftime('%Y-%m-%d')
    #         response = self.client.put(url, data=ads_data)
    #         self.assertEqual(response.status_code, 200)
    #         self.assertEqual(response.json()[field_], ads_data[field_])

    def test_department_scope(self):
        self.client.force_authenticate(user=self.user_marketer)
        model_1_id = str(self.create_only_fans_model(1).id)
        model_2_id = str(self.create_only_fans_model(2).id)

        ads_marketing_id = self.client.post(
            reverse('ads-list'),
            data={
                'only_fans_model': model_1_id,
                'date': '2023-01-01',
                'cost': 1000,
            }
        ).json()['id']

        self.client.force_authenticate(user=self.user_business_dev)
        ads_bd_id = self.client.post(
            reverse('ads-list'),
            data={
                'only_fans_model': model_2_id,
                'date': '2023-01-01',
                'cost': 1000,
            }
        ).json()['id']

        for user in (self.user_hom, self.user_marketer, self.user_superuser, self.user_financier):
            self.client.force_authenticate(user=user)
            response = self.client.get(reverse('ads-list'), data={'department_scope': 'true'})
            self.assertEqual(response.status_code, 200)
            self.assertEqual(len(response.json()['results']), 1)
            self.assertEqual(response.json()['results'][0]['id'], ads_marketing_id)

        for user in (self.user_hobd, self.user_business_dev):
            self.client.force_authenticate(user=user)
            response = self.client.get(reverse('ads-list'), data={'department_scope': 'true'})
            self.assertEqual(response.status_code, 200)
            self.assertEqual(len(response.json()['results']), 1)
            self.assertEqual(response.json()['results'][0]['id'], ads_bd_id)

    def test_refund_cost_greater_than_cost(self):
        model = self.create_only_fans_model(1)
        ads = Ads.objects.create(
            date=timezone.now().date(),
            only_fans_model=model,
            cost=1
        )
        self.client.force_authenticate(user=self.user_marketer)
        response = self.client.patch(
            reverse('ads-detail', args=(ads.id, )),
            data={
                'refund_cost': 1,
            }
        )
        self.assertEqual(response.status_code, 400)
        self.assertIn('cost', response.json())

        ads.cost = 1000
        ads.save()
        response = self.client.patch(
            reverse('ads-detail', args=(ads.id, )),
            data={
                'refund_cost': 1500,
            }
        )
        self.assertEqual(response.status_code, 400)

        response = self.client.patch(
            reverse('ads-detail', args=(ads.id,)),
            data={
                'refund_cost': 1000,
            }
        )
        self.assertEqual(response.status_code, 200)

    def test_ads_friend_listed(self):
        response = self.client.get(
            reverse('ads-friends')
        )
        self.assertEqual(response.status_code, 403)
        status, _ = Status.objects.get_or_create(name='other_type')

        Ads.objects.create(
            date=timezone.now() - timedelta(days=10),
            date_end=timezone.now() + timedelta(days=10),
            only_fans_model=self.create_only_fans_model(1),
            of_link='<EMAIL>',
            status=status
        )
        Ads.objects.create(
            date=timezone.now() - timedelta(days=11),
            date_end=timezone.now() + timedelta(days=11),
            only_fans_model=self.create_only_fans_model(2),
            of_link='<EMAIL>',
            status=status
        )
        Ads.objects.create(
            date=timezone.now() - timedelta(days=20),
            date_end=timezone.now() - timedelta(days=10),
            only_fans_model=self.create_only_fans_model(3),
            of_link='<EMAIL>',
            status=status
        )

        response = self.client.get(
            reverse('ads-friends'),
            headers=self.api_key_headers
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()['results']), 2)

    def test_ads_calendar_dates(self):
        current_date = timezone.now().replace(day=1)
        model_id = str(self.create_only_fans_model(1).id)
        invalid_end_date = {
            'only_fans_model': model_id,
            'date': current_date.strftime('%Y-%m-%d'),
            'cost': 1000,
            'calendar_start_date':  current_date.strftime('%Y-%m-%d'),
            'calendar_end_date': (current_date - timedelta(days=1)).strftime('%Y-%m-%d')
        }
        response = self.client.post(reverse('ads-list'), data=invalid_end_date)
        self.assertEqual(response.status_code, 400)
        self.assertIn('calendar_end_date', response.json())

        valid_date = {
            'only_fans_model': model_id,
            'date': current_date.strftime('%Y-%m-%d'),
            'cost': 1000,
            'calendar_start_date': current_date.strftime('%Y-%m-%d'),
            'calendar_end_date': (current_date + timedelta(days=1)).strftime('%Y-%m-%d')
        }
        response = self.client.post(reverse('ads-list'), data=valid_date)
        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.json()['calendar_start_date'], valid_date['calendar_start_date'])
        self.assertEqual(response.json()['calendar_end_date'], valid_date['calendar_end_date'])

        ads_id = response.json()['id']

        response = self.client.patch(
            reverse('ads-detail', args=(ads_id,)),
            data={
                'calendar_end_date': current_date.strftime('%Y-%m-%d'),
                'calendar_start_date': (current_date + timedelta(days=1)).strftime('%Y-%m-%d')
            }
        )
        self.assertEqual(response.status_code, 400)
        self.assertIn('calendar_end_date', response.json())

        response = self.client.patch(
            reverse('ads-detail', args=(ads_id,)),
            data={
                'calendar_end_date': (current_date + timedelta(days=11)).strftime('%Y-%m-%d'),
                'calendar_start_date': (current_date + timedelta(days=10)).strftime('%Y-%m-%d')
            }
        )
        self.assertEqual(response.status_code, 200)
        self.assertIn('calendar_end_date', response.json())

    def test_ads_calendar_list(self):
        model_1 = self.create_only_fans_model(1)
        Ads.objects.create(
            date=datetime.date(2025, 1, 1),
            only_fans_model=model_1,
            calendar_start_date=datetime.date(2025, 1, 1),
            calendar_end_date=datetime.date(2025, 1, 1)
        )

        model_2 = self.create_only_fans_model(2)
        Ads.objects.create(
            date=datetime.date(2025, 1, 2),
            only_fans_model=model_2,
            calendar_start_date=datetime.date(2025, 1, 2),
            calendar_end_date=datetime.date(2025, 1, 3)
        )

        model_3 = self.create_only_fans_model(3)
        Ads.objects.create(
            date=datetime.date(2025, 1, 3),
            only_fans_model=model_3,
            calendar_start_date=datetime.date(2025, 1, 3),
            calendar_end_date=datetime.date(2025, 1, 4)
        )

        response = self.client.get(
            reverse('ads-calendar'),
            data={
                'date_after':  '2025-01-01',
                'date_before': '2025-01-01'
            }
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()), 1)

        response = self.client.get(
            reverse('ads-calendar'),
            data={
                'date_after':  '2025-01-01',
                'date_before': '2025-01-02'
            }
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()), 2)

        response = self.client.get(
            reverse('ads-calendar'),
            data={
                'date_after':  '2025-01-01',
                'date_before': '2025-01-04'
            }
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()), 3)

        response = self.client.get(
            reverse('ads-calendar'),
            data={
                'date_after': '2025-01-01',
                'date_before': '2025-01-04',
                'only_fans_models':  f"{str(model_1.id)},{str(model_2.id)}"
            }
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()), 2)

        response = self.client.get(
            reverse('ads-calendar'),
            data={
                'date_after': '2025-01-01',
                'date_before': '2025-01-04',
                'only_fans_models': str(model_1.id)
            }
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()), 1)

        for user in self.users:
            self.client.force_authenticate(user)

            response = self.client.get(
                reverse('ads-calendar'),
                data={
                    'date_after': '2025-01-01',
                    'date_before': '2025-01-01'
                }
            )

            if user in [
                self.user_hom,
                self.user_marketer,
                self.user_superuser,
                self.user_team_lead,
                self.user_senior_operator
            ]:
                self.assertEqual(response.status_code, 200)
            else:
                self.assertEqual(response.status_code, 403)

    def test_delete_ads(self):
        only_fans_model = self.create_only_fans_model(1)
        ads = Ads.objects.create(
            only_fans_model=only_fans_model,
            date='2023-01-01',
            cost=1000,
            fans_count=100,
            arpu=10
        )
        response = self.client.delete(
            reverse('ads-detail', args=(ads.id,))
        )
        self.assertEqual(response.status_code, 204)
        self.assertFalse(Ads.objects.filter(id=ads.id).exists())

        ads = Ads.objects.create(
            only_fans_model=only_fans_model,
            date='2023-01-01',
            cost=1000,
            fans_count=100,
            arpu=10
        )
        ads_draft = AdsDraft.objects.create(
            only_fans_model=only_fans_model,
            date='2023-01-01',
            cost=1000,
            fans_count=100,
            arpu=10,
            ads=ads
        )
        response = self.client.delete(
            reverse('ads-detail', args=(ads.id,))
        )
        self.assertEqual(response.status_code, 204)
        self.assertFalse(Ads.objects.filter(id=ads.id).exists())
        self.assertFalse(AdsDraft.objects.filter(id=ads_draft.id).exists())

    # def test_donor_platform_promo_limitation(self):
    #     current_date = timezone.now().date().strftime('%Y-%m-%d')
    #     ads_data = {
    #         'only_fans_model':  self.create_only_fans_model(1),
    #         'date': current_date,
    #         'platform_type': PlatformType.objects.create(name='Guaranteed'),
    #         'promo': Promo.objects.create(name='GG'),
    #         'donor': Donor.objects.create(name='@donor'),
    #         'cost': 1000
    #     }
    #
    #     for _ in range(3):
    #         Ads.objects.create(**ads_data)
    #
    #     self.client.force_authenticate(user=self.user_marketer)
    #
    #     ads_data['only_fans_model'] = str(ads_data['only_fans_model'].id)
    #     ads_data['platform_type'] = str(ads_data['platform_type'].name)
    #     ads_data['promo'] = str(ads_data['promo'].name)
    #     ads_data['donor'] = str(ads_data['donor'].name)
    #
    #     response = self.client.post(reverse('ads-list'), data=ads_data)
    #     self.assertEqual(response.status_code, 400)
    #     self.assertIn(
    #         'You cannot create more than 3 promotions with this donor, platform_type Guaranteed and promo GG.',
    #         response.content.decode()
    #     )
    #
    #     response = self.client.patch(
    #         reverse('ads-detail', args=(Ads.objects.first().id,)),
    #         data={'cost': 2000}
    #     )
    #     self.assertEqual(response.status_code, 200)
    #
    #     new_ads_data = ads_data.copy()
    #     new_ads_data['date'] = (timezone.now() + relativedelta(months=1)).strftime('%Y-%m-%d')
    #     response = self.client.post(reverse('ads-list'), data=new_ads_data)
    #     self.assertEqual(response.status_code, 201)
    #     new_ads_id = response.json()['id']
    #
    #     response = self.client.patch(
    #         reverse('ads-detail', args=(new_ads_id,)),
    #         data={'date': current_date}
    #     )
    #     self.assertEqual(response.status_code, 400)
    #     self.assertIn(
    #         'You cannot create more than 3 promotions with this donor, platform_type Guaranteed and promo GG.',
    #         response.content.decode()
    #     )


class TestAdsDraftViews(BaseCRMTest):
    def setUp(self):
        super().setUp()
        self.client.force_authenticate(user=self.user_hom)

    def test_ads_draft_list_permissions(self):
        for user in self.users:
            self.client.force_authenticate(user=user)
            response = self.client.get(reverse('ads-drafts-list'))

            if user in [
                self.user_hom,
                self.user_marketer,
            ]:
                self.assertEqual(response.status_code, 200)
            else:
                self.assertEqual(response.status_code, 403)

    @patch.object(services.AdsDraftTelegramBotService, 'send_draft_review_message')
    def test_create_ads_draft(self, mock_send_draft_review_message: Mock):
        mock_send_draft_review_message.return_value = {'message_id': 123456, 'chat': {'id': 123456}}
        self.client.force_authenticate(user=self.user_marketer)
        only_fans_model = self.create_only_fans_model(1)
        data = {
            'only_fans_model': only_fans_model.id,
            'date': '2023-01-01',
            'cost': 1000,
            'fans_count': 100,
            'arpu': 10,
            'donor': ' @donor',
            'donor_debt': 100,
            'promo': ' @promo',
        }
        response = self.client.post(reverse('ads-drafts-list'), data=data)
        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.json()['cost'], '1000.00')
        self.assertEqual(response.json()['fans_count'], data['fans_count'])
        self.assertEqual(response.json()['arpu'], data['arpu'])
        self.assertEqual(response.json()['marketer']['id'], str(self.user_marketer.id))
        self.assertEqual(response.json()['status'], AdsDraft.StatusChoices.REVIEW)
        self.assertEqual(response.json()['cost_per_fan'], '10.000')

        mock_send_draft_review_message.assert_called_once_with(AdsDraft.objects.get(id=response.json()['id']))

    @patch.object(services.AdsDraftTelegramBotService, 'edit_review_message')
    @patch.object(services.AdsDraftTelegramBotService, 'send_review_result_message')
    def test_update_ads_draft(self, mock_edit_review_message: Mock, mock_send_review_result_message: Mock):
        only_fans_model = self.create_only_fans_model(1)
        draft = AdsDraft.objects.create(
            only_fans_model=only_fans_model,
            date='2023-01-01',
            cost=1000,
            fans_count=100,
            arpu=10,
            marketer=self.user_marketer
        )
        data = {
            'cost': 2000,
            'fans_count': 200,
            'arpu': 20
        }
        response = self.client.patch(
            reverse('ads-drafts-detail', args=(draft.id,)),
            data=data
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()['cost'], '2000.00')
        self.assertEqual(response.json()['fans_count'], data['fans_count'])
        self.assertEqual(response.json()['arpu'], data['arpu'])
        self.assertEqual(response.json()['cost_per_fan'], '10.000')

        mock_edit_review_message.assert_not_called()
        mock_send_review_result_message.assert_not_called()

        data = {
            'cost': 3000,
            'fans_count': 100,
            'arpu': 20
        }

        self.client.force_authenticate(user=self.user_marketer)
        response = self.client.patch(
            reverse('ads-drafts-detail', args=(draft.id,)),
            data=data
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()['cost'], '3000.00')
        self.assertEqual(response.json()['fans_count'], data['fans_count'])
        self.assertEqual(response.json()['arpu'], data['arpu'])
        self.assertEqual(response.json()['cost_per_fan'], '30.000')

        mock_edit_review_message.assert_not_called()
        mock_send_review_result_message.assert_not_called()

        response = self.client.patch(
            reverse('ads-drafts-detail', args=(draft.id,)),
            data={'status': AdsDraft.StatusChoices.ACCEPT}
        )
        self.assertEqual(response.status_code, 403)

        self.client.force_authenticate(user=self.user_hom)
        response = self.client.patch(
            reverse('ads-drafts-detail', args=(draft.id,)),
            data={'status': AdsDraft.StatusChoices.ACCEPT}
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()['status'], AdsDraft.StatusChoices.ACCEPT)
        self.assertIsNotNone(response.json()['ads'])
        draft.refresh_from_db()
        self.assertEqual(draft.reviewer, self.user_hom)
        self.assertIsNotNone(draft.review_date)

        new_marketer = get_user_model().objects.create(
            email='<EMAIL>',
            first_name='first_name',
            last_name='last_name',
            role=self.marketer_role,
        )
        self.client.force_authenticate(user=new_marketer)
        response = self.client.patch(
            reverse('ads-drafts-detail', args=(draft.id,)),
            data=data
        )
        self.assertEqual(response.status_code, 403)

        draft.status = AdsDraft.StatusChoices.ACCEPT
        draft.save()
        response = self.client.patch(
            reverse('ads-drafts-detail', args=(draft.id,)),
            data=data
        )
        self.assertEqual(response.status_code, 403)

        draft.status = AdsDraft.StatusChoices.DECLINE
        draft.save()
        response = self.client.patch(
            reverse('ads-drafts-detail', args=(draft.id,)),
            data=data
        )
        self.assertEqual(response.status_code, 403)

        mock_edit_review_message.assert_called_once_with(draft)
        mock_send_review_result_message.assert_called_once_with(draft)

    @patch.object(services.AdsDraftTelegramBotService, 'edit_review_message')
    def test_delete_ads_draft(self, mock_edit_review_message: Mock):
        only_fans_model = self.create_only_fans_model(1)
        ads = Ads.objects.create(
            only_fans_model=only_fans_model,
            date='2023-01-01',
            cost=1000,
            fans_count=100,
            arpu=10
        )
        ads_id = ads.id
        draft = AdsDraft.objects.create(
            only_fans_model=only_fans_model,
            date='2023-01-01',
            cost=1000,
            fans_count=100,
            arpu=10,
            ads=ads
        )
        response = self.client.delete(
            reverse('ads-drafts-detail', args=(draft.id,))
        )
        self.assertEqual(response.status_code, 204)
        self.assertEqual(AdsDraft.objects.count(), 0)
        self.assertFalse(Ads.objects.filter(id=ads_id).exists())

        mock_edit_review_message.assert_called_once()

        self.client.force_authenticate(user=self.user_marketer)

        ads = Ads.objects.create(
            only_fans_model=only_fans_model,
            date='2023-01-01',
            cost=1000,
            fans_count=100,
            arpu=10
        )
        ads_id = ads.id
        draft = AdsDraft.objects.create(
            only_fans_model=only_fans_model,
            date='2023-01-01',
            cost=1000,
            fans_count=100,
            arpu=10,
            ads=ads,
            marketer=self.user_marketer
        )
        response = self.client.delete(
            reverse('ads-drafts-detail', args=(draft.id,))
        )
        self.assertEqual(response.status_code, 204)
        self.assertEqual(AdsDraft.objects.count(), 0)
        self.assertFalse(Ads.objects.filter(id=ads_id).exists())

        draft = AdsDraft.objects.create(
            only_fans_model=only_fans_model,
            date='2023-01-01',
            cost=1000,
            fans_count=100,
            arpu=10
        )

        draft.status = AdsDraft.StatusChoices.ACCEPT
        draft.save()
        response = self.client.delete(
            reverse('ads-drafts-detail', args=(draft.id,))
        )
        self.assertEqual(response.status_code, 403)
        self.assertEqual(AdsDraft.objects.count(), 1)

        draft.status = AdsDraft.StatusChoices.DECLINE
        draft.save()
        response = self.client.delete(
            reverse('ads-drafts-detail', args=(draft.id,))
        )
        self.assertEqual(response.status_code, 403)
        self.assertEqual(AdsDraft.objects.count(), 1)

    @patch.object(services.AdsDraftTelegramBotService, 'edit_review_message')
    @patch.object(services.AdsDraftTelegramBotService, 'send_review_result_message')
    def test_accept_ads_draft(self, mock_edit_review_message: Mock, mock_send_review_result_message: Mock):
        only_fans_model = self.create_only_fans_model(1)
        promo = Promo.objects.create(name='promo')

        draft = AdsDraft.objects.create(
            only_fans_model=only_fans_model,
            date='2023-01-01',
            cost=1000,
            fans_count=100,
            arpu=10,
            promo=promo
        )
        response = self.client.post(
            reverse('ads-drafts-accept', args=(draft.id,))
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()['status'], AdsDraft.StatusChoices.ACCEPT)
        self.assertIsNotNone(response.json()['ads'])
        self.assertIsNone(response.json()['review_comment'])

        draft.refresh_from_db()
        self.assertEqual(draft.ads.cost, draft.cost)
        self.assertEqual(draft.ads.fans_count, draft.fans_count)
        self.assertEqual(draft.ads.marketer, draft.marketer)
        self.assertEqual(draft.ads.date, draft.date)
        self.assertEqual(draft.ads.only_fans_model, draft.only_fans_model)
        self.assertEqual(draft.ads.donor, draft.donor)
        self.assertEqual(draft.ads.cost_per_fan, draft.cost_per_fan)
        self.assertEqual(draft.ads.promo, draft.promo)

        self.client.force_authenticate(user=self.user_marketer)
        response = self.client.post(
            reverse('ads-drafts-accept', args=(draft.id,))
        )
        self.assertEqual(response.status_code, 403)

        response = self.client.post(
            reverse('ads-drafts-decline', args=(draft.id,))
        )
        self.assertEqual(response.status_code, 403)

        mock_edit_review_message.assert_called_once_with(draft)
        mock_send_review_result_message.assert_called_once_with(draft)

    @patch.object(services.AdsDraftTelegramBotService, 'edit_review_message')
    @patch.object(services.AdsDraftTelegramBotService, 'send_review_result_message')
    def test_decline_ads_draft(self, mock_edit_review_message: Mock, mock_send_review_result_message: Mock):
        only_fans_model = self.create_only_fans_model(1)
        draft = AdsDraft.objects.create(
            only_fans_model=only_fans_model,
            date='2023-01-01',
            cost=1000,
            fans_count=100,
            arpu=10
        )
        response = self.client.post(
            reverse('ads-drafts-decline', args=(draft.id,))
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()['status'], AdsDraft.StatusChoices.DECLINE)
        self.assertIsNone(response.json()['ads'])
        self.assertIsNone(response.json()['review_comment'])

        draft.refresh_from_db()
        self.assertEqual(draft.status, AdsDraft.StatusChoices.DECLINE)
        self.assertIsNone(draft.ads)

        self.client.force_authenticate(user=self.user_marketer)
        draft.status = AdsDraft.StatusChoices.REVIEW
        draft.save()
        response = self.client.post(
            reverse('ads-drafts-decline', args=(draft.id,))
        )
        self.assertEqual(response.status_code, 403)

        response = self.client.post(
            reverse('ads-drafts-accept', args=(draft.id,))
        )
        self.assertEqual(response.status_code, 403)

        mock_edit_review_message.assert_called_once_with(draft)
        mock_send_review_result_message.assert_called_once_with(draft)

    @patch.object(services.AdsDraftTelegramBotService, 'edit_review_message')
    @patch.object(services.AdsDraftTelegramBotService, 'send_review_result_message')
    def test_accept_draft_with_review_comment(
        self,
        mock_edit_review_message: Mock,
        mock_send_review_result_message: Mock
    ):
        only_fans_model = self.create_only_fans_model(1)
        draft = AdsDraft.objects.create(
            only_fans_model=only_fans_model,
            date='2023-01-01',
            cost=1000,
            fans_count=100,
            arpu=10,
            review_comment='test'
        )
        response = self.client.post(
            reverse('ads-drafts-accept', args=(draft.id,)),
            data={'review_comment': 'test_accept'}
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()['status'], AdsDraft.StatusChoices.ACCEPT)
        self.assertIsNotNone(response.json()['ads'])
        self.assertEqual(response.json()['review_comment'], 'test_accept')

        mock_edit_review_message.assert_called_once_with(draft)
        mock_send_review_result_message.assert_called_once_with(draft)

    @patch.object(services.AdsDraftTelegramBotService, 'edit_review_message')
    @patch.object(services.AdsDraftTelegramBotService, 'send_review_result_message')
    def test_decline_draft_with_review_comment(
        self,
        mock_edit_review_message: Mock,
        mock_send_review_result_message: Mock
    ):
        only_fans_model = self.create_only_fans_model(1)
        draft = AdsDraft.objects.create(
            only_fans_model=only_fans_model,
            date='2023-01-01',
            cost=1000,
            fans_count=100,
            arpu=10,
            review_comment='test'
        )
        response = self.client.post(
            reverse('ads-drafts-decline', args=(draft.id,)),
            data={'review_comment': 'test_decline'}
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()['status'], AdsDraft.StatusChoices.DECLINE)
        self.assertIsNone(response.json()['ads'])
        self.assertEqual(response.json()['review_comment'], 'test_decline')

        mock_edit_review_message.assert_called_once_with(draft)
        mock_send_review_result_message.assert_called_once_with(draft)

    @patch.object(services.AdsDraftTelegramBotService, 'edit_review_message')
    @patch.object(services.AdsDraftTelegramBotService, 'send_review_result_message')
    def test_review_comment_can_be_updated(
        self,
        mock_edit_review_message: Mock,
        mock_send_review_result_message: Mock
    ):
        only_fans_model = self.create_only_fans_model(1)
        draft = AdsDraft.objects.create(
            only_fans_model=only_fans_model,
            date='2023-01-01',
            cost=1000,
            fans_count=100,
            arpu=10,
        )

        response = self.client.patch(
            reverse('ads-drafts-detail', args=(draft.id,)),
            data={'review_comment': 'test_accept'}
        )
        self.assertEqual(response.status_code, 400)
        self.assertIn('review_comment', response.json())

        response = self.client.patch(
            reverse('ads-drafts-detail', args=(draft.id,)),
            data={'review_comment': 'test_accept', 'status': AdsDraft.StatusChoices.ACCEPT}
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()['review_comment'], 'test_accept')
        self.assertEqual(response.json()['status'], AdsDraft.StatusChoices.ACCEPT)

        mock_edit_review_message.assert_called_once_with(draft)
        mock_send_review_result_message.assert_called_once_with(draft)
