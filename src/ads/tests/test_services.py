from datetime import timedelta

from django.utils import timezone

from ads.models import (
    Ads,
    AdsSwapLog,
    Donor,
    Promo,
)
from ads.services import AdsService
from base.tests.mixins import BaseCRMTest


class TestAdsService(BaseCRMTest):
    def test_create_and_delete_ads_swap(self):
        # create
        only_fans_model = self.create_only_fans_model(1)
        donor, _ = Donor.objects.get_or_create(name="@Test Donor")

        parent_ads = Ads.objects.create(
            date=timezone.now().date(),
            cost=1000,
            fans_count=500,
            claims_count=250,
            only_fans_model=only_fans_model,
            trial_link='<EMAIL>',
            donor=donor
        )
        child_ads = AdsService.create_swap(
            parent=parent_ads,
            user=self.user_marketer,
            validated_data={
                'date': timezone.now().date() + timedelta(days=1),
                'only_fans_model': only_fans_model,
                'trial_link': '<EMAIL>'
            }
        )
        parent_ads.refresh_from_db()
        self.assertTrue(parent_ads.is_parent)
        self.assertFalse(parent_ads.is_child)
        self.assertEqual(parent_ads.phantom_cost, 500)
        self.assertEqual(parent_ads.fans_count, 250)
        self.assertEqual(parent_ads.status.name, 'done')
        self.assertEqual(parent_ads.donor, donor)

        self.assertFalse(child_ads.is_parent)
        self.assertTrue(child_ads.is_child)
        self.assertEqual(child_ads.phantom_cost, 500)
        self.assertEqual(parent_ads.fans_count, 250)
        self.assertEqual(child_ads.status.name, 'swap')
        self.assertEqual(child_ads.donor, donor)

        # check log is created
        ads_swap_log = AdsSwapLog.objects.get(
            parent_ads_number=parent_ads.ads_number,
            child_ads_number=child_ads.ads_number,
        )
        self.assertEqual(ads_swap_log.parent_model, parent_ads.only_fans_model)
        self.assertEqual(ads_swap_log.child_model, child_ads.only_fans_model)
        self.assertEqual(ads_swap_log.fans_transferred, child_ads.fans_count)
        self.assertEqual(ads_swap_log.donor, parent_ads.donor)

        # delete
        AdsService(instance=child_ads).delete_swap_child_ads()
        parent_ads.refresh_from_db()
        self.assertFalse(parent_ads.is_parent)
        self.assertFalse(parent_ads.is_child)
        self.assertIsNone(parent_ads.phantom_cost)
        self.assertEqual(parent_ads.fans_count, 500)
        self.assertEqual(parent_ads.status.name, 'progress')

        self.assertTrue(AdsSwapLog.objects.filter(pk=ads_swap_log.pk).exists())

    def test_ads_new_donor_filled_date_set(self):
        only_fans_model = self.create_only_fans_model(1)
        promo_switch_to = Promo.objects.create(name='promo_switch_to')
        promo_switch_from = Promo.objects.create(name='promo_switch_from', switch_to_promo=promo_switch_to)

        ads = AdsService.create(
            validated_data={
                'date': timezone.now().date(),
                'only_fans_model': only_fans_model,
                'trial_link': 'https://link.here',
                'cost': 1000,
                'promo': promo_switch_from
            },
            user=self.user_marketer
        )
        self.assertIsNotNone(ads.new_donor_filled_date)

        ads = AdsService(ads).update(
            validated_data={
                'promo': promo_switch_to
            }
        )
        self.assertIsNone(ads.new_donor_filled_date)

        ads = AdsService(ads).update(
            validated_data={
                'promo': promo_switch_from
            }
        )
        self.assertIsNotNone(ads.new_donor_filled_date)
