from django.db.models import (
    <PERSON>,
    F,
    OuterRef,
    Prefetch,
    Q,
    Subquery,
    Sum,
)
from django.utils import timezone
from django_filters.rest_framework import DjangoFilterBackend
from drf_spectacular.utils import extend_schema, extend_schema_view
from rest_framework import status
from rest_framework.decorators import action
from rest_framework.exceptions import ValidationError
from rest_framework.filters import SearchFilter
from rest_framework.mixins import ListModelMixin
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.viewsets import GenericViewSet, ModelViewSet
from rest_framework_api_key.permissions import HasAP<PERSON>Key

from ads.exceptions import AdsDraftAlreadyAcceptedError
from ads.filters import (
    AdsDraftFilter,
    AdsFilter,
    AdsTrackingFilter,
    CustomOrderingFilter,
    DonorFilter,
)
from ads.models import (
    Ads,
    AdsDraft,
    AdsTracking,
    AdsTrackingDailyData,
    BlackList,
    Donor,
    <PERSON>or<PERSON><PERSON>,
    DonorType,
    PaymentMethod,
    PlatformType,
    ProblemStatus,
    Promo,
    Status,
)
from ads.permissions import CanEditDeleteAdsDraft
from ads.serializers import (
    AdsCalendarListSerializer,
    AdsCatalogSerializer,
    AdsCopySerializer,
    AdsDraftCreateSerializer,
    AdsDraftListSerializer,
    AdsDraftReviewSerializer,
    AdsDraftSerializer,
    AdsDraftUpdateSerializer,
    AdsListSerializer,
    AdsModelsSummarySerializer,
    AdsSerializer,
    AdsSummarySerializer,
    AdsSwapSerializer,
    AdsTrackingCreateSerializer,
    AdsTrackingDetailSerializer,
    AdsTrackingHistorySerializer,
    AdsTrackingListSerializer,
    AdsTrackingUpdateSerializer,
    BlackListDetailSerializer,
    BlackListSerializer,
    CalendarQueryParamsSerializer,
    DonorCreateSerializer,
    DonorListSerializer,
    DonorOptionSerializer,
    DonorSelectAllSerializer,
    DonorTypeSerializer,
    FriendsAdsListSerializer,
    NegativeRomiSerializer,
    PaymentMethodSerializer,
    PlatformTypeSerializer,
    PromoSerializer,
    SMMAdsListSerializer,
    StatusSerializer,
)
from ads.services import (
    AdsDraftService,
    AdsService,
    AdsTrackingService,
)
from ads.spectecular_schemas import (
    ADS_CALENDAR_SCHEMA,
    ADS_TRACKING_DETAIL_SCHEMA,
    ADS_TRACKING_LIST_SCHEMA,
    MODELS_SUMMARY_SCHEMA,
    NEGATIVE_ROMI_SCHEMA,
    RESEARCH_DONOR_SCHEMA,
    SMM_ADS_LIST_SCHEMA,
    SUMMARY_SCHEMA,
)
from base.crm_cache.decorators import cache_response
from base.filter_backends import CustomFilterBackend
from base.filters import CustomSearchFilter
from base.mixins import BaseViewMethodsMixin, ListSerializerResponseMixin
from base.pagination import (
    FiftyPerPagePagination,
    HundredLimitOffsetPagination,
    SixtyPerPagePagination,
    TwentyPerPagePagination,
)
from base.permissions import (
    IsBusinessDevOrHOBD,
    IsHOM,
    IsHOMorMarketer,
    IsResearcher,
    IsSMMorHOMorMarketer,
    IsSuperUserOrHOMorMarketer,
    IsSuperUserOrHOMorMarketerOrHOFOrFinancier,
    WriteOnlyForHOMAndMarketerRole,
    create_role_permission,
)
from base.tools import sort_data_by_field_in_list_of_dicts


@extend_schema(tags=['buying - Payment Methods'])
class PaymentMethodViewSet(ModelViewSet):
    queryset = PaymentMethod.objects.all()
    serializer_class = PaymentMethodSerializer
    permission_classes = [IsHOMorMarketer | IsBusinessDevOrHOBD]

    @cache_response('buying-payment-methods')
    def list(self, request, *args, **kwargs):
        """
        Rewrite list to cache the data
        """
        return super().list(request, *args, **kwargs)


@extend_schema(tags=['buying - Platform Types'])
class PlatformTypeViewSet(ModelViewSet):
    queryset = PlatformType.objects.all()
    serializer_class = PlatformTypeSerializer
    permission_classes = [IsSMMorHOMorMarketer | IsBusinessDevOrHOBD]

    @cache_response('buying-platform-types')
    def list(self, request, *args, **kwargs):
        """
        Rewrite list to cache the data
        """
        return super().list(request, *args, **kwargs)


@extend_schema(tags=['buying - Promos'])
class PromoViewSet(ModelViewSet):
    queryset = Promo.objects.all()
    serializer_class = PromoSerializer
    permission_classes = [IsSuperUserOrHOMorMarketerOrHOFOrFinancier | IsBusinessDevOrHOBD]

    @cache_response('buying-promos')
    def list(self, request, *args, **kwargs):
        """
        Rewrite list to cache the data
        """
        return super().list(request, *args, **kwargs)


@extend_schema(tags=['buying - Donors'])
class DonorViewSet(ListSerializerResponseMixin, BaseViewMethodsMixin, ModelViewSet):
    queryset = Donor.objects.all().select_related('black_list').prefetch_related('options', 'types')

    serializer_class = DonorListSerializer
    action_permissions = {
        'list': [IsSuperUserOrHOMorMarketer | IsBusinessDevOrHOBD],
        'retrieve': [IsSuperUserOrHOMorMarketer | IsBusinessDevOrHOBD],
        'create': [IsSuperUserOrHOMorMarketer | IsBusinessDevOrHOBD],
        'update': [IsSuperUserOrHOMorMarketer | IsBusinessDevOrHOBD],
        'destroy': [IsSuperUserOrHOMorMarketer | IsBusinessDevOrHOBD],
        'partial_update': [IsSuperUserOrHOMorMarketer | IsBusinessDevOrHOBD],
        'research': [IsResearcher],
        'select_all': [IsSuperUserOrHOMorMarketer | IsBusinessDevOrHOBD],
    }
    action_serializers = {
        'list': DonorListSerializer,
        'retrieve': DonorListSerializer,
        'create': DonorCreateSerializer,
        'update': DonorCreateSerializer,
        'partial_update': DonorCreateSerializer,
        'select_all':  DonorSelectAllSerializer
    }

    filter_backends = [DjangoFilterBackend, CustomSearchFilter]
    filterset_class = DonorFilter
    search_fields = ['name', 'types__name', 'options__name']

    pagination_class = FiftyPerPagePagination

    @cache_response('buying-donors')
    def list(self, request, *args, **kwargs):
        """
        Rewrite list to cache the data
        """
        return super().list(request, *args, **kwargs)

    @extend_schema(**RESEARCH_DONOR_SCHEMA)
    @action(detail=False, methods=['get'], url_path='research')
    def research(self, request, *args, **kwargs):
        """
        Check that donor exists
        """
        donor_name = self.request.query_params.get('name')

        if donor_name is None:
            raise ValidationError('name parameter is required')

        donor_name = donor_name.strip()

        if not donor_name:
            raise ValidationError('name parameter cannot be empty')

        with_at_donor_name = '@' + donor_name if not donor_name.startswith('@') else donor_name
        without_at_donor_name = donor_name.lstrip('@')

        exists = Donor.objects.filter(
            Q(name=with_at_donor_name) |
            Q(name=without_at_donor_name)
        ).exists()

        return Response({'exists': exists})

    @action(
        methods=['GET'],
        detail=False,
        url_path='select-all',
        serializer_class=DonorSelectAllSerializer,
        pagination_class=None,
        filter_backends=[]
    )
    def select_all(self, request, *args, **kwargs):
        """
        Select all donors
        """
        queryset = self.get_queryset()

        serializer = self.get_serializer(queryset, many=True)

        return Response(serializer.data)


@extend_schema(tags=['buying - Donors'])
class DonorTypeViewSet(ListModelMixin, GenericViewSet):
    serializer_class = DonorTypeSerializer
    queryset = DonorType.objects.all()
    permission_classes = [IsSuperUserOrHOMorMarketer | IsBusinessDevOrHOBD]


@extend_schema(tags=['buying - Donors'])
class DonorOptionViewSet(ListModelMixin, GenericViewSet):
    serializer_class = DonorOptionSerializer
    queryset = DonorOption.objects.all()
    permission_classes = [IsSuperUserOrHOMorMarketer | IsBusinessDevOrHOBD]


@extend_schema(tags=['buying - Status'])
class StatusViewSet(ModelViewSet):
    queryset = Status.objects.all()
    serializer_class = StatusSerializer
    permission_classes = [IsHOMorMarketer | IsBusinessDevOrHOBD]


@extend_schema(tags=['buying - Problem Status'])
class ProblemStatusViewSet(ListModelMixin, GenericViewSet):
    queryset = ProblemStatus.objects.all()
    serializer_class = StatusSerializer
    permission_classes = [IsHOMorMarketer | IsBusinessDevOrHOBD]


@extend_schema(tags=['buying - Black List'])
class BlackListViewSet(BaseViewMethodsMixin, ListSerializerResponseMixin, ModelViewSet):
    queryset = BlackList.objects.select_related('marketer', 'donor')
    serializer_class = BlackListSerializer
    permission_classes = [(IsSuperUserOrHOMorMarketer & WriteOnlyForHOMAndMarketerRole) | IsBusinessDevOrHOBD]
    pagination_class = TwentyPerPagePagination
    filter_backends = [SearchFilter]
    search_fields = [
        'marketer__first_name',
        'marketer__last_name',
        'donor__name',
        'comment',
    ]

    action_serializers = {
        'list': BlackListDetailSerializer,
        'retrieve': BlackListDetailSerializer,
    }

    @extend_schema(
        request=BlackListSerializer,
        responses=BlackListDetailSerializer,
    )
    def create(self, request, *args, **kwargs):
        """
        Rewrite update method to get correct swagger schema.
        """
        return super().create(request, *args, **kwargs)

    @extend_schema(
        request=BlackListSerializer,
        responses=BlackListDetailSerializer,
    )
    def update(self, request, *args, **kwargs):
        """
        Rewrite update method to get correct swagger schema.
        """
        return super().update(request, *args, **kwargs)

    @extend_schema(
        request=BlackListSerializer,
        responses=BlackListDetailSerializer,
    )
    def partial_update(self, request, *args, **kwargs):
        """
        Rewrite partial_update method to get correct response data structure.
        """
        return super().partial_update(request, *args, **kwargs)

    @cache_response('buying-black-list')
    def list(self, request, *args, **kwargs):
        """
        Rewrite list to cache the data
        """
        return super().list(request, *args, **kwargs)


@extend_schema(tags=['buying - Ads'])
class AdsViewSet(BaseViewMethodsMixin, ListSerializerResponseMixin, ModelViewSet):
    serializer_class = AdsSerializer
    action_permissions = {
        'list': [
            IsSuperUserOrHOMorMarketerOrHOFOrFinancier | IsBusinessDevOrHOBD
        ],
        'retrieve': [IsSuperUserOrHOMorMarketerOrHOFOrFinancier | IsBusinessDevOrHOBD],
        'create': [IsHOMorMarketer | IsBusinessDevOrHOBD],
        'update': [IsHOMorMarketer | IsBusinessDevOrHOBD],
        'partial_update': [IsHOMorMarketer | IsBusinessDevOrHOBD],
        'destroy': [IsHOMorMarketer | IsBusinessDevOrHOBD],
        'catalog': [IsHOMorMarketer | IsBusinessDevOrHOBD],
        'models_summary': [IsSuperUserOrHOMorMarketer | IsBusinessDevOrHOBD],
        'summary': [IsSuperUserOrHOMorMarketer | IsBusinessDevOrHOBD],
        'negative_romi': [IsSuperUserOrHOMorMarketer | IsBusinessDevOrHOBD],
        'smm_ads_list': [IsSMMorHOMorMarketer | IsBusinessDevOrHOBD],
        'copy': [IsSuperUserOrHOMorMarketerOrHOFOrFinancier | IsBusinessDevOrHOBD],
        'swap': [IsHOMorMarketer | IsBusinessDevOrHOBD],
        'calendar': [
            create_role_permission(
                allowed_roles=['hom', 'marketer', 'superuser', 'team_lead', 'senior_operator']
            )
        ]
    }
    filter_backends = [CustomFilterBackend, CustomOrderingFilter, CustomSearchFilter]
    ordering_fields = [
        'date',
        'buy_date',
        'cost',
        'revenue',
        'profit',
        'romi',
        'payment',
        'claims_count',
        'fans_count',
        'donor__name',
        'only_fans_model__nickname',
        'payment_method__name',
        'platform_type__name',
        'promo__name',
        'status__name',
        'marketer__first_name',
        'cost_result'
    ]
    ordering = ['date']
    filterset_class = AdsFilter
    search_fields = [
        'only_fans_model__nickname',
        'only_fans_model__username_of',
        'marketer__first_name',
        'marketer__last_name',
        'donor__name',
        'trial_link',
        'payment_method__name',
        'payment',
        'platform_type__name',
        'promo__name',
        'status__name',
        'comment',
        'ads_number'
    ]
    pagination_class = SixtyPerPagePagination
    action_serializers = {
        'list': AdsListSerializer,
        'models_summary': AdsModelsSummarySerializer,
        'summary': AdsSummarySerializer,
        'negative_romi': NegativeRomiSerializer,
        'retrieve': AdsListSerializer,
        'smm_ads_list': SMMAdsListSerializer,
        'swap': AdsSwapSerializer
    }
    action_search = {
        'models_summary': [
            'only_fans_model__nickname',
            'only_fans_model__username_of',
            'donor__name'
        ],
        'summary': [
            'only_fans_model__nickname',
            'only_fans_model__username_of',
            'donor__name',
            'of_link'
        ],
        'negative_romi': [
            'only_fans_model__nickname',
            'only_fans_model__username_of',
            'marketer__first_name',
            'marketer__last_name',
            'donor__name'
        ]
    }

    def get_queryset(self):
        queryset = Ads.objects.select_related(
            'only_fans_model',
            'marketer',
            'donor__black_list',
            'payment_method',
            'platform_type',
            'promo',
            'status',
            'parent',
            'child',
            'inner_model',
        ).prefetch_related(
            'donor__options',
            'donor__types'
        )

        if hasattr(self.request.user, 'role') and self.request.user.role.name:
            department = Ads.ROLE_DEPARTMENT_MAP.get(self.request.user.role.name, None)

            if department:
                queryset = queryset.filter(department=department)

        return queryset

    @extend_schema(
        request=AdsSerializer,
        responses=AdsListSerializer,
    )
    def create(self, request, *args, **kwargs):
        """
        Rewrite to get correct swagger schema
        """
        return super().create(request, *args, **kwargs)

    @extend_schema(
        request=AdsSerializer,
        responses=AdsListSerializer,
    )
    def update(self, request, *args, **kwargs):
        """
        Rewrite to get correct swagger schema
        """
        return super().update(request, *args, **kwargs)

    @extend_schema(
        request=AdsSerializer,
        responses=AdsListSerializer,
    )
    def partial_update(self, request, *args, **kwargs):
        """
        Rewrite to get correct swagger schema
        """
        return super().partial_update(request, *args, **kwargs)

    @cache_response('buying-catalog')
    @extend_schema(
        request=None,
        responses=AdsCatalogSerializer,
        description='Endpoint for getting all available objects for related fields.',
        summary='Get all available related objects',
    )
    @action(detail=False, methods=['get'], url_path='catalog')
    def catalog(self, request, *args, **kwargs):
        """
        Endpoint for getting all available objects for related fields.
        """
        catalog_data = AdsService.get_catalog()

        return Response(catalog_data, status=status.HTTP_200_OK)

    @cache_response('buying-ads-models-summary')
    @extend_schema(**MODELS_SUMMARY_SCHEMA)
    @action(detail=False, methods=['get'], url_path='models-summary')
    def models_summary(self, request, *args, **kwargs):
        """
        Endpoint for getting summary data for models.
        """
        queryset = self.filter_queryset(self.get_queryset())

        result_list = list(
            queryset.values(
                only_fans_model_nickname=F('only_fans_model__nickname'),
                donor_name=F('donor__name')
            )
            .annotate(
                total_fans_count=Sum('fans_count'),
                promo_count=Count('id'),
                total_claims_count=Sum('claims_count'),
                total_romi=Sum('romi'),
            )
            .order_by()
        )
        ordering = request.query_params.get('ordering')

        if ordering:
            result_list = sort_data_by_field_in_list_of_dicts(result_list, ordering.split(',')[0])

        page = self.paginate_queryset(result_list)

        if page is not None:
            serializer = self.get_serializer(page, many=True)

            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(result_list, many=True)

        return Response(serializer.data, status=status.HTTP_200_OK)

    @cache_response('buying-ads-summary')
    @extend_schema(**SUMMARY_SCHEMA)
    @action(detail=False, methods=['get'], url_path='summary')
    def summary(self, request, *args, **kwargs):
        """
        Endpoint for getting ads summary data.
        """
        queryset = self.filter_queryset(self.get_queryset())
        result_list = list(queryset.filter(only_fans_model__isnull=False, date__isnull=False))
        grouped_data = AdsService.get_ads_summary_data(result_list)

        self.pagination_class = TwentyPerPagePagination
        page = self.paginate_queryset(grouped_data)

        if page is not None:
            serializer = self.get_serializer(data=page, many=True)
            serializer.is_valid(raise_exception=True)

            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(data=grouped_data, many=True)
        serializer.is_valid(raise_exception=True)

        return Response(serializer.data, status=status.HTTP_200_OK)

    @cache_response('buying-ads-negative-romi')
    @extend_schema(**NEGATIVE_ROMI_SCHEMA)
    @action(detail=False, methods=['get'], url_path='negative-romi')
    def negative_romi(self, request, *args, **kwargs):
        """
        Endpoint for getting ads summary data.
        """
        queryset = self.filter_queryset(self.get_queryset())
        result_list = list(queryset.filter(
            romi__lt=0, only_fans_model__isnull=False, date__isnull=False
        ))

        page = self.paginate_queryset(result_list)

        if page is not None:
            serializer = self.get_serializer(page, many=True)

            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)

        return Response(serializer.data, status=status.HTTP_200_OK)

    @cache_response('buying-ads-list')
    def list(self, request, *args, **kwargs):
        """Endpoint for getting ads list data."""
        return super().list(request, *args, **kwargs)

    @extend_schema(**SMM_ADS_LIST_SCHEMA)
    @action(detail=False, methods=['get'], url_path='smm')
    def smm_ads_list(self, request, *args, **kwargs):
        """
        Ads list for SMM
        """
        link = self.request.query_params.get('trial_link')

        if link is None:
            raise ValidationError('Trial link is required')

        if not link.strip():
            raise ValidationError('Trial link can not be empty')

        ads_list = Ads.objects.filter(trial_link=link)
        serializer = self.get_serializer(ads_list, many=True)

        return Response(serializer.data)

    @extend_schema(request=AdsCopySerializer, responses=AdsListSerializer)
    @action(detail=True, methods=['post'], url_path='copy')
    def copy(self, request, pk=None):
        """
        Endpoint to copy an ads.
        """
        ads_data = self.get_serializer(instance=self.get_object()).data
        for attr in AdsSerializer.Meta.read_only_fields + (
                'trial_link', 'refund_date', 'refund_cost', 'refund_payment', 'new_fans_count'
        ):
            ads_data.pop(attr, None)
        ads_data['date'] = request.data.get('date')
        ads_data['only_fans_model'] = request.data.get('only_fans_model')

        serializer = self.get_serializer(data=ads_data)
        serializer.is_valid(raise_exception=True)
        new_ads_instance = serializer.save()
        list_serializer = AdsListSerializer(new_ads_instance)

        return Response(list_serializer.data, status=status.HTTP_201_CREATED)

    @extend_schema(request=AdsSwapSerializer, responses=AdsListSerializer)
    @action(detail=True, methods=['post'], url_path='swap')
    def swap(self, request, pk=None):
        """
        Endpoint to swap an ads.
        """
        parent_ads = self.get_object()
        serializer = self.get_serializer(data=request.data)
        serializer.context['parent'] = parent_ads
        serializer.is_valid(raise_exception=True)
        serializer.save()

        parent_ads.refresh_from_db()
        list_serializer = AdsListSerializer(parent_ads)

        return Response(list_serializer.data, status=status.HTTP_200_OK)

    def perform_destroy(self, instance):
        if instance.is_parent:
            raise ValidationError('You can not delete parent ads, delete child first')
        elif instance.is_child:
            AdsService(instance=instance).delete_swap_child_ads()
        else:
            AdsService(instance=instance).delete_ads()

    @extend_schema(**ADS_CALENDAR_SCHEMA)
    @action(detail=False, methods=['GET'], url_path='calendar')
    def calendar(self, request, *args, **kwargs):
        """
        Endpoint for getting calendar data.
        """
        serializer = CalendarQueryParamsSerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data

        date_after = validated_data['date_after']
        date_before = validated_data['date_before']
        only_fans_models = validated_data.get('only_fans_models')

        calendar_data = AdsService.get_calendar_data(
            date_after=date_after,
            date_before=date_before,
            only_fans_models_ids=only_fans_models,
            user=request.user
        )
        response_serializer = AdsCalendarListSerializer(calendar_data, many=True)

        return Response(response_serializer.data, status=status.HTTP_200_OK)


# --------------------------------------------------SMM-----------------------------------------
@extend_schema(tags=['buying - Tracking'])
@extend_schema_view(
    list=extend_schema(**ADS_TRACKING_LIST_SCHEMA),
    history=extend_schema(**ADS_TRACKING_LIST_SCHEMA),
    retrieve=extend_schema(**ADS_TRACKING_DETAIL_SCHEMA),
)
class AdsTrackingViewSet(BaseViewMethodsMixin, ListSerializerResponseMixin, ModelViewSet):
    """
    API endpoint for AdsTrackings
    """
    queryset = AdsTracking.objects.select_related(
        'ads__only_fans_model', 'ads__platform_type'
    ).prefetch_related(
        'daily_data'
    )
    serializer_class = AdsTrackingListSerializer
    permission_classes = [IsSMMorHOMorMarketer | IsBusinessDevOrHOBD]
    action_serializers = {
        'create': AdsTrackingCreateSerializer,
        'update': AdsTrackingUpdateSerializer,
        'partial_update': AdsTrackingUpdateSerializer,
        'list': AdsTrackingListSerializer,
        'retrieve': AdsTrackingDetailSerializer,
        'history': AdsTrackingHistorySerializer
    }
    filter_backends = [AdsTrackingFilter]

    def get_queryset(self):
        queryset = super().get_queryset()

        if self.action == 'list':
            department_scope = self.request.query_params.get('department_scope') == 'true'
            queryset = queryset.filter(track_end__isnull=True)

            if department_scope:
                queryset = queryset.filter(creator_role=self.request.user.role)
            else:
                queryset = queryset.filter(created_by=self.request.user)

        elif self.action == 'history':
            latest_daily_data = AdsTrackingDailyData.objects.filter(
                ads_tracking=OuterRef('pk')
            ).order_by('-date')
            queryset = queryset.filter(track_end__isnull=False).annotate(
                latest_revenue_end=Subquery(latest_daily_data.values('revenue_end')[:1]),
                latest_claims_count_end=Subquery(latest_daily_data.values('claims_count_end')[:1])
            )

        elif self.action == 'retrieve':
            date_after = self.request.query_params.get('date_after')
            date_before = self.request.query_params.get('date_before')

            ads_tracking_daily_data_queryset = AdsTrackingDailyData.objects.all()

            if date_after:
                ads_tracking_daily_data_queryset = ads_tracking_daily_data_queryset.filter(date__gte=date_after)

            if date_before:
                ads_tracking_daily_data_queryset = ads_tracking_daily_data_queryset.filter(date__lte=date_before)

            queryset = queryset.prefetch_related(
                Prefetch(
                    'daily_data',
                    queryset=ads_tracking_daily_data_queryset,
                    to_attr='filtered_daily_data'
                )
            )

        return queryset

    @extend_schema(request=None)
    @action(detail=True, methods=['post'], url_path='end-tracking')
    def end_tracking(self, request, *args, **kwargs):
        """
        End tracking (move AdsTracking to history)
        """
        ads_tracking = self.get_object()

        AdsTrackingService(ads_tracking).end_tracking()
        serializer = AdsTrackingListSerializer(ads_tracking, many=False)

        return Response(serializer.data, status=status.HTTP_200_OK)

    @action(detail=False, methods=['get'], url_path='history')
    def history(self, request, *args, **kwargs):
        """
        Get history of AdsTracking
        """
        queryset = self.filter_queryset(self.get_queryset())
        serializer = self.get_serializer(queryset, many=True)

        return Response(serializer.data, status=status.HTTP_200_OK)

    def perform_create(self, serializer):
        serializer.save(
            created_by=self.request.user,
            creator_role=self.request.user.role,
        )


# ----------------------------------------------FOR PARSERS-------------------------------------------------

@extend_schema(
    tags=['buying - Ads'],
    responses={
        200: FriendsAdsListSerializer(many=True),  # Automatically handles pagination in the schema
    }
)
class FriendsAdsListView(APIView):
    """
    Endpoint for listing friend ads
    """
    permission_classes = [HasAPIKey]
    pagination_class = HundredLimitOffsetPagination

    def get(self, request):
        current_date = timezone.now().date()
        queryset = Ads.objects.filter(
            status__name='other_type',
            date__lte=current_date,
            date_end__gte=current_date,
            of_link__isnull=False
        ).select_related('only_fans_model')
        paginator = self.pagination_class()
        ads = paginator.paginate_queryset(
            queryset=queryset,
            request=request,
            view=self
        )
        serializer = FriendsAdsListSerializer(ads, many=True)

        return paginator.get_paginated_response(serializer.data)


# -------------------------------------------------Ads Draft--------------------------------
@extend_schema(tags=['buying - Ads Draft'])
class AdsDraftViewSet(BaseViewMethodsMixin, ListSerializerResponseMixin, ModelViewSet):
    serializer_class = AdsDraftSerializer
    action_serializers = {
        'list': AdsDraftListSerializer,
        'retrieve': AdsDraftListSerializer,
        'create': AdsDraftCreateSerializer,
        'update': AdsDraftUpdateSerializer,
        'partial_update': AdsDraftUpdateSerializer,
        'accept': AdsDraftReviewSerializer,
        'decline': AdsDraftReviewSerializer,
    }
    filterset_class = AdsDraftFilter
    permission_classes = (IsHOMorMarketer, CanEditDeleteAdsDraft)
    pagination_class = SixtyPerPagePagination
    action_permissions = {
        'accept': [IsHOM | HasAPIKey],
        'decline': [IsHOM | HasAPIKey],
    }

    def get_queryset(self):
        queryset = AdsDraft.objects.select_related(
            'only_fans_model',
            'donor',
            'promo',
            'marketer',
            'donor__black_list',
            'ads'
        ).prefetch_related(
            'donor__options',
            'donor__types'
        )

        if self.action == 'list' and hasattr(self.request.user, 'role') and self.request.user.role.name == 'marketer':
            queryset = queryset.filter(marketer=self.request.user)

        return queryset

    def perform_destroy(self, instance):
        service = AdsDraftService()
        service.delete_draft(instance=instance)

    @extend_schema(request=AdsDraftReviewSerializer, responses=AdsDraftListSerializer)
    @action(detail=True, methods=['post'], url_path='accept')
    def accept(self, request, pk=None):
        """
        Accept ads draft
        """
        ads_draft = self.get_object()

        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        service = AdsDraftService()
        try:
            ads_draft = service.accept_draft(
                instance=ads_draft,
                reviewer=request.user,
                **serializer.validated_data
            )
        except AdsDraftAlreadyAcceptedError as e:
            raise ValidationError(e)

        serializer = self.action_serializers.get('list')(ads_draft)

        return Response(serializer.data, status=status.HTTP_200_OK)

    @extend_schema(request=AdsDraftReviewSerializer, responses=AdsDraftListSerializer)
    @action(detail=True, methods=['post'], url_path='decline')
    def decline(self, request, pk=None):
        """
        Decline ads draft
        """
        ads_draft = self.get_object()

        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        service = AdsDraftService()
        ads_draft = service.decline_draft(
            instance=ads_draft,
            reviewer=request.user,
            **serializer.validated_data
        )

        serializer = self.action_serializers.get('list')(ads_draft)

        return Response(serializer.data, status=status.HTTP_200_OK)
