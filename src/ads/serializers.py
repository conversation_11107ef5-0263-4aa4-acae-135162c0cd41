from django.contrib.auth import get_user_model
from drf_spectacular.types import OpenApiTypes
from drf_spectacular.utils import extend_schema_field
from rest_framework import serializers
from rest_framework.relations import StringRelatedField
from rest_framework.validators import UniqueValidator

from accounts.serializers import PureUserSerializer
from ads.exceptions import AdsDraftError
from ads.models import (
    Ads,
    AdsDraft,
    AdsTracking,
    AdsTrackingDailyData,
    BlackList,
    Donor,
    DonorOption,
    DonorType,
    PaymentMethod,
    PlatformType,
    ProblemStatus,
    Promo,
    Status,
)
from ads.services import (
    AdsDraftService,
    AdsService,
    AdsTrackingService,
)
from ads.validators import AdsValidator
from base.serializiers import CreatableSlugRelated<PERSON>ield, CreatableSlugRelatedFieldIgnoreExtraSpaces
from only_fans_models.models import OnlyFansModel
from only_fans_models.serializers import OnlyFansModelsSerializer


def get_user_department(user: get_user_model()) -> Ads.DepartmentChoices:
    if not user.role:
        raise serializers.ValidationError("User role is required.")

    department = Ads.ROLE_DEPARTMENT_MAP.get(user.role.name, None)
    if not department:
        raise serializers.ValidationError(
            f"Department for user role: {user.role.name} not found."
        )

    return department


class PaymentMethodSerializer(serializers.ModelSerializer):
    """
    Payment method serializer
    """

    class Meta:
        model = PaymentMethod
        fields = ('id', 'name')

    def validate_name(self, value):
        """
        Check that the name is unique (case-insensitive).
        """
        if PaymentMethod.objects.filter(name__iexact=value).exists():
            raise serializers.ValidationError(
                "Payment method with this name already exists."
            )

        return value


class PlatformTypeSerializer(serializers.ModelSerializer):
    """
    Platform type serializer
    """

    class Meta:
        model = PlatformType
        fields = ('id', 'name')

    def validate_name(self, value):
        """
        Check that the name is unique (case-insensitive).
        """
        if PlatformType.objects.filter(name__iexact=value).exists():
            raise serializers.ValidationError(
                "Platform type with this name already exists."
            )

        return value


class PromoSerializer(serializers.ModelSerializer):
    """
    Promo serializer
    """

    class Meta:
        model = Promo
        fields = ('id', 'name')

    def validate_name(self, value):
        """
        Check that the name is unique (case-insensitive).
        """
        if Promo.objects.filter(name__iexact=value).exists():
            raise serializers.ValidationError("Promo with this name already exists.")

        return value


class DonorTypeSerializer(serializers.ModelSerializer):
    """
    Donor type serializer
    """

    class Meta:
        model = DonorType
        fields = ('id', 'name')


class DonorOptionSerializer(serializers.ModelSerializer):
    """
    Donor option serializer
    """

    class Meta:
        model = DonorOption
        fields = ('id', 'name')


class DonorCreateSerializer(serializers.ModelSerializer):
    """
    Donor create serializer
    """

    class Meta:
        model = Donor
        fields = ('name', 'is_limited', 'types', 'options', 'secret_key')

    def validate_name(self, value):
        """
        Check that the name is unique (case-insensitive).
        """
        if Donor.objects.filter(name__iexact=value).exists():
            raise serializers.ValidationError("Donor with this name already exists.")

        return value


class DonorListSerializer(serializers.ModelSerializer):
    """
    Donor list serializer
    """

    is_blacklist = serializers.SerializerMethodField(read_only=True)
    types = DonorTypeSerializer(read_only=True, many=True)
    options = DonorOptionSerializer(read_only=True, many=True)

    class Meta:
        model = Donor
        fields = (
            'id',
            'name',
            'is_blacklist',
            'is_limited',
            'types',
            'options',
            'speed_completion',
            'secret_key',
        )
        read_only_fields = fields

    @extend_schema_field(serializers.BooleanField())
    def get_is_blacklist(self, obj) -> bool:
        """
        Check if the donor is in the black list.
        """
        return hasattr(obj, 'black_list')


class DonorSelectAllSerializer(DonorListSerializer):
    """
    Donor select all serializer
    """

    class Meta:
        model = Donor
        fields = ('id', 'name', 'is_blacklist', 'is_limited')
        read_only_fields = fields


class StatusSerializer(serializers.ModelSerializer):
    """
    Status serializer
    """

    class Meta:
        model = Status
        fields = ('id', 'name')

    def validate_name(self, value):
        """
        Check that the name is unique (case-insensitive).
        """
        if Status.objects.filter(name__iexact=value).exists():
            raise serializers.ValidationError("Status with this name already exists.")

        return value


class ProblemStatusSerializer(serializers.ModelSerializer):
    """
    ProblemStatus serializer
    """

    class Meta:
        model = ProblemStatus
        fields = ('id', 'name')


class BlackListSerializer(serializers.ModelSerializer):
    """
    BlackList serializer
    """

    marketer = serializers.PrimaryKeyRelatedField(
        queryset=get_user_model().objects.filter(role__name='marketer'),
        required=False,
        allow_null=True,
    )
    donor = CreatableSlugRelatedFieldIgnoreExtraSpaces(
        slug_field='name',
        queryset=Donor.objects.all(),
        validators=[UniqueValidator(queryset=BlackList.objects.all())]
    )

    class Meta:
        model = BlackList
        fields = ('id', 'comment', 'marketer', 'donor')


class BlackListDetailSerializer(BlackListSerializer):
    """
    BlackList serializer
    """

    donor = DonorListSerializer()
    marketer = PureUserSerializer()


class AdsSerializer(serializers.ModelSerializer):
    """
    Ads serializer
    """

    only_fans_model = serializers.PrimaryKeyRelatedField(
        queryset=OnlyFansModel.objects.all(), required=True, allow_null=False
    )
    donor = CreatableSlugRelatedFieldIgnoreExtraSpaces(
        slug_field='name', allow_null=True, queryset=Donor.objects.all(), required=False
    )
    payment_method = CreatableSlugRelatedField(
        slug_field='name',
        allow_null=True,
        queryset=PaymentMethod.objects.all(),
        required=False,
    )
    platform_type = CreatableSlugRelatedField(
        slug_field='name',
        allow_null=True,
        queryset=PlatformType.objects.all(),
        required=False,
    )
    promo = CreatableSlugRelatedField(
        slug_field='name', allow_null=True, queryset=Promo.objects.all(), required=False
    )
    status = CreatableSlugRelatedField(
        slug_field='name',
        allow_null=True,
        queryset=Status.objects.all(),
        required=False,
    )
    date = serializers.DateField(required=True, allow_null=False)
    is_blocked = serializers.SerializerMethodField()
    fans_profit = serializers.DecimalField(
        max_digits=10,
        decimal_places=2,
        read_only=True,
    )

    class Meta:
        model = Ads
        fields = (
            'id',
            'date',
            'trial_link',
            'buy_date',
            'cost',
            'payment',
            'only_fans_model',
            'payment_method',
            'platform_type',
            'donor',
            'promo',
            'fans_count',
            'cwvt',
            'comment',
            'of_link',
            'marketer',
            'status',
            'revenue',
            'profit',
            'romi',
            'claims_count',
            'of_link_date',
            'fans_delta',
            'refund_date',
            'refund_cost',
            'refund_payment',
            'new_fans_count',
            'cost_result',
            'ads_number',
            'spenders',
            'spenders_low',
            'spenders_high',
            'is_blocked',
            'is_parent',
            'is_child',
            'parent',
            'child',
            'phantom_cost',
            'friends_status',
            'reserve_link',
            'date_end',
            'fans_profit',
            'date_counter',
            'inner_traffic',
            'inner_model',
            'cost_per_fan',
            'arpu',
            'problem_status',
            'problem_comment',
            'refund_date_start',
            'debt',
            'calendar_start_date',
            'calendar_end_date',
            'fans_per_day',
        )
        read_only_fields = (
            'id',
            'revenue',
            'profit',
            'romi',
            'claims_count',
            'fans_delta',
            'cost_result',
            'ads_number',
            'spenders',
            'spenders_low',
            'spenders_high',
            'is_blocked',
            'is_parent',
            'is_child',
            'parent',
            'child',
            'phantom_cost',
            'friends_status',
            'fans_profit',
            'date_counter',
            'inner_traffic',
            'cost_per_fan',
            'debt',
            'fans_per_day',
            'arpu'
        )

    def create(self, validated_data):
        """
        Create and return a new `Ads` instance, given the validated data.
        """

        return AdsService.create(validated_data, user=self.context['request'].user)

    def validate(self, attrs):
        if self.context.get('request'):
            user = self.context['request'].user
            department = get_user_department(user)

            if self.instance:
                AdsValidator().validate_update(
                    instance=self.instance, data=attrs, user=user, department=department
                )
            else:
                AdsValidator().validate_create(data=attrs, department=department)

        return attrs

    def update(self, instance, validated_data):
        """
        Update and return an existing `Ads` instance, given the validated data.
        """
        service = AdsService(instance)
        instance = service.update(validated_data=validated_data)

        return instance

    def to_internal_value(self, data):
        if 'trial_link' in data and data['trial_link'] == '':
            data['trial_link'] = None

        return super().to_internal_value(data)

    @extend_schema_field(OpenApiTypes.BOOL)
    def get_is_blocked(self, obj):
        return obj.is_blocked


class NestedAdsSerializer(serializers.ModelSerializer):
    """
    Nested Ads serializer
    """

    class Meta:
        model = Ads
        fields = (
            'id',
            'date',
            'cost',
            'cost_result',
            'cwvt',
            'phantom_cost',
            'claims_count',
            'fans_count',
            'revenue',
            'profit',
            'romi',
            'spenders',
            'fans_delta',
            'ads_number',
        )


class AdsListSerializer(AdsSerializer):
    """
    Ads list serializer
    """

    only_fans_model = OnlyFansModelsSerializer(read_only=True)
    marketer = PureUserSerializer(read_only=True)
    donor = DonorListSerializer(read_only=True)
    payment_method = PaymentMethodSerializer(read_only=True)
    platform_type = PlatformTypeSerializer(read_only=True)
    promo = PromoSerializer(read_only=True)
    status = StatusSerializer(read_only=True)
    parent = NestedAdsSerializer(read_only=True, allow_null=True)
    child = NestedAdsSerializer(read_only=True, allow_null=True)
    inner_model = OnlyFansModelsSerializer(read_only=True, allow_null=True)


class AdsCatalogSerializer(serializers.Serializer):
    """
    Ads catalog serializer
    """

    only_fans_models = OnlyFansModelsSerializer(many=True, read_only=True)
    donors = DonorListSerializer(many=True, read_only=True)
    payment_methods = PaymentMethodSerializer(many=True, read_only=True)
    platform_types = PlatformTypeSerializer(many=True, read_only=True)
    marketers = PureUserSerializer(many=True, read_only=True)
    promos = PromoSerializer(many=True, read_only=True)


class AdsModelsSummarySerializer(serializers.Serializer):
    """
    Ads models summary serializer
    """

    only_fans_model_nickname = serializers.CharField()
    donor_name = serializers.CharField()
    total_fans_count = serializers.IntegerField()
    total_claims_count = serializers.IntegerField()
    total_romi = serializers.DecimalField(max_digits=10, decimal_places=2)
    promo_count = serializers.IntegerField()


class PaginatedAdsModelsSummarySerializer(serializers.Serializer):
    """
    Paginated ads models summary serializer for swagger
    """

    count = serializers.IntegerField()
    next = serializers.IntegerField()
    previous = serializers.IntegerField()
    results = AdsModelsSummarySerializer(many=True)


class NestedAdsSummarySerializer(serializers.Serializer):
    """
    Nested ads summary serializer
    """

    id = serializers.UUIDField()
    only_fans_model = serializers.CharField()
    donor = serializers.CharField(allow_null=True, required=False)
    claims_count = serializers.IntegerField(allow_null=True, required=False)
    fans_count = serializers.IntegerField(allow_null=True, required=False)
    romi = serializers.DecimalField(
        max_digits=10, decimal_places=2, allow_null=True, required=False
    )
    of_link = serializers.CharField(allow_null=True, required=False)


class AdsSummarySerializer(serializers.Serializer):
    """
    Grouped ads serializer
    """

    date = serializers.DateField()
    items = NestedAdsSummarySerializer(many=True)


class PaginatedAdsSummarySerializer(serializers.Serializer):
    """
    Paginated grouped ads serializer for swagger
    """

    count = serializers.IntegerField()
    next = serializers.IntegerField()
    previous = serializers.IntegerField()
    results = AdsSummarySerializer(many=True)


class NegativeRomiSerializer(serializers.ModelSerializer):
    """
    Negative romi serializer
    """

    marketer = StringRelatedField()
    only_fans_model = StringRelatedField()
    donor = StringRelatedField()

    class Meta:
        model = Ads
        fields = (
            'id',
            'marketer',
            'only_fans_model',
            'donor',
            'date',
            'buy_date',
            'claims_count',
            'cost_result',
            'romi',
        )


class PaginatedNegativeRomiSerializer(serializers.Serializer):
    """
    Paginated negative romi serializer for swagger
    """

    count = serializers.IntegerField()
    next = serializers.IntegerField()
    previous = serializers.IntegerField()
    results = NegativeRomiSerializer(many=True)


class AdsCopySerializer(serializers.Serializer):
    """
    Ads copy serializer
    """

    date = serializers.DateField()
    only_fans_model = serializers.PrimaryKeyRelatedField(
        queryset=OnlyFansModel.objects.all()
    )


# --------------------------------------------SMM--------------------------------------------------------
class SMMAdsListSerializer(serializers.ModelSerializer):
    """
    SMM ads list serializer
    """

    only_fans_model_nickname = serializers.CharField(source='only_fans_model.nickname')
    platform_type_name = serializers.CharField(
        source='platform_type.name', allow_null=True
    )

    class Meta:
        model = Ads
        fields = (
            'id',
            'trial_link',
            'platform_type_name',
            'only_fans_model_nickname',
            'revenue',
            'claims_count',
        )


class AdsTrackingListSerializer(serializers.ModelSerializer):
    """
    Ads tracking list serializer
    """

    link = serializers.URLField(source='ads.trial_link')
    revenue = serializers.DecimalField(
        read_only=True, max_digits=10, decimal_places=2, source='ads.revenue'
    )
    claims_count = serializers.IntegerField(read_only=True, source='ads.claims_count')
    only_fans_model_nickname = serializers.CharField(
        read_only=True, source='ads.only_fans_model.nickname'
    )
    track_start = serializers.DateTimeField(read_only=True, format="%Y-%m-%d")
    track_end = serializers.DateTimeField(read_only=True, format="%Y-%m-%d")
    platform_type_name = serializers.CharField(
        read_only=True, source='ads.platform_type.name'
    )

    class Meta:
        model = AdsTracking
        fields = (
            'id',
            'track_start',
            'track_end',
            'comment',
            'link',
            'revenue',
            'claims_count',
            'only_fans_model_nickname',
            'platform_type_name',
        )


class AdsTrackingUpdateSerializer(serializers.ModelSerializer):
    """
    Ads tracking update serializer
    """

    class Meta:
        model = AdsTracking
        fields = ('comment',)


class AdsTrackingCreateSerializer(serializers.ModelSerializer):
    """
    Ads tracking create serializer
    """

    ads = serializers.PrimaryKeyRelatedField(
        queryset=Ads.objects.select_related('only_fans_model')
    )

    class Meta:
        model = AdsTracking
        fields = ('comment', 'ads')

    def create(self, validated_data):
        """
        Create and return a new `AdsTracking` instance, given the validated data.
        """
        validated_data['creator'] = self.context['request'].user

        return AdsTrackingService.create(validated_data)


class AdsTrackingDailyDataSerializer(serializers.ModelSerializer):
    """
    Ads tracking daily data serializer
    """

    class Meta:
        model = AdsTrackingDailyData
        fields = (
            'id',
            'date',
            'revenue_start',
            'revenue_end',
            'total_day_revenue',
            'claims_count_start',
            'claims_count_end',
            'total_day_claims_count',
        )


class AdsTrackingDetailSerializer(AdsTrackingListSerializer):
    """
    AdsTracking serializer for retrieve action
    """

    daily_data = AdsTrackingDailyDataSerializer(
        source='filtered_daily_data', many=True, read_only=True
    )

    class Meta(AdsTrackingListSerializer.Meta):
        fields = AdsTrackingListSerializer.Meta.fields + ('daily_data',)


class AdsTrackingHistorySerializer(AdsTrackingListSerializer):
    """
    AdsTracking history serializer
    """

    revenue = serializers.SerializerMethodField()
    claims_count = serializers.SerializerMethodField()

    def get_revenue(self, obj):
        # from the Subquery
        return getattr(obj, 'latest_revenue_end', None)

    def get_claims_count(self, obj):
        # from the Subquery
        return getattr(obj, 'latest_claims_count_end', None)


# --------------------------------------------------RESEARCHER---------------------------------------
class DonorResearchSerializer(serializers.Serializer):
    """
    Donor research serializer
    """

    exists = serializers.BooleanField()


# -------------------------------------------------SWAP---------------------------------------------
class AdsSwapSerializer(serializers.ModelSerializer):
    """
    Ads swap serializer
    """

    class Meta:
        model = Ads
        fields = (
            'date',
            'trial_link',
            'only_fans_model',
            'promo',
            'comment',
            'of_link_date',
            'marketer',
        )
        extra_kwargs = {
            'date': {
                'required': True,
                'allow_null': False,
            },
            'only_fans_model': {'required': True, 'allow_null': False},
            'trial_link': {'required': True, 'allow_null': False, 'allow_blank': False},
        }

    def validate(self, attrs):
        assert (
            parent := self.context.get('parent')
        ) is not None, 'Parent instance in the context is required'
        assert (
            request := self.context.get('request')
        ) is not None, 'Request instance in the context is required'

        user = request.user
        department = get_user_department(user)

        AdsValidator().validate_swap(
            parent=parent,
            data=attrs,
            department=department,
        )

        return attrs

    def create(self, validated_data):
        return AdsService.create_swap(
            validated_data=validated_data,
            parent=self.context['parent'],
            user=self.context['request'].user,
        )


# -----------------------------------------FOR PARSERS---------------------------------------------------
class FriendsAdsListSerializer(serializers.ModelSerializer):
    """
    Friends ads list serializer
    """

    model_id = serializers.IntegerField(source='only_fans_model.model_id')
    promo_id = serializers.IntegerField(source='ads_number', allow_null=True)

    class Meta:
        model = Ads
        fields = ('date', 'date_end', 'of_link', 'model_id', 'reserve_link', 'promo_id')


# -----------------------------------------Ads calendar--------------------------------------------------------
class CalendarQueryParamsSerializer(serializers.Serializer):
    date_after = serializers.DateField(required=True)
    date_before = serializers.DateField(required=True)
    only_fans_models = serializers.CharField(required=False, allow_blank=True)

    def validate_only_fans_models(self, value):
        """
        Splits the only_fans_models string by comma,
        strips extra whitespace, and removes empty entries.
        """
        models = [model.strip() for model in value.split(',') if model.strip()]

        return models

    def validate(self, attrs):
        """
        Validates the date_after and date_before fields.
        """
        date_after = attrs['date_after']
        date_before = attrs['date_before']

        if date_after > date_before:
            raise serializers.ValidationError(
                'date_after must be less than date_before'
            )

        return attrs


class AdsCalendarListSerializer(serializers.ModelSerializer):
    """
    Ads calendar list serializer
    """

    only_fans_model = serializers.CharField(source='only_fans_model.username_of')
    status = serializers.CharField(source='status.name', allow_null=True)

    class Meta:
        model = Ads
        fields = (
            'id',
            'calendar_start_date',
            'calendar_end_date',
            'only_fans_model',
            'status',
        )


# --------------------------------------------AdsDraft-------------------------------------------------
class AdsDraftSerializer(serializers.ModelSerializer):
    """
    Ads draft serializer
    """

    class Meta:
        model = AdsDraft
        fields = (
            'id',
            'date',
            'only_fans_model',
            'donor',
            'cost',
            'arpu',
            'fans_count',
            'cost_per_fan',
            'status',
            'marketer',
            'ads',
            'promo',
            'donor_debt',
            'review_comment'
        )
        read_only_fields = (
            'id',
            'cost_per_fan',
            'status',
            'ads',
            'marketer'
        )


class DraftNestedAdsSerializer(serializers.ModelSerializer):
    """
    Nested Ads serializer
    """

    class Meta:
        model = Ads
        fields = (
            'id',
            'ads_number',
        )


class AdsDraftListSerializer(AdsDraftSerializer):
    """
    Ads draft list serializer
    """

    only_fans_model = OnlyFansModelsSerializer(read_only=True)
    donor = DonorListSerializer(read_only=True)
    marketer = PureUserSerializer(read_only=True)
    promo = PromoSerializer(read_only=True)
    ads = DraftNestedAdsSerializer(read_only=True)


class AdsDraftCreateSerializer(AdsDraftSerializer):
    """
    Ads draft create serializer
    """

    donor = CreatableSlugRelatedFieldIgnoreExtraSpaces(
        slug_field='name', queryset=Donor.objects.all(), required=True
    )
    cost = serializers.DecimalField(
        max_digits=10,
        decimal_places=2,
        allow_null=False,
        required=True,
    )
    fans_count = serializers.IntegerField(allow_null=False, required=True)
    promo = CreatableSlugRelatedField(
        slug_field='name',
        queryset=Promo.objects.all(),
        required=True,
        allow_null=False
    )

    def create(self, validated_data):
        service = AdsDraftService()

        try:
            new_draft = service.create_draft(marketer=self.context['request'].user, **validated_data)
        except AdsDraftError as e:
            raise serializers.ValidationError(str(e))

        return new_draft


class AdsDraftUpdateSerializer(AdsDraftSerializer):
    """
    Ads draft update serializer
    """

    donor = CreatableSlugRelatedFieldIgnoreExtraSpaces(
        slug_field='name', queryset=Donor.objects.all(), required=True
    )
    cost = serializers.DecimalField(
        max_digits=10,
        decimal_places=2,
        allow_null=False,
        required=True,
    )
    fans_count = serializers.IntegerField(allow_null=False, required=True)
    status = serializers.ChoiceField(
        choices=(AdsDraft.StatusChoices.ACCEPT, AdsDraft.StatusChoices.DECLINE),
        required=False,
    )
    promo = CreatableSlugRelatedField(
        slug_field='name',
        queryset=Promo.objects.all(),
        required=True,
        allow_null=False
    )

    class Meta(AdsDraftSerializer.Meta):
        read_only_fields = (
            'id',
            'cost_per_fan',
            'ads',
            'marketer'
        )

    def validate(self, attrs):
        status = attrs.get('status') or self.instance.status
        review_comment = attrs.get('review_comment')

        if review_comment and status not in [
            AdsDraft.StatusChoices.ACCEPT,
            AdsDraft.StatusChoices.DECLINE,
        ]:
            raise serializers.ValidationError(
                {'review_comment': 'Review comment without status is not allowed'}
            )

        return attrs

    def update(self, instance, validated_data):
        user = self.context['request'].user

        service = AdsDraftService()

        return service.update_draft(instance=instance, user=user, **validated_data)


class AdsDraftReviewSerializer(serializers.ModelSerializer):
    """
    AdsDraft review serializer
    """

    class Meta:
        model = AdsDraft
        fields = ('review_comment',)
