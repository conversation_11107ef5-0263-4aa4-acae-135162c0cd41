from rest_framework import permissions
from rest_framework.request import Request
from rest_framework.views import APIView

from ads.models import AdsDraft


class CanEditDeleteAdsDraft(permissions.BasePermission):
    def has_object_permission(self, request: Request, view: APIView, obj: AdsDraft) -> bool:
        if request.method in permissions.SAFE_METHODS:
            return True

        if obj.status != AdsDraft.StatusChoices.REVIEW:
            return False

        if request.user.role.name == 'marketer':
            return obj.marketer == request.user

        return True
