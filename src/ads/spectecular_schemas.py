from drf_spectacular.types import OpenApiTypes
from drf_spectacular.utils import Open<PERSON><PERSON>Example, OpenApiParameter
from rest_framework import status

from ads.serializers import (
    AdsCalendarListSerializer,
    AdsTrackingListSerializer,
    DonorResearchSerializer,
    PaginatedAdsModelsSummarySerializer,
    PaginatedAdsSummarySerializer,
    PaginatedNegativeRomiSerializer,
    SMMAdsListSerializer,
)

MODELS_SUMMARY_SCHEMA = {
    'parameters': [
        OpenApiParameter(
            name='date_after',
            description='Filter summary by date after',
            required=False,
            type=OpenApiTypes.DATE,
            location=OpenApiParameter.QUERY,
        ),
        OpenApiParameter(
            name='date_before',
            description='Filter summary by date before',
            required=False,
            type=OpenApiTypes.DATE,
            location=OpenApiParameter.QUERY,
        ),
        OpenApiParameter(
            name='only_fans_model',
            description='Filter summary by only fans model',
            required=False,
            type=OpenApiTypes.UUID,
            location=OpenApiParameter.QUERY,
        ),
        OpenApiParameter(
            name='donor',
            description='Filter summary by donor',
            required=False,
            type=OpenApiTypes.UUID,
            location=OpenApiParameter.QUERY,
        ),
        OpenApiParameter(
            name='page',
            description='A page number within the paginated result set',
            required=False,
            type=OpenApiTypes.INT,
            location=OpenApiParameter.QUERY,
        ),
        OpenApiParameter(
            name='search',
            description='A search term',
            required=False,
            type=OpenApiTypes.STR,
            location=OpenApiParameter.QUERY,
        ),
        OpenApiParameter(
            name='ordering',
            description='Which field to use when ordering the results',
            required=False,
            type=OpenApiTypes.STR,
        ),
    ],
    'description': 'Endpoint for getting summary data for models and donors.',
    'summary': 'Get summary data for models and donors',
    'responses': PaginatedAdsModelsSummarySerializer,
}

SUMMARY_SCHEMA = {
    'parameters': [
        OpenApiParameter(
            name='date_after',
            description='Filter summary by date after',
            required=False,
            type=OpenApiTypes.DATE,
        ),
        OpenApiParameter(
            name='date_before',
            description='Filter summary by date before',
            required=False,
            type=OpenApiTypes.DATE,
        ),
        OpenApiParameter(
            name='only_fans_model',
            description='Filter summary by only fans model',
            required=False,
            type=OpenApiTypes.UUID,
        ),
        OpenApiParameter(
            name='donor',
            description='Filter summary by donor',
            required=False,
            type=OpenApiTypes.UUID,
        ),
        OpenApiParameter(
            name='page',
            description='A page number within the paginated result set',
            required=False,
            type=OpenApiTypes.INT,
        ),
        OpenApiParameter(
            name='search',
            description='A search term',
            required=False,
            type=OpenApiTypes.STR,
        ),
        OpenApiParameter(
            name='promo',
            description='Filter summary by promo',
            required=False,
            type=OpenApiTypes.UUID,
        ),
    ],
    'description': 'Endpoint for getting ads summary data.',
    'summary': 'Get ads summary data',
    'responses': PaginatedAdsSummarySerializer,
}

NEGATIVE_ROMI_SCHEMA = {
    'parameters': [
        OpenApiParameter(
            name='date_after',
            description='Filter rows by date after',
            required=False,
            type=OpenApiTypes.DATE,
        ),
        OpenApiParameter(
            name='date_before',
            description='Filter rows by date before',
            required=False,
            type=OpenApiTypes.DATE,
        ),
        OpenApiParameter(
            name='only_fans_model',
            description='Filter rows by only fans model',
            required=False,
            type=OpenApiTypes.UUID,
        ),
        OpenApiParameter(
            name='marketer',
            description='Filter rows by marketer',
            required=False,
            type=OpenApiTypes.UUID,
        ),
        OpenApiParameter(
            name='claims_count_min',
            description='Filter rows by claims count min',
            required=False,
            type=OpenApiTypes.INT,
        ),
        OpenApiParameter(
            name='claims_count_max',
            description='Filter rows by claims count max',
            required=False,
            type=OpenApiTypes.INT,
        ),
        OpenApiParameter(
            name='page',
            description='A page number within the paginated result set',
            required=False,
            type=OpenApiTypes.INT,
        ),
        OpenApiParameter(
            name='search',
            description='A search term',
            required=False,
            type=OpenApiTypes.STR,
        ),
        OpenApiParameter(
            name='ordering',
            description='Which field to use when ordering the results',
            required=False,
            type=OpenApiTypes.STR,
        ),
    ],
    'description': 'Endpoint for getting negative romi data.',
    'summary': 'Get negative romi data',
    'responses': PaginatedNegativeRomiSerializer,
}

SMM_ADS_LIST_SCHEMA = {
    'parameters': [
        OpenApiParameter(
            name='trial_link',
            description='Filter rows by trial link',
            required=True,
            type=OpenApiTypes.STR,
        ),
    ],
    'description': 'Endpoint for getting ads for SMM',
    'summary': 'Get ads for SMM',
    'responses': SMMAdsListSerializer,
}

ADS_TRACKING_LIST_SCHEMA = {
    'parameters': [
            OpenApiParameter(
                name='date_after',
                type=OpenApiTypes.DATE,
                description='Filter by track start date after this date',
                required=False,
            ),
            OpenApiParameter(
                name='date_before',
                type=OpenApiTypes.DATE,
                description='Filter by track start date before this date',
                required=False,
            ),
            OpenApiParameter(
                name='only_fans_model',
                type=OpenApiTypes.INT,
                description='Filter by OnlyFansModel ID',
                required=False,
            ),
            OpenApiParameter(
                name='platform_type',
                type=OpenApiTypes.INT,
                description='Filter by PlatformType ID',
                required=False,
            ),
            OpenApiParameter(
                name='department_scope',
                type=OpenApiTypes.BOOL,
                description='List tracking for user department scope',
                required=False,
            )
        ],
    'examples': [
        OpenApiExample(
            'Example response',
            value={
                'id': '3fa85f64-5717-4562-b3fc-2c963f66afa6',
                'track_start': '2023-01-01',
                'track_end': '2023-01-31',
                'comment': 'Example comment',
                'link': 'https://example.com',
                'revenue': '123.45',
                'claims_count': 10,
                'only_fans_model_nickname': 'Model',
                'platform_type_name': 'Twitter',
            },
            response_only=True,
        )
    ],
    'responses': AdsTrackingListSerializer(many=True),
}

ADS_TRACKING_DETAIL_SCHEMA = {
    'parameters': [
        OpenApiParameter(
            name='date_after',
            type=OpenApiTypes.DATE,
            description='Filter nested daily tracking data after this date',
            required=False,
        ),
        OpenApiParameter(
            name='date_before',
            type=OpenApiTypes.DATE,
            description='Filter nested daily tracking data before this date',
            required=False,
        )
    ]
}

RESEARCH_DONOR_SCHEMA = {
    'parameters': [
        OpenApiParameter(
            name='name',
            description='Check donor exists by this name',
            required=True,
            type=OpenApiTypes.STR,
        ),
    ],
    'description': 'Endpoint for checking that donor with current name already exists',
    'summary': 'Check donor exists',
    'responses': DonorResearchSerializer(many=False)
}

ADS_CALENDAR_SCHEMA = {
    'parameters': [
        OpenApiParameter(
            name='date_after',
            description='Start date (YYYY-MM-DD)',
            required=True,
            type=OpenApiTypes.DATE,
        ),
        OpenApiParameter(
            name='date_before',
            description='End date (YYYY-MM-DD)',
            required=True,
            type=OpenApiTypes.DATE,
        ),
        OpenApiParameter(
            name='only_fans_models',
            description='Comma separated list of model identifiers',
            required=False,
            many=True,
            type=OpenApiTypes.UUID
        ),
    ],
    'responses': {status.HTTP_200_OK: AdsCalendarListSerializer}
}
