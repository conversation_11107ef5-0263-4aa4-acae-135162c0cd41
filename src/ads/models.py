from decimal import Decimal

from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.db import models
from django.db.models.functions import Lower
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from simple_history.models import HistoricalRecords

from base.models import TimeStampedUUIDModel


class PaymentMethod(TimeStampedUUIDModel):
    """
    Payment method model
    """
    name = models.CharField(
        max_length=240,
        help_text=_('Payment method name'),
        unique=True
    )

    class Meta:
        ordering = ('name',)
        constraints = [
            models.UniqueConstraint(
                Lower('name'),
                name='unique_payment_method_name',
                violation_error_message="Payment method with this name already exists."
            )
        ]

    def __str__(self):
        return self.name


class PlatformType(TimeStampedUUIDModel):
    """
    Platform type model
    """
    name = models.CharField(
        max_length=240,
        help_text=_('Platform type name'),
        unique=True
    )

    class Meta:
        ordering = ('name',)
        constraints = [
            models.UniqueConstraint(
                Lower('name'),
                name='unique_platform_type_name',
                violation_error_message="Platform type with this name already exists."
            )
        ]

    def __str__(self):
        return self.name


class Promo(TimeStampedUUIDModel):
    """
    Promo model
    """
    name = models.CharField(
        max_length=240,
        help_text=_('Promo name'),
        unique=True
    )
    switch_to_promo = models.ForeignKey(
        "self",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text=_(
            'Switch ads to this promo after 2 months from the date of the ads newdonor_is_filled_date'
        ),
        related_name='switch_from_promos'
    )
    new_donor_budget = models.BooleanField(
        default=False,
        help_text=_('Use promo for "new donor" filter budget marketing')
    )

    class Meta:
        ordering = ('name',)
        constraints = [
            models.UniqueConstraint(
                Lower('name'),
                name='unique_promo_name',
                violation_error_message="Promo with this name already exists."
            )
        ]

    def __str__(self):
        return self.name


class DonorType(TimeStampedUUIDModel):
    name = models.CharField(
        max_length=255,
        help_text=_('Donor type name'),
        unique=True
    )

    class Meta:
        ordering = ('name',)

    def __str__(self):
        return self.name


class DonorOption(TimeStampedUUIDModel):
    name = models.CharField(
        max_length=255,
        help_text=_('Donor option name'),
        unique=True
    )

    class Meta:
        ordering = ('name',)

    def __str__(self):
        return self.name


class Donor(TimeStampedUUIDModel):
    name = models.CharField(
        max_length=240,
        help_text=_('Donor name'),
        unique=True
    )
    types = models.ManyToManyField(
        DonorType,
        related_name='donors',
        help_text=_('Donor types'),
        blank=True
    )
    options = models.ManyToManyField(
        DonorOption,
        related_name='donors',
        help_text=_('Donor options'),
        blank=True
    )
    is_limited = models.BooleanField(
        default=False,
        help_text=_('Is limited')
    )
    speed_completion = models.PositiveIntegerField(
        help_text=_('Speed completion'),
        null=True,
        blank=True
    )
    secret_key = models.CharField(
        max_length=255,
        help_text=_('Secret key'),
        null=True,
        blank=True
    )

    class Meta:
        ordering = ('name',)
        constraints = [
            models.UniqueConstraint(
                Lower('name'),
                name='unique_donor_name',
                violation_error_message="Donor with this name already exists."
            )
        ]

    def __str__(self):
        return self.name


class Status(TimeStampedUUIDModel):
    name = models.CharField(
        max_length=240,
        help_text=_('Status name'),
        unique=True
    )

    class Meta:
        ordering = ('name',)
        constraints = [
            models.UniqueConstraint(
                Lower('name'),
                name='unique_status_name',
                violation_error_message="Status with this name already exists."
            )
        ]

    def __str__(self):
        return self.name


class BlackList(TimeStampedUUIDModel):
    donor = models.OneToOneField(
        Donor,
        on_delete=models.CASCADE,
        related_name='black_list',
        help_text=_('Donor')
    )
    marketer = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        related_name='black_list',
        help_text=_('Marketer')
    )
    comment = models.TextField(
        help_text=_('Comment'),
        blank=True,
        null=True
    )
    history = HistoricalRecords()

    class Meta:
        ordering = ('donor',)


class ProblemStatus(TimeStampedUUIDModel):
    """
    ProblemStatus model
    """
    name = models.CharField(
        max_length=240,
        help_text=_('Name of the problem status'),
        unique=True
    )

    class Meta:
        ordering = ('name',)

    def __str__(self):
        return self.name


class Ads(TimeStampedUUIDModel):
    """
    Ads model
    """
    class DepartmentChoices(models.TextChoices):
        MARKETING = 'marketing'
        BUSINESS_DEV = 'business_dev'

    ROLE_DEPARTMENT_MAP = {
        'hom': DepartmentChoices.MARKETING,
        'marketer': DepartmentChoices.MARKETING,
        'hobd': DepartmentChoices.BUSINESS_DEV,
        'business_dev': DepartmentChoices.BUSINESS_DEV,
        'superuser': DepartmentChoices.MARKETING,
        'financier': DepartmentChoices.MARKETING,
    }

    date = models.DateField(
        help_text=_('Date of the ads start'),
        null=True,
        blank=True
    )
    trial_link = models.URLField(
        help_text=_('Trial link from OF'),
        null=True,
        blank=True,
        unique=True
    )
    buy_date = models.DateField(
        help_text=_('Buy date'),
        null=True,
        blank=True
    )
    cost = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text=_('Cost'),
        null=True,
        blank=True
    )
    revenue = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text=_('Revenue'),
        null=True,
        blank=True
    )
    profit = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text=_('Profit'),
        null=True,
        blank=True
    )
    romi = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text=_('ROMI'),
        null=True,
        blank=True
    )
    payment = models.CharField(
        max_length=240,
        help_text=_('Payment source'),
        null=True,
        blank=True
    )
    claims_count = models.IntegerField(
        help_text=_('Claims count'),
        null=True,
        blank=True
    )
    only_fans_model = models.ForeignKey(
        'only_fans_models.OnlyFansModel',
        on_delete=models.SET_NULL,
        null=True,
        help_text=_('Model'),
        blank=False
    )
    payment_method = models.ForeignKey(
        PaymentMethod,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text=_('Payment method')
    )
    platform_type = models.ForeignKey(
        PlatformType,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text=_('Platform type')
    )
    donor = models.ForeignKey(
        Donor,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text=_('Donor')
    )
    promo = models.ForeignKey(
        Promo,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text=_('Promo')
    )
    fans_count = models.IntegerField(
        help_text=_('Fans count'),
        null=True,
        blank=True
    )
    cwvt = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text=_('Cost without VAT'),
        null=True,
        blank=True
    )
    comment = models.TextField(
        help_text=_('Comment'),
        null=True,
        blank=True
    )
    of_link = models.URLField(
        help_text=_('OF link date'),
        null=True,
        blank=True
    )
    marketer = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text=_('Marketer'),
    )
    status = models.ForeignKey(
        Status,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text=_('Status')
    )
    saved_revenue = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text=_('Revenue'),
        blank=True,
        null=True
    )
    of_link_date = models.DateField(
        help_text=_('OF link date'),
        null=True,
        blank=True
    )
    fans_delta = models.IntegerField(
        help_text=_('Fans claims count delta'),
        null=True,
        blank=True
    )
    refund_date = models.DateField(
        help_text=_('Refund date'),
        null=True,
        blank=True
    )
    refund_cost = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text=_('Refund cost'),
        null=True,
        blank=True
    )
    new_fans_count = models.IntegerField(
        help_text=_('New fans count'),
        null=True,
        blank=True
    )
    refund_payment = models.CharField(
        max_length=240,
        help_text=_('Refund payment'),
        null=True,
        blank=True
    )
    refund_date_start = models.DateField(
        help_text=_('Refund date start'),
        null=True,
        blank=True
    )
    fans_per_day = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text=_('Fans flow per day'),
        null=True,
        blank=True
    )
    days_remains = models.IntegerField(
        help_text=_('days remains based on fans flow'),
        null=True,
        blank=True
    )
    probable_end_date = models.DateField(
        help_text=_('Probable end date based on days_remains'),
        null=True,
        blank=True
    )
    cost_result = models.DecimalField(
        help_text=_('Difference between cost and refund_cost'),
        max_digits=10,
        decimal_places=2,
        null=True,
        editable=False
    )
    ads_number = models.IntegerField(
        help_text=_('Ads number'),
        editable=False,
        unique=True,
        blank=True,
        null=True
    )
    spenders = models.IntegerField(
        help_text=_('Spenders'),
        default=0,
        editable=False
    )
    spenders_low = models.IntegerField(
        help_text=_('Spenders who spend less than 500'),
        default=0,
        editable=False
    )
    spenders_high = models.IntegerField(
        help_text=_('Spenders who spend more than 500'),
        default=0,
        editable=False
    )
    initial_date = models.DateField(
        help_text=_('Date of the ads start'),
        editable=False,
        null=True
    )
    department = models.CharField(
        choices=DepartmentChoices.choices,
        max_length=240,
        help_text=_('Department'),
        default=DepartmentChoices.MARKETING
    )
    parent = models.OneToOneField(
        'self',
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        help_text=_('Parent ads'),
        related_name='child'
    )
    phantom_cost = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text=_('Phantom cost that appears in the result of the swap'),
        null=True,
        editable=False
    )
    date_end = models.DateField(
        null=True,
        blank=True,
        help_text=_('Date of the ads end')
    )
    reserve_link = models.URLField(
        null=True,
        blank=True,
        help_text=_('Link needed to subscribe on paid accounts')
    )
    friends_status = models.BooleanField(
        null=True,
        blank=True,
        help_text=_('Friends status'),
        editable=False
    )
    date_counter = models.DateField(
        null=True,
        blank=True,
        help_text=_('Date of the ads start claims count')
    )
    inner_traffic = models.BooleanField(
        default=False,
        help_text=_('Inner traffic')
    )
    inner_model = models.ForeignKey(
        'only_fans_models.OnlyFansModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text=_('Inner traffic model'),
        related_name='inner_traffic_ads'
    )
    date_counter_end = models.DateField(
        null=True,
        blank=True,
        help_text=_('Date when claims_count became gte fans_count')
    )
    days_completion = models.IntegerField(
        null=True,
        blank=True,
        help_text=_('Difference in days between date_counter and date_counter_end')
    )
    arpu = models.DecimalField(
        default=0,
        help_text=_('APRU'),
        editable=False,
        max_digits=10,
        decimal_places=2
    )
    cost_per_fan = models.DecimalField(
        max_digits=10,
        decimal_places=3,
        help_text=_('Cost per fan'),
        default=0,
        editable=False
    )
    problem_status = models.ForeignKey(
        ProblemStatus,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text=_('Problem status')
    )
    problem_comment = models.TextField(
        help_text=_('Problem comment'),
        null=True,
        blank=True
    )
    debt = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text=_('Debt'),
        editable=False,
        default=0
    )
    history = HistoricalRecords()
    calendar_start_date = models.DateField(
        null=True,
        blank=True,
        help_text=_('Calendar start date')
    )
    calendar_end_date = models.DateField(
        null=True,
        blank=True,
        help_text=_('Calendar end date')
    )
    date_counter_extra = models.DateField(
        null=True,
        blank=True,
        help_text=_('Set when claims_count > 0 when link created/updated')
    )
    new_donor_filled_date = models.DateField(
        null=True,
        blank=True,
        help_text=_('Date when new donor switched to "switch_to_promo"')
    )

    class Meta:
        ordering = ('date',)
        db_table = 'ads_list'

    @property
    def is_blocked(self) -> bool:
        return timezone.now().date() > self.date + relativedelta(months=2)

    @property
    def is_parent(self) -> bool:
        try:
            return self.child is not None
        except Ads.DoesNotExist:
            return False

    @property
    def is_child(self) -> bool:
        return self.parent is not None

    @property
    def fans_profit(self):
        return self.cost_per_fan * (self.claims_count or 0)

    def __str__(self) -> str:
        return f'{self.date} - {self.only_fans_model}'

    def get_cost_per_fan(self):
        if self.fans_count:
            if self.is_child:
                return round(
                    (self.phantom_cost or Decimal(0)) / self.fans_count, 3
                )

            return round(
                (self.cost_result or self.cost or Decimal(0)) / self.fans_count, 3
            )

        return Decimal(0)

    def get_arpu(self):
        if self.claims_count and self.revenue:
            return round(self.revenue / self.claims_count, 2)

        return 0

    def save(self, *args, **kwargs):
        if self.cost:
            self.cost_result = self.cost

            if self.refund_cost:
                self.cost_result -= self.refund_cost

        self.cost_per_fan = self.get_cost_per_fan()

        if not self.ads_number:
            last_ads_number = Ads.objects.aggregate(models.Max('ads_number'))['ads_number__max'] or 0
            self.ads_number = last_ads_number + 1

        self.inner_traffic = bool(self.inner_model)

        if not self.initial_date:
            self.initial_date = self.date

        self.arpu = self.get_arpu()

        super().save(*args, **kwargs)


class LinkLogging(TimeStampedUUIDModel):
    """
    LinkLogging model
    """
    LINK_TYPE_CHOICES = (
        ('trial', _('Trial')),
        ('campaign',  _('Campaign')),
    )

    fan_id = models.BigIntegerField(
        help_text=_('Fan id from of')
    )
    model_id = models.BigIntegerField(
        help_text=_('Model id from of')
    )
    link_url = models.URLField(
        help_text=_('Url link')
    )
    join_date = models.DateTimeField(
        help_text=_('Join date from fans_models trial_dates')
    )
    type = models.CharField(
        max_length=240,
        choices=LINK_TYPE_CHOICES,
        help_text=_('Type of link')
    )

    class Meta:
        ordering = ('join_date',)
        constraints = [
            models.UniqueConstraint(
                fields=['fan_id', 'model_id', 'join_date', 'link_url'],
                name='unique_fan_model_join_link'
            )
        ]

    def __str__(self):
        return f'{self.fan_id} - {self.model_id} - {self.join_date} - {self.link_url}'


class AdsSale(TimeStampedUUIDModel):
    """
    AdsSale model
    """
    ads = models.ForeignKey(
        Ads,
        on_delete=models.CASCADE,
        null=True,
        help_text=_('Ads from ads_list'),
        related_name='sales'

    )
    trans_id = models.CharField(max_length=64, unique=True)
    trans_date = models.DateTimeField()
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    fan_id = models.BigIntegerField(blank=True, null=True)
    model_id = models.BigIntegerField(blank=True, null=True)
    type = models.IntegerField(blank=True, null=True)

    class Meta:
        db_table = 'ads_sales'
        ordering = ('trans_date',)

    def __str__(self):
        return f'{self.ads_id} - {self.trans_date}'


# ---------------------------------------------------SMM-----------------------------------------------
class AdsTracking(TimeStampedUUIDModel):
    """
    AdsTracking model
    """
    ads = models.ForeignKey(
        Ads,
        on_delete=models.SET_NULL,
        null=True,
        help_text=_('Ads from ads_list'),
        related_name='ads_tracking'

    )
    track_start = models.DateTimeField(
        auto_now_add=True,
        help_text=_('When started tracking(created row)')
    )
    track_end = models.DateTimeField(
        null=True,
        blank=True,
        help_text=_('When finished tracking')
    )
    comment = models.TextField(
        null=True,
        blank=True,
        help_text=_('Comment')
    )
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text=_('User, who created tracking')
    )
    creator_role = models.ForeignKey(
        'accounts.UserRole',
        on_delete=models.SET_NULL,
        help_text=_('Role of the User, who created this tracking'),
        null=True
    )

    class Meta:
        ordering = ('track_start',)
        db_table = 'ads_tracking_list'

    def __str__(self):
        return f'{self.ads_id} - {self.track_end or "active"}'


class AdsTrackingDailyData(TimeStampedUUIDModel):
    ads_tracking = models.ForeignKey(
        'ads.AdsTracking',
        on_delete=models.CASCADE,
        related_name='daily_data',
        help_text=_('Stored every day data during tracking'),
    )
    date = models.DateField()
    revenue_start = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text=_('Revenue at the beginning of the day')
    )
    revenue_end = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text=_('Revenue at the end of the day'),
        blank=True,
        null=True
    )
    total_day_revenue = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text=_('Total day revenue'),
        blank=True,
        null=True
    )
    claims_count_start = models.IntegerField(
        help_text=_('Claims count at the beginning of the day')
    )
    claims_count_end = models.IntegerField(
        help_text=_('Claims count at the end of the day'),
        blank=True,
        null=True
    )
    total_day_claims_count = models.IntegerField(
        help_text=_('Total day claims'),
        blank=True,
        null=True
    )

    class Meta:
        db_table = 'ads_tracking_daily_data'
        ordering = ('date',)
        constraints = [
            models.UniqueConstraint(
                fields=['date', 'ads_tracking'],
                name='unique_ads_tracking_date'
            )
        ]

    def __str__(self):
        return f'{self.ads_tracking.id} - {self.date}'


# ---------------------------------------------------Promotions Calc-------------------------------------
class PromotionCount(TimeStampedUUIDModel):
    """
    PromotionsCount model
    """
    model_id = models.BigIntegerField(
        help_text=_('Model id from of')
    )
    promotion_name = models.CharField(
        max_length=255,
        help_text=_('Promotion name')
    )
    promotion_date = models.DateTimeField(
        help_text=_('Promotion date')
    )
    total_count = models.IntegerField(
        help_text=_('Total count of promotion')
    )
    updated_count = models.IntegerField(
        help_text=_('Total count with excluded campaign link subscriptions')
    )
    of_db_promotion_id = models.BigIntegerField()

    class Meta:
        ordering = ('promotion_date',)
        db_table = 'promotions_count'

    def __str__(self):
        return f'{self.promotion_date} - {self.of_db_promotion_id} - {self.promotion_name}'


# -----------------------------------------Ads Analytics--------------------------------------
class AdsDailyData(TimeStampedUUIDModel):
    """
    AdsDailyData model
    """
    date = models.DateField(
        db_index=True,
        help_text=_('Date of the daily data')
    )
    ads = models.ForeignKey(
        Ads,
        on_delete=models.CASCADE,
        help_text=_('Ads from ads_list'),
        related_name='daily_data'
    )
    revenue = models.DecimalField(max_digits=10, decimal_places=2)
    claims_count = models.IntegerField()

    class Meta:
        ordering = ('date',)
        db_table = 'ads_daily_data'
        constraints = [
            models.UniqueConstraint(
                fields=['date', 'ads'],
                name='unique_ads_daily_data'
            )
        ]

    def __str__(self):
        return f'{self.ads_id} - {self.date}'


class ModelAdsFansIncomeCost(TimeStampedUUIDModel):
    """
    ModelAdsFansIncomeCost model
    """
    date = models.DateField(
        help_text=_('Date of the daily data')
    )
    only_fans_model = models.ForeignKey(
        'only_fans_models.OnlyFansModel',
        on_delete=models.CASCADE,
        help_text=_('OnlyFansModel from of'),
        related_name='model_ads_fans_income'
    )
    start_fans_count = models.IntegerField(
        help_text=_('Start fans count')
    )
    end_fans_count = models.IntegerField(
        help_text=_('End fans count')
    )
    fans_income_cost = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text=_('Fans income cost'),
        default=0
    )
    avg_fans_income_cost = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text=_('Average fans income cost'),
        default=0
    )

    class Meta:
        ordering = ('date',)
        db_table = 'model_ads_fans_income_cost'
        constraints = [
            models.UniqueConstraint(
                fields=['date', 'only_fans_model'],
                name='unique_model_ads_fans_income_cost'
            )
        ]

    def __str__(self):
        return f'{self.only_fans_model_id} - {self.date}'


# ------------------------------------------Ads swap logging--------------------------------
class AdsSwapLog(TimeStampedUUIDModel):
    parent_model = models.ForeignKey(
        'only_fans_models.OnlyFansModel',
        on_delete=models.SET_NULL,
        help_text=_('Parent model'),
        related_name='parent_model_ads_swap_logs',
        null=True
    )
    child_model = models.ForeignKey(
        'only_fans_models.OnlyFansModel',
        on_delete=models.SET_NULL,
        help_text=_('Child model'),
        related_name='child_model_ads_swap_logs',
        null=True
    )
    fans_transferred = models.IntegerField(
        help_text=_('Fans transferred')
    )
    donor = models.ForeignKey(
        Donor,
        on_delete=models.SET_NULL,
        help_text=_('Donor'),
        related_name='ads_swap_logs',
        null=True
    )
    parent_ads_number = models.BigIntegerField(
        help_text=_('Parent ads number')
    )
    child_ads_number = models.BigIntegerField(
        help_text=_('Child ads number')
    )

    class Meta:
        ordering = ('-created_at',)

    def __str__(self):
        return f'{self.parent_ads_number} - {self.child_ads_number}'


# -----------------------------------------Ads draft-----------------------------------------------------
class AdsDraft(TimeStampedUUIDModel):
    class StatusChoices(models.TextChoices):
        REVIEW = 'review'
        ACCEPT = 'accept'
        DECLINE = 'decline'

    date = models.DateField()
    only_fans_model = models.ForeignKey(
        'only_fans_models.OnlyFansModel',
        on_delete=models.PROTECT,
        help_text=_('OnlyFansModel from of'),
        related_name='ads_drafts'
    )
    donor = models.ForeignKey(
        Donor,
        on_delete=models.PROTECT,
        help_text=_('Donor'),
        related_name='ads_drafts',
        null=True,
        blank=True
    )
    cost = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text=_('Cost'),
        null=True,
        blank=True
    )
    arpu = models.IntegerField(
        help_text=_('APRU'),
        null=True,
        blank=True
    )
    fans_count = models.IntegerField(
        help_text=_('Fans'),
        null=True,
        blank=True
    )
    cost_per_fan = models.DecimalField(
        max_digits=10,
        decimal_places=3,
        help_text=_('Cost per fan'),
        default=0,
        editable=False
    )
    status = models.CharField(
        max_length=240,
        help_text=_('Status'),
        choices=StatusChoices.choices,
        default=StatusChoices.REVIEW
    )
    marketer = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        help_text=_('Marketer'),
        null=True,
        related_name='ads_drafts'
    )
    reviewer = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text=_('Reviewer'),
        related_name='ads_drafts_reviewed'
    )
    review_date = models.DateTimeField(
        null=True,
        blank=True,
        help_text=_('Review date')
    )
    ads = models.OneToOneField(
        Ads,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text=_('Ads from ads_list'),
        related_name='draft'
    )
    history = HistoricalRecords()
    tg_message = models.JSONField(
        help_text=_('Tg message data'),
        null=True,
        blank=True
    )
    donor_debt = models.IntegerField(
        help_text=_('Donor debt'),
        null=True,
        blank=True
    )
    promo = models.ForeignKey(
        Promo,
        on_delete=models.SET_NULL,
        null=True,
        blank=False,
        help_text=_('Promo')
    )
    review_comment = models.TextField(
        help_text=_('Review comment'),
        null=True,
        blank=True
    )

    class Meta:
        ordering = ('-date',)

    def __str__(self):
        return f'{self.date}'

    def save(self, *args, **kwargs):
        if self.cost and self.fans_count:
            self.cost_per_fan = self.cost / self.fans_count

        return super().save(*args, **kwargs)
