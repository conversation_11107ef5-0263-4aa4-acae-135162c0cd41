# Generated by Django 4.2.2 on 2023-12-27 13:33

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("ads", "0014_linklogging_linklogging_unique_fan_model_join_link"),
    ]

    operations = [
        migrations.AddField(
            model_name="ads",
            name="new_fans_count",
            field=models.IntegerField(
                blank=True, help_text="New fans count", null=True
            ),
        ),
        migrations.AddField(
            model_name="ads",
            name="refund_cost",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                help_text="Refund cost",
                max_digits=10,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="ads",
            name="refund_date",
            field=models.DateField(blank=True, help_text="Refund date", null=True),
        ),
        migrations.AddField(
            model_name="ads",
            name="refund_payment",
            field=models.CharField(
                blank=True, help_text="Refund payment", max_length=240, null=True
            ),
        ),
    ]
