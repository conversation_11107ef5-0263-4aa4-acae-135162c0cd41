# Generated by Django 4.2.2 on 2025-01-17 17:11
from decimal import Decimal

from django.db import migrations


def get_cost_per_fan(self):
    if self.fans_count:
        return round(
            (self.cost_result or self.cost or Decimal(0)) / self.fans_count, 1
        )

    return Decimal(0)


def update_cost_per_fan_for_existing_ads(apps, schema_editor):
    """
    Save every ads.
    """
    Ads = apps.get_model('ads', 'Ads')

    ads_for_update = []
    for ads in Ads.objects.all():
        new_cost_per_fan = get_cost_per_fan(ads)

        if new_cost_per_fan !=  ads.cost_per_fan:
            ads.cost_per_fan = new_cost_per_fan
            ads_for_update.append(ads)

    Ads.objects.bulk_update(ads_for_update, ['cost_per_fan'],  batch_size=500)


class Migration(migrations.Migration):

    dependencies = [
        ("ads", "0034_ads_apru_ads_cost_per_fan"),
    ]

    operations = [
        migrations.RunPython(update_cost_per_fan_for_existing_ads, migrations.RunPython.noop)
    ]
