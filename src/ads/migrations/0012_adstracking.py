# Generated by Django 4.2.2 on 2023-12-11 11:45

import uuid

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models

import base.models


class Migration(migrations.Migration):

    dependencies = [
        ("ads", "0011_ads_fans_delta_ads_of_link_date"),
    ]

    operations = [
        migrations.CreateModel(
            name="AdsTracking",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "track_start",
                    models.DateTimeField(
                        auto_now_add=True,
                        help_text="When started tracking(created row)",
                    ),
                ),
                (
                    "track_end",
                    models.DateTimeField(
                        blank=True, help_text="When finished tracking", null=True
                    ),
                ),
                (
                    "comment",
                    models.TextField(blank=True, help_text="Comment", null=True),
                ),
                (
                    "ads",
                    models.OneToOneField(
                        help_text="Ads from ads_list",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="ads_tracking",
                        to="ads.ads",
                    ),
                ),
            ],
            options={
                "db_table": "ads_tracking_list",
                "ordering": ("track_start",),
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
    ]
