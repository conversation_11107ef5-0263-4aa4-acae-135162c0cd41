# Generated by Django 4.2.2 on 2024-08-26 19:04

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("ads", "0028_ads_parent_ads_phantom_cost"),
    ]

    operations = [
        migrations.AddField(
            model_name="ads",
            name="date_end",
            field=models.DateField(
                blank=True, help_text="Date of the ads end", null=True
            ),
        ),
        migrations.AddField(
            model_name="ads",
            name="friends_status",
            field=models.BooleanField(
                blank=True, editable=False, help_text="Friends status", null=True
            ),
        ),
        migrations.AddField(
            model_name="ads",
            name="reserve_link",
            field=models.URLField(
                blank=True,
                help_text="Link needed to subscribe on paid accounts",
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="ads",
            name="parent",
            field=models.OneToOneField(
                blank=True,
                help_text="Parent ads",
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="child",
                to="ads.ads",
            ),
        ),
    ]
