# Generated by Django 4.2.2 on 2025-07-10 16:53

import base.models
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import simple_history.models
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("only_fans_models", "0013_historicalonlyfansmodel"),
        ("ads", "0051_remove_ads_unique_ads_date_only_fans_model_department"),
    ]

    operations = [
        migrations.CreateModel(
            name="HistoricalAdsDraft",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(blank=True, editable=False, null=True),
                ),
                (
                    "id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, editable=False),
                ),
                ("date", models.DateField()),
                (
                    "cost",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Cost",
                        max_digits=10,
                        null=True,
                    ),
                ),
                ("apru", models.IntegerField(blank=True, help_text="APRU", null=True)),
                ("fans", models.IntegerField(blank=True, help_text="Fans", null=True)),
                (
                    "cost_per_fan",
                    models.DecimalField(
                        decimal_places=3,
                        default=0,
                        editable=False,
                        help_text="Cost per fan",
                        max_digits=10,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("review", "Review"),
                            ("accept", "Accept"),
                            ("decline", "Decline"),
                        ],
                        default="review",
                        help_text="Status",
                        max_length=240,
                    ),
                ),
                (
                    "review_date",
                    models.DateTimeField(
                        blank=True, help_text="Review date", null=True
                    ),
                ),
                ("history_id", models.AutoField(primary_key=True, serialize=False)),
                ("history_date", models.DateTimeField(db_index=True)),
                ("history_change_reason", models.CharField(max_length=100, null=True)),
                (
                    "history_type",
                    models.CharField(
                        choices=[("+", "Created"), ("~", "Changed"), ("-", "Deleted")],
                        max_length=1,
                    ),
                ),
                (
                    "ads",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        help_text="Ads from ads_list",
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="ads.ads",
                    ),
                ),
                (
                    "donor",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        help_text="Donor",
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="ads.donor",
                    ),
                ),
                (
                    "history_user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "marketer",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        help_text="Marketer",
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "only_fans_model",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        help_text="OnlyFansModel from of",
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="only_fans_models.onlyfansmodel",
                    ),
                ),
                (
                    "reviewer",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        help_text="Reviewer",
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "historical ads draft",
                "verbose_name_plural": "historical ads drafts",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name="AdsDraft",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("date", models.DateField()),
                (
                    "cost",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Cost",
                        max_digits=10,
                        null=True,
                    ),
                ),
                ("apru", models.IntegerField(blank=True, help_text="APRU", null=True)),
                ("fans", models.IntegerField(blank=True, help_text="Fans", null=True)),
                (
                    "cost_per_fan",
                    models.DecimalField(
                        decimal_places=3,
                        default=0,
                        editable=False,
                        help_text="Cost per fan",
                        max_digits=10,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("review", "Review"),
                            ("accept", "Accept"),
                            ("decline", "Decline"),
                        ],
                        default="review",
                        help_text="Status",
                        max_length=240,
                    ),
                ),
                (
                    "review_date",
                    models.DateTimeField(
                        blank=True, help_text="Review date", null=True
                    ),
                ),
                (
                    "ads",
                    models.OneToOneField(
                        blank=True,
                        help_text="Ads from ads_list",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="draft",
                        to="ads.ads",
                    ),
                ),
                (
                    "donor",
                    models.ForeignKey(
                        blank=True,
                        help_text="Donor",
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="ads_drafts",
                        to="ads.donor",
                    ),
                ),
                (
                    "marketer",
                    models.ForeignKey(
                        help_text="Marketer",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="ads_drafts",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "only_fans_model",
                    models.ForeignKey(
                        help_text="OnlyFansModel from of",
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="ads_drafts",
                        to="only_fans_models.onlyfansmodel",
                    ),
                ),
                (
                    "reviewer",
                    models.ForeignKey(
                        blank=True,
                        help_text="Reviewer",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="ads_drafts_reviewed",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ("-date",),
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
    ]
