# Generated by Django 4.2.2 on 2024-02-14 12:57

import uuid

import django.utils.timezone
from django.db import migrations, models

import base.models


class Migration(migrations.Migration):

    dependencies = [
        ("ads", "0019_ads_cost_result"),
    ]

    operations = [
        migrations.CreateModel(
            name="PromotionCount",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("model_id", models.BigIntegerField(help_text="Model id from of")),
                (
                    "promotion_name",
                    models.CharField(help_text="Promotion name", max_length=255),
                ),
                ("promotion_date", models.DateTimeField(help_text="Promotion date")),
                (
                    "total_count",
                    models.IntegerField(help_text="Total count of promotion"),
                ),
                (
                    "updated_count",
                    models.Integer<PERSON>ield(
                        help_text="Total count with excluded campaign link subscriptions"
                    ),
                ),
                ("of_db_promotion_id", models.BigIntegerField()),
            ],
            options={
                "db_table": "promotions_count",
                "ordering": ("promotion_date",),
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
    ]
