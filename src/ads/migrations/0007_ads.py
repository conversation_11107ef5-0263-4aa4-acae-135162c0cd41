# Generated by Django 4.2.2 on 2023-09-13 14:39

import uuid

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models

import base.models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("only_fans_models", "0007_onlyfansmodel_marketer"),
        ("ads", "0006_blacklist"),
    ]

    operations = [
        migrations.CreateModel(
            name="Ads",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "date",
                    models.DateField(
                        blank=True, help_text="Date of the ads start", null=True
                    ),
                ),
                (
                    "trial_link",
                    models.URLField(
                        blank=True, help_text="Trial link from OF", null=True
                    ),
                ),
                (
                    "buy_date",
                    models.DateField(blank=True, help_text="Buy date", null=True),
                ),
                (
                    "cost",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Cost",
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "revenue",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Revenue",
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "profit",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Profit",
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "romi",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="ROMI",
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "payment",
                    models.CharField(
                        blank=True,
                        help_text="Payment source",
                        max_length=240,
                        null=True,
                    ),
                ),
                (
                    "claims_count",
                    models.IntegerField(
                        blank=True, help_text="Claims count", null=True
                    ),
                ),
                (
                    "fans_count",
                    models.IntegerField(blank=True, help_text="Fans count", null=True),
                ),
                (
                    "cwvt",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Cost without VAT",
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "comment",
                    models.TextField(blank=True, help_text="Comment", null=True),
                ),
                (
                    "of_link",
                    models.DateField(blank=True, help_text="OF link date", null=True),
                ),
                (
                    "donor",
                    models.ForeignKey(
                        blank=True,
                        help_text="Donor",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="ads.donor",
                    ),
                ),
                (
                    "marketer",
                    models.ForeignKey(
                        blank=True,
                        help_text="Marketer",
                        limit_choices_to={"role__name": "marketer"},
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "only_fans_model",
                    models.ForeignKey(
                        help_text="Model",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="only_fans_models.onlyfansmodel",
                    ),
                ),
                (
                    "payment_method",
                    models.ForeignKey(
                        blank=True,
                        help_text="Payment method",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="ads.paymentmethod",
                    ),
                ),
                (
                    "platform_type",
                    models.ForeignKey(
                        blank=True,
                        help_text="Platform type",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="ads.platformtype",
                    ),
                ),
                (
                    "promo",
                    models.ForeignKey(
                        blank=True,
                        help_text="Promo",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="ads.promo",
                    ),
                ),
                (
                    "status",
                    models.ForeignKey(
                        blank=True,
                        help_text="Status",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="ads.status",
                    ),
                ),
            ],
            options={
                "db_table": "ads_list",
                "ordering": ("date",),
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
    ]
