# Generated by Django 4.2.2 on 2023-12-26 11:30

import uuid

import django.utils.timezone
from django.db import migrations, models

import base.models


class Migration(migrations.Migration):

    dependencies = [
        ("ads", "0013_adstrackingdailydata_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="LinkLogging",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("fan_id", models.BigIntegerField(help_text="Fan id from of")),
                ("model_id", models.BigIntegerField(help_text="Model id from of")),
                ("link_url", models.URLField(help_text="Url link")),
                (
                    "join_date",
                    models.DateTimeField(
                        help_text="Join date from fans_models trial_dates"
                    ),
                ),
                (
                    "type",
                    models.CharField(
                        choices=[("trial", "Trial"), ("campaign", "Campaign")],
                        help_text="Type of link",
                        max_length=240,
                    ),
                ),
            ],
            options={
                "ordering": ("join_date",),
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.AddConstraint(
            model_name="linklogging",
            constraint=models.UniqueConstraint(
                fields=("fan_id", "model_id", "join_date", "link_url"),
                name="unique_fan_model_join_link",
            ),
        ),
    ]
