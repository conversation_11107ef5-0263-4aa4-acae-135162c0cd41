# Generated by Django 4.2.2 on 2023-09-12 12:51

import uuid

import django.db.models.functions.text
import django.utils.timezone
from django.db import migrations, models

import base.models


class Migration(migrations.Migration):

    dependencies = [
        ("ads", "0004_donor_donor_unique_donor_name"),
    ]

    operations = [
        migrations.CreateModel(
            name="Status",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Status name", max_length=240, unique=True
                    ),
                ),
            ],
            options={
                "ordering": ("name",),
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.AddConstraint(
            model_name="status",
            constraint=models.UniqueConstraint(
                django.db.models.functions.text.Lower("name"),
                name="unique_status_name",
                violation_error_message="Status with this name already exists.",
            ),
        ),
    ]
