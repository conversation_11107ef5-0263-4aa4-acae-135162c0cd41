# Generated by Django 4.2.2 on 2025-01-13 15:02

import base.models
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("ads", "0031_ads_inner_model_ads_inner_traffic"),
    ]

    operations = [
        migrations.CreateModel(
            name="DonorOption",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Donor option name", max_length=255, unique=True
                    ),
                ),
            ],
            options={
                "ordering": ("name",),
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.CreateModel(
            name="DonorType",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Donor type name", max_length=255, unique=True
                    ),
                ),
            ],
            options={
                "ordering": ("name",),
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.AddField(
            model_name="donor",
            name="is_limited",
            field=models.BooleanField(default=False, help_text="Is limited"),
        ),
        migrations.AddField(
            model_name="donor",
            name="options",
            field=models.ManyToManyField(
                blank=True,
                help_text="Donor options",
                related_name="donors",
                to="ads.donoroption",
            ),
        ),
        migrations.AddField(
            model_name="donor",
            name="type",
            field=models.ForeignKey(
                help_text="Donor type",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="donors",
                to="ads.donortype",
            ),
        ),
    ]
