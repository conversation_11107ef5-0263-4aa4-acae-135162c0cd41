# Generated by Django 4.2.2 on 2024-01-17 16:25

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("accounts", "0007_usersmmproxy"),
        ("ads", "0016_ads_days_remains_ads_fans_per_day_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="adstracking",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                help_text="User, who created tracking",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="adstracking",
            name="creator_role",
            field=models.ForeignKey(
                help_text="Role of the User, who created this tracking",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="accounts.userrole",
            ),
        ),
    ]
