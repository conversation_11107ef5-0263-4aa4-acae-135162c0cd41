# Generated by Django 4.2.2 on 2025-02-27 16:22

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import simple_history.models
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("only_fans_models", "0012_subprofile"),
        ("ads", "0043_calculate_debt_and_cost_per_fan"),
    ]

    operations = [
        migrations.CreateModel(
            name="HistoricalAds",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(blank=True, editable=False, null=True),
                ),
                (
                    "id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, editable=False),
                ),
                (
                    "date",
                    models.DateField(
                        blank=True, help_text="Date of the ads start", null=True
                    ),
                ),
                (
                    "trial_link",
                    models.URLField(
                        blank=True,
                        db_index=True,
                        help_text="Trial link from OF",
                        null=True,
                    ),
                ),
                (
                    "buy_date",
                    models.DateField(blank=True, help_text="Buy date", null=True),
                ),
                (
                    "cost",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Cost",
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "revenue",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Revenue",
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "profit",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Profit",
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "romi",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="ROMI",
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "payment",
                    models.CharField(
                        blank=True,
                        help_text="Payment source",
                        max_length=240,
                        null=True,
                    ),
                ),
                (
                    "claims_count",
                    models.IntegerField(
                        blank=True, help_text="Claims count", null=True
                    ),
                ),
                (
                    "fans_count",
                    models.IntegerField(blank=True, help_text="Fans count", null=True),
                ),
                (
                    "cwvt",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Cost without VAT",
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "comment",
                    models.TextField(blank=True, help_text="Comment", null=True),
                ),
                (
                    "of_link",
                    models.URLField(blank=True, help_text="OF link date", null=True),
                ),
                (
                    "saved_revenue",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Revenue",
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "of_link_date",
                    models.DateField(blank=True, help_text="OF link date", null=True),
                ),
                (
                    "fans_delta",
                    models.IntegerField(
                        blank=True, help_text="Fans claims count delta", null=True
                    ),
                ),
                (
                    "refund_date",
                    models.DateField(blank=True, help_text="Refund date", null=True),
                ),
                (
                    "refund_cost",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Refund cost",
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "new_fans_count",
                    models.IntegerField(
                        blank=True, help_text="New fans count", null=True
                    ),
                ),
                (
                    "refund_payment",
                    models.CharField(
                        blank=True,
                        help_text="Refund payment",
                        max_length=240,
                        null=True,
                    ),
                ),
                (
                    "refund_date_start",
                    models.DateField(
                        blank=True, help_text="Refund date start", null=True
                    ),
                ),
                (
                    "fans_per_day",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Fans flow per day",
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "days_remains",
                    models.IntegerField(
                        blank=True,
                        help_text="days remains based on fans flow",
                        null=True,
                    ),
                ),
                (
                    "probable_end_date",
                    models.DateField(
                        blank=True,
                        help_text="Probable end date based on days_remains",
                        null=True,
                    ),
                ),
                (
                    "cost_result",
                    models.DecimalField(
                        decimal_places=2,
                        editable=False,
                        help_text="Difference between cost and refund_cost",
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "ads_number",
                    models.IntegerField(
                        blank=True,
                        db_index=True,
                        editable=False,
                        help_text="Ads number",
                        null=True,
                    ),
                ),
                (
                    "spenders",
                    models.IntegerField(
                        default=0, editable=False, help_text="Spenders"
                    ),
                ),
                (
                    "spenders_low",
                    models.IntegerField(
                        default=0,
                        editable=False,
                        help_text="Spenders who spend less than 500",
                    ),
                ),
                (
                    "spenders_high",
                    models.IntegerField(
                        default=0,
                        editable=False,
                        help_text="Spenders who spend more than 500",
                    ),
                ),
                (
                    "initial_date",
                    models.DateField(
                        editable=False, help_text="Date of the ads start", null=True
                    ),
                ),
                (
                    "department",
                    models.CharField(
                        choices=[
                            ("marketing", "Marketing"),
                            ("business_dev", "Business Dev"),
                        ],
                        default="marketing",
                        help_text="Department",
                        max_length=240,
                    ),
                ),
                (
                    "phantom_cost",
                    models.DecimalField(
                        decimal_places=2,
                        editable=False,
                        help_text="Phantom cost that appears in the result of the swap",
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "date_end",
                    models.DateField(
                        blank=True, help_text="Date of the ads end", null=True
                    ),
                ),
                (
                    "reserve_link",
                    models.URLField(
                        blank=True,
                        help_text="Link needed to subscribe on paid accounts",
                        null=True,
                    ),
                ),
                (
                    "friends_status",
                    models.BooleanField(
                        blank=True,
                        editable=False,
                        help_text="Friends status",
                        null=True,
                    ),
                ),
                (
                    "date_counter",
                    models.DateField(
                        blank=True,
                        help_text="Date of the ads start claims count",
                        null=True,
                    ),
                ),
                (
                    "inner_traffic",
                    models.BooleanField(default=False, help_text="Inner traffic"),
                ),
                (
                    "date_counter_end",
                    models.DateField(
                        blank=True,
                        help_text="Date when claims_count became gte fans_count",
                        null=True,
                    ),
                ),
                (
                    "days_completion",
                    models.IntegerField(
                        blank=True,
                        help_text="Difference in days between date_counter and date_counter_end",
                        null=True,
                    ),
                ),
                ("apru", models.IntegerField(blank=True, help_text="APRU", null=True)),
                (
                    "cost_per_fan",
                    models.DecimalField(
                        decimal_places=3,
                        default=0,
                        editable=False,
                        help_text="Cost per fan",
                        max_digits=10,
                    ),
                ),
                (
                    "problem_comment",
                    models.TextField(
                        blank=True, help_text="Problem comment", null=True
                    ),
                ),
                (
                    "debt",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        editable=False,
                        help_text="Debt",
                        max_digits=10,
                    ),
                ),
                ("history_id", models.AutoField(primary_key=True, serialize=False)),
                ("history_date", models.DateTimeField(db_index=True)),
                ("history_change_reason", models.CharField(max_length=100, null=True)),
                (
                    "history_type",
                    models.CharField(
                        choices=[("+", "Created"), ("~", "Changed"), ("-", "Deleted")],
                        max_length=1,
                    ),
                ),
                (
                    "donor",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        help_text="Donor",
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="ads.donor",
                    ),
                ),
                (
                    "history_user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "inner_model",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        help_text="Inner traffic model",
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="only_fans_models.onlyfansmodel",
                    ),
                ),
                (
                    "marketer",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        help_text="Marketer",
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "only_fans_model",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        help_text="Model",
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="only_fans_models.onlyfansmodel",
                    ),
                ),
                (
                    "parent",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        help_text="Parent ads",
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="ads.ads",
                    ),
                ),
                (
                    "payment_method",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        help_text="Payment method",
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="ads.paymentmethod",
                    ),
                ),
                (
                    "platform_type",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        help_text="Platform type",
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="ads.platformtype",
                    ),
                ),
                (
                    "problem_status",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        help_text="Problem status",
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="ads.problemstatus",
                    ),
                ),
                (
                    "promo",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        help_text="Promo",
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="ads.promo",
                    ),
                ),
                (
                    "status",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        help_text="Status",
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="ads.status",
                    ),
                ),
            ],
            options={
                "verbose_name": "historical ads",
                "verbose_name_plural": "historical adss",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
    ]
