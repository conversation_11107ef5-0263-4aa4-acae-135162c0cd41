# Generated by Django 4.2.2 on 2025-02-10 13:39

import base.models
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("ads", "0036_modeladsfansincomecost_adsdailydata_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="ProblemStatus",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Name of the problem status",
                        max_length=240,
                        unique=True,
                    ),
                ),
            ],
            options={
                "ordering": ("name",),
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.AddField(
            model_name="ads",
            name="problem_comment",
            field=models.TextField(blank=True, help_text="Problem comment", null=True),
        ),
        migrations.AddField(
            model_name="ads",
            name="refund_date_start",
            field=models.DateField(
                blank=True, help_text="Refund date start", null=True
            ),
        ),
        migrations.AddField(
            model_name="ads",
            name="problem_status",
            field=models.ForeignKey(
                blank=True,
                help_text="Problem status",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="ads.problemstatus",
            ),
        ),
    ]
