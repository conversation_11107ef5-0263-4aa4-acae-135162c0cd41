# Generated by Django 4.2.2 on 2025-02-27 13:14
from decimal import Decimal

from django.db import migrations


def get_cost_per_fan(ads):
    if ads.fans_count:
        if ads.parent_id:
            return round(
                (ads.phantom_cost or Decimal(0)) / ads.fans_count, 3
            )

        return round(
            (ads.cost_result or ads.cost or Decimal(0)) / ads.fans_count, 3
        )

    return Decimal(0)


def update_cost_per_fan_and_debt_for_existing_ads(apps, schema_editor):
    Ads = apps.get_model('ads', 'Ads')

    ads_for_update = []
    for ads in Ads.objects.all():
        ads.cost_per_fan = get_cost_per_fan(ads)
        ads.debt = ads.fans_delta or 0 * ads.cost_per_fan
        ads_for_update.append(ads)

    Ads.objects.bulk_update(ads_for_update, ['cost_per_fan', 'debt'],  batch_size=500)


class Migration(migrations.Migration):

    dependencies = [
        ("ads", "0042_alter_ads_cost_per_fan"),
    ]
    operations = [
        migrations.RunPython(update_cost_per_fan_and_debt_for_existing_ads, migrations.RunPython.noop)
    ]

