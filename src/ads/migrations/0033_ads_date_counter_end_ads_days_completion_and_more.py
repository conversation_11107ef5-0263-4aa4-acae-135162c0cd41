# Generated by Django 4.2.2 on 2025-01-16 19:00

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("ads", "0032_donoroption_donortype_donor_is_limited_donor_options_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="ads",
            name="date_counter_end",
            field=models.DateField(
                blank=True,
                help_text="Date when claims_count became gte fans_count",
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="ads",
            name="days_completion",
            field=models.IntegerField(
                blank=True,
                help_text="Difference in days between date_counter and date_counter_end",
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="donor",
            name="speed_completion",
            field=models.PositiveIntegerField(
                blank=True, help_text="Speed completion", null=True
            ),
        ),
    ]
