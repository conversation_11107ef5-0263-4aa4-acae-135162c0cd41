# Generated by Django 4.2.2 on 2024-01-11 12:09

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("ads", "0015_ads_new_fans_count_ads_refund_cost_ads_refund_date_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="ads",
            name="days_remains",
            field=models.IntegerField(
                blank=True, help_text="days remains based on fans flow", null=True
            ),
        ),
        migrations.AddField(
            model_name="ads",
            name="fans_per_day",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                help_text="Fans flow per day",
                max_digits=10,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="ads",
            name="probable_end_date",
            field=models.DateField(
                blank=True,
                help_text="Probable end date based on days_remains",
                null=True,
            ),
        ),
    ]
