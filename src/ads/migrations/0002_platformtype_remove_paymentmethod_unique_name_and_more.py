# Generated by Django 4.2.2 on 2023-09-11 15:31

import uuid

import django.db.models.functions.text
import django.utils.timezone
from django.db import migrations, models

import base.models


class Migration(migrations.Migration):

    dependencies = [
        ("ads", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="PlatformType",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Platform type name", max_length=240, unique=True
                    ),
                ),
            ],
            options={
                "ordering": ("name",),
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.RemoveConstraint(
            model_name="paymentmethod",
            name="unique_name",
        ),
        migrations.AddConstraint(
            model_name="paymentmethod",
            constraint=models.UniqueConstraint(
                django.db.models.functions.text.Lower("name"),
                name="unique_payment_method_name",
                violation_error_message="Payment method with this name already exists.",
            ),
        ),
        migrations.AddConstraint(
            model_name="platformtype",
            constraint=models.UniqueConstraint(
                django.db.models.functions.text.Lower("name"),
                name="unique_platform_type_name",
                violation_error_message="Platform type with this name already exists.",
            ),
        ),
    ]
