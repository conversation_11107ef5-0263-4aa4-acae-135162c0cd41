# Generated by Django 4.2.2 on 2025-07-17 18:41

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("ads", "0055_remove_adsdraft_tg_message_id_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="adsdraft",
            name="donor_debt",
            field=models.IntegerField(blank=True, help_text="Donor debt", null=True),
        ),
        migrations.AddField(
            model_name="adsdraft",
            name="promo",
            field=models.ForeignKey(
                help_text="Promo",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="ads.promo",
            ),
        ),
        migrations.AddField(
            model_name="historicaladsdraft",
            name="donor_debt",
            field=models.IntegerField(blank=True, help_text="Donor debt", null=True),
        ),
        migrations.AddField(
            model_name="historicaladsdraft",
            name="promo",
            field=models.ForeignKey(
                blank=True,
                db_constraint=False,
                help_text="Promo",
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="+",
                to="ads.promo",
            ),
        ),
    ]
