# Generated by Django 4.2.2 on 2025-01-28 18:52

import base.models
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("only_fans_models", "0012_subprofile"),
        ("ads", "0035_set_cost_per_fan_for_existing_ads"),
    ]

    operations = [
        migrations.CreateModel(
            name="ModelAdsFansIncomeCost",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("date", models.DateField(help_text="Date of the daily data")),
                ("start_fans_count", models.IntegerField(help_text="Start fans count")),
                ("end_fans_count", models.IntegerField(help_text="End fans count")),
                (
                    "fans_income_cost",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Fans income cost",
                        max_digits=10,
                    ),
                ),
                (
                    "avg_fans_income_cost",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Average fans income cost",
                        max_digits=10,
                    ),
                ),
                (
                    "only_fans_model",
                    models.ForeignKey(
                        help_text="OnlyFansModel from of",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="model_ads_fans_income",
                        to="only_fans_models.onlyfansmodel",
                    ),
                ),
            ],
            options={
                "db_table": "model_ads_fans_income_cost",
                "ordering": ("date",),
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.CreateModel(
            name="AdsDailyData",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "date",
                    models.DateField(db_index=True, help_text="Date of the daily data"),
                ),
                ("revenue", models.DecimalField(decimal_places=2, max_digits=10)),
                ("claims_count", models.IntegerField()),
                (
                    "ads",
                    models.ForeignKey(
                        help_text="Ads from ads_list",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="daily_data",
                        to="ads.ads",
                    ),
                ),
            ],
            options={
                "db_table": "ads_daily_data",
                "ordering": ("date",),
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.AddConstraint(
            model_name="modeladsfansincomecost",
            constraint=models.UniqueConstraint(
                fields=("date", "only_fans_model"),
                name="unique_model_ads_fans_income_cost",
            ),
        ),
        migrations.AddConstraint(
            model_name="adsdailydata",
            constraint=models.UniqueConstraint(
                fields=("date", "ads"), name="unique_ads_daily_data"
            ),
        ),
    ]
