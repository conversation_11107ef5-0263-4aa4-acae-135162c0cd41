# Generated by Django 4.2.2 on 2025-04-29 15:00

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("ads", "0046_donor_secret_key"),
    ]

    operations = [
        migrations.AddField(
            model_name="ads",
            name="date_counter_extra",
            field=models.DateField(
                blank=True,
                help_text="Set when claims_count > 0 when link created/updated",
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="historicalads",
            name="date_counter_extra",
            field=models.DateField(
                blank=True,
                help_text="Set when claims_count > 0 when link created/updated",
                null=True,
            ),
        ),
    ]
