# Generated by Django 4.2.2 on 2023-09-12 19:00

import uuid

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models

import base.models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("ads", "0005_status_status_unique_status_name"),
    ]

    operations = [
        migrations.CreateModel(
            name="BlackList",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "comment",
                    models.TextField(blank=True, help_text="Comment", null=True),
                ),
                (
                    "donor",
                    models.OneToOneField(
                        help_text="Donor",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="black_list",
                        to="ads.donor",
                    ),
                ),
                (
                    "marketer",
                    models.ForeignKey(
                        help_text="Marketer",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="black_list",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ("donor",),
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
    ]
