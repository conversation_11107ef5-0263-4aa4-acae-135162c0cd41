# Generated by Django 4.2.2 on 2023-12-11 18:35

import uuid

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models

import base.models


class Migration(migrations.Migration):

    dependencies = [
        ("ads", "0012_adstracking"),
    ]

    operations = [
        migrations.CreateModel(
            name="AdsTrackingDailyData",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("date", models.DateField()),
                (
                    "revenue_start",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Revenue at the beginning of the day",
                        max_digits=10,
                    ),
                ),
                (
                    "revenue_end",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Revenue at the end of the day",
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "total_day_revenue",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Total day revenue",
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "claims_count_start",
                    models.IntegerField(
                        help_text="Claims count at the beginning of the day"
                    ),
                ),
                (
                    "claims_count_end",
                    models.IntegerField(
                        blank=True,
                        help_text="Claims count at the end of the day",
                        null=True,
                    ),
                ),
                (
                    "total_day_claims_count",
                    models.IntegerField(
                        blank=True, help_text="Total day claims", null=True
                    ),
                ),
                (
                    "ads_tracking",
                    models.ForeignKey(
                        help_text="Stored every day data during tracking",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="daily_data",
                        to="ads.adstracking",
                    ),
                ),
            ],
            options={
                "db_table": "ads_tracking_daily_data",
                "ordering": ("date",),
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.AddConstraint(
            model_name="adstrackingdailydata",
            constraint=models.UniqueConstraint(
                fields=("date", "ads_tracking"), name="unique_ads_tracking_date"
            ),
        ),
    ]
