# Generated by Django 4.2.2 on 2024-05-07 15:32

import uuid

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models

import base.models


class Migration(migrations.Migration):

    dependencies = [
        ("ads", "0022_alter_ads_marketer"),
    ]

    operations = [
        migrations.AddField(
            model_name="ads",
            name="spenders",
            field=models.IntegerField(default=0, editable=False, help_text="Spenders"),
        ),
        migrations.AddField(
            model_name="ads",
            name="spenders_high",
            field=models.IntegerField(
                default=0, editable=False, help_text="Spenders who spend more than 500"
            ),
        ),
        migrations.AddField(
            model_name="ads",
            name="spenders_low",
            field=models.IntegerField(
                default=0, editable=False, help_text="Spenders who spend less than 500"
            ),
        ),
        migrations.CreateModel(
            name="AdsSale",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("trans_id", models.CharField(max_length=64, unique=True)),
                ("trans_date", models.DateTimeField()),
                ("amount", models.DecimalField(decimal_places=2, max_digits=10)),
                ("fan_id", models.BigIntegerField(blank=True, null=True)),
                ("model_id", models.BigIntegerField(blank=True, null=True)),
                ("type", models.IntegerField(blank=True, null=True)),
                (
                    "ads",
                    models.ForeignKey(
                        help_text="Ads from ads_list",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="sales",
                        to="ads.ads",
                    ),
                ),
            ],
            options={
                "db_table": "ads_sales",
                "ordering": ("trans_date",),
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
    ]
