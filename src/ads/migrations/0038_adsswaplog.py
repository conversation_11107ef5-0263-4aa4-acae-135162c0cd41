# Generated by Django 4.2.2 on 2025-02-11 13:46

import base.models
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("only_fans_models", "0012_subprofile"),
        ("ads", "0037_problemstatus_ads_problem_comment_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="AdsSwapLog",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("fans_transferred", models.IntegerField(help_text="Fans transferred")),
                (
                    "parent_ads_number",
                    models.BigIntegerField(help_text="Parent ads number"),
                ),
                (
                    "child_ads_number",
                    models.BigIntegerField(help_text="Child ads number"),
                ),
                (
                    "child_model",
                    models.ForeignKey(
                        help_text="Child model",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="child_model_ads_swap_logs",
                        to="only_fans_models.onlyfansmodel",
                    ),
                ),
                (
                    "donor",
                    models.ForeignKey(
                        help_text="Donor",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="ads_swap_logs",
                        to="only_fans_models.onlyfansmodel",
                    ),
                ),
                (
                    "parent_model",
                    models.ForeignKey(
                        help_text="Parent model",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="parent_model_ads_swap_logs",
                        to="only_fans_models.onlyfansmodel",
                    ),
                ),
            ],
            options={
                "ordering": ("-created_at",),
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
    ]
