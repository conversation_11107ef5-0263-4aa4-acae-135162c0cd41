from datetime import date

from dateutil.relativedelta import relativedelta
from django.contrib.auth import get_user_model
from django.utils import timezone
from rest_framework import serializers

from ads.models import Ads

User = get_user_model()


class AdsValidator:
    """
    Validator for ads
    """
    DATE_MONTH_LIMIT = 2
    DATE_NOT_TERMINATED_LIMIT = 30
    DATE_FROM_DONOR_PLATFORM_PROMO_LIMITATION = date(2025, 8, 1)

    @staticmethod
    def check_date_not_terminated(value: date, days_limit: int) -> bool:
        """
        Check if date is not terminated by days_limit
        """
        today = timezone.now().date()
        return value >= today - relativedelta(days=days_limit)

    @staticmethod
    def validate_restricted_fields(instance: Ads, data: dict) -> None:
        """
        Validate that restricted fields are not modified when saved_revenue is not zero.
        """
        if instance.saved_revenue:
            restricted_fields = ('trial_link', 'only_fans_model', 'date')

            for field in restricted_fields:
                instance_field_data = getattr(instance, field, None)
                field_data = data.get(field)

                if field not in data or not instance_field_data:
                    continue

                if field == 'trial_link':
                    field_data = field_data.strip()
                    instance_field_data = instance_field_data.strip()

                if instance_field_data != field_data:
                    raise serializers.ValidationError(
                        {
                            field: f"{field} is a read-only field for campaign with saved revenue."
                        }
                    )

    @staticmethod
    def validate_blocked_ad_fields(instance: Ads, data: dict) -> None:
        """
        Validate fields that can be updated when the ad is blocked.
        """
        if instance.is_blocked:
            fields_could_be_updated = (
                'comment',
                'refund_date',
                'refund_cost',
                'refund_payment',
                'marketer',
                'status',
                'new_fans_count',
                'donor',
                'inner_model',
                'refund_date_start',
                'problem_status',
                'problem_comment',
            )
            for key, value in data.items():
                if key not in fields_could_be_updated and value != getattr(instance, key):
                    raise serializers.ValidationError(
                        {key: f"Field {key} could not be updated if the ad is blocked."}
                    )

    @staticmethod
    def validate_date(instance: Ads, data: dict) -> None:
        """
        Validate the 'date' field.
        """
        if 'date' in data and data['date'] < instance.date - relativedelta(months=AdsValidator.DATE_MONTH_LIMIT):
            raise serializers.ValidationError(
                {'date': "Date should be greater than 2 months before the current date."}
            )

    def validate_dates_within_limit(self, data: dict, instance: Ads | None = None) -> None:
        """
        Validate 'buy_date' and 'refund_date' fields are within the specified limit.
        """
        # ToDo TEMPORARY OFF

        # for field in ('buy_date', 'refund_date'):
        #     if instance and field in data and getattr(instance, field, None) == data[field]:
        #         continue
        #
        #     if check_date := data.get(field):
        #         if not self.check_date_not_terminated(check_date, self.DATE_NOT_TERMINATED_LIMIT):
        #             raise serializers.ValidationError(
        #                 {field: f"{field} should not be earlier than 30 days before today."}
        #             )

        pass

    @staticmethod
    def validate_refund_cost(data: dict, instance: Ads | None = None):
        if 'refund_cost' not in data or not data.get('refund_cost'):
            return

        cost = instance.cost if instance and instance.cost is not None else data.get('cost', 0)
        refund_cost = data['refund_cost']

        if cost <= 1:
            raise serializers.ValidationError(
                {'cost': "Refund is not available if cost less than or equals one"}
            )

        if refund_cost > cost:
            raise serializers.ValidationError(
                {'refund_cost': "Refund cost should not be greater than the cost"}
            )

    @staticmethod
    def validate_calendar_dates(data: dict):
        """
        Validate calendar dates.
        """
        if (start_date := data.get('calendar_start_date')) and (end_date := data.get('calendar_end_date')):
            if start_date > end_date:
                raise serializers.ValidationError(
                    {'calendar_end_date': "End date should be greater than start date."}
                )

    def validate_update(self, instance: Ads, data: dict, user: User, department: Ads.DepartmentChoices) -> None:
        """
        Validate update of the ads. Check which fields could be updated if the ad has saved_revenue attr.
        """
        self.validate_calendar_dates(data=data)

        self.validate_restricted_fields(instance=instance, data=data)

        self.validate_blocked_ad_fields(instance=instance, data=data)
        self.validate_date(instance=instance, data=data)

        self.validate_dates_within_limit(data=data, instance=instance)
        self.validate_after_swap(instance=instance, data=data)
        self.validate_refund_cost(data=data, instance=instance)
        # self.validate_donor_platform_promo_limitation(data=data, instance=instance)

    @staticmethod
    def validate_after_swap(instance: Ads, data: dict):
        """
        Extra validations for ads after swap
        """
        if instance.is_parent or instance.is_child:
            if 'refund_date' in data and data['refund_date'] != instance.refund_date:
                raise serializers.ValidationError(
                    {'refund_date': 'Refund is not allowed for ads after swap.'}
                )

            if 'donor' in data and data['donor'] != instance.donor:
                raise serializers.ValidationError(
                    {'donor': 'Donor is not allowed for update after swap.'}
                )

    def validate_donor_platform_promo_limitation(self, data: dict, instance: Ads | None = None) -> None:
        """
        Validate donor, platform and promo limitation
        """
        platform_type = data.get('platform_type')
        promo = data.get('promo')
        donor = data.get('donor')
        ads_date = data.get('date')

        if instance:
            if instance.created_at.date() < self.DATE_FROM_DONOR_PLATFORM_PROMO_LIMITATION:
                return

            platform_type = platform_type or instance.platform_type
            promo = promo or instance.promo
            donor = donor or instance.donor
            ads_date = ads_date or instance.date

        if platform_type and promo and donor:
            if platform_type.name.lower() == 'guaranteed' and promo.name.lower() == 'gg' and donor:
                queryset = Ads.objects.filter(
                    platform_type=platform_type,
                    promo=promo,
                    donor=donor,
                    date__month=ads_date.month,
                    date__year=ads_date.year,
                )

                if instance:
                    queryset = queryset.exclude(id=instance.id)

                if queryset.count() >= 3:
                    raise serializers.ValidationError(
                        'You cannot create more than 3 promotions with this donor, platform_type Guaranteed and promo GG.'
                    )

    def validate_create(self, data: dict, department: Ads.DepartmentChoices) -> None:
        """
        Validate create of the ads.
        """
        self.validate_calendar_dates(data=data)
        self.validate_dates_within_limit(data=data)
        self.validate_refund_cost(data=data)
        # self.validate_donor_platform_promo_limitation(data=data)

    def validate_swap(self, parent: Ads, data: dict, department: Ads.DepartmentChoices) -> None:
        """
        Validate swap of the ads.
        """
        if not parent.fans_count:
            raise serializers.ValidationError(
                {
                    "fans_count": "Cannot swap ads without fans."
                }
            )

        if parent.claims_count >= parent.fans_count:
            raise serializers.ValidationError(
                {
                    "parent": "Cannot swap ads with more claims than fans."
                }
            )

        if parent.refund_date:
            raise serializers.ValidationError(
                {
                    "parent": "Cannot swap ads with refund."
                }
            )

        if not parent.cost or parent.cost < 1:
            raise serializers.ValidationError(
                {
                    "cost": "Cannot swap ads with cost less than 1."
                }
            )
