from django.db.models.signals import (
    post_delete,
    post_save,
    pre_save,
)
from django.dispatch import receiver

from accounts.models import User, UserMarketerProxy
from ads.models import (
    Ads,
    BlackList,
    Donor,
    PaymentMethod,
    PlatformType,
    Promo,
)
from ads.services import DonorService
from base.crm_cache.service import RedisCacheService
from base.signals import post_bulk_update
from only_fans_models.models import OnlyFansModel


@receiver([post_save, post_delete], sender=OnlyFansModel)
@receiver([post_save, post_delete], sender=Donor)
@receiver([post_save, post_delete], sender=PaymentMethod)
@receiver([post_save, post_delete], sender=PlatformType)
@receiver([post_save, post_delete], sender=Promo)
@receiver([post_save, post_delete], sender=BlackList)
@receiver([post_save, post_delete], sender=UserMarketerProxy)
@receiver([post_save, post_delete], sender=User)
def invalidate_cache_buying(sender, instance, *args, **kwargs):
    """
    Invalidates the cache for the buying catalog.

    Args:
    - sender: The sender object.
    - instance: The instance of the object being saved or deleted.
    """
    if isinstance(instance, User) and getattr(instance.role, 'name', None) != 'marketer':

        return

    cache_service = RedisCacheService()
    cache_service.delete_keys_with_prefix('buying-catalog')

    if isinstance(instance, PaymentMethod):
        cache_service.delete_keys_with_prefix('buying-payment-methods')

    if isinstance(instance, PlatformType):
        cache_service.delete_keys_with_prefix('buying-platform-types')

    if isinstance(instance, Promo):
        cache_service.delete_keys_with_prefix('buying-promos')

    if isinstance(instance, Donor):
        cache_service.delete_keys_with_prefix('buying-donors')

    if isinstance(instance, BlackList):
        cache_service.delete_keys_with_prefix('buying-black-list')


@receiver([post_save, post_delete, post_bulk_update], sender=Ads)
def invalidate_cache_buying_ads(sender, *args, **kwargs):
    """
    Signal receiver function that invalidates cache related to buying ads.

    Args:
    - sender: The sender of the signal.
    - instance: The instance that triggered the signal.
    - args: Additional positional arguments.
    - kwargs: Additional keyword arguments.
    """
    cache_service = RedisCacheService()
    prefixes = [
        'buying-ads-models-summary',
        'buying-ads-summary',
        'buying-ads-negative-romi',
        'buying-ads-list',
    ]

    for prefix in prefixes:
        cache_service.delete_keys_with_prefix(prefix)


@receiver(pre_save, sender=Ads)
def cache_previous_ads(sender, instance, *args, **kwargs):
    """

    Cache the previous date and model of the Ads object before it is saved.

    This method is a signal receiver function that listens
    for the pre_save signal from the Ads model.
    It caches the previous date and model of the Ads object if it already exists in the database.

    """
    original_date = None
    original_only_fans_model = None
    original_donor = None

    try:
        original_ads = Ads.objects.get(pk=instance.id)
        original_date = original_ads.date
        original_only_fans_model = original_ads.only_fans_model
        original_donor = original_ads.donor
    except Ads.DoesNotExist:
        pass  # Ads object does not exist in the database, no need to cache it.

    instance.__original_date = original_date
    instance.__original_only_fans_model = original_only_fans_model
    instance.__original_donor = original_donor


@receiver(post_bulk_update, sender=Ads)
def update_donors_speed_completion(sender, instances: None | list[Ads] = None, *args, **kwargs):
    """
    Signal receiver function that updates the donors speed completion.

    Args:
    - sender: The sender of the signal.
    - instances: The instances that triggered the signal.
    - args: Additional positional arguments.
    - kwargs: Additional keyword arguments.
    """
    if not instances:
        return

    donors_set = {
        ads.donor
        for ads in instances
        if ads.donor
    }

    if not donors_set:
        return

    donor_service = DonorService()

    for donor in donors_set:
        donor_service.update_donor_speed_completion(donor)
