from django_filters import Date<PERSON>romToRange<PERSON>ilter, RangeFilter
from django_filters.rest_framework import FilterSet
from rest_framework.filters import BaseFilterBackend, OrderingFilter

from ads.models import (
    Ads,
    AdsDraft,
    Donor,
)
from base.filters import ListCharFilter


class AdsFilter(FilterSet):
    """

    Class: AdsFilter

    A class that represents a filter for ads.

    Attributes:
    - date: DateFromToRangeFilter instance, filter for date range
    - claims_count: RangeFilter instance, filter for claims count

    Meta Attributes:
    - model: Ads model
    - fields: List of fields to filter

    """
    date = DateFromToRangeFilter()
    claims_count = RangeFilter()
    status = ListCharFilter(field_name='status__name', lookup_expr='in')
    promos = ListCharFilter(field_name='promo_id', lookup_expr='in')

    class Meta:
        model = Ads
        fields = [
            'only_fans_model',
            'donor',
            'marketer',
        ]


class CustomOrderingFilter(OrderingFilter):
    """
    Custom ordering filter based on OrderingFilter for filtering by related fields
    """
    def get_ordering(self, request, queryset, view):
        """
        Ordering is set by a comma delimited ?ordering=... query parameter.

        The `ordering` query parameter can be overridden by setting
        the `ordering_param` value on the OrderingFilter or by
        specifying an `ORDERING_PARAM` value in the API settings.
        """
        params = request.query_params.get(self.ordering_param)
        if params:
            fields = [
                param.strip()
                .replace('donor', 'donor__name')
                .replace('only_fans_model', 'only_fans_model__nickname')
                .replace('payment_method', 'payment_method__name')
                .replace('platform_type', 'platform_type__name')
                .replace('promo', 'promo__name')
                .replace('status', 'status__name')
                .replace('marketer', 'marketer__first_name')

                for param in params.split(',')
            ]
            ordering = self.remove_invalid_fields(queryset, fields, view, request)
            if ordering:
                return ordering

        # No ordering was included, or all the ordering fields were invalid
        return self.get_default_ordering(view)


class AdsTrackingFilter(BaseFilterBackend):
    """
    AdsTrackingFilter

    This class is a filter backend that can be used to filter querysets based on certain parameters related to ads tracking.

    Methods:
    - filter_queryset(request, queryset, view): Filters the queryset based on the provided request parameters and returns the filtered queryset.

    Parameters:
    - request: The request object received by the view.
    - queryset: The queryset to be filtered.
    - view: The view class or instance that is being filtered.

    Returns:
    - The filtered queryset.
    """
    def filter_queryset(self, request, queryset, view):
        if view.action not in ['list', 'history']:

            return queryset

        date_after = request.query_params.get('date_after')
        date_before = request.query_params.get('date_before')
        only_fans_model = request.query_params.get('only_fans_model')
        platform_type = request.query_params.get('platform_type')

        if date_after:
            queryset = queryset.filter(track_start__date__gte=date_after)

        if date_before:
            queryset = queryset.filter(track_start__date__lte=date_before)

        if only_fans_model:
            queryset = queryset.filter(ads__only_fans_model_id=only_fans_model)

        if platform_type:
            queryset = queryset.filter(ads__platform_type_id=platform_type)

        return queryset


class DonorFilter(FilterSet):
    """

    Class: AdsFilter

    A class that represents a filter for ads.

    Attributes:
    - date: DateFromToRangeFilter instance, filter for date range
    - claims_count: RangeFilter instance, filter for claims count

    Meta Attributes:
    - model: Ads model
    - fields: List of fields to filter

    """
    options = ListCharFilter(field_name='options', lookup_expr='in')
    types = ListCharFilter(field_name='types', lookup_expr='in')

    class Meta:
        model = Donor
        fields = [
            'types', 'options'
        ]


class AdsDraftFilter(FilterSet):
    """

    Class: AdsDraftFilter

    A class that represents a filter for ads drafts.

    Attributes:
    - date: DateFromToRangeFilter instance, filter for date range
    - only_fans_model: ListCharFilter instance, filter for only_fans_model
    - donor: ListCharFilter instance, filter for donor
    - status: ListCharFilter instance, filter for status

    Meta Attributes:
    - model: AdsDraft model
    - fields: List of fields to filter

    """
    date = DateFromToRangeFilter()
    only_fans_model = ListCharFilter()
    status = ListCharFilter()
    marketer = ListCharFilter()

    class Meta:
        model = AdsDraft
        fields = (
            'date', 'only_fans_model', 'status', 'marketer'
        )
