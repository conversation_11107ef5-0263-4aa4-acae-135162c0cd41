class AdsDraftError(Exception):
    """
    Base exception for AdsDraft errors
    """
    pass


class AdsDraftAlreadyAcceptedError(AdsDraftError):
    """
    Exception for AdsDraft that already accepted
    """
    pass


class AdsDraftMarketerWithoutParentError(AdsDraftError):
    """
    Exception for AdsDraft when marketer does not have a parent
    """
    pass


class AdsDraftTelegramServiceError(Exception):
    """
    Exception for AdsDraftTelegramService errors
    """
    pass


class AdsDraftTelegramServiceReviewerError(AdsDraftTelegramServiceError):
    """
    Exception for AdsDraftTelegramService when reviewer does not have a telegram id
    """
    pass


class AdsDraftTelegramServiceSendReviewResultError(AdsDraftTelegramServiceError):
    """
    Exception for AdsDraftTelegramService when failed to send review result
    """
    pass
