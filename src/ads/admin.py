from django.contrib import admin
from django.contrib.auth import get_user_model
from simple_history.admin import SimpleHistoryAdmin

from ads.models import (
    Ads,
    AdsDraft,
    AdsSale,
    AdsSwapLog,
    AdsTracking,
    AdsTrackingDailyData,
    BlackList,
    Donor,
    DonorOption,
    DonorType,
    PaymentMethod,
    PlatformType,
    ProblemStatus,
    Promo,
    PromotionCount,
    Status,
)
from base.mixins import ReadOnlyAdminMixin


@admin.register(PaymentMethod)
class PaymentMethodAdmin(admin.ModelAdmin):
    """
    Admin class for managing Payment Methods in the admin panel.
    """
    search_fields = ('name',)


@admin.register(PlatformType)
class PlatformTypeAdmin(admin.ModelAdmin):
    """
    Admin class for managing Platform Types in the admin panel.
    """
    search_fields = ('name',)


@admin.register(Promo)
class PromoAdmin(admin.ModelAdmin):
    """
    Admin class for managing Promos in the admin panel.
    """
    search_fields = ('name',)


@admin.register(Donor)
class DonorAdmin(admin.ModelAdmin):
    """
    Admin class for managing Donors in the admin panel.
    """
    search_fields = ('name',)


@admin.register(DonorType)
class DonorTypeAdmin(admin.ModelAdmin):
    """
    Admin class for managing DonorTypes in the admin panel.
    """
    search_fields = ('name',)


@admin.register(DonorOption)
class DonorOptionAdmin(admin.ModelAdmin):
    """
    Admin class for managing DonorOptions in the admin panel.
    """
    search_fields = ('name',)


@admin.register(Status)
class StatusAdmin(admin.ModelAdmin):
    """
    Admin class for managing Statuses in the admin panel.
    """
    search_fields = ('name',)


@admin.register(ProblemStatus)
class ProblemStatusAdmin(admin.ModelAdmin):
    """
    Admin class for managing ProblemStatuses in the admin panel.
    """
    search_fields = ('name',)


@admin.register(BlackList)
class BlackListAdmin(SimpleHistoryAdmin):
    """
    Admin class for managing BlackList in the admin panel.
    """
    search_fields = ('comment',)
    list_display = ('donor', 'marketer', 'comment')
    list_select_related = ('donor', 'marketer')

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """
        Returns the form field for the specified database field.
        """
        if db_field.name == 'marketer':
            kwargs['queryset'] = get_user_model().objects.filter(role__name='marketer')

        return super().formfield_for_foreignkey(db_field, request, **kwargs)


@admin.register(Ads)
class AdsAdmin(ReadOnlyAdminMixin, SimpleHistoryAdmin):
    """
    Admin class for managing Ads in the admin panel.
    """
    search_fields = ('ads_number', 'only_fans_model__nickname')
    list_display = ('date', 'only_fans_model', 'ads_number')
    autocomplete_fields = (
        'only_fans_model',
        'parent',
        'inner_model',
        'donor',
        'marketer',
        'platform_type',
        'promo',
        'status',
        'problem_status'
    )
    list_select_related = ('only_fans_model',)
    ordering = ('-date',)
    readonly_fields = [
        field.name
        for field in Ads._meta.get_fields()
        if field.name != 'id' and field.concrete
    ]


@admin.register(AdsSale)
class AdsSaleAdmin(admin.ModelAdmin):
    pass


# -------------------------------------------------SMM-----------------------------------------------------
class AdsTrackingDailyDataInline(admin.TabularInline):
    """
    Class: AdsTrackingDailyDataInline

    Represents an inline table for the model AdsTrackingDailyData in the Django admin interface.

    Attributes:
    - model: The model class that this inline table is related to (AdsTrackingDailyData).
    - can_delete: Specifies whether the rows in the inline table can be deleted or not. Default is False.

    Methods:
    - has_add_permission(request, obj): Determines if the user has permission to add new rows in the inline table.
      Parameters:
        - request: The django.http.HttpRequest object representing the current request.
        - obj: The model object being edited, if any.
      Returns:
        - True if the user has permission to add new rows, False otherwise.

    - has_change_permission(request, obj=None):
    Determines if the user has permission to change existing rows in the inline table.
      Parameters:
        - request: The django.http.HttpRequest object representing the current request.
        - obj: The model object being edited, if any.
      Returns:
        - True if the user has permission to change existing rows, False otherwise.
    """
    model = AdsTrackingDailyData
    can_delete = False

    def has_add_permission(self, request, obj):

        return False

    def has_change_permission(self, request, obj=None):

        return False


@admin.register(AdsTracking)
class AdsTrackingAdmin(admin.ModelAdmin):
    """
    Class: AdsTrackingAdmin

    Description:
    This class is the admin configuration for the AdsTracking model in Django.
    It provides additional functionality for managing and displaying AdsTracking records in the Django admin interface
    *.

    Attributes:
    - list_filter: A tuple containing the fields that can be used as filters in the admin list view.
    In this class, the 'track_start' field is used as a filter.
    - inlines: A list of inline classes that define the related models to be
    displayed inline in the AdsTracking admin form. In this class, the AdsTrackingDailyDataInline class is included
    *.

    Methods:
    - formfield_for_foreignkey: This method is called to get the form field for a
    foreign key database field in the admin form. It is overridden in this class to customize the queryset of
    * the 'ads' field. The method returns the form field with the modified queryset.

    """
    list_filter = ('track_start', 'creator_role')
    inlines = [AdsTrackingDailyDataInline,]

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """
        Returns the form field for the specified database field.
        """
        if db_field.name == 'ads':
            kwargs['queryset'] = Ads.objects.select_related('only_fans_model')

        return super().formfield_for_foreignkey(db_field, request, **kwargs)


# -------------------------------------------------Promotions Count-----------------------------------------------
@admin.register(PromotionCount)
class PromotionCountAdmin(admin.ModelAdmin):
    """
    Admin class for managing Statuses in the admin panel.
    """
    search_fields = ('model_id', 'promotion_name', 'of_db_promotion_id')


# --------------------------------------------------Ads Swap Log--------------------------------------------------
@admin.register(AdsSwapLog)
class AdsSwapLogAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    """
    Admin class for managing AdsSwapLog in the admin panel.
    """
    pass


# -------------------------------------------------Ads Draft-------------------------------------------------
@admin.register(AdsDraft)
class AdsDraftAdmin(SimpleHistoryAdmin):
    """
    Admin class for managing AdsDraft in the admin panel.
    """
    list_display = ("created_at", "date", "only_fans_model", "cost", "fans_count", "marketer", "status", "reviewer")
    list_select_related = ("only_fans_model", "marketer", "reviewer")
    search_fields = ("only_fans_model__nickname", "marketer__first_name", "marketer__last_name")
