[run]
branch = True
omit =
    *tests*
    *env/*
    *__init__*

[htmlcov]
never = true

[report]
# Regexes for lines to exclude from consideration
exclude_lines =
    # Have to re-enable the standard pragma
    pragma: no cover

    # Don't complain about missing debug-only code:
    def __repr__
    if self\.debug

    # Don't complain if tests don't hit defensive assertion code:
    raise AssertionError
    raise NotImplementedError

    # Don't complain if non-runnable code isn't run:
    if 0:
    if __name__ == .__main__.:

ignore_errors = True
omit =
    *__init__*
    *asgi.py*
    *wsgi.py*
    *apps.py
    *manage.py
    *__init__.py
    *migrations*
    *admin.py
    *urls.py
    *models.py
    *settings.py
    *celery.py
    *middlewares.py
    *sendgrid.py
    *mixin.py
    *tasks.py
    *local_settings.py

[html]
directory = htmlcov