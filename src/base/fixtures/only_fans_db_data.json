[{"model": "only_fans_db.fans", "pk": 1, "fields": {"fan_username": "Fan1"}}, {"model": "only_fans_db.models", "pk": 1, "fields": {"email": "<EMAIL>", "username": "username1", "headers": {}, "proxies": "", "selfie": true, "is_active": true, "is_sfs": false}}, {"model": "only_fans_db.massmessages", "pk": 1, "fields": {"message_text": "This is a mass message.", "message_hash": "", "model": 1, "send": 1, "viewed": 0}}, {"model": "only_fans_db.messageslist", "pk": 1, "fields": {"fan": 1, "model": 1, "is_incoming": false, "message_text": "This is a message.", "have_content": false, "text_hash": "", "is_read": false, "is_paid": false, "is_mass_message": false}}, {"model": "only_fans_db.sales", "pk": "1", "fields": {"amount": 100.0, "fan": 1, "model": 1, "type": "Type1", "message": 1}}, {"model": "only_fans_db.distance", "pk": "1", "fields": {"model_id": 1, "distance": "100", "time": "2023-08-29 11:24:58.320221"}}]