from django.utils.deprecation import MiddlewareMixin


class User<PERSON><PERSON><PERSON><PERSON><PERSON>(MiddlewareMixin):
    """
    Middleware to set user cookie
    If user is authenticated and there is no cookie, set the cookie,
    If the user is not authenticated and the cookie remains, delete it
    """

    def process_response(self, request, response):
        if request.user.is_authenticated and not request.COOKIES.get('user'):
            response.set_cookie("user", str(request.user.pk), max_age=30 * 24 * 60 * 60)
        elif not request.user.is_authenticated and request.COOKIES.get('user'):
            response.delete_cookie("user")
        return response
