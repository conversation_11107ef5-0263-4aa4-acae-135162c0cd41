from rest_framework.exceptions import APIException


class CurrencyConversionException(APIException):
    status_code = 500
    default_detail = "Error occurred during currency conversion."
    default_code = "currency_conversion_error"


class CurrencyNotFoundException(APIException):
    def __init__(self, currency_name: str = ""):
        super().__init__(f"Currency {currency_name} not found.")

    status_code = 500
    default_code = "currency_not_found"
