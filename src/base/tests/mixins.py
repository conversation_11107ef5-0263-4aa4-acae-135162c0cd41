import base64
import datetime
from io import BytesIO

from django.contrib.auth import get_user_model
from PIL import Image
from rest_framework.test import APITestCase
from rest_framework_api_key.models import APIKey

from accounts.models import UserRole
from base.crm_cache.mixins import ClearCacheTestMixin
from base.crm_cache.service import RedisCacheService
from only_fans_models.models import OnlyFansModel
from shifts.models import ShiftIndex, ShiftNumber

User = get_user_model()


class BaseCRMTest(ClearCacheTestMixin, APITestCase):
    """
    Base class for CRM tests.

    Inherits from ClearCacheTestMixin and APITestCase to utilize API testing methods and functionality in Django.

    Attributes:
        fixtures (list): List of fixtures to be loaded before running the tests.
    """
    databases = '__all__'
    fixtures = [
        'base/fixtures/accounts_data.json',
        'base/fixtures/only_fans_db_data.json',
    ]

    def setUp(self) -> None:
        """
        Set up the test environment.

        Executed before each test in the class.
        Initializes the necessary objects and variables for testing.
        """
        _, self.api_key = APIKey.objects.create_key(name="test_api_key")
        self.api_key_headers = {
            "Content-Type": "application/json",
            "X-Api-Key": self.api_key
        }

        self.cache_service = RedisCacheService()
        self.users = []

        self.operator_role = UserRole.objects.get(name='operator')
        self.team_lead_role = UserRole.objects.get(name='team_lead')
        self.superuser_role = UserRole.objects.get(name='superuser')
        self.senior_operator_role = UserRole.objects.get(name='senior_operator')
        self.marketer_role = UserRole.objects.get(name='marketer')
        self.hom_role = UserRole.objects.get(name='hom')
        self.hof_role = UserRole.objects.get(name='hof')
        self.financier_role = UserRole.objects.get(name='financier')
        self.smm_role = UserRole.objects.get(name='smm')
        self.smm_of_role = UserRole.objects.get(name='smm_of')
        self.researcher_role = UserRole.objects.get(name='researcher')
        self.hocm_role = UserRole.objects.get(name='hocm')
        self.client_manager_role = UserRole.objects.get(name='client_manager')
        self.business_dev_role = UserRole.objects.get(name='business_dev')
        self.hobd_role = UserRole.objects.get(name='hobd')
        self.business_unit_role = UserRole.objects.get(name='business_unit')

        self.user_superuser = User.objects.create(
            email='<EMAIL>',
            first_name='first',
            last_name='last',
            role=self.superuser_role
        )
        self.user_team_lead = User.objects.create(
            email='<EMAIL>',
            first_name='first',
            last_name='last',
            role=self.team_lead_role
        )
        self.user_operator = User.objects.create(
            email='<EMAIL>',
            first_name='first',
            last_name='last',
            role=self.operator_role,
            parent=self.user_team_lead
        )
        self.user_senior_operator = User.objects.create(
            email='<EMAIL>',
            first_name='first',
            last_name='last',
            role=self.senior_operator_role,
            parent=self.user_team_lead
        )
        self.user_hom = User.objects.create(
            email='<EMAIL>',
            first_name='first_name',
            last_name='last_name',
            role=self.hom_role
        )
        self.user_marketer = User.objects.create(
            email='<EMAIL>',
            first_name='first',
            last_name='last',
            role=self.marketer_role,
            parent=self.user_hom
        )
        self.user_hof = User.objects.create(
            email='<EMAIL>',
            first_name='first_name',
            last_name='last_name',
            role=self.hof_role
        )
        self.user_financier = User.objects.create(
            email='<EMAIL>',
            first_name='first_name',
            last_name='last_name',
            role=self.financier_role
        )
        self.user_smm = User.objects.create(
            email='<EMAIL>',
            first_name='first_name',
            last_name='last_name',
            role=self.smm_role,
        )
        self.user_smm_of = User.objects.create(
            email='<EMAIL>',
            first_name='first_name',
            last_name='last_name',
            role=self.smm_of_role,
        )
        self.user_researcher = User.objects.create(
            email='<EMAIL>',
            first_name='first_name',
            last_name='last_name',
            role=self.researcher_role,
        )
        self.user_hocm = User.objects.create(
            email='<EMAIL>',
            first_name='first_name',
            last_name='last_name',
            role=self.hocm_role,
        )
        self.user_client_manager = User.objects.create(
            email='<EMAIL>',
            first_name='first_name',
            last_name='last_name',
            role=self.client_manager_role,
        )
        self.user_business_dev = User.objects.create(
            email='<EMAIL>',
            first_name='first_name',
            last_name='last_name',
            role=self.business_dev_role,
        )
        self.user_hobd = User.objects.create(
            email='<EMAIL>',
            first_name='first_name',
            last_name='last_name',
            role=self.hobd_role,
        )
        self.user_business_unit = User.objects.create(
            email='<EMAIL>',
            first_name='first_name',
            last_name='last_name',
            role=self.business_unit_role,
        )

        self.user_roles = [value for key, value in self.__dict__.items() if isinstance(value, UserRole)]
        self.users = [value for key, value in self.__dict__.items() if isinstance(value, User)]

        self.client.force_authenticate(user=self.user_team_lead)

    @staticmethod
    def create_only_fans_model(number: int) -> OnlyFansModel:
        """
        Create OnlyFansModel object.

        Args:
            number (i): Value to be used in the creation of the OnlyFansModel objects.
        """
        return OnlyFansModel.objects.create(
                model_id=number,
                nickname=f'OnlyFansModel {number}',
                username_of=f'model_{number}',
            )

    @staticmethod
    def create_shift_number(
            shift_number: int,
            time_start: datetime.time,
            time_end: datetime.time
    ) -> ShiftNumber:
        """
        Create ShiftNumber object.

        Args:
            shift_number (int): Value to be used in the creation of the ShiftNumber objects.
            time_start (datetime.time): Start time of the shift.
            time_end (datetime.time): End time of the shift.
        """
        shift_index, _ = ShiftIndex.objects.get_or_create(
            number=shift_number
        )

        return ShiftNumber.objects.create(
            name=f'ShiftNumber {shift_number}',
            index=shift_index,
            time_start=time_start,
            time_end=time_end
        )

    @staticmethod
    def get_temporary_image():
        bts = BytesIO()
        img = Image.new("RGB", (100, 100))
        img.save(bts, 'jpeg')

        result = base64.b64encode(bts.getvalue()).decode('utf-8')

        return result
