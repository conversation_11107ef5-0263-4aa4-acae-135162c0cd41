from django.core.exceptions import ObjectDoesNotExist
from django.utils.encoding import smart_str
from rest_framework import serializers
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer as _TokenObtainPairSerializer

from accounts.serializers import UserSerializer


class TokenObtainPairSerializer(_TokenObtainPairSerializer):
    user = UserSerializer(read_only=True)

    def validate(self, attrs):
        data = super().validate(attrs)

        # Add extra responses here
        data['user'] = UserSerializer(self.user).data
        return data


class CreatableSlugRelatedField(serializers.SlugRelatedField):
    """
    Custom SlugRelatedField that allows to create new object if it does not exist
    """
    def to_internal_value(self, data):
        try:
            return self.get_queryset().get_or_create(**{self.slug_field: data})[0]
        except ObjectDoesNotExist:
            self.fail('does_not_exist', slug_name=self.slug_field, value=smart_str(data))
        except (TypeError, ValueError):
            self.fail('invalid')


class CreatableSlugRelatedFieldIgnoreExtraSpaces(serializers.SlugRelatedField):
    """
    Custom SlugRelatedField that allows to create new object if it does not exist with ignoring extra spaces.
    """
    def to_internal_value(self, data):
        clean_data = " ".join(data.split())

        if not clean_data:
            return None

        try:
            return self.get_queryset().get_or_create(
                **{
                    f'{self.slug_field}__iexact': clean_data,
                    'defaults': {
                        self.slug_field: clean_data
                    }
                }
            )[0]
        except ObjectDoesNotExist:
            self.fail('does_not_exist', slug_name=self.slug_field, value=smart_str(clean_data))
        except (TypeError, ValueError):
            self.fail('invalid')
