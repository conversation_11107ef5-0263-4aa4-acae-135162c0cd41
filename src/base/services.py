import hashlib
import json
import logging
from datetime import datetime
from decimal import Decimal
from urllib.parse import urljoin

import gspread
import httpx
import pytz
from django.conf import settings

from base.exceptions import CurrencyConversionException, CurrencyNotFoundException
from base.tools import DecimalEncoder


class DateTokenService:
    TIMEZONE = pytz.timezone('Europe/Kiev')

    @classmethod
    def get_token_key(cls) -> str:
        """
        Get generated a token key based on the current date.

        Returns:
            str: The generated token key.
        """
        data = datetime.now(cls.TIMEZONE).date().isoformat()
        md5_hash = hashlib.md5()
        md5_hash.update(data.encode())
        return md5_hash.hexdigest()

    @classmethod
    def check_token_key(cls, token: str) -> bool:
        """
        Check if the provided token matches the generated token key.

        Args:
            token (str): The token to be checked.

        Returns:
            bool: True if the token matches the generated token key, False otherwise.
        """

        if token == cls.get_token_key():
            return True
        return False


class GoogleSheetsManager:
    """
    Class to manage Google Sheets.
    """

    SCOPES = [
        "https://www.googleapis.com/auth/spreadsheets",
        "https://www.googleapis.com/auth/drive",
    ]

    def __init__(self, creds_file, spreadsheet_id):
        self.creds_file = creds_file
        self.spreadsheet_id = spreadsheet_id
        self.client = self.authenticate()

    def authenticate(self):
        client = gspread.service_account(filename=self.creds_file, scopes=self.SCOPES)

        return client


class CurrencyConverter:
    """
    Service for Converting Currencies
    """
    def __init__(self, api_url="https://api.exchangerate-api.com/v4/latest/"):
        self.api_url = api_url

    def get_exchange_rate(self, from_currency: str, to_currency: str) -> Decimal:
        from_currency = from_currency.upper()
        to_currency = to_currency.upper()
        url = f"{self.api_url}{from_currency}"

        try:
            with httpx.Client() as client:
                response = client.get(url)
                response.raise_for_status()
                data = response.json()

                return Decimal(data['rates'][to_currency])

        except httpx.RequestError as error:
            logging.error(f"Request error: {error}")
            raise CurrencyConversionException(detail=str(error))

        except httpx.HTTPStatusError as error:
            logging.error(f"HTTP error: {error}")

            if error.response.status_code == 404 and "unsupported_code" in error.response.text:
                raise CurrencyNotFoundException(from_currency)

            raise CurrencyConversionException(detail=f"HTTP error: Status code {error.response.status_code}")

        except KeyError:
            raise CurrencyNotFoundException(from_currency)

    def convert(self, from_currency: str, to_currency: str, amount: int | float | Decimal) -> Decimal:
        rate = self.get_exchange_rate(from_currency, to_currency)

        return Decimal(amount) * rate


class TelegramBotService:
    """
    Base Telegram Bot service
    """
    def __init__(self, bot_token: str):
        self.bot_token = bot_token
        self.url = f"https://api.telegram.org/bot{self.bot_token}/sendMessage"

    def send_message(self, chat_id: int, text: str, reply_markup: dict = None, parse_mode: str = "HTML"):
        """
        Sends a message to a Telegram chat.

        :param chat_id: The Telegram chat ID to send the message to
        :param text: The message text to send
        :param reply_markup: The reply markup for the message (e.g., inline keyboard)
        :param parse_mode: The parse mode for formatting (e.g., "HTML" or "Markdown")
        """
        data = {
            "chat_id": chat_id,
            "text": text,
        }

        if reply_markup:
            data["reply_markup"] = reply_markup

        data["parse_mode"] = parse_mode

        with httpx.Client() as client:
            response = client.post(self.url, json=data)
            response.raise_for_status()

            return response.json()

    def edit_message(
            self,
            chat_id: int,
            message_id: int,
            text: str,
            reply_markup: dict = None,
            parse_mode: str = "HTML"
    ):
        """
        Edit a message in a Telegram chat.

        :param chat_id: The Telegram chat ID to edit the message in
        :param message_id: The ID of the message to edit
        :param text: The new message text
        :param reply_markup: The reply markup for the message (e.g., inline keyboard)
        :param parse_mode: The parse mode for formatting (e.g., "HTML" or "Markdown")
        """
        url = f"https://api.telegram.org/bot{self.bot_token}/editMessageText"

        data = {
            "chat_id": chat_id,
            "message_id": message_id,
            "text": text,
            "parse_mode": parse_mode
        }

        if reply_markup:
            data["reply_markup"] = reply_markup

        with httpx.Client() as client:
            response = client.post(url, json=data)
            response.raise_for_status()

            return response.json()


class WebSocketEventSender:
    """
    Service for sending WebSocket events to FastAPI.
    """
    BASE_URL = 'http://fast_api:5000/'

    def __init__(self, url_path: str):
        self.client = httpx.Client()
        self.url = self._generate_url(url_path)

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.client.close()

    def _generate_url(self, url_path: str):
        if not url_path.startswith('/'):
            url_path = '/' + url_path

        return urljoin(self.BASE_URL, url_path)

    def send_event(self, data: dict):
        headers = {'Content-Type': 'application/json'}
        data = json.dumps(data, ensure_ascii=False, cls=DecimalEncoder)

        response = self.client.post(self.url, headers=headers, data=data)
        response.raise_for_status()


class TelegramBugsNotificationService(TelegramBotService):
    """
    Service for sending notifications based on bugs
    """

    def __init__(self):
        if not settings.BUGS_BOT_TOKEN:
            raise ValueError('BUGS_BOT_TOKEN is not set')

        super().__init__(settings.BUGS_BOT_TOKEN)

    def send_message(self, text: str, *args, **kwargs):
        super().send_message(settings.BUGS_CHAT_ID, text)
