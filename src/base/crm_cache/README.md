# Backoffice cache
Caching package for Backoffice

## Getting started

if you need to add caching to the endpoint, you should perform the following steps

### 1. Add a cache setting decorator from this module

```sh
@cache_response(prefix='my_group')
 def my_group(self, request, *args, **kwargs):
     # your code
     ...
```

**prefix** - part of the endpoint name, for example "my_group"

cache_key is formed according to the pattern 
```sh
    f'{prefix}_{request.user.id}'
```


### 2. implement cache invalidation signals
    
you can use the **CacheService** from this module

    cache_service = RedisCacheService()
    cache_service.delete_keys_with_prefix('my_group')

implementation of key generation for invalidation custom

EXAMPLE

```sh
@receiver([post_save, post_delete], sender=User)
@receiver([post_save, post_delete], sender=OnlyFansModel)
def invalidate_cache_my_group(*args, **kwargs):
    """
    Signal receiver function to invalidate cache for 'my_group' key prefix.

    This function is triggered after a User or OnlyFansModel object is saved or deleted.
    It deletes all cache keys with the 'my_group' prefix, effectively invalidating
    the cache for the 'my_group' data.

    Args:
        *args: Variable length argument list.
        **kwargs: Arbitrary keyword arguments.
    """
    cache_service = RedisCacheService()
    cache_service.delete_keys_with_prefix('my_group')
```

### 3. Write tests

When writing your TestCase, be sure to inherit from **BaseCRMTest** from **base.test** module

Example
```sh
class YourTestCase(BaseCRMTest):
    ...
    # your tests methods
```
