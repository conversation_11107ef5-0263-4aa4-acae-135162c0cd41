from base.crm_cache.service import RedisCacheService


class ClearCacheTestMixin:
    """
    Mixin for clearing the cache during test teardown.

    This mixin provides a convenient way to clear the cache after each test case in a test class. It should be used
    as a base class for test classes that require cache clearance between test cases.

    Usage:
        class MyTestCase(ClearCacheTestMixin, TestCase):
            # ... test cases ...

    The `tearDown` method of this mixin clears the cache using `cache.clear()` at the end of each test case.

    """
    def tearDown(self):
        cache_service = RedisCacheService()
        cache_service.clear_cache()
