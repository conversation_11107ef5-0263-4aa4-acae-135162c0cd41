import functools
from urllib import parse

from rest_framework.response import Response

from base.crm_cache.service import RedisCacheService


def serialize_query_params(request):
    """

    Serialize Query Params

    Serialize the query parameters of a given request into a URL-encoded string.

    Parameters:
    - request: A Django HttpRequest object containing the query parameters to be serialized.

    Returns:
    - A string representing the URL-encoded query parameters.

    Example:

        query_params = {
            'param1': 'value1',
            'param2': 'value2',
        }
        request = Request.GET(query_params)
        serialized_params = serialize_query_params(request)
        print(serialized_params)
        # Output: 'param1=value1&param2=value2'

    """
    query_params = sorted(request.GET.items())

    return parse.urlencode(query_params)


def cache_response(prefix: str):
    """
    Decorator to cache the response of a view function.

    The decorator checks if the response for the current request is already
    cached using the provided `prefix` and the user ID from the request. If the
    response is found in the cache, it is returned directly. Otherwise, the
    view function is called to generate the response, and if the response is an
    instance of `Response`, it is cached for future requests.

    Args:
        prefix (str): The prefix to use for the cache key.

    Returns:
        function: The decorated view function.

    """
    def decorator(view_func):
        @functools.wraps(view_func)
        def wrapper(self, request, *args, **kwargs):
            query_params = serialize_query_params(request)
            key = f'{prefix}_{request.user.id}_{query_params}'
            cache_service = RedisCacheService()
            cache_data = cache_service.get_cache_data(key=key)

            if cache_data:
                response = Response(cache_data)

                return response

            response = view_func(self, request, *args, **kwargs)

            if isinstance(response, Response):
                cache_service.set_cache_data(key=key, data=response.data)

            return response

        return wrapper

    return decorator


def cache_retrieve_response(
        prefix: str
):
    """
    Decorator to cache the response of a view function for a retrieve operation, based on the specified key type.

    Args:
        prefix (str): The prefix for the cache key

    Returns:
        The decorated view function with caching support.

    """

    def decorator(view_func):
        @functools.wraps(view_func)
        def wrapper(self, request, pk, *args, **kwargs):
            query_params = serialize_query_params(request)
            key = f'{prefix}_{request.user.id}_{pk}_{query_params}'

            cache_service = RedisCacheService()
            cache_data = cache_service.get_cache_data(key=key)

            if cache_data:
                return Response(cache_data)

            response = view_func(self, request, *args, **kwargs)

            if isinstance(response, Response):
                cache_service.set_cache_data(
                    key=key,
                    data=response.data,
                )

            return response

        return wrapper

    return decorator
