from rest_framework.pagination import LimitOffsetPagination, PageNumberPagination
from rest_framework.response import Response


class PageNumberPaginationWithoutUrl(PageNumberPagination):
    """
    Class: PageNumberPaginationWithoutUrl

    Extends: PageNumberPagination

    This class provides a custom pagination response without including the URL in the response.
    It is a subclass of the `PageNumberPagination` class from the `rest_framework.pagination` module.

    Methods:
    - get_paginated_response(data):
        Returns a custom paginated response without including the URL.
        It receives the paginated data and returns a `Response` object.
        Parameters:
            - data (list): The paginated data.
        Returns:
            - Response: The custom paginated response.

    - get_paginated_response_schema(schema):
        Generates the schema for the custom paginated response.
        It receives the schema for the `results` field and returns a dictionary representing the schema
        for the custom paginated response.
        Parameters:
            - schema (dict): The schema for the `results` field.
        Returns:
            - dict: The schema for the custom paginated response.
    """
    def get_paginated_response(self, data):
        return Response({
            'next': self.page.next_page_number() if self.page.has_next() else None,
            'previous': self.page.previous_page_number() if self.page.has_previous() else None,
            'count': self.page.paginator.count,
            'total_pages':  self.page.paginator.num_pages,
            'results': data
        })

    def get_paginated_response_schema(self, schema):
        return {
            'type': 'object',
            'properties': {
                'count': {
                    'type': 'integer',
                    'example': 123,
                },
                'next': {
                    'type': 'integer',
                    'nullable': True,
                    'example': 3,
                },
                'previous': {
                    'type': 'integer',
                    'nullable': True,
                    'example': 1,
                },
                'results': schema,
            },
        }


class TwentyPerPagePagination(PageNumberPaginationWithoutUrl):
    page_size = 20


class SixtyPerPagePagination(PageNumberPaginationWithoutUrl):
    page_size = 60


class HundredLimitOffsetPagination(LimitOffsetPagination):
    default_limit = 100
    max_limit = 100


class FiftyPerPagePagination(PageNumberPaginationWithoutUrl):
    page_size = 50


class ThirtyFivePerPagePagination(PageNumberPaginationWithoutUrl):
    page_size = 35


class FifteenPerPagePagination(PageNumberPaginationWithoutUrl):
    page_size = 15
