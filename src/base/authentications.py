from django.contrib.auth.backends import BaseBackend
from django.contrib.auth.models import AnonymousUser

from base.services import DateTokenService


class DateKeyAuthentication(BaseBackend):

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def authenticate(self, request, **kwargs):
        """
        Authenticates the user based on date token key and sets the peer in the request object.
        """
        token = request.META.get('HTTP_TOKEN_MODELS')
        if not token:
            return None
        if DateTokenService.check_token_key(token=token):
            request.date_token = True
            return AnonymousUser(), None

    def authenticate_header(self, request) -> str:
        """Return a string that will be used as the value of the WWW-Authenticate header"""
        return 'HTTP_TOKEN_MODELS'
