from rest_framework import serializers


class SerializerAttrsValidator:
    """
    Class for validation serializer attributes.
    """

    def __init__(self, attrs: dict) -> None:
        self.attrs = attrs

    def validate_len_of_objects(self, key: str, min_len: int, max_len: int) -> None:
        """
        Validate length of objects in list.

        Args:
            key (str): key of attribute in serializer
            min_len (int): min length of objects
            max_len (int): max length of objects
        """
        if not (min_len <= len(self.attrs[key]) <= max_len):
            raise serializers.ValidationError({
                    key: f"Length of objects must be in range[{min_len}, {max_len}] not {len(self.attrs[key])}"
            })

    def validate_len_of_string(
            self,
            key: str,
            min_len: int = None,
            max_len: int = None
    ) -> None:
        """
        Validate length of string.

        Args:
            key (str): key of attribute in serializer
            min_len (int): min length of string
            max_len (int): max length of string
        """
        if min_len is not None and len(self.attrs[key]) < min_len:
            raise serializers.ValidationError({
                    key: f"Length must be more than {min_len} symbols not {len(self.attrs[key])}"
            })
        if max_len is not None and len(self.attrs[key]) > max_len:
            raise serializers.ValidationError({
                    key: f"Length of string must be less than {max_len} symbols not {len(self.attrs[key])}"
            })
