import uuid

from rest_framework import permissions
from rest_framework.permissions import BasePermission


class BaseRolePermission(BasePermission):
    """
    Class: BaseRolePermission

    The BaseRolePermission class is a subclass of the BasePermission class
    from the rest_framework.permissions module.
    It provides a base implementation for handling permissions based on user roles.

    Attributes:
    - allowed_roles: A list of roles that are allowed to access the resource.
    - disallowed_roles: A list of roles that are not allowed to access the resource.

    Methods:
    - has_permission(self, request, view) -> bool:
        This method is called to check if the user associated with the provided request
        has the permission to access the given view.

        Parameters:
            - request: The request object representing the incoming request.
            - view: The view object representing the view being accessed.

        Returns:
            - True: If the user has the required role to access the view.
            - False: If the user is not authenticated or does not have the required role.
    """

    allowed_roles = []
    disallowed_roles = []

    def has_permission(self, request, view) -> bool:

        if not request.user.is_authenticated:
            return False

        if not request.user.role:
            return False

        if request.user.role.name in getattr(self, 'disallowed_roles', []):
            return False

        if self.allowed_roles == 'all':
            return True

        if not isinstance(self.allowed_roles, (list, tuple, set)):
            raise ValueError(
                f'allowed_roles must be a list, tuple, '
                f'or set in permission class: {self.__class__.__name__}'
            )

        return request.user.role.name in self.allowed_roles


class IsTeamLeadOrSeniorOperator(BaseRolePermission):
    """
    Custom permission to allow access only to users with role 'team_lead' of 'senior_operator'.
    """

    allowed_roles = ['team_lead', 'senior_operator']


class IsOperator(BaseRolePermission):
    """
    Custom permission to allow access only to users with role 'operator'.
    """

    allowed_roles = ['operator']


class IsTeamLeadOrSeniorOperatorOrOperator(BaseRolePermission):
    """
    Custom permission to allow access only to users with role 'team_lead'
    or 'senior_operator' or 'operator'.
    """

    allowed_roles = ['team_lead', 'senior_operator', 'operator']


class IsTeamLeadOrSeniorOperatorOrOperatorOrSuperUser(BaseRolePermission):
    """
    Custom permission to allow access only to users with role 'team_lead'
    or 'senior_operator' or 'operator' or 'superuser'.
    """

    allowed_roles = ['team_lead', 'senior_operator', 'operator', 'superuser']


class IsDateTokenAuthenticated(BasePermission):
    """
    Permission class that checks if the request has a 'date_token' attribute.

    This permission class is used to verify if the request has a 'partner' attribute,
    indicating that the partner is authenticated. If the 'partner' attribute is present,
    the permission is granted. Otherwise, the permission is denied.

    Note:
        This permission assumes that the authentication process has already been performed
        and has set the 'partner' attribute on the request
    """

    def has_permission(self, request, view):
        return hasattr(request, 'date_token')


class IsTeamLeadOrSeniorOperatorOrSuperUser(BaseRolePermission):
    """
    Custom permission to allow access only to users with role 'team_lead' or 'senior_operator' or 'super_user'.
    """

    allowed_roles = ['team_lead', 'senior_operator', 'superuser']


class IsSuperUser(BaseRolePermission):
    """
    Custom permission to allow access only to users with role 'superuser'.
    """

    allowed_roles = ['superuser']


class IsHOM(BaseRolePermission):
    """
    Custom permission to allow access only to users with role 'hom'.
    """

    allowed_roles = ['hom']


class IsHOMorMarketer(BaseRolePermission):
    """
    Custom permission to allow access only to users with role 'hom' or 'marketer'.
    """

    allowed_roles = ['hom', 'marketer']


class IsTeamLeadOrSeniorOperatorOrSuperUserOrHOMOrMarketerOrHOFOrFinancier(
    BaseRolePermission
):
    """
    Custom permission to allow access only to users with role 'team_lead' or 'senior_operator'
    or 'super_user' or 'hom' or 'marketer' or 'hof' or 'financier'.
    """

    allowed_roles = [
        'team_lead',
        'senior_operator',
        'superuser',
        'hom',
        'marketer',
        'hof',
        'financier',
    ]


class IsSuperUserOrHOMorMarketer(BaseRolePermission):
    """
    Custom permission to allow access only to users with role 'superuser' or 'hom' or 'marketer'.
    """

    allowed_roles = ['superuser', 'hom', 'marketer']


class WriteOnlyForSuperuserRole(permissions.BasePermission):
    def has_permission(self, request, view):
        if (
            request.method not in permissions.SAFE_METHODS
            and not request.user.role.name == 'superuser'
        ):
            return False

        return True


class IsHofOrFinancier(BaseRolePermission):
    allowed_roles = ['hof', 'financier']


class WriteOnlyForHOMRole(permissions.BasePermission):
    def has_permission(self, request, view):
        if (
            request.method not in permissions.SAFE_METHODS
            and not request.user.role.name == 'hom'
        ):
            return False

        return True


class WriteOnlyForHOMAndMarketerRole(permissions.BasePermission):
    def has_permission(self, request, view):
        if (
            request.method not in permissions.SAFE_METHODS
            and request.user.role.name not in ['hom', 'marketer']
        ):
            return False

        return True


class IsSuperUserOrHOMorMarketerOrHOFOrFinancier(BaseRolePermission):
    """
    Custom permission to allow access only to users with role 'superuser'
    or 'hom' or 'marketer' or 'hof' or 'financier'.
    """

    allowed_roles = ['superuser', 'hom', 'marketer', 'hof', 'financier']


class IsSuperUserOrHOMorMarketerOrHOF(BaseRolePermission):
    """
    Custom permission to allow access only to users with role 'superuser'
    or 'hom' or 'marketer' or 'hof' or 'financier'.
    """

    allowed_roles = ['superuser', 'hom', 'marketer', 'hof']


class IsSMM(BaseRolePermission):
    """
    Custom permission to allow access only to users with role 'smm'.
    """

    allowed_roles = ['smm']


class IsSMMorHOMorMarketer(BaseRolePermission):
    """
    Custom permission to allow access only to users with role 'smm' or 'hom' or 'marketer'.
    """

    allowed_roles = ['smm', 'hom', 'marketer', 'smm_of']


class IsNotOperator(BaseRolePermission):
    """
    Custom permission to allow access only to users with role 'not operator'.
    """

    disallowed_roles = ['operator']
    allowed_roles = 'all'


class IsHofOrSuperuser(BaseRolePermission):
    allowed_roles = ['hof', 'superuser']


class WriteOnlyForSuperUserOrTeamLeadOrSeniorOperatorRole(permissions.BasePermission):
    def has_permission(self, request, view):
        if (
            request.method not in permissions.SAFE_METHODS
            and request.user.role.name
            not in ['superuser', 'team_lead', 'senior_operator']
        ):
            return False

        return True


class IsTeamLeadOrSuperUser(BaseRolePermission):
    allowed_roles = ['team_lead', 'superuser']


class IsResearcher(BaseRolePermission):
    allowed_roles = ['researcher']


class IsHOCM(BaseRolePermission):
    """
    Custom permission to allow access only to users with role 'hocm'.
    """

    allowed_roles = ['hocm']


class IsClientManagerOrHOCM(BaseRolePermission):
    """
    Custom permission to allow access only to users with role 'client_manager'.
    """

    allowed_roles = ['client_manager', 'hocm']


class IsBusinessDevOrHOBD(BaseRolePermission):
    """
    Custom permission to allow access only to users with role 'business_dev' or 'hobd'.
    """

    allowed_roles = ['business_dev', 'hobd']


class IsOwnerOrReadOnly(permissions.BasePermission):
    """
    Object-level permission to only allow owners of an object to edit it.
    Assumes the model instance has an `user` attribute.
    """

    def has_object_permission(self, request, view, obj):
        if request.method in permissions.SAFE_METHODS or (
            request.user.role and request.user.role.name == 'hocm'
        ):

            return True

        return obj.user == request.user


class IsSpectator(BaseRolePermission):
    allowed_roles = ['spectator']


def create_role_permission(
    allowed_roles: list[str] | None = None,
    disallowed_roles: list[str] | None = None
) -> type:
    allowed_roles = set(allowed_roles or [])
    disallowed_roles = set(disallowed_roles or [])

    permission_class = type(
        f'RolePermission_{uuid.uuid4().hex}',
        (BaseRolePermission,),
        {
            'allowed_roles': allowed_roles,
            'disallowed_roles': disallowed_roles,
        }
    )
    return permission_class
