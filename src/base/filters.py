from django.db import models
from django_filters import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.filters import SearchFilter


class CustomSearchFilter(SearchFilter):
    """

    This class is a custom search filter that extends the `SearchFilter` class from the `rest_framework.filters` module.
    It provides a method `get_search_fields` that returns the search fields based on the action.
    """
    def get_search_fields(self, view, request):
        """
        Returns the search fields according to the action
        """
        action_search = getattr(view, 'action_search', None)

        if action_search and view.action in action_search:

            return action_search[view.action]

        return super().get_search_fields(view, request)


class ListCharFilter(BaseInFilter, CharFilter):
    pass


class SearchForDjangoCryptography(CustomSearchFilter):
    """
    Search filter for model with encrypted fields using django-cryptography
    """

    @staticmethod
    def check_lookup(search_field: str):
        if '__' in search_field:
            return search_field.split('__')

    def get_attr_value(self, obj: models.Model, search_field: str):
        lookup = self.check_lookup(search_field)

        if not lookup:
            return getattr(obj, search_field, None)

        value = obj
        for field in lookup:
            value = getattr(obj, field, None)

            if not value:
                return

        return value

    def filter_queryset(self, request, queryset, view):
        search_fields = self.get_search_fields(view, request)
        search_terms = self.get_search_terms(request)

        if not search_fields or not search_terms:
            return queryset

        matching_ids = []

        for obj in queryset.iterator():
            appropriate = False

            for field in search_fields:
                if appropriate:
                    break

                for search_term in search_terms:
                    field_value = self.get_attr_value(obj, field)

                    if field_value and search_term.lower() in str(field_value).lower():
                        appropriate = True

            if appropriate:
                matching_ids.append(obj.id)

        queryset = queryset.filter(id__in=matching_ids)

        return queryset
