import copy
import datetime
from dataclasses import dataclass
from decimal import Decimal

import numpy as np
from django.db.models import F, Q
from django.utils import timezone

from base.tools import TimeManager
from only_fans_db.models import (
    Distance,
    MassMessages,
    MessagesList,
    MessagesListTemp,
    ReachStatsV2,
    Sales,
    TingzSales,
)
from only_fans_models.models import OnlyFansModel
from only_fans_models_plan.models import ExpectedResult
from shifts.models import (
    DBSale,
    Shift,
    ShiftResult,
)


@dataclass
class ChatsCounter:
    """
    Class for counting chats
    """

    total_chats = 0
    active_chats = 0
    replied_chats = 0
    not_replied_chats = 0
    chats_with_paid_content = 0


class DataCollectorMixin:
    @staticmethod
    def _convert_fields_from_utc(data: list[dict], fields: list[str]) -> list[dict]:
        """
        Convert datetime fields from UTC

        Args:
            data (dict): data
            fields (list[str]): fields to add timezone info
        """
        for row in data:
            for field in fields:
                if row.get(field):
                    row[field] = TimeManager.time_from_utc(row[field])

        return data

    def _group_data_by_model_ids(self, rows_data: list[dict]) -> dict[int, list]:
        result = {model.model_id: [] for model in self.shifts_models}

        for data in rows_data:
            try:
                result[data.pop('model_id')].append(data)

            except KeyError:
                pass

        return result


class OnlyFansDBDataCollector(DataCollectorMixin):
    """
    Service for collecting data from only_fans_db

    Args:
        start_time (datetime): bound for the start of the time range for which statistics are collected
        end_time (datetime): bound for the end of the time range for which statistics are collected
    """

    def __init__(
        self,
        start_time: datetime,
        shifts_models: list[OnlyFansModel],
        end_time: datetime = None,
        use_temp_data: bool = True,
    ) -> None:
        self.start_time = start_time
        self.end_time = end_time if end_time is not None else timezone.now()
        self.utc_start_time = TimeManager.time_to_utc(self.start_time)
        self.utc_end_time = TimeManager.time_to_utc(self.end_time)
        self.shifts_models = shifts_models
        self.use_temp_data = use_temp_data
        self._messages_data = self._get_messages_data()
        self._mass_messages_data = self._get_mass_messages_data()

    @property
    def messages_data(self) -> dict[int, list]:
        """
        Get messages data for shifts
        """

        return copy.deepcopy(self._messages_data)

    @property
    def mass_messages_data(self) -> dict[int, list]:
        """
        Get mass messages data for shifts
        """

        return copy.deepcopy(self._mass_messages_data)

    def _get_messages_data(self) -> dict:
        """
        Get messages data for shift models in time range
        """
        db_table = MessagesListTemp if self.use_temp_data else MessagesList

        data = list(
            db_table.objects.filter(
                model_id__in=[model.model_id for model in self.shifts_models],
                message_time__range=(self.utc_start_time, self.utc_end_time),
                fan_id__isnull=False,
            )
            .order_by('message_time')
            .values(
                'model_id',
                'message_time',
                'is_incoming',
                'fan_id',
                'is_mass_message',
                'message_id',
                'is_paid',
                'have_content',
                'message_text',
            )
        )
        data = self._convert_fields_from_utc(data, ['message_time'])

        return self._group_data_by_model_ids(data)

    def _get_mass_messages_data(self) -> dict:
        """
        Get mass messages data for shift models in time range
        """
        data = list(
            MassMessages.objects.filter(
                model_id__in=[model.model_id for model in self.shifts_models],
                date_time__range=(self.start_time, self.end_time),
            )
            .order_by('date_time')
            .values('model_id', 'date_time', 'id')
        )

        return self._group_data_by_model_ids(data)


class InfoService:
    """
    Service for collecting info about shift

    Args:
        shift (Shift): shift
        end_time (datetime): bound for the end of the time range for which statistics are collected
    """

    MIN_MESSAGES_LIMIT_IN_SESSION = 3
    MAX_ACTIVE_SESSION_DURATION = datetime.timedelta(hours=2)

    def __init__(
        self,
        messages_data: dict[int, list[dict]],
        mass_messages_data: dict[int, list[dict]],
        shift: Shift,
        end_time: datetime = None,
    ) -> None:
        self.shift = shift
        self.start_time = (
            self.shift.shift_start_fact
            if self.shift.shift_start_fact
            else self.shift.shift_start
        )
        self.end_time = end_time if end_time is not None else timezone.now()
        self.shift_results = self._get_all_shift_results()
        self.shift_models = self._get_all_shift_models()
        self.shift_models_ids = [model.model_id for model in self.shift_models]
        self.excluded_time_ranges_for_models = (
            self._get_excluded_time_ranges_for_models()
        )
        self.sales_data = self._get_sales_data()
        self.tingz_sales_data = self._get_tingz_sales_data()
        self.messages_data = self.exclude_data_for_models_in_excluded_time_ranges(
            messages_data, 'message_time'
        )
        self.mass_messages_data = self.exclude_data_for_models_in_excluded_time_ranges(
            mass_messages_data, 'date_time'
        )

    def get_active_sessions(
        self,
        model_id: int,
        start_time: datetime = None,
        end_time: datetime = None,
    ) -> int:
        """
        Get active sessions with fans for model

        Args:
            model_id(int): model_id from only_fans_db
            start_time(datetime): start of the time range (start_fact from shift result)
            end_time(datetime): end of the time range
        """
        if model_id not in self.messages_data:
            return 0

        messages_from_fans = self._get_all_messages_with_fans(
            model_id=model_id,
            start_time=start_time,
            end_time=end_time,
        )

        active_sessions = self._count_active_sessions_from_messages_from_fans(
            messages_from_fans=messages_from_fans
        )

        return sum(active_sessions.values())

    def _count_active_sessions_from_messages_from_fans(
        self, messages_from_fans: dict[int, list]
    ) -> dict[int, int]:
        """
        Count active sessions from messages from fans

        Args:
            messages_from_fans(dict[int, list]): messages from fans
        """
        active_sessions = {}

        for fan_id, message_times in messages_from_fans.items():
            current_message_time = message_times[0]
            number_messages_in_session = 1
            count_sessions = 0
            last_message_time = message_times[-1]

            for message_time in message_times[1:]:
                if (
                    message_time - current_message_time
                    > self.MAX_ACTIVE_SESSION_DURATION
                    or message_time == last_message_time
                ):

                    if number_messages_in_session >= self.MIN_MESSAGES_LIMIT_IN_SESSION:
                        count_sessions += 1

                    number_messages_in_session = 1
                else:
                    number_messages_in_session += 1

                current_message_time = message_time

            if count_sessions:
                active_sessions[fan_id] = count_sessions

        return active_sessions

    def _get_all_messages_with_fans(
        self,
        model_id: int,
        content: bool = False,
        only_incoming: bool = True,
        start_time: datetime = None,
        end_time: datetime = None,
    ) -> dict[int, list]:
        """
        Get all messages from fans for model

        Args:
            model_id(int): model_id from only_fans_db
            content(bool): get messages with content
            start_time(datetime): start of the time range (start_fact from shift result)
            end_time(datetime): end of the time range
        """
        messages_from_fans = {}
        start_time = start_time if start_time else self.start_time
        end_time = end_time if end_time else self.end_time

        for message in self.messages_data[model_id]:
            try:
                message_time = message['message_time']

                if message_time > end_time:
                    break

                if (
                    start_time < message_time < end_time
                    and message['is_mass_message'] is False
                ):
                    if message['is_incoming']:
                        messages_from_fans.setdefault(message['fan_id'], []).append(
                            message_time if not content else message
                        )
                    else:
                        if not only_incoming:
                            messages_from_fans.setdefault(message['fan_id'], []).append(
                                message_time if not content else message
                            )
            except KeyError:
                continue

        return messages_from_fans

    def _group_data_by_model_ids_with_model_tingz_username(
        self, rows_data: list[dict]
    ) -> dict[int, list]:
        """
        Group data by model ids with model username

        Args:
            rows_data(list[dict]): data from only_fans_db
        """
        model_username_to_id = {
            model.tingz_username.lower(): model.model_id
            for model in self.shift_models
            if model.tingz_username
        }

        result = {}

        for data in rows_data:
            try:
                model_username = data.pop('model_username')

                if not model_username:
                    continue

                model_username = model_username.lower()
                model_id = model_username_to_id.get(model_username)

                if model_id:
                    result.setdefault(model_id, []).append(data)

            except KeyError:
                pass

        return result

    def _group_data_by_model_ids(self, rows_data: list[dict]) -> dict[int, list]:
        result = {model_id: [] for model_id in self.shift_models_ids}

        for data in rows_data:
            try:
                result[data.pop('model_id')].append(data)

            except KeyError:
                pass

        return result

    def _get_tingz_sales_data(self):
        all_tingz_sales = (
            self.shift.sales.filter(db_sale__sale_source=DBSale.SaleSource.TINGZ)
            .order_by('db_sale__trans_date')
            .values(
                'amount',
                trans_id=F('db_sale__trans_id'),
                model_username=F('db_sale__model_username'),
                trans_date=F('db_sale__trans_date'),
                fan_username=F('db_sale__fan_username'),
            )
        )

        return self._group_data_by_model_ids_with_model_tingz_username(all_tingz_sales)

    def _get_sales_data(self):
        all_sales = (
            self.shift.sales.filter(db_sale__sale_source=DBSale.SaleSource.ONLYFANS)
            .order_by('db_sale__trans_date')
            .values(
                'amount',
                trans_id=F('db_sale__trans_id'),
                model_id=F('db_sale__model_id'),
                trans_date=F('db_sale__trans_date'),
                sale_type=F('db_sale__sale_type'),
                fan_id=F('db_sale__fan_id'),
            )
        )

        return self._group_data_by_model_ids(all_sales)

    def exclude_data_for_models_in_excluded_time_ranges(
        self,
        data: dict[int, list[dict]],
        time_detect_field: str,
    ) -> dict[int, list[dict]]:
        """
        Exclude data for models in excluded time ranges if they are exist

        Args:
            data(list[dict]): data
            time_detect_field(str): field for detect time to exclude
        """
        if not self.excluded_time_ranges_for_models:
            return data

        cleaned_result = copy.deepcopy(data)

        for (
            model_id,
            excluded_time_ranges,
        ) in self.excluded_time_ranges_for_models.items():
            cleaned_data = []
            model_data = data.get(model_id, [])

            for row in model_data:
                try:
                    data_time = row[time_detect_field]
                except KeyError:
                    continue

                exclude = False

                for start_time, end_time in excluded_time_ranges:
                    if end_time and start_time <= data_time <= end_time:
                        exclude = True
                        continue

                    if not end_time and data_time >= start_time:
                        exclude = True
                        continue

                if not exclude:
                    cleaned_data.append(row)

            cleaned_result[model_id] = cleaned_data

        return cleaned_result

    def _get_excluded_time_ranges_for_models(self) -> dict[int, list[list]]:
        """
        Get excluded times for models if number of models is not equal to number of shift results
        """
        excluded_time_ranges_for_models = {}

        if len(self.shift_models) == len(self.shift_results):
            return excluded_time_ranges_for_models

        for model in self.shift_models:
            model_results = self._get_model_shift_results(model)

            if len(model_results) <= 1:
                continue

            results_time_ranges = [
                (result.shift_start_fact, result.shift_end_fact)
                for result in model_results
            ]

            for start_fact, end_fact in results_time_ranges:
                if not excluded_time_ranges_for_models.get(model.model_id):
                    excluded_time_ranges_for_models[model.model_id] = [[end_fact, None]]
                else:
                    last_excluded_range = excluded_time_ranges_for_models[
                        model.model_id
                    ][-1]

                    if last_excluded_range[1] is None and start_fact is not None:
                        excluded_time_ranges_for_models[model.model_id][-1][
                            1
                        ] = start_fact
                    if last_excluded_range[1] is not None and end_fact is not None:
                        excluded_time_ranges_for_models[model.model_id].append(
                            [end_fact, None]
                        )

        return excluded_time_ranges_for_models

    def _get_all_shift_results(self) -> list[ShiftResult]:
        """
        Get all shift results
        """
        shift_results = list(self.shift.results.all())

        return shift_results

    def _get_shift_model(self, model_id: int) -> OnlyFansModel:
        """
        Get shift model
        """
        return list(
            filter(lambda model: model.model_id == model_id, self.shift_models)
        )[0]

    def _get_all_shift_models(self) -> list[OnlyFansModel]:
        """
        Get all shift models
        """
        return list(set(self.shift.only_fans_models.all()))

    def _get_sales_amount_for_model_in_time_range(
        self,
        model_id: int,
        start_time: datetime,
        end_time: datetime,
    ) -> int:
        """
        Get  sales amount for model in time range

        Args:
            model_id(int): model_id from only_fans_db
            start_time(datetime): start of the time range (start_fact from shift result)
            end_time(datetime): end of the time range
        """
        count = 0

        if model_id not in self.sales_data:
            return count

        for sale in self.sales_data[model_id]:
            try:
                if sale['trans_date'] > end_time:
                    break

                if start_time < sale['trans_date'] < end_time:
                    count += sale['amount']

            except KeyError:
                continue

        return round(count)

    def _get_of_payments_count_for_model_in_time_range(
        self,
        model_id: int,
        start_time: datetime,
        end_time: datetime,
    ) -> int:
        """
        Get of payments count for model in time range

        Args:
            model_id(int): model_id from only_fans_db
            start_time(datetime): start of the time range (start_fact from shift result)
            end_time(datetime): end of the time range
        """
        count = 0

        if model_id not in self.sales_data:
            return count

        for sale in self.sales_data[model_id]:
            try:
                if sale['trans_date'] > end_time:
                    break

                if start_time < sale['trans_date'] < end_time:
                    count += 1

            except KeyError:
                continue

        return count

    def get_tingz_sales_by_model(
        self,
        model_id: int,
        start_time: datetime = None,
        end_time: datetime = None,
    ) -> list:
        """
        Get tingz sales for model

        Args:
            model_id(int): model_id from only_fans_db
            start_time(datetime): start of the time range (start_fact from shift result)
            end_time(datetime): end of the time range
        """
        result = []
        start_time = start_time if start_time else self.start_time
        end_time = end_time if end_time else self.end_time

        if model_id not in self.tingz_sales_data:
            return result

        for sale in self.tingz_sales_data[model_id]:
            try:
                trans_date = sale['trans_date']

                if trans_date > self.end_time:
                    break

                if start_time < trans_date < end_time:
                    result.append(sale)

            except KeyError:
                continue

        return result

    def get_tingz_sales_amount(
        self, model_id: int, start_time: datetime = None, end_time: datetime = None
    ) -> int:
        """
        Get tingz sales amount for model

        Args:
            model_id(int): model_id from only_fans_db
            start_time(datetime): start of the time range (start_fact from shift result)
            end_time(datetime): end of the time range (end_fact from shift result)
        """

        return sum(
            sale['amount']
            for sale in self.get_tingz_sales_by_model(model_id, start_time, end_time)
        )

    def _get_tingz_payments_count_for_model_in_time_range(
        self, model_id: int, start_time: datetime = None, end_time: datetime = None
    ) -> int:
        """
        Get tingz payments count for model in time range

        Args:
            model_id(int): model_id from only_fans_db
            start_time(datetime): start of the time range (start_fact from shift result)
            end_time(datetime): end of the time range (end_fact from shift result)
        """

        return len(self.get_tingz_sales_by_model(model_id, start_time, end_time))

    def _get_model_shift_results(self, model: type[OnlyFansModel]) -> list[ShiftResult]:
        """
        Get model shift result

        Args:
            model(OnlyFansModel): model
        """
        shift_results = list(
            filter(
                lambda result: result.only_fans_model_id == model.id, self.shift_results
            )
        )

        if len(shift_results) > 1:
            shift_results = sorted(
                shift_results,
                key=lambda result: (
                    result.shift_start_fact is None,
                    result.shift_start_fact,
                ),
            )

        return shift_results

    def _get_model_shift_start_fact(self, model_id: int) -> datetime:
        """
        Get model shift start fact

        Args:
            model_id(int): model id
        """
        model = self._get_shift_model(model_id=model_id)
        model_results = self._get_model_shift_results(model)

        min_shift_start_fact = None

        if not model_results:
            return min_shift_start_fact

        if len(model_results) == 1:
            return model_results[0].shift_start_fact

        for result in model_results:
            if result.shift_start_fact is not None:
                if min_shift_start_fact is None:
                    min_shift_start_fact = result.shift_start_fact
                else:
                    min_shift_start_fact = min(
                        min_shift_start_fact, result.shift_start_fact
                    )

        return min_shift_start_fact

    def _get_model_shift_end_fact(self, model_id: int) -> datetime:
        """
        Get model shift end fact

        Args:
            model_id(int): model id
        """
        model = self._get_shift_model(model_id=model_id)
        model_results = self._get_model_shift_results(model)

        max_shift_end_fact = None

        if not model_results:
            return max_shift_end_fact

        if len(model_results) == 1:
            return model_results[0].shift_end_fact

        for result in model_results:
            if result.shift_end_fact is not None:
                if max_shift_end_fact is None:
                    max_shift_end_fact = result.shift_end_fact
                else:
                    max_shift_end_fact = max(max_shift_end_fact, result.shift_end_fact)
            if result.shift_start_fact is not None and result.shift_end_fact is None:
                return None

        return max_shift_end_fact

    def __process_outgoing_messages(
        self,
        chats_timedeltas: dict[int, list[tuple]],
        fan_id: int,
        message_time: datetime,
        all_timedeltas: list[datetime.timedelta],
    ) -> None:
        """
        Process outgoing messages

        Args:
            chats_timedeltas(dict[int, list[tuple]]): dict with chats timedeltas
            fan_id(int): fan id
            message_time(datetime): message time
            all_timedeltas(list[datetime.timedelta]): list with all timedeltas
        """
        if chats_timedeltas.get(fan_id) is not None:
            if chats_timedeltas[fan_id][-1][1] is None:
                timedelta = message_time - chats_timedeltas[fan_id][-1][0]
                chats_timedeltas[fan_id][-1] = (
                    chats_timedeltas[fan_id][-1][0],
                    timedelta,
                )
                all_timedeltas.append(timedelta)

    def __process_incoming_messages(
        self, chats_timedeltas: dict[int, list[tuple]], fan_id: int, message_time
    ) -> None:
        """
        Process incoming messages

        Args:
            chats_timedeltas(dict[int, list[tuple]]): dict with chats timedeltas
            fan_id(int): fan id
            message_time(datetime): message time
        """
        if chats_timedeltas.get(fan_id) is None:
            chats_timedeltas[fan_id] = [(message_time, None)]
        else:
            if chats_timedeltas[fan_id][-1][1] is not None:
                chats_timedeltas[fan_id].append((message_time, None))

    def _get_chats_timedeltas(
        self,
        model_id: int,
        model_shift_start_fact: datetime,
        model_shift_end_fact: datetime = None,
    ) -> tuple[list, dict]:
        """
        Get chats timedeltas

        Args:
            model_id(int): model id
            model_shift_start_fact(datetime): model shift start fact
        """
        all_timedeltas = []
        chats_timedeltas = {}

        if not model_shift_end_fact:
            model_shift_end_fact = self.end_time

        for message in self.messages_data.get(model_id, []):
            is_mass_message = message['is_mass_message']
            message_time = message['message_time']
            fan_id = message['fan_id']
            is_incoming = message['is_incoming']

            if (
                is_mass_message is True
                or message_time < model_shift_start_fact
                or message_time > model_shift_end_fact
            ):
                continue

            if is_incoming is True:
                self.__process_incoming_messages(chats_timedeltas, fan_id, message_time)

            if is_incoming is False:
                self.__process_outgoing_messages(
                    chats_timedeltas, fan_id, message_time, all_timedeltas
                )

        return all_timedeltas, chats_timedeltas

    @staticmethod
    def _calculate_average_timedelta(
        timedelta_list: list[datetime.timedelta],
    ) -> datetime.timedelta:
        """
        Calculate average time

        Args:
            timedelta_list(list[datetime]): list of timedeltas for model
        """
        if not timedelta_list:
            return datetime.timedelta()

        return sum(timedelta_list, datetime.timedelta()) / len(timedelta_list)

    def __generate_average_speed_answer_by_chats(
        self, chats_timedeltas: dict[int, list[tuple]]
    ) -> datetime.timedelta:
        """
        Generate average speed answer by chats

        Args:
            chats_timedeltas(dict[int, list[datetime]]): dict with chats timedeltas
        """
        if not chats_timedeltas:
            return datetime.timedelta()

        average_speed_answer_by_chats = []

        for chat, results in chats_timedeltas.items():
            chat_timedeltas = []

            for result in results:
                if result[1] is not None:
                    chat_timedeltas.append(result[1])

            if chat_timedeltas:
                average_speed_answer_by_chats.append(
                    self._calculate_average_timedelta(chat_timedeltas)
                )

        return self._calculate_average_timedelta(average_speed_answer_by_chats)

    @staticmethod
    def _calculate_median_timedelta(
        timedelta_list: list[datetime.timedelta],
    ) -> datetime.timedelta:
        """
        Calculate median timedelta

        Args:
            timedelta_list(list[datetime]): list of timedeltas for model
        """
        if not timedelta_list:
            return datetime.timedelta()

        timedeltas_in_seconds = [td.total_seconds() for td in timedelta_list]
        median_seconds = np.median(timedeltas_in_seconds)

        return datetime.timedelta(seconds=median_seconds)

    def __generate_median_speed_answer_by_chats(
        self, chats_timedeltas: dict[int, list[tuple]]
    ) -> datetime.timedelta:
        """
        Generate median speed answer by chats

        Args:
            chats_timedeltas(dict[int, list[datetime]]): dict with chats timedeltas
        """
        if not chats_timedeltas:
            return datetime.timedelta()

        average_speed_answer_by_chats = []

        for chat, results in chats_timedeltas.items():
            chat_timedeltas = []

            for result in results:
                if result[1] is not None:
                    chat_timedeltas.append(result[1])

            if chat_timedeltas:
                average_speed_answer_by_chats.append(
                    self._calculate_median_timedelta(chat_timedeltas)
                )

        return self._calculate_median_timedelta(average_speed_answer_by_chats)

    def _get_median_speed_answer(self, model_id: int) -> tuple[datetime, datetime]:
        """
        Get median speed answer for model

        Args:
            model_id(int): model_id from only_fans_db
        """
        model_shift_start_fact = self._get_model_shift_start_fact(model_id)
        model_shift_end_fact = self._get_model_shift_end_fact(model_id)

        if not model_shift_start_fact:
            return datetime.timedelta(), datetime.timedelta()

        all_timedeltas, chats_timedeltas = self._get_chats_timedeltas(
            model_id=model_id,
            model_shift_start_fact=model_shift_start_fact,
            model_shift_end_fact=model_shift_end_fact,
        )

        median_speed_answer = self._calculate_median_timedelta(all_timedeltas)
        median_speed_answer_by_chats = self.__generate_median_speed_answer_by_chats(
            chats_timedeltas
        )

        return median_speed_answer, median_speed_answer_by_chats

    def _get_average_speed_answer(self, model_id: int) -> tuple[datetime, datetime]:
        """
        Get average speed answer for model

        Args:
            model_id(int): model_id from only_fans_db
        """
        model_shift_start_fact = self._get_model_shift_start_fact(model_id)
        model_shift_end_fact = self._get_model_shift_end_fact(model_id)

        if not model_shift_start_fact:
            return datetime.timedelta(), datetime.timedelta()

        all_timedeltas, chats_timedeltas = self._get_chats_timedeltas(
            model_id=model_id,
            model_shift_start_fact=model_shift_start_fact,
            model_shift_end_fact=model_shift_end_fact,
        )

        average_speed_answer = self._calculate_average_timedelta(all_timedeltas)
        average_speed_answer_by_chats = self.__generate_average_speed_answer_by_chats(
            chats_timedeltas
        )

        return average_speed_answer, average_speed_answer_by_chats

    @staticmethod
    def _set_chats_counter(
        counter: ChatsCounter,
        has_incoming_message: bool,
        has_outgoing_message: bool,
        has_paid_content: bool,
    ) -> None:
        """
        Set chats counter attributes

        Attrs:
            counter(ChatsCounter): chats counter
            has_incoming_message(bool): has incoming message
            has_outgoing_message(bool): has outgoing message
            has_paid_content(bool): has paid content
        """
        counter.total_chats += 1

        if has_incoming_message and has_outgoing_message:
            counter.replied_chats += 1

        if has_outgoing_message and not has_incoming_message:
            counter.not_replied_chats += 1

        if has_paid_content:
            counter.chats_with_paid_content += 1

    def calculate_chats(
        self,
        model_id: int,
        start_time: datetime = None,
        end_time: datetime = None,
    ) -> ChatsCounter:
        """
        Calculate count total, active, replied, not replied chats,
        count chats with paid content

        Args:
            model_id(int): model_id from only_fans_db
            start_time(datetime): start of the time range (start_fact from shift result)
            end_time(datetime): end of the time range
        """
        counter = ChatsCounter()

        if not self.messages_data.get(model_id):
            return counter

        messages_from_fans = self._get_all_messages_with_fans(
            model_id=model_id,
            content=True,
            only_incoming=False,
            start_time=start_time,
            end_time=end_time,
        )
        counter.active_chats = self.get_active_sessions(
            model_id=model_id, start_time=start_time, end_time=end_time
        )

        for fan_id, messages in messages_from_fans.items():
            try:
                has_incoming_message = messages[0]['is_incoming']
                has_outgoing_message = not messages[0]['is_incoming']
                has_paid_content = messages[0]['is_paid']

                for message in messages[1:]:
                    if message['is_incoming']:
                        has_incoming_message = True
                    else:
                        has_outgoing_message = True

                    if message['is_paid']:
                        has_paid_content = True

                self._set_chats_counter(
                    counter=counter,
                    has_incoming_message=has_incoming_message,
                    has_outgoing_message=has_outgoing_message,
                    has_paid_content=has_paid_content,
                )

            except KeyError:
                continue

        return counter

    @staticmethod
    def _check_fan_is_new(fan_id: int, model_id: int, start_time: datetime) -> bool:
        """
        Check if fan is new based on previous sales
        """
        return not Sales.objects.filter(
            fan_id=fan_id,
            model_id=model_id,
            trans_date__lt=TimeManager.time_to_utc(start_time),
        ).exists()

    def get_fans_count_and_sales(
        self, model_id: int, start_time: datetime, end_time: datetime
    ) -> dict[str, dict[str, int]]:
        """
        Number and amount sales of new and old fans who made purchases during the shift

        Args:
            model_id(int): model_id from only_fans_db
            start_time(datetime): start of the time range (start_fact from shift result)
            end_time(datetime): end of the time range (end_fact from shift result)
        """
        start_time = start_time or self.start_time

        result = {
            'new_fans': {'count': 0, 'sales': 0},
            'old_fans': {'count': 0, 'sales': 0},
        }
        check_fans = {}
        fans_counted = set()

        for sale in self.sales_data.get(model_id, []):
            try:
                if sale['trans_date'] > end_time:
                    break

                if start_time < sale['trans_date'] < end_time:
                    if (fan_id := sale['fan_id']) not in check_fans:
                        is_new = self._check_fan_is_new(
                            fan_id=fan_id, model_id=model_id, start_time=start_time
                        )
                        check_fans[fan_id] = is_new

                    if check_fans[fan_id]:
                        if fan_id not in fans_counted:
                            fans_counted.add(fan_id)
                            result['new_fans']['count'] += 1
                        result['new_fans']['sales'] += sale['amount']
                    else:
                        if fan_id not in fans_counted:
                            fans_counted.add(fan_id)
                            result['old_fans']['count'] += 1
                        result['old_fans']['sales'] += sale['amount']

            except KeyError:
                continue
        result['new_fans']['sales'] = round(result['new_fans']['sales'])
        result['old_fans']['sales'] = round(result['old_fans']['sales'])

        return result

    def group_sales_by_fan_id(
        self,
        model_id: int,
        sale_types: list[int] = None,
        start_time: datetime = None,
        end_time: datetime = None,
    ) -> dict:
        """
        Group sales by fan_id

        Args:
            model_id(int): model_id from only_fans_db
            sale_types(list[int]): list of sale types
            start_time(datetime): start of the time range (start_fact from shift result)
            end_time(datetime): end of the time range
        """
        result_dict = {}
        start_time = start_time if start_time else self.start_time
        end_time = end_time if end_time else self.end_time

        model_sales = self._get_sales_for_model(
            model_id=model_id,
            sale_types=sale_types,
            start_time=start_time,
            end_time=end_time,
        )

        for sale in model_sales:
            result_dict.setdefault(sale['fan_id'], []).append(sale)

        return result_dict

    def group_tingz_sales_by_fan_username(
        self, model_id: int, start_time: datetime = None, end_time: datetime = None
    ) -> dict:
        """
        Group tingz sales by fan_username

        Args:
            model_id(int): model_id from only_fans_db
            start_time(datetime): start of the time range (start_fact from shift result)
            end_time(datetime): end of the time range
        """
        result_dict = {}
        start_time = start_time if start_time else self.start_time
        end_time = end_time if end_time else self.end_time

        tingz_sales = self.get_tingz_sales_by_model(
            model_id=model_id, start_time=start_time, end_time=end_time
        )

        for sale in tingz_sales:
            result_dict.setdefault(sale['fan_username'], []).append(sale)

        return result_dict

    def _get_sales_for_model(
        self,
        model_id: int,
        sale_types: list[int] = None,
        start_time: datetime = None,
        end_time: datetime = None,
    ) -> list[dict]:
        """
        Get sales for model

        Args:
            model_id(int): model_id from only_fans_db
            sale_types(list[int]): list of sale types
            start_time(datetime): start of the time range (start_fact from shift result)
            end_time(datetime): end of the time range
        """
        result = []

        if model_id not in self.sales_data:
            return result

        start_time = start_time if start_time else self.start_time
        end_time = end_time if end_time else self.end_time

        for sale in self.sales_data[model_id]:
            try:
                trans_date = sale['trans_date']

                if trans_date > end_time:
                    break

                if start_time < trans_date < end_time:
                    if not sale_types:
                        result.append(sale)
                    elif sale['type'] in sale_types:
                        result.append(sale)

            except KeyError:
                continue

        return result


class DetailTotalInfoService(InfoService):
    """
    Service for collecting statistics for models from the only_fans_db

    Args:
        shift (Shift): shift
        time_intervals (list[tuple[datetime, datetime]]): time intervals for shift in hours
    """

    def __init__(
        self,
        time_intervals: list[tuple[datetime, datetime]],
        shift: Shift,
        messages_data: dict[int, list[dict]],
        mass_messages_data: dict[int, list[dict]],
        **kwargs,
    ) -> None:
        super().__init__(
            shift=shift,
            messages_data=messages_data,
            mass_messages_data=mass_messages_data,
            **kwargs,
        )
        self.shift_time_intervals = time_intervals

    def _check_model_is_active_on_the_shift(self, model_id: int) -> bool:
        """
        Check model is on the shift
        """
        shift_model = self._get_shift_model(model_id)
        return bool(
            list(
                filter(
                    lambda result: (
                        result.only_fans_model_id == shift_model.id
                        and result.shift_start_fact is not None
                    ),
                    self.shift_results,
                )
            )
        )

    def _get_count_unique_chats_in_time_range(
        self,
        model_id: int,
        start_time: datetime,
        end_time: datetime,
    ) -> int:
        """
        Get count unique chats for model in time range

        Args:
            model_id(int): model_id from only_fans_db
            start_time(datetime): start of the time range (start_fact from shift result)
            end_time(datetime): end of the time range
        """
        uniq_fans = {}

        if model_id not in self.messages_data:
            return 0

        for message in self.messages_data[model_id]:
            try:
                if message['message_time'] > end_time:
                    break

                if (
                    start_time < message['message_time'] < end_time
                    and message['is_mass_message'] is False
                ):
                    fan_from_dict = uniq_fans.setdefault(message['fan_id'], {})

                    if message['is_incoming'] is True:
                        fan_from_dict['incoming'] = True

                    if message['is_incoming'] is False:
                        fan_from_dict['outgoing'] = True

            except KeyError:
                continue

        return sum(
            1
            for fan in uniq_fans.values()
            if fan.get('incoming', False) and fan.get('outgoing', False)
        )

    def _get_count_mass_messages_in_time_range(
        self,
        model_id: int,
        start_time: datetime,
        end_time: datetime,
    ) -> int:
        """
        Get count mass messages for model in time range

        Args:
            model_id(int): model_id from only_fans_db
            start_time(datetime): start of the time range (start_fact from shift result)
            end_time(datetime): end of the time range
        """
        messages_set = set()

        if model_id not in self.mass_messages_data:
            return 0

        for message in self.mass_messages_data[model_id]:
            try:
                if message['date_time'] > end_time:
                    break

                if start_time < message['date_time'] < end_time:
                    messages_set.add(message['id'])

            except KeyError:
                continue

        return len(messages_set)

    def _get_count_outgoing_messages_in_time_range(
        self,
        model_id: int,
        start_time: datetime,
        end_time: datetime,
    ) -> int:
        """
        Get count outgoing messages for model in time range

        Args:
            model_id(int): model_id from only_fans_db
            start_time(datetime): start of the time range (start_fact from shift result)
            end_time(datetime): end of the time range
        """
        messages_set = set()

        if model_id not in self.messages_data:
            return 0

        for message in self.messages_data[model_id]:
            try:
                if message['message_time'] > end_time:
                    break

                if (
                    start_time < message['message_time'] < end_time
                    and message['is_mass_message'] is False
                    and message['is_incoming'] is False
                ):
                    messages_set.add(message['message_id'])

            except KeyError:
                continue

        return len(messages_set)

    @staticmethod
    def _adjust_start_time_according_to_fact_start_time(
        start_time: datetime,
        model_shift_start_fact: datetime,
        end_time: datetime,
    ) -> datetime:
        """
        Adjust start time according to fact start time

        Args:
            start_time(datetime): start of the time range (start_fact from shift result)
            model_shift_start_fact(datetime): start_fact
            end_time(datetime): end of the time range
        """
        if start_time < model_shift_start_fact < end_time:
            start_time = model_shift_start_fact

        return start_time

    @staticmethod
    def _adjust_end_time_according_to_fact_end_time(
        start_time: datetime,
        model_shift_end_fact: datetime,
        end_time: datetime,
    ) -> datetime:
        """
        Adjust end time according to fact end time

        Args:
            start_time(datetime): start of the time range (start_fact from shift result)
            model_shift_end_fact(datetime): end_fact
            end_time(datetime): end of the time range
        """
        if model_shift_end_fact and start_time < model_shift_end_fact < end_time:
            end_time = model_shift_end_fact

        return end_time

    def count_total_statistic_per_model(
        self,
        detail_statistic_per_model: list[
            dict[str, [int, str, list[dict[str, [str, int]]]]]
        ],
    ) -> list[dict[str, [int, str, datetime.timedelta, list[dict[str, [str, int]]]]]]:
        """
        Count total statistic per model

        Args:
            detail_statistic_per_model(list[dict[str, [int, str, list[dict[str, [str, int]]]]]]):
                detail models statistic
        """
        result = copy.deepcopy(detail_statistic_per_model)

        for model_statistic in result:
            model_statistic.update(
                {
                    'total_sales_amount': 0,
                    'count_unique_chats': 0,
                    'count_mass_messages': 0,
                    'count_outgoing_messages': 0,
                    'average_speed_answer': datetime.timedelta(),
                    'average_speed_answer_by_chat': datetime.timedelta(),
                    'total_tingz_sales_amount': 0,
                }
            )

            for hourly_statistic in model_statistic['detail']:
                model_statistic['count_mass_messages'] += hourly_statistic[
                    'count_mass_messages'
                ]
                model_statistic['count_outgoing_messages'] += hourly_statistic[
                    'count_outgoing_messages'
                ]

            model_id = model_statistic['model_id']
            model_shift_start_fact = self._get_model_shift_start_fact(model_id=model_id)
            model_shift_end_fact = self._get_model_shift_end_fact(model_id=model_id)

            if model_shift_start_fact:
                model_statistic[
                    'count_unique_chats'
                ] = self._get_count_unique_chats_in_time_range(
                    model_id=model_id,
                    start_time=model_shift_start_fact,
                    end_time=model_shift_end_fact
                    if model_shift_end_fact
                    else self.end_time,
                )
                total_sales_amount = self._get_sales_amount_for_model_in_time_range(
                    model_id=model_id,
                    start_time=model_shift_start_fact,
                    end_time=model_shift_end_fact
                    if model_shift_end_fact
                    else self.end_time,
                )
                total_tingz_sales_amount = self.get_tingz_sales_amount(
                    model_id=model_id,
                    start_time=model_shift_start_fact,
                    end_time=model_shift_end_fact
                    if model_shift_end_fact
                    else self.end_time,
                )
                model_statistic['total_tingz_sales_amount'] = total_tingz_sales_amount
                model_statistic['total_sales_amount'] = (
                    total_sales_amount + total_tingz_sales_amount
                )

            (
                average_speed_answer,
                average_speed_answer_by_chat,
            ) = self._get_average_speed_answer(model_id=model_statistic['model_id'])
            model_statistic['average_speed_answer'] = average_speed_answer
            model_statistic[
                'average_speed_answer_by_chat'
            ] = average_speed_answer_by_chat

            # additional metrics for bot
            chats_calculator = self.calculate_chats(
                model_id=model_id,
                start_time=model_shift_start_fact,
                end_time=model_shift_end_fact
                if model_shift_end_fact
                else self.end_time,
            )
            active_chats = chats_calculator.active_chats
            replied_chats = chats_calculator.replied_chats

            model_statistic['active_chats'] = active_chats
            model_statistic['replied_conversion'] = (
                round(active_chats / replied_chats, 2) if replied_chats else 0
            )

            chats_with_payments = len(
                self.group_sales_by_fan_id(
                    model_id,
                    start_time=model_shift_start_fact,
                    end_time=model_shift_end_fact,
                ).keys()
            )

            model_statistic['chats_with_payments'] = chats_with_payments
            model_statistic['payment_conversion'] = (
                round(chats_with_payments / active_chats, 2) if active_chats else 0
            )
            model_statistic['revenue_per_fan'] = (
                round(model_statistic['total_sales_amount'] / chats_with_payments, 2)
                if chats_with_payments
                else 0
            )

            chats_with_paid_content = chats_calculator.chats_with_paid_content
            model_statistic['chats_with_paid_content'] = chats_with_paid_content
            model_statistic['paid_content_conversion'] = (
                round(chats_with_paid_content / active_chats, 2) if active_chats else 0
            )

            fans_count_and_sales = self.get_fans_count_and_sales(
                model_id,
                model_shift_start_fact,
                model_shift_end_fact if model_shift_end_fact else self.end_time,
            )
            model_statistic['new_fans_count'] = fans_count_and_sales['new_fans'][
                'count'
            ]
            model_statistic['old_fans_count'] = fans_count_and_sales['old_fans'][
                'count'
            ]
            model_statistic['new_fans_sales'] = fans_count_and_sales['new_fans'][
                'sales'
            ]
            model_statistic['old_fans_sales'] = fans_count_and_sales['old_fans'][
                'sales'
            ]

        return result

    def _get_last_operator_message_time(self) -> datetime:
        """
        Get last operator message time
        """
        last_operator_message_time = None
        shift_models_ids = [model.model_id for model in self.shift_models]

        for model_id in self.messages_data:
            if model_id not in shift_models_ids:
                continue

            for message in self.messages_data[model_id]:
                if message['is_incoming'] is True:
                    continue

                if (
                    last_operator_message_time is None
                    or message['message_time'] > last_operator_message_time
                ):
                    last_operator_message_time = message['message_time']

        return last_operator_message_time

    def count_total_statistic(
        self,
        total_statistic_per_model: list[
            dict[str, [int, str, datetime.timedelta, list[dict[str, [str, int]]]]]
        ],
    ) -> dict[str, [int, datetime.timedelta]]:
        """
        Count total statistic for all models

        Args:
            total_statistic_per_model(list[dict[str, [int, str, datetime, list[dict[str, [str, int]]]]]]):
        """
        result = {}
        total_mass_messages = 0
        total_outgoing_messages = 0
        total_unique_chats = 0
        average_speed_answer_list = []
        average_speed_answer_by_chat_list = []

        for model_statistic in total_statistic_per_model:
            total_mass_messages += model_statistic['count_mass_messages']
            total_outgoing_messages += model_statistic['count_outgoing_messages']
            total_unique_chats += model_statistic['count_unique_chats']

            if (
                model_statistic['average_speed_answer'] != datetime.timedelta()
                and model_statistic['average_speed_answer_by_chat']
                != datetime.timedelta()
            ):
                average_speed_answer_list.append(
                    model_statistic['average_speed_answer']
                )

                average_speed_answer_by_chat_list.append(
                    model_statistic['average_speed_answer_by_chat']
                )

        result['total_mass_messages'] = total_mass_messages
        result['total_outgoing_messages'] = total_outgoing_messages
        result['total_unique_chats'] = total_unique_chats
        result['average_speed_answer'] = self._calculate_average_timedelta(
            average_speed_answer_list
        )
        result['average_speed_answer_by_chat'] = self._calculate_average_timedelta(
            average_speed_answer_by_chat_list
        )

        result['last_operator_message_time'] = (
            self._get_last_operator_message_time()
            if total_outgoing_messages
            else datetime.timedelta()
        )

        return result

    @staticmethod
    def get_model_current_expected_result(model: OnlyFansModel) -> ExpectedResult:
        for expected_result in model.expected_results.all():

            if (
                expected_result.period.start_date
                <= datetime.datetime.now().date()
                <= expected_result.period.end_date
            ):
                return expected_result

    def detail_statistic_per_model(
        self,
    ) -> list[dict[str, [int, str, list[dict[str, [str, int]]]]]]:
        """
        Get detail statistic per model
        """
        result = []

        for model in self.shift_models:
            model_shift_start_fact = self._get_model_shift_start_fact(
                model_id=model.model_id
            )

            model_shift_end_fact = self._get_model_shift_end_fact(
                model_id=model.model_id
            )

            model_expected_result = self.get_model_current_expected_result(model)
            model_expected_revenue = (
                model_expected_result.expected_revenue
                if model_expected_result
                else None
            )

            model_result = {
                "model_id": model.model_id,
                "expected_revenue": model_expected_revenue,
                "id": model.id,
                "name": model.username_of,
                "spammer_key": model.spammer_key,
                "detail": [],
                "ended": model_shift_end_fact is not None,
            }

            for start_time_range, end_time_range in self.shift_time_intervals:
                time_range_result = {
                    "time": f"{start_time_range.strftime('%H:%M')} -"
                    f" {end_time_range.strftime('%H:%M')}",
                }

                if (
                    not model_shift_start_fact
                    or model_shift_start_fact > end_time_range
                    or timezone.now() < start_time_range
                ) or (model_shift_end_fact and model_shift_end_fact < start_time_range):
                    time_range_result["total_sales_amount"] = 0
                    time_range_result["total_tingz_sales_amount"] = 0
                    time_range_result["count_unique_chats"] = 0
                    time_range_result["count_mass_messages"] = 0
                    time_range_result["count_outgoing_messages"] = 0
                    model_result['detail'].append(time_range_result)

                    continue

                start_time = self._adjust_start_time_according_to_fact_start_time(
                    start_time=start_time_range,
                    model_shift_start_fact=model_shift_start_fact,
                    end_time=end_time_range,
                )
                end_time = self._adjust_end_time_according_to_fact_end_time(
                    start_time=start_time_range,
                    model_shift_end_fact=model_shift_end_fact,
                    end_time=end_time_range,
                )

                total_sales_amount_per_hour = (
                    self._get_sales_amount_for_model_in_time_range(
                        model_id=model.model_id,
                        start_time=start_time,
                        end_time=end_time,
                    )
                )
                total_tingz_sales_amount_per_hour = self.get_tingz_sales_amount(
                    model_id=model.model_id,
                    start_time=start_time,
                    end_time=end_time,
                )
                time_range_result[
                    "total_tingz_sales_amount"
                ] = total_tingz_sales_amount_per_hour
                time_range_result["total_sales_amount"] = (
                    total_sales_amount_per_hour + total_tingz_sales_amount_per_hour
                )

                count_unique_chats_per_hour = (
                    self._get_count_unique_chats_in_time_range(
                        model_id=model.model_id,
                        start_time=start_time,
                        end_time=end_time,
                    )
                )
                time_range_result['count_unique_chats'] = count_unique_chats_per_hour

                count_mass_messages_per_hour = (
                    self._get_count_mass_messages_in_time_range(
                        model_id=model.model_id,
                        start_time=start_time,
                        end_time=end_time,
                    )
                )
                time_range_result['count_mass_messages'] = count_mass_messages_per_hour

                count_outgoing_messages_per_hour = (
                    self._get_count_outgoing_messages_in_time_range(
                        model_id=model.model_id,
                        start_time=start_time,
                        end_time=end_time,
                    )
                )
                time_range_result[
                    'count_outgoing_messages'
                ] = count_outgoing_messages_per_hour

                model_result['detail'].append(time_range_result)

            result.append(model_result)

        return result

    def get_full_statistic(self) -> dict[str, dict | int]:
        """
        Get full statistic
        """
        result = {}

        detail_statistic_per_model = self.detail_statistic_per_model()
        total_statistic_per_model = self.count_total_statistic_per_model(
            detail_statistic_per_model
        )
        total_statistic = self.count_total_statistic(total_statistic_per_model)

        result['models'] = total_statistic_per_model
        result['total_statistic'] = total_statistic

        return result


class RatingService(InfoService):
    """
    Service for collecting rating info
    """

    ROUND_VALUE = 1

    def get_sales_amount(
        self,
        model_id: int,
        start_time: datetime = None,
        end_time: datetime = None,
    ) -> int:
        """
        Get sales amount for model

        Args:
            model_id(int): model_id from only_fans_db
            start_time(datetime): start of the time range (start_fact from shift result)
            end_time(datetime): end of the time range
        """
        return self._get_sales_amount_for_model_in_time_range(
            model_id=model_id,
            start_time=start_time if start_time else self.start_time,
            end_time=end_time if end_time else self.end_time,
        )

    def _get_messages_with_content_for_model(
        self, model_id: int, start_time: datetime = None, end_time: datetime = None
    ) -> list[dict]:
        """
        Get messages with content for model

        Args:
            model_id(int): model_id from only_fans_db
            start_time(datetime): start of the time range (start_fact from shift result)
            end_time(datetime): end of the time range
        """
        start_time = start_time if start_time else self.start_time
        end_time = end_time if end_time else self.end_time

        result = []

        for message in self.messages_data[model_id]:
            try:
                message_time = message['message_time']

                if message_time > end_time:
                    break

                if (
                    start_time < message_time < end_time
                    and message['is_mass_message'] is False
                ):

                    if message.get('have_content'):
                        result.append(message)

            except KeyError:
                continue

        return result

    def get_sales_ratio(
        self,
        model_id: int,
        start_time: datetime = None,
        end_time: datetime = None,
    ) -> float:
        """
        Get sales ratio for model

        Args:
            model_id(int): model_id from only_fans_db
            start_time(datetime): start of the time range (start_fact from shift result)
            end_time(datetime): end of the time range
        """
        if model_id not in self.messages_data:
            return 0

        sales = self._get_sales_for_model(
            model_id=model_id, sale_types=[3], start_time=start_time, end_time=end_time
        )
        messages_with_content = self._get_messages_with_content_for_model(
            model_id=model_id, start_time=start_time, end_time=end_time
        )

        if not messages_with_content:
            return 0

        return round(len(sales) / len(messages_with_content), self.ROUND_VALUE)

    def get_response_ratio(
        self, model_id: int, start_time: datetime = None, end_time: datetime = None
    ) -> float:
        """
        Get chats ratio for model

        Args:
            model_id(int): model_id from only_fans_db
            start_time(datetime): start of the time range (start_fact from shift result)
            end_time(datetime): end of the time range
        """
        start_time = start_time if start_time else self.start_time
        end_time = end_time if end_time else self.end_time

        all_timedeltas, chats_timedeltas = self._get_chats_timedeltas(
            model_id=model_id,
            model_shift_start_fact=start_time,
            model_shift_end_fact=end_time,
        )

        if not all_timedeltas:
            return 0

        ratio = (
            len(all_timedeltas)
            * 60
            / int(sum(all_timedeltas, datetime.timedelta()).total_seconds())
        )

        return 1 if ratio >= 1 else round(ratio, self.ROUND_VALUE)

    def get_operator_ratio(
        self,
        sales_ratio: float,
        chats_count: int,
        response_ratio: float,
    ) -> float:
        """
        Get operator ratio for model

        Args:
            sales_ratio(float): sales ratio
            chats_count(int): chats count
            response_ratio(int): response ratio
        """
        return round(sales_ratio * chats_count * response_ratio, self.ROUND_VALUE)

    def get_rating_data(self) -> list[dict[str, str | int | float]]:
        """
        Get rating data
        """
        result = []

        for model in self.shift_models:
            model_shift_start_fact = self._get_model_shift_start_fact(model.model_id)
            model_shift_end_fact = self._get_model_shift_end_fact(model.model_id)
            sales_amount = self.get_sales_amount(
                model_id=model.model_id,
                start_time=model_shift_start_fact,
                end_time=model_shift_end_fact,
            )
            sales_ratio = self.get_sales_ratio(
                model_id=model.model_id,
                start_time=model_shift_start_fact,
                end_time=model_shift_end_fact,
            )
            active_sessions = self.get_active_sessions(
                model_id=model.model_id,
                start_time=model_shift_start_fact,
                end_time=model_shift_end_fact,
            )
            response_ratio = self.get_response_ratio(
                model_id=model.model_id,
                start_time=model_shift_start_fact,
                end_time=model_shift_end_fact,
            )
            operator_ratio = self.get_operator_ratio(
                sales_ratio=sales_ratio,
                chats_count=active_sessions,
                response_ratio=response_ratio,
            )

            result.append(
                {
                    "shift": self.shift,
                    "operator": self.shift.operator,
                    "only_fans_model": model,
                    "sales_amount": sales_amount,
                    "sales_ratio": sales_ratio,
                    "chats_count": active_sessions,
                    "response_ratio": response_ratio,
                    "operator_ratio": operator_ratio,
                }
            )

        return result


class OnlyFansDBDataCollectorWithDistance(OnlyFansDBDataCollector):
    def __init__(
        self,
        start_time: datetime,
        shifts_models: list[OnlyFansModel],
        end_time: datetime = None,
        use_temp_data: bool = True,
    ) -> None:
        super().__init__(
            start_time=start_time,
            shifts_models=shifts_models,
            end_time=end_time,
            use_temp_data=use_temp_data,
        )
        self._distance_data = self._get_distance_data()

    def _get_distance_data(self) -> dict[int, list]:
        """
        Get distance data for shift models in time range
        """

        data = list(
            Distance.objects.filter(
                model_id__in=[model.model_id for model in self.shifts_models],
                time__range=(self.start_time, self.end_time),
            )
            .order_by('time')
            .values('model_id', 'time', 'distance')
        )

        return self._group_data_by_model_ids(data)

    @property
    def distance_data(self) -> dict[int, list]:
        """
        Distance data
        """
        return copy.deepcopy(self._distance_data)


class StatsService(RatingService):
    """
    Service for collecting stats shift info
    """

    @dataclass
    class MessagesCounter:
        """
        Class for counting messages
        """

        incoming_messages_count: int = 0
        outgoing_messages_count: int = 0
        messages_with_content_count: int = 0
        messages_with_paid_content_count: int = 0
        outgoing_symbols_count: int = 0

    def __init__(self, distance_data: dict[int, list], *args, **kwargs) -> None:
        super().__init__(*args, **kwargs)
        self.distance_data = self.exclude_data_for_models_in_excluded_time_ranges(
            distance_data, 'time'
        )

    def calculate_messages(
        self,
        model_id: int,
        start_time: datetime = None,
        end_time: datetime = None,
    ) -> MessagesCounter:
        """
        Calculate count incoming and outgoing messages,
        count messages with content and with paid content,
        count outgoing symbols

        Args:
            model_id(int): model_id from only_fans_db
            start_time(datetime): start of the time range (start_fact from shift result)
            end_time(datetime): end of the time range
        """
        counter = self.MessagesCounter()
        start_time = start_time if start_time else self.start_time
        end_time = end_time if end_time else self.end_time

        if model_id not in self.messages_data:
            return counter

        for message in self.messages_data[model_id]:
            try:
                message_time = message['message_time']

                if message_time > end_time:
                    break

                if (
                    start_time < message_time < end_time
                    and message['is_mass_message'] is False
                ):
                    if message['is_incoming']:
                        counter.incoming_messages_count += 1

                    else:
                        counter.outgoing_messages_count += 1
                        counter.outgoing_symbols_count += len(
                            message['message_text'].replace(' ', '')
                        )

                        if message.get('have_content'):
                            counter.messages_with_content_count += 1

                        if message.get('is_paid'):
                            counter.messages_with_paid_content_count += 1

            except KeyError:
                continue

        return counter

    def get_afk_data(
        self, model_id: int, start_time: datetime = None, end_time: datetime = None
    ) -> tuple[datetime.timedelta, datetime.timedelta]:
        """
        Get afk data for model (afk_summ, max_afk_duration)

        Args:
            model_id(int): model_id from only_fans_db
            start_time(datetime): start of the time range (start_fact from shift result)
            end_time(datetime): end of the time range (end_fact from shift result)
        """
        start_time = start_time if start_time else self.start_time
        end_time = end_time if end_time else self.end_time

        afk_summ = datetime.timedelta()
        max_afk_duration = datetime.timedelta()

        start_afk = None
        previous_time = start_time

        for index, distance in enumerate(self.distance_data.get(model_id, [])):
            try:
                distance_time = distance['time']

                if distance_time > end_time:
                    break

                if start_time < distance_time < end_time:
                    distance = distance['distance']

                    if distance == 0:
                        afk_summ += distance_time - previous_time

                        if not start_afk:
                            start_afk = previous_time
                    else:
                        if start_afk:
                            max_afk_duration = max(
                                max_afk_duration, distance_time - start_afk
                            )
                            start_afk = None

                    previous_time = distance_time

                    if index == len(self.distance_data[model_id]) - 1 and start_afk:
                        max_afk_duration = max(
                            max_afk_duration, distance_time - start_afk
                        )

            except KeyError:
                continue

        return afk_summ, max_afk_duration

    def get_stats(self) -> list[dict[str, str | int | float]]:
        """
        Get statistic for ShiftStats
        """
        result = []

        for model in self.shift_models:
            model_id = model.model_id
            model_shift_start_fact = self._get_model_shift_start_fact(model_id)

            if not model_shift_start_fact:
                continue

            model_shift_end_fact = (
                self._get_model_shift_end_fact(model_id) or self.end_time
            )

            revenue = self.get_sales_amount(
                model_id, model_shift_start_fact, model_shift_end_fact
            )
            revenue_tingz = self.get_tingz_sales_amount(
                model_id, model_shift_start_fact, model_shift_end_fact
            )
            payments_count = self.get_payments_count(
                model_id, model_shift_start_fact, model_shift_end_fact
            )

            fans_count_and_sales = self.get_fans_count_and_sales(
                model_id, model_shift_start_fact, model_shift_end_fact
            )
            new_fans_count = fans_count_and_sales['new_fans']['count']
            old_fans_count = fans_count_and_sales['old_fans']['count']
            new_fans_sales = fans_count_and_sales['new_fans']['sales']
            old_fans_sales = fans_count_and_sales['old_fans']['sales']

            messages_counter = self.calculate_messages(
                model_id, model_shift_start_fact, model_shift_end_fact
            )
            incoming_messages_count = messages_counter.incoming_messages_count
            outgoing_messages_count = messages_counter.outgoing_messages_count
            messages_with_content_count = messages_counter.messages_with_content_count
            messages_with_paid_content_count = (
                messages_counter.messages_with_paid_content_count
            )
            outgoing_symbols_count = messages_counter.outgoing_symbols_count

            chats_counter = self.calculate_chats(
                model_id, model_shift_start_fact, model_shift_end_fact
            )
            total_chats = chats_counter.total_chats
            active_chats = chats_counter.active_chats
            replied_chats = chats_counter.replied_chats
            not_replied_chats = chats_counter.not_replied_chats
            chats_with_paid_content = chats_counter.chats_with_paid_content

            paid_messages = len(
                self._get_sales_for_model(
                    model_id=model_id,
                    sale_types=[3],
                    start_time=model_shift_start_fact,
                    end_time=model_shift_end_fact,
                )
            )
            chats_with_payments = len(
                self.group_sales_by_fan_id(
                    model_id,
                    start_time=model_shift_start_fact,
                    end_time=model_shift_end_fact,
                ).keys()
            )
            chats_tingz = len(
                self.group_tingz_sales_by_fan_username(
                    model_id,
                    start_time=model_shift_start_fact,
                    end_time=model_shift_end_fact,
                ).keys()
            )

            (
                time_to_respond_by_message,
                time_to_respond_by_chat,
            ) = self._get_average_speed_answer(model_id)
            (
                median_time_to_respond_by_message,
                median_time_to_respond_by_chat,
            ) = self._get_median_speed_answer(model_id)

            afk_summ, max_afk_duration = self.get_afk_data(
                model_id, model_shift_start_fact, model_shift_end_fact
            )

            result.append(
                {
                    'shift': self.shift,
                    'only_fans_model': model,
                    'operator': self.shift.operator,
                    'revenue': revenue,
                    'revenue_tingz': revenue_tingz,
                    'incoming_messages': incoming_messages_count,
                    'outgoing_messages': outgoing_messages_count,
                    'messages_with_content': messages_with_content_count,
                    'messages_with_paid_content': messages_with_paid_content_count,
                    'paid_messages': paid_messages,
                    'chats_with_payments': chats_with_payments,
                    'outgoing_symbols': outgoing_symbols_count,
                    'total_chats': total_chats,
                    'active_chats': active_chats,
                    'replied_chats': replied_chats,
                    'not_replied_chats': not_replied_chats,
                    'chats_with_paid_content': chats_with_paid_content,
                    'chats_tingz': chats_tingz,
                    'time_to_respond_by_message': time_to_respond_by_message,
                    'time_to_respond_by_chat': time_to_respond_by_chat,
                    'afk_summ': afk_summ,
                    'max_afk_duration': max_afk_duration,
                    'median_time_to_respond_by_message': median_time_to_respond_by_message,
                    'median_time_to_respond_by_chat': median_time_to_respond_by_chat,
                    'payments_count': payments_count,
                    'new_fans_count': new_fans_count,
                    'old_fans_count': old_fans_count,
                    'new_fans_sales': new_fans_sales,
                    'old_fans_sales': old_fans_sales,
                }
            )

        return result

    def get_payments_count(
        self, model_id: int, start_time: datetime, end_time: datetime
    ) -> int:
        """
        Get payments count for model

        Args:
            model_id(int): model_id from only_fans_db
            start_time(datetime): start of the time range (start_fact from shift result)
            end_time(datetime): end of the time range (end_fact from shift result)
        """
        return self._get_of_payments_count_for_model_in_time_range(
            model_id=model_id, start_time=start_time, end_time=end_time
        ) + self._get_tingz_payments_count_for_model_in_time_range(
            model_id=model_id, start_time=start_time, end_time=end_time
        )


class ProfitService(StatsService):
    def __init__(self, *args, **kwargs) -> None:
        super().__init__(*args, **kwargs)
        self.models_profit_percentage = self._get_models_profit_percentage()

    def _get_models_profit_percentage(self) -> dict[int, dict]:
        """
        Get models profit percentage
        """

        return {
            model.model_id: {
                'of_percentage': model.category.balance_profit.percentage_of_profit,
                'tingz_percentage': model.category.balance_profit.percentage_tingz_profit,
            }
            for model in self.shift_models
        }

    @staticmethod
    def calculate_profit(amount: int, percentage: Decimal) -> int:
        """
        Calculate profit

        Args:
            amount(int): amount
            percentage(float): percentage
        """
        return round(amount * percentage / 100)

    def get_profits(self) -> list[dict[str, str | int | float]]:
        result = []

        for model in self.shift_models:
            model_id = model.model_id
            model_shift_start_fact = self._get_model_shift_start_fact(model_id)
            model_shift_end_fact = self._get_model_shift_end_fact(model_id)

            of_amount = self.get_sales_amount(
                model_id, model_shift_start_fact, model_shift_end_fact
            )
            of_percentage = self.models_profit_percentage[model_id]['of_percentage']
            of_profit = self.calculate_profit(of_amount, of_percentage)

            tingz_amount = self.get_tingz_sales_amount(
                model_id, model_shift_start_fact, model_shift_end_fact
            )
            tingz_percentage = self.models_profit_percentage[model_id][
                'tingz_percentage'
            ]
            tingz_profit = self.calculate_profit(tingz_amount, tingz_percentage)

            total_amount = of_amount + tingz_amount
            total_profit = of_profit + tingz_profit

            result.append(
                {
                    'shift_date': self.shift.shift_date,
                    'shift': self.shift,
                    'only_fans_model': model,
                    'only_fans_model_name': model.username_of,
                    'operator': self.shift.operator,
                    'operator_name': self.shift.operator_name,
                    'team_lead': self.shift.team_lead,
                    'team_lead_name': self.shift.team_lead.full_name,
                    'model_category': model.category,
                    'model_category_name': model.category.name,
                    'of_amount': of_amount,
                    'of_profit': of_profit,
                    'tingz_amount': tingz_amount,
                    'tingz_profit': tingz_profit,
                    'total_amount': total_amount,
                    'total_profit': total_profit,
                }
            )

        return result


class OnlyFansDBSalesCollector(DataCollectorMixin):
    """
    A specialized data collector focused on retrieving sales data from the OnlyFansDB database.
    This collector prioritizes sales and tingz sales data, and omits message-related data.
    """

    def __init__(
        self,
        start_time: datetime,
        shifts_models: list[OnlyFansModel],
        end_time: datetime = None,
        *args,
        **kwargs,
    ) -> None:
        self.start_time = start_time
        self.end_time = end_time if end_time is not None else timezone.now()
        self.utc_start_time = TimeManager.time_to_utc(self.start_time)
        self.utc_end_time = TimeManager.time_to_utc(self.end_time)
        self.shifts_models = shifts_models
        self._sales_data = self._get_sales_data()
        self._tingz_sales_data = self._get_tingz_sales_data()

    @property
    def sales_data(self) -> dict[int, list]:
        """
        Get sales data for shifts
        """
        return copy.deepcopy(self._sales_data)

    def _get_sales_data(self) -> dict[int, list]:
        """
        Get sales data for shift models in time range
        """
        data = list(
            Sales.objects.filter(
                model_id__in=[model.model_id for model in self.shifts_models],
                trans_date__range=(self.utc_start_time, self.utc_end_time),
                amount__isnull=False,
            )
            .order_by('trans_date')
            .values(
                'trans_id',
                'model_id',
                'amount',
                'trans_date',
                'message_id',
                'type',
                'fan_id',
            )
        )
        data = self._convert_fields_from_utc(data, ['trans_date'])

        return self._group_data_by_model_ids(data)

    @property
    def tingz_sales_data(self) -> dict[int, list]:
        """
        Tingz sales data
        """
        return copy.deepcopy(self._tingz_sales_data)

    def _get_tingz_sales_data(self) -> dict[int, list]:
        """
        Get tingz sales data for shift models in time range
        """
        models_tingz_usernames = [
            model.tingz_username.lower()
            for model in self.shifts_models
            if model.tingz_username
        ]

        q_tingz_usernames = Q()
        for tingz_username in models_tingz_usernames:
            q_tingz_usernames |= Q(model_username__iexact=tingz_username)

        data = list(
            TingzSales.objects.filter(
                q_tingz_usernames,
                created_at__range=(self.utc_start_time, self.utc_end_time),
                amount__isnull=False,
            )
            .order_by('created_at')
            .values(
                'trans_id',
                'model_username',
                'amount',
                'fan_username',
                'model_username',
                'created_at',
            )
        )
        data = self._convert_fields_from_utc(data, ['created_at'])

        return self._group_data_by_model_ids_with_model_tingz_username(data)

    def _group_data_by_model_ids_with_model_tingz_username(
        self, rows_data: list[dict]
    ) -> dict[int, list]:
        """
        Group data by model ids with model username

        Args:
            rows_data(list[dict]): data from only_fans_db
        """
        model_username_to_id = {
            model.tingz_username.lower(): model.model_id
            for model in self.shifts_models
            if model.tingz_username
        }

        result = {}

        for data in rows_data:
            try:
                model_username = data.pop('model_username')

                if not model_username:
                    continue

                model_username = model_username.lower()
                model_id = model_username_to_id.get(model_username)

                if model_id:
                    result.setdefault(model_id, []).append(data)

            except KeyError:
                pass

        return result


class OnlyFansDBSalesSubscribersCollector(OnlyFansDBSalesCollector):
    """
    A specialized data collector focused on retrieving sales data and subscribers data from the OnlyFansDB database.

    Inherits:
        OnlyFansDBSalesCollector
    """

    def __init__(
        self,
        start_time: datetime,
        shifts_models: list[OnlyFansModel],
        end_time: datetime = None,
        *args,
        **kwargs,
    ) -> None:
        super().__init__(
            start_time=start_time,
            shifts_models=shifts_models,
            end_time=end_time,
            *args,
            **kwargs,
        )
        self._subscribers_data = self._get_subscribers_data()

    def _get_subscribers_data(self) -> dict[int, list]:
        """
        Get subscribers data for shift models in time range
        """
        data = list(
            ReachStatsV2.objects.filter(
                model_id__in=[model.model_id for model in self.shifts_models],
                insert_date__range=(self.utc_start_time, self.utc_end_time),
                all_subs__isnull=False,
            )
            .order_by('insert_date')
            .values('insert_date', 'model_id', 'all_subs')
            .distinct('insert_date', 'model_id')
        )
        data = self._convert_fields_from_utc(data, ['insert_date'])

        return self._group_data_by_model_ids(data)

    @property
    def subscribers_data(self) -> dict[int, list]:
        """
        Get subscribers data for shifts
        """
        return copy.deepcopy(self._subscribers_data)
