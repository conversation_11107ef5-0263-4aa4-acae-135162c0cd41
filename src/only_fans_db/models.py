from django.contrib.postgres.fields import ArrayField
from django.db import models


class Content(models.Model):
    content_id = models.BigIntegerField(primary_key=True)
    model = models.ForeignKey('Models', models.DO_NOTHING, blank=True, null=True)
    content_type = models.CharField(max_length=255, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True)
    categories = models.CharField(max_length=255, blank=True, null=True)
    url = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'content'


class ExtensionHeaders(models.Model):
    model_id = models.IntegerField(unique=True, blank=True, null=True)
    headers = models.JSONField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'extension_headers'


class Fans(models.Model):
    fan_id = models.AutoField(primary_key=True)
    fan_username = models.Char<PERSON>ield(max_length=255, blank=True, null=True)
    is_exist = models.BooleanField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'fans'


class FansModels(models.Model):
    fan = models.OneToOneField(Fans, models.DO_NOTHING, primary_key=True)
    model = models.ForeignKey('Models', models.DO_NOTHING)
    total_summ = models.FloatField(blank=True, null=True)
    sub_date = models.DateTimeField(blank=True, null=True)
    trial_links = ArrayField(models.DateTimeField())
    trial_dates = models.JSONField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'fans_models'
        unique_together = (('fan', 'model'),)


class MassMessages(models.Model):
    date_time = models.DateTimeField(blank=True, null=True)
    message_text = models.TextField(blank=True, null=True)
    message_hash = models.TextField(blank=True, null=True)
    model = models.ForeignKey('Models', models.DO_NOTHING, blank=True, null=True)
    send = models.IntegerField(blank=True, null=True)
    viewed = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'mass_messages'


class MessagesList(models.Model):
    message_id = models.BigIntegerField(primary_key=True)
    fan = models.ForeignKey(Fans, models.DO_NOTHING, blank=True, null=True)
    message_time = models.DateTimeField(blank=True, null=True)
    model = models.ForeignKey('Models', models.DO_NOTHING, blank=True, null=True)
    is_incoming = models.BooleanField(blank=True, null=True)
    message_text = models.TextField(blank=True, null=True)
    have_content = models.BooleanField(blank=True, null=True)
    content = models.ForeignKey(Content, models.DO_NOTHING, blank=True, null=True)
    text_hash = models.TextField(blank=True, null=True)
    is_read = models.BooleanField(blank=True, null=True)
    is_paid = models.BooleanField(blank=True, null=True)
    is_mass_message = models.BooleanField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'messages_list'


class MessagesListTemp(models.Model):
    """
    Duplicate messages_list table stores data for the last 3 days
    """
    message_id = models.BigIntegerField(primary_key=True)
    fan = models.ForeignKey(Fans, models.DO_NOTHING, blank=True, null=True)
    message_time = models.DateTimeField(blank=True, null=True)
    model = models.ForeignKey('Models', models.DO_NOTHING, blank=True, null=True)
    is_incoming = models.BooleanField(blank=True, null=True)
    message_text = models.TextField(blank=True, null=True)
    have_content = models.BooleanField(blank=True, null=True)
    content = models.ForeignKey(Content, models.DO_NOTHING, blank=True, null=True)
    text_hash = models.TextField(blank=True, null=True)
    is_read = models.BooleanField(blank=True, null=True)
    is_paid = models.BooleanField(blank=True, null=True)
    is_mass_message = models.BooleanField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'messages_list_temp'


class Models(models.Model):
    model_id = models.IntegerField(primary_key=True)
    email = models.CharField(max_length=255, blank=True, null=True)
    username = models.CharField(max_length=255, blank=True, null=True)
    headers = models.JSONField()
    proxies = models.TextField()
    selfie = models.BooleanField()
    is_active = models.BooleanField(blank=True, null=True)
    is_sfs = models.BooleanField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'models'


class OnlineStats(models.Model):
    id = models.IntegerField(primary_key=True)
    model = models.ForeignKey(Models, models.DO_NOTHING)
    date = models.DateTimeField()
    online_count = models.IntegerField()
    hour = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'online_stats'


class ReachStats(models.Model):
    update_date = models.DateTimeField()
    model = models.ForeignKey(Models, models.DO_NOTHING, blank=True, null=True)
    ofrate = models.FloatField(blank=True, null=True)
    visitors = models.IntegerField(blank=True, null=True)
    visits = models.IntegerField(blank=True, null=True)
    view_duration = models.FloatField(blank=True, null=True)
    subs = models.IntegerField(blank=True, null=True)
    renews = models.IntegerField(blank=True, null=True)
    visit_by_countries = models.JSONField(blank=True, null=True)
    total_followers = models.IntegerField(blank=True, null=True)
    total_subscribers = models.IntegerField(blank=True, null=True)
    post_q = models.IntegerField(blank=True, null=True)
    post_likes = models.IntegerField(blank=True, null=True)
    post_comments = models.IntegerField(blank=True, null=True)
    post_views = models.IntegerField(blank=True, null=True)
    story_q = models.IntegerField(blank=True, null=True)
    story_likes = models.IntegerField(blank=True, null=True)
    story_comments = models.IntegerField(blank=True, null=True)
    story_viewers = models.IntegerField(blank=True, null=True)
    id = models.BigAutoField(primary_key=True)

    class Meta:
        managed = False
        db_table = 'reach_stats'


class ReachStatsOld(models.Model):
    datetime = models.DateTimeField(blank=True, null=True)
    model = models.ForeignKey(Models, models.DO_NOTHING, blank=True, null=True)
    ofrate = models.FloatField(blank=True, null=True)
    visitors = models.IntegerField(blank=True, null=True)
    visits = models.IntegerField(blank=True, null=True)
    view_duration = models.FloatField(blank=True, null=True)
    subs = models.IntegerField(blank=True, null=True)
    renews = models.IntegerField(blank=True, null=True)
    visit_by_countries = models.TextField(blank=True, null=True)
    total_followers = models.IntegerField(blank=True, null=True)
    total_subscibers = models.IntegerField(blank=True, null=True)
    post_q = models.IntegerField(blank=True, null=True)
    post_likes = models.IntegerField(blank=True, null=True)
    post_comments = models.IntegerField(blank=True, null=True)
    post_views = models.IntegerField(blank=True, null=True)
    story_q = models.IntegerField(blank=True, null=True)
    story_likes = models.IntegerField(blank=True, null=True)
    story_comments = models.IntegerField(blank=True, null=True)
    story_viewers = models.IntegerField(blank=True, null=True)
    id = models.BigAutoField(primary_key=True)

    class Meta:
        managed = False
        db_table = 'reach_stats_old'


class Sales(models.Model):
    trans_id = models.CharField(primary_key=True, max_length=64)
    trans_date = models.DateTimeField(blank=True, null=True)
    amount = models.FloatField(blank=True, null=True)
    fan = models.ForeignKey(Fans, models.DO_NOTHING, blank=True, null=True)
    model = models.ForeignKey(Models, models.DO_NOTHING, blank=True, null=True)
    type = models.CharField(max_length=255, blank=True, null=True)
    message = models.ForeignKey(MessagesListTemp, models.DO_NOTHING, blank=True, null=True)
    insert_date = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'sales'


class TingzSales(models.Model):
    trans_id = models.TextField(primary_key=True)
    created_at = models.DateTimeField()
    fan_username = models.CharField(max_length=255)
    amount = models.IntegerField()
    payment_comment = models.CharField(max_length=255, blank=True, null=True)
    model_username = models.CharField(max_length=255)

    class Meta:
        managed = False
        db_table = 'tingz_sales'


class TrialLinks(models.Model):
    creation_date = models.DateTimeField(blank=True, null=True)
    model = models.ForeignKey(Models, models.DO_NOTHING, blank=True, null=True)
    claims_count = models.IntegerField(blank=True, null=True)
    link_url = models.CharField(max_length=255, primary_key=True)

    class Meta:
        managed = False
        db_table = 'trial_links'


class Distance(models.Model):
    model_id = models.IntegerField()
    distance = models.DecimalField(max_digits=5, decimal_places=2)
    time = models.DateTimeField()

    class Meta:
        managed = False
        db_table = 'distance'


class ReachStatsV2(models.Model):
    id = models.BigIntegerField(primary_key=True)
    model = models.ForeignKey('Models', models.DO_NOTHING, blank=True, null=True)
    visitors = models.IntegerField(blank=True, null=True)
    of_rate = models.DecimalField(max_digits=5, decimal_places=2, blank=True, null=True)
    new_subs = models.IntegerField(blank=True, null=True)
    all_subs = models.IntegerField(blank=True, null=True)
    renew_subs = models.IntegerField(blank=True, null=True)
    active_fans = models.IntegerField(blank=True, null=True)
    of_tv = models.IntegerField(blank=True, null=True)
    posts_count = models.IntegerField(blank=True, null=True)
    insert_date = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'reach_stats_v2'


class CampaignLinks(models.Model):
    creation_date = models.DateTimeField(blank=True, null=True)
    model = models.ForeignKey(Models, models.DO_NOTHING, blank=True, null=True)
    claims_count = models.IntegerField(blank=True, null=True)
    link_url = models.CharField(max_length=255, primary_key=True, db_column='url')

    class Meta:
        managed = False
        db_table = 'campaign_links'


class CampaignUsers(models.Model):
    id = models.BigIntegerField(primary_key=True)
    campaign_link = models.ForeignKey(
        CampaignLinks,
        models.DO_NOTHING,
        blank=True,
        null=True,
        db_column='camp_url'
    )
    model = models.ForeignKey(Models, models.DO_NOTHING, blank=True, null=True)
    fan = models.ForeignKey(Fans, models.DO_NOTHING, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'campaign_users'


class PromotionsDetails(models.Model):
    promotion_id = models.BigIntegerField(primary_key=True)
    model = models.ForeignKey(Models, models.DO_NOTHING, blank=True, null=True)
    promotion_name = models.CharField(max_length=255, blank=True, null=True)
    promotion_date = models.DateTimeField(blank=True, null=True)
    claims_count = models.IntegerField(blank=True, null=True)
    type_subscription = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'promotions_details'


class PayoutRequests(models.Model):
    invoice_id = models.BigAutoField(primary_key=True)
    created_at = models.DateTimeField(blank=True, null=True)
    model = models.ForeignKey(Models, models.DO_NOTHING, blank=True, null=True)
    amount = models.IntegerField(blank=True, null=True)
    status = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'payout_requests'


class Promos(models.Model):
    id = models.BigAutoField(primary_key=True)
    promo_id = models.BigIntegerField(blank=True, null=True)
    check_date = models.DateTimeField(blank=True, null=True)
    status = models.BooleanField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'promos'
