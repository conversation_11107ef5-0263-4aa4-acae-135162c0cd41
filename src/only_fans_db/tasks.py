from datetime import timedelta

from django.utils import timezone

from base.tools import TimeManager
from core.celery import BaseTaskWithRetry
from core.celery import app as celery_app
from only_fans_db.models import MessagesListTemp


@celery_app.task(bind=BaseTaskWithRetry)
def list_messages_temp_cleaner(self):
    qs = MessagesListTemp.objects.using('only_fans_db').filter(
        message_time__lt=TimeManager.time_to_utc(timezone.now() - timedelta(hours=24 * 1))
    )
    result = qs.delete()

    return f'cleaned {result[0]}'
