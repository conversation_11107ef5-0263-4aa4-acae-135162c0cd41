# Generated by Django 4.2.2 on 2023-07-19 15:26

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('only_fans_db', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='content',
            options={'managed': False},
        ),
        migrations.AlterModelOptions(
            name='extensionheaders',
            options={'managed': False},
        ),
        migrations.AlterModelOptions(
            name='fans',
            options={'managed': False},
        ),
        migrations.AlterModelOptions(
            name='fansmodels',
            options={'managed': False},
        ),
        migrations.AlterModelOptions(
            name='massmessages',
            options={'managed': False},
        ),
        migrations.AlterModelOptions(
            name='messageslist',
            options={'managed': False},
        ),
        migrations.AlterModelOptions(
            name='models',
            options={'managed': False},
        ),
        migrations.AlterModelOptions(
            name='onlinestats',
            options={'managed': False},
        ),
        migrations.AlterModelOptions(
            name='reachstats',
            options={'managed': False},
        ),
        migrations.AlterModelOptions(
            name='reachstatsold',
            options={'managed': False},
        ),
        migrations.AlterModelOptions(
            name='sales',
            options={'managed': False},
        ),
        migrations.AlterModelOptions(
            name='tingzsales',
            options={'managed': False},
        ),
        migrations.CreateModel(
            name='MessagesListTemp',
            fields=[
                ('message_id', models.BigIntegerField(primary_key=True, serialize=False)),
                ('message_time', models.DateTimeField(blank=True, null=True)),
                ('is_incoming', models.BooleanField(blank=True, null=True)),
                ('message_text', models.TextField(blank=True, null=True)),
                ('have_content', models.BooleanField(blank=True, null=True)),
                ('text_hash', models.TextField(blank=True, null=True)),
                ('is_read', models.BooleanField(blank=True, null=True)),
                ('is_paid', models.BooleanField(blank=True, null=True)),
                ('is_mass_message', models.BooleanField(blank=True, null=True)),
                ('content', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='only_fans_db.content')),
                ('fan', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='only_fans_db.fans')),
                ('model', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='only_fans_db.models')),
            ],
            options={
                'db_table': 'messages_list_temp',
                'managed': True,
            },
        ),
    ]
