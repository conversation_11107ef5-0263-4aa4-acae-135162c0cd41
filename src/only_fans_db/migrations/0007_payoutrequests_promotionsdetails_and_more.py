# Generated by Django 4.2.2 on 2024-06-20 21:16
import django
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("only_fans_db", "0006_campaignlinks_alter_reachstatsv2_options_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="PayoutRequests",
            fields=[
                ("invoice_id", models.BigAutoField(primary_key=True, serialize=False)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                (
                    "model",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to="only_fans_db.models",
                    ),
                ),
                ("amount", models.IntegerField(blank=True, null=True)),
                ("status", models.CharField(blank=True, max_length=255, null=True)),
            ],
            options={
                "db_table": "payout_requests",
            },
        ),
        migrations.CreateModel(
            name="PromotionsDetails",
            fields=[
                (
                    "promotion_id",
                    models.BigIntegerField(primary_key=True, serialize=False),
                ),
                (
                    "model",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to="only_fans_db.models",
                    ),
                ),
                (
                    "promotion_name",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("promotion_date", models.DateTimeField(blank=True, null=True)),
                ("claims_count", models.IntegerField(blank=True, null=True)),
                (
                    "type_subscription",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
            ],
            options={
                "db_table": "promotions_details",
            },
        )
    ]
