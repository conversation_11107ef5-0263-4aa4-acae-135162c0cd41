# Generated by Django 4.2.2 on 2023-10-19 13:21

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("only_fans_db", "0004_alter_sales_options_triallinks"),
    ]

    operations = [
        migrations.CreateModel(
            name="Distance",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("model_id", models.IntegerField()),
                ("distance", models.DecimalField(decimal_places=2, max_digits=5)),
                ("time", models.DateTimeField()),
            ],
            options={
                "db_table": "distance"
            },
        ),
        migrations.AlterModelOptions(
            name="triallinks",
            options={"managed": False},
        ),
        migrations.CreateModel(
            name="ReachStatsV2",
            fields=[
                ("id", models.BigIntegerField(primary_key=True, serialize=False)),
                ("visitors", models.IntegerField(blank=True, null=True)),
                (
                    "of_rate",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=5, null=True
                    ),
                ),
                ("new_subs", models.IntegerField(blank=True, null=True)),
                ("all_subs", models.IntegerField(blank=True, null=True)),
                ("renew_subs", models.IntegerField(blank=True, null=True)),
                ("active_fans", models.IntegerField(blank=True, null=True)),
                ("of_tv", models.IntegerField(blank=True, null=True)),
                ("posts_count", models.IntegerField(blank=True, null=True)),
                ("insert_date", models.DateTimeField(blank=True, null=True)),
                (
                    "model",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to="only_fans_db.models",
                    ),
                ),
            ],
            options={
                "db_table": "reach_stats_v2",
            },
        ),
    ]
