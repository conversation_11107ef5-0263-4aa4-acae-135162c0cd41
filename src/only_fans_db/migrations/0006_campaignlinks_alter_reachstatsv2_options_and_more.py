# Generated by Django 4.2.2 on 2023-12-26 19:09

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("only_fans_db", "0005_distance_alter_triallinks_options_reachstatsv2"),
    ]

    operations = [
        migrations.CreateModel(
            name="CampaignLinks",
            fields=[
                ("creation_date", models.DateTimeField(blank=True, null=True)),
                ("claims_count", models.IntegerField(blank=True, null=True)),
                (
                    "link_url",
                    models.Char<PERSON>ield(
                        db_column="url",
                        max_length=255,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "model",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to="only_fans_db.models",
                    ),
                ),
            ],
            options={
                "db_table": "campaign_links",
            },
        ),
        migrations.AlterModelOptions(
            name="reachstatsv2",
            options={"managed": False},
        ),
        migrations.CreateModel(
            name="CampaignUsers",
            fields=[
                ("id", models.BigIntegerField(primary_key=True, serialize=False)),
                (
                    "campaign_link",
                    models.ForeignKey(
                        blank=True,
                        db_column="camp_url",
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to="only_fans_db.campaignlinks",
                    ),
                ),
                (
                    "fan",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to="only_fans_db.fans",
                    ),
                ),
                (
                    "model",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to="only_fans_db.models",
                    ),
                ),
            ],
            options={
                "db_table": "campaign_users",
            },
        ),
    ]
