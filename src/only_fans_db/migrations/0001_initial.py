# Generated by Django 4.2.2 on 2023-07-17 07:50

import django.contrib.postgres.fields
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Content",
            fields=[
                (
                    "content_id",
                    models.BigIntegerField(primary_key=True, serialize=False),
                ),
                (
                    "content_type",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("categories", models.CharField(blank=True, max_length=255, null=True)),
                ("url", models.CharField(blank=True, max_length=255, null=True)),
            ],
            options={
                "db_table": "content",
                "managed": True,
            },
        ),
        migrations.CreateModel(
            name="ExtensionHeaders",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("model_id", models.Integer<PERSON>ield(blank=True, null=True, unique=True)),
                ("headers", models.J<PERSON><PERSON><PERSON>(blank=True, null=True)),
            ],
            options={
                "db_table": "extension_headers",
                "managed": True,
            },
        ),
        migrations.CreateModel(
            name="Fans",
            fields=[
                ("fan_id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "fan_username",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("is_exist", models.BooleanField(blank=True, null=True))
            ],
            options={
                "db_table": "fans",
                "managed": True,
            },
        ),
        migrations.CreateModel(
            name="MessagesList",
            fields=[
                (
                    "message_id",
                    models.BigIntegerField(primary_key=True, serialize=False),
                ),
                ("message_time", models.DateTimeField(blank=True, null=True)),
                ("is_incoming", models.BooleanField(blank=True, null=True)),
                ("message_text", models.TextField(blank=True, null=True)),
                ("have_content", models.BooleanField(blank=True, null=True)),
                ("text_hash", models.TextField(blank=True, null=True)),
                ("is_read", models.BooleanField(blank=True, null=True)),
                ("is_paid", models.BooleanField(blank=True, null=True)),
                ("is_mass_message", models.BooleanField(blank=True, null=True)),
                (
                    "content",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to="only_fans_db.content",
                    ),
                ),
                (
                    "fan",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to="only_fans_db.fans",
                    ),
                ),
            ],
            options={
                "db_table": "messages_list",
                "managed": True,
            },
        ),
        migrations.CreateModel(
            name="Models",
            fields=[
                ("model_id", models.IntegerField(primary_key=True, serialize=False)),
                ("email", models.CharField(blank=True, max_length=255, null=True)),
                ("username", models.CharField(blank=True, max_length=255, null=True)),
                ("headers", models.JSONField()),
                ("proxies", models.TextField()),
                ("selfie", models.BooleanField()),
                ("is_active", models.BooleanField(blank=True, null=True)),
                ("is_sfs", models.BooleanField(blank=True, null=True)),
            ],
            options={
                "db_table": "models",
                "managed": True,
            },
        ),
        migrations.CreateModel(
            name="TingzSales",
            fields=[
                ("trans_id", models.TextField(primary_key=True, serialize=False)),
                ("created_at", models.DateTimeField()),
                ("fan_username", models.CharField(max_length=255)),
                ("amount", models.IntegerField()),
                (
                    "payment_comment",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("model_username", models.CharField(max_length=255)),
            ],
            options={
                "db_table": "tingz_sales",
                "managed": True,
            },
        ),
        migrations.CreateModel(
            name="Sales",
            fields=[
                (
                    "trans_id",
                    models.CharField(max_length=64, primary_key=True, serialize=False),
                ),
                ("trans_date", models.DateTimeField(blank=True, null=True)),
                ("amount", models.FloatField(blank=True, null=True)),
                ("type", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "fan",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to="only_fans_db.fans",
                    ),
                ),
                (
                    "message",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to="only_fans_db.messageslist",
                    ),
                ),
                (
                    "model",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to="only_fans_db.models",
                    ),
                ),
                (
                    "insert_date",
                    models.DateTimeField(blank=True, null=True)
                )
            ],
            options={
                "db_table": "sales",
                "managed": True,
            },
        ),
        migrations.CreateModel(
            name="ReachStatsOld",
            fields=[
                ("datetime", models.DateTimeField(blank=True, null=True)),
                ("ofrate", models.FloatField(blank=True, null=True)),
                ("visitors", models.IntegerField(blank=True, null=True)),
                ("visits", models.IntegerField(blank=True, null=True)),
                ("view_duration", models.FloatField(blank=True, null=True)),
                ("subs", models.IntegerField(blank=True, null=True)),
                ("renews", models.IntegerField(blank=True, null=True)),
                ("visit_by_countries", models.TextField(blank=True, null=True)),
                ("total_followers", models.IntegerField(blank=True, null=True)),
                ("total_subscibers", models.IntegerField(blank=True, null=True)),
                ("post_q", models.IntegerField(blank=True, null=True)),
                ("post_likes", models.IntegerField(blank=True, null=True)),
                ("post_comments", models.IntegerField(blank=True, null=True)),
                ("post_views", models.IntegerField(blank=True, null=True)),
                ("story_q", models.IntegerField(blank=True, null=True)),
                ("story_likes", models.IntegerField(blank=True, null=True)),
                ("story_comments", models.IntegerField(blank=True, null=True)),
                ("story_viewers", models.IntegerField(blank=True, null=True)),
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                (
                    "model",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to="only_fans_db.models",
                    ),
                ),
            ],
            options={
                "db_table": "reach_stats_old",
                "managed": True,
            },
        ),
        migrations.CreateModel(
            name="ReachStats",
            fields=[
                ("update_date", models.DateTimeField()),
                ("ofrate", models.FloatField(blank=True, null=True)),
                ("visitors", models.IntegerField(blank=True, null=True)),
                ("visits", models.IntegerField(blank=True, null=True)),
                ("view_duration", models.FloatField(blank=True, null=True)),
                ("subs", models.IntegerField(blank=True, null=True)),
                ("renews", models.IntegerField(blank=True, null=True)),
                ("visit_by_countries", models.JSONField(blank=True, null=True)),
                ("total_followers", models.IntegerField(blank=True, null=True)),
                ("total_subscribers", models.IntegerField(blank=True, null=True)),
                ("post_q", models.IntegerField(blank=True, null=True)),
                ("post_likes", models.IntegerField(blank=True, null=True)),
                ("post_comments", models.IntegerField(blank=True, null=True)),
                ("post_views", models.IntegerField(blank=True, null=True)),
                ("story_q", models.IntegerField(blank=True, null=True)),
                ("story_likes", models.IntegerField(blank=True, null=True)),
                ("story_comments", models.IntegerField(blank=True, null=True)),
                ("story_viewers", models.IntegerField(blank=True, null=True)),
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                (
                    "model",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to="only_fans_db.models",
                    ),
                ),
            ],
            options={
                "db_table": "reach_stats",
                "managed": True,
            },
        ),
        migrations.CreateModel(
            name="OnlineStats",
            fields=[
                ("id", models.IntegerField(primary_key=True, serialize=False)),
                ("date", models.DateTimeField()),
                ("online_count", models.IntegerField()),
                ("hour", models.IntegerField()),
                (
                    "model",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to="only_fans_db.models",
                    ),
                ),
            ],
            options={
                "db_table": "online_stats",
                "managed": True,
            },
        ),
        migrations.AddField(
            model_name="messageslist",
            name="model",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                to="only_fans_db.models",
            ),
        ),
        migrations.CreateModel(
            name="MassMessages",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("date_time", models.DateTimeField(blank=True, null=True)),
                ("message_text", models.TextField(blank=True, null=True)),
                ("message_hash", models.TextField(blank=True, null=True)),
                ("send", models.IntegerField(blank=True, null=True)),
                ("viewed", models.IntegerField(blank=True, null=True)),
                (
                    "model",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to="only_fans_db.models",
                    ),
                ),
            ],
            options={
                "db_table": "mass_messages",
                "managed": True,
            },
        ),
        migrations.AddField(
            model_name="content",
            name="model",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                to="only_fans_db.models",
            ),
        ),
        migrations.CreateModel(
            name="FansModels",
            fields=[
                (
                    "fan",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        primary_key=True,
                        serialize=False,
                        to="only_fans_db.fans",
                    ),
                ),
                ("total_summ", models.FloatField(blank=True, null=True)),
                ("sub_date", models.DateTimeField(blank=True, null=True)),
                (
                    "trial_links",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.DateTimeField(), size=None
                    ),
                ),
                (
                    "model",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to="only_fans_db.models",
                    ),
                ),
            ],
            options={
                "db_table": "fans_models",
                "managed": True,
                "unique_together": {("fan", "model")},
            },
        ),
    ]
