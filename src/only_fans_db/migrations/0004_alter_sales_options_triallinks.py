# Generated by Django 4.2.2 on 2023-09-14 12:39

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("only_fans_db", "0003_alter_messageslisttemp_options_alter_sales_options"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="sales",
            options={"managed": False},
        ),
        migrations.CreateModel(
            name="TrialLinks",
            fields=[
                ("creation_date", models.DateTimeField(blank=True, null=True)),
                ("claims_count", models.IntegerField(blank=True, null=True)),
                (
                    "link_url",
                    models.CharField(max_length=255, primary_key=True, serialize=False),
                ),
                (
                    "model",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to="only_fans_db.models",
                    ),
                ),
            ],
            options={
                "db_table": "trial_links",
            },
        ),
    ]
