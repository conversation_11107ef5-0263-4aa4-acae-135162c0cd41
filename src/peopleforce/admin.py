from django.contrib import admin

from peopleforce.models import Employee, Termination


class TerminationInline(admin.TabularInline):
    model = Termination


@admin.register(Employee)
class EmployeeAdmin(admin.ModelAdmin):
    inlines = [TerminationInline]
    list_prefetch_related = ['terminations']
    select_related = ['position', 'department']
    list_display = ['full_name', 'fired']

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def fired(self, obj):
        return bool(obj.terminations.all())
