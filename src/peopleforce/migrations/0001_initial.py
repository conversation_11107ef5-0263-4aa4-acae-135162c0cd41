# Generated by Django 4.2.2 on 2024-06-07 20:31

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models

import base.models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Department",
            fields=[
                ("id", models.BigIntegerField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=120)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Employee",
            fields=[
                ("id", models.BigIntegerField(primary_key=True, serialize=False)),
                ("full_name", models.Char<PERSON>ield(max_length=120, null=True)),
                ("gender", models.CharField(max_length=50, null=True)),
                ("date_of_birth", models.DateField(null=True)),
                ("hired_on", models.DateField(null=True)),
                ("email", models.EmailField(max_length=254, null=True)),
                (
                    "department",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="peopleforce.department",
                    ),
                ),
                (
                    "head",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="peopleforce.employee",
                    ),
                ),
            ],
            options={
                "default_related_name": "employees",
            },
        ),
        migrations.CreateModel(
            name="Position",
            fields=[
                ("id", models.BigIntegerField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=120)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="TerminationReason",
            fields=[
                ("id", models.BigIntegerField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=120)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="TerminationType",
            fields=[
                ("id", models.BigIntegerField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=120)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Termination",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                ("date", models.DateField()),
                ("comment", models.TextField(null=True)),
                (
                    "employee",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="peopleforce.employee",
                    ),
                ),
                (
                    "reason",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="peopleforce.terminationreason",
                    ),
                ),
                (
                    "type",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="peopleforce.terminationtype",
                    ),
                ),
            ],
            options={
                "default_related_name": "terminations",
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.AddField(
            model_name="employee",
            name="position",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="peopleforce.position",
            ),
        ),
    ]
