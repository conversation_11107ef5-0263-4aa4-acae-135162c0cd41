from django.db import models

from base.models import TimeStampedModel


class PeopleForceBaseModel(models.Model):
    """
    Abstract base model for PeopleForce entities with common fields.
    """
    id = models.BigIntegerField(primary_key=True)
    name = models.CharField(max_length=120)

    class Meta:
        abstract = True

    def __str__(self):
        return self.name


class Position(PeopleForceBaseModel):
    """
    PeopleForce Position
    """
    pass


class Department(PeopleForceBaseModel):
    """
    PeopleForce Department
    """
    pass


class TerminationType(PeopleForceBaseModel):
    """
    PeopleForce Termination Type
    """
    pass


class TerminationReason(PeopleForceBaseModel):
    """
    PeopleForce Termination Reason
    """
    pass


class Employee(models.Model):
    """
    PeopleForce Employee
    """
    id = models.BigIntegerField(primary_key=True)
    full_name = models.CharField(max_length=120, null=True)
    gender = models.Char<PERSON>ield(max_length=50, null=True)
    date_of_birth = models.DateField(null=True)
    hired_on = models.DateField(null=True)
    position = models.ForeignKey(Position, on_delete=models.CASCADE, null=True)
    department = models.ForeignKey(Department, on_delete=models.CASCADE, null=True)
    head = models.ForeignKey('self', on_delete=models.CASCADE, null=True)
    email = models.EmailField(null=True)

    class Meta:
        default_related_name = 'employees'

    def __str__(self):
        return str(self.full_name)


class Termination(TimeStampedModel):
    """
    PeopleForce Termination
    """
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE)
    type = models.ForeignKey(TerminationType, on_delete=models.CASCADE, null=True)
    reason = models.ForeignKey(TerminationReason, on_delete=models.CASCADE, null=True)
    date = models.DateField()
    comment = models.TextField(null=True)

    class Meta:
        default_related_name = 'terminations'

    def __str__(self):
        return f'{self.date}'
