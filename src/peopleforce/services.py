from datetime import datetime
from typing import Generator
from urllib.parse import urljoin

import httpx
from dacite import from_dict
from django.conf import settings

from peopleforce.dtos import (
    EmployeeDTO,
    TerminationReasonDTO,
    TerminationTypeDTO,
)
from peopleforce.models import (
    Department,
    Employee,
    Position,
    Termination,
    TerminationReason,
    TerminationType,
)


class PeopleForceApiService:
    """
    API service for PeopleForce
    """
    def __init__(
            self,
            url: str = settings.PEOPLEFORCE_URL,
            api_key: str = settings.PEOPLEFORCE_API_KEY
    ):
        assert url is not None, 'provide url or set it in settings.PEOPLEFORCE_URL'
        assert api_key is not None, 'provide api_key or set it in settings.PEOPLEFORCE_API_KEY'
        self.url = url
        self.api_key = api_key
        self.headers = self._get_headers()

    def _get_headers(self) -> dict:
        return {"X-API-KEY": self.api_key}

    def request_generator(self, endpoint: str, user_params: dict = None) -> Generator[dict, None, None]:
        page = 1
        pages = 1

        with httpx.Client() as client:
            while page <= pages:
                params = {'page': page}
                if user_params:
                    params.update(user_params)

                response = client.get(
                    url=urljoin(self.url, endpoint),
                    params=params,
                    headers=self.headers,
                )
                response.raise_for_status()
                yield response.json().get('data', {})

                metadata = response.json().get('metadata', {'pagination': {'pages': 1}})
                pages = metadata['pagination']['pages']
                page += 1

    @staticmethod
    def _covert_fields_to_datetime(data: dict, fields: list[str], format_: str = "%Y-%m-%d"):
        for field in fields:
            if value := data.get(field):
                data[field] = datetime.strptime(value, format_)

    def employees_generator(self) -> Generator[list[EmployeeDTO], None, None]:
        for data_list in self.request_generator('employees', {"status": "all"}):
            employees_list = []
            for data in data_list:
                self._covert_fields_to_datetime(data, ["date_of_birth", "hired_on", "termination_effective_on"])
                employees_list.append(from_dict(EmployeeDTO, data=data))

            yield employees_list

    def termination_reasons_generator(self) -> Generator[list[TerminationReasonDTO], None, None]:
        for data_list in self.request_generator('termination_reasons'):
            termination_reasons_list = []
            for data in data_list:
                termination_reasons_list.append(from_dict(TerminationReasonDTO, data=data))

            yield termination_reasons_list

    def termination_types_generator(self) -> Generator[list[TerminationTypeDTO], None, None]:
        for data_list in self.request_generator('termination_types'):
            termination_types_list = []
            for data in data_list:
                termination_types_list.append(from_dict(TerminationTypeDTO, data=data))

            yield termination_types_list


class PeopleForceSynchronizeService:
    def __init__(self):
        self.api_service = PeopleForceApiService()

    @staticmethod
    def create_or_update_employee(employee: EmployeeDTO):
        if position := employee.position:
            position, _ = Position.objects.update_or_create(
                id=employee.position.id,
                defaults={
                    'name': employee.position.name
                }
            )
        if department := employee.department:
            department, _ = Department.objects.update_or_create(
                id=employee.department.id,
                defaults={
                    'name': employee.department.name
                }
            )
        if head := employee.reporting_to:
            head, _ = Employee.objects.get_or_create(
                id=employee.reporting_to.id
            )

        db_employee, _ = Employee.objects.update_or_create(
            id=employee.id,
            defaults={
                'full_name': employee.full_name,
                'gender': employee.gender,
                'date_of_birth': employee.date_of_birth,
                'hired_on': employee.hired_on,
                'position': position,
                'department': department,
                'head': head,
                'email': employee.email,
            }
        )

        if employee.termination_effective_on:
            try:
                termination_type = TerminationType.objects.get(
                    id=employee.termination_type_id,
                )
            except TerminationType.DoesNotExist:
                termination_type = None

            try:
                termination_reason, _ = TerminationReason.objects.get_or_create(
                    id=employee.termination_reason_id,
                    defaults={
                        'name': 'Not found'
                    }
                )
            except TerminationReason.DoesNotExist:
                termination_reason = None

            Termination.objects.get_or_create(
                employee=db_employee,
                defaults={
                    'date': employee.termination_effective_on,
                    'comment': employee.termination_comment,
                    'type': termination_type,
                    'reason': termination_reason
                }
            )

    def synchronize_employees_with_api(self):
        for termination_reason_list in self.api_service.termination_reasons_generator():
            for termination_reason in termination_reason_list:
                TerminationReason.objects.update_or_create(
                    id=termination_reason.id,
                    defaults={
                        'name': termination_reason.name
                    }
                )

        for termination_type_list in self.api_service.termination_types_generator():
            for termination_type in termination_type_list:
                TerminationType.objects.update_or_create(
                    id=termination_type.id,
                    defaults={
                        'name': termination_type.name
                    }
                )

        for employee_list in self.api_service.employees_generator():
            for employee in employee_list:
                self.create_or_update_employee(employee)
