from core.celery import BaseTaskWithRetry
from core.celery import app as celery_app
from peopleforce.models import Employee
from peopleforce.services import PeopleForceSynchronizeService


@celery_app.task(bind=BaseTaskWithRetry)
def synchronize_people_force(self):
    PeopleForceSynchronizeService().synchronize_employees_with_api()

    return f"Done! Number employees in the db: {Employee.objects.count()}"
