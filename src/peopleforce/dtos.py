import datetime
from dataclasses import dataclass


@dataclass
class PeopleForceBaseDTO:
    id: int
    name: str


@dataclass
class PositionDTO(PeopleForceBaseDTO):
    pass


@dataclass
class DepartmentDTO(PeopleForceBaseDTO):
    pass


@dataclass
class TerminationTypeDTO(PeopleForceBaseDTO):
    pass


@dataclass
class TerminationReasonDTO(PeopleForceBaseDTO):
    pass


@dataclass
class ReportingToDTO:
    id: int
    first_name: str
    last_name: str
    email: str


@dataclass
class EmployeeDTO:
    id: int
    full_name: str | None
    gender: str | None
    date_of_birth: datetime.date | None
    hired_on: datetime.date | None
    position: PositionDTO | None
    department: DepartmentDTO | None
    reporting_to: ReportingToDTO | None
    email: str | None
    termination_type_id: int | None
    termination_reason_id: int | None
    termination_effective_on: datetime.date | None
    termination_comment: str | None
