from django.core.exceptions import ValidationError as DjangoValidationError
from rest_framework.exceptions import Throttled
from rest_framework.exceptions import ValidationError as DRFValidationError
from rest_framework.serializers import as_serializer_error
from rest_framework.views import exception_handler as drf_exception_handler


def exception_handler(exc, context):
    """
    Custom exception handler for DRF.
    Converts django's ValidationError to DRF ValidationError.
    """
    if isinstance(exc, DjangoValidationError):
        exc = DRFValidationError(as_serializer_error(exc))
    elif isinstance(exc, Throttled):
        exc.detail = 'Requests limit is exited, try a bit later'

    return drf_exception_handler(exc, context)
