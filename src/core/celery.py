import functools
import os

import pytz
import redis
from celery import Celery, Task
from celery.schedules import crontab
from django.conf import settings

from core.celery_config import config

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "core.settings")

app = Celery(
    "core",
    broker=settings.CELERY_BROKER_URL,
    backend=settings.CELERY_RESULT_BACKEND
)
app.config_from_object(config)
app.conf.timezone = pytz.timezone('Europe/Kiev')
app.conf.enable_utc = False
app.log.setup(loglevel=settings.CELERY_LOG_LEVEL)
app.conf.broker_connection_retry_on_startup = True
app.conf.broker_transport_options = {'visibility_timeout': 6 * 60 * 60}

app.autodiscover_tasks()


class BaseTaskWithRetry(Task):
    autoretry_for = (Exception,)
    max_retries = 5
    default_retry_delay = 3
    retry_backoff = False
    retry_backoff_max = 700
    retry_jitter = False


app.conf.beat_schedule = {
    'list_messages_temp_cleaner': {
        'task': 'only_fans_db.tasks.list_messages_temp_cleaner',
        'schedule': 60.0
    },
    # TEMPORARY OFF
    # 'collecting_fans_sessions': {
    #     'task': 'fans_sessions.tasks.collecting_fans_sessions',
    #     'schedule': crontab(minute='0', hour='0')
    # },
    'check_shifts_without_result_stats': {
        'task': 'shifts.tasks.check_shifts_without_result_stats',
        'schedule': crontab(minute='0', hour='1', day_of_week='mon')
    },
    'update_ads_stats': {
        'task': 'ads.tasks.update_ads_stats',
        'schedule': crontab(minute='0', hour='*/2')
    },
    'update_plan_fact_results': {
        'task': 'only_fans_models_plan.tasks.update_plan_fact_results',
        'schedule': crontab(minute='*/15')
    },
    'create_ads_tracking_daily_datas': {
        'task': 'ads.tasks.create_ads_tracking_daily_datas',
        'schedule': crontab(minute='0', hour='0')
    },
    'write_promotions_count': {
        'task': 'ads.tasks.write_promotions_count',
        'schedule': crontab(minute='0', hour='0')
    },
    'update_day_before_yesterday_shifts_data': {
        'task': 'shifts.tasks.update_day_before_yesterday_shifts_data',
        'schedule': crontab(minute='10', hour='0')
    },
    'synchronize_people_force': {
        'task': 'peopleforce.tasks.synchronize_people_force',
        'schedule': crontab(minute='0', hour='1')
    },
    'collect_new_payouts': {
        'task':  'finance.tasks.collect_new_payouts',
        'schedule': crontab(minute='*/5')
    },
    'write_total_debt_history': {
        'task': 'finance.tasks.write_total_debt_history',
        'schedule': crontab(minute='0', hour='10')
    },
    'write_total_income_fans_history': {
        'task': 'finance.tasks.write_total_income_fans_history',
        'schedule': crontab(minute='0', hour='10')
    },
    'create_externals_shifts': {
        'task': 'shifts.tasks.create_externals_shifts',
        'schedule': crontab(minute='0', hour='5')
    },
    'update_shift_stats_for_progress_shifts': {
        'task': 'shifts.tasks.update_shift_stats_for_progress_shifts',
        'schedule': 120.0
    },
    'update_ads_friends_status': {
        'task': 'ads.tasks.update_ads_friends_status',
        'schedule': crontab(minute='0', hour='*/4')
    },
    'send_marketing_budget_alerts': {
        'task': 'only_fans_models_plan.tasks.send_marketing_budget_alerts',
        'schedule': crontab(minute='30', hour='11', day_of_week='1-5')
    },
    'write_ads_daily_data': {
        'task': 'ads.tasks.write_ads_daily_data',
        'schedule': crontab(minute='0', hour='13')
    },
    'db_sales_synchronization': {
        'task':  'shifts.tasks.db_sales_synchronization',
        'schedule': 60.0
    },
    'create_total_spend_copy_table_snapshots': {
        'task': 'finance.tasks.create_total_spend_copy_table_snapshots',
        'schedule': crontab(minute='0', hour='23')
    },
    'switch_ads_new_donor_promos': {
        'task': 'ads.tasks.switch_ads_new_donor_promos',
        'schedule': crontab(minute='0', hour='1')
    },
    'write_model_donor_debt_history': {
        'task': 'finance.tasks.write_model_donor_debt_history',
        'schedule': crontab(minute='0', hour='12', day_of_week='mon')
    }
}

if settings.RUN_REFRESH_FANS_MODELS:
    app.conf.beat_schedule['refresh_fans_models'] = {
            'task': 'refresh_fans_models.tasks.refresh',
            'schedule': crontab(minute='*/30')
        }
    app.conf.beat_schedule['retry_refresh_fans_models'] = {
            'task': 'refresh_fans_models.tasks.retry_refresh',
            'schedule': crontab(minute='0', hour='*')
        }


def celery_task_locker_decorator(key: str, timeout: int):
    """
    Decorator to lock celery tasks.

    Args:
        key (str): Redis key.
        timeout (int): Lock timeout in seconds.
    Returns:
        function: Decorated function.
    """
    def inner(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            redis_client = redis.Redis(
                host=settings.REDIS_HOST_NAME,
                port=settings.REDIS_PORT,
                password=settings.REDIS_PASSWORD,
                db=3
            )
            have_lock = redis_client.set(key, "true", nx=True, ex=timeout)

            if not have_lock:
                return f'Previous task with key: {key} is still running'

            try:
                return func(*args, **kwargs)
            finally:
                redis_client.delete(key)

        return wrapper
    return inner
