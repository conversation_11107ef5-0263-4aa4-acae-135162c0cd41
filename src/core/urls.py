"""
URL configuration for crm_backend project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.conf import settings
from django.conf.urls import include
from django.conf.urls.static import static
from django.contrib import admin
from django.urls import path
from drf_spectacular.views import (
    SpectacularAPIView,
    SpectacularRedocView,
    SpectacularSwaggerView,
)
from rest_framework_simplejwt.views import TokenRefreshView

from base.views import TokenObtainPairView

# API v1 endpoints
api_v1_patterns = [
    # Authentication
    path('token/', TokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),

    # Core functionality
    path('', include('accounts.urls')),
    path('', include('shifts.urls')),
    path('buying/', include('ads.urls')),
    path('finance/', include('finance.urls')),
    path('third_party/', include('only_fans_models.third_party.urls')),
    path('only-fans-models/', include('only_fans_models.urls')),
    path('models-plan/', include('only_fans_models_plan.urls')),
    path('refresh-fans-models/', include('refresh_fans_models.urls')),
    path('client-management/', include('client_management.urls')),
    path('donors-budget/', include('donors_budget.urls')),
    path('content-requests/', include('content_request.urls')),
    path('request-sign/', include('request_sign.urls')),
]

# Documentation endpoints
api_docs_patterns = [
    path("schema/", SpectacularAPIView.as_view(), name="spectacular-schema"),
    path("swagger/", SpectacularSwaggerView.as_view(url_name="spectacular-schema"), name="swagger"),
    path("redoc/", SpectacularRedocView.as_view(url_name="spectacular-schema"), name="redoc"),
]

urlpatterns = [
    # Django admin
    path('admin/', admin.site.urls),

    # API documentation
    path("api/docs/", include(api_docs_patterns)),

    # API v1
    path('api/v1/', include(api_v1_patterns)),

    # Authentication for browsable API
    path('api-auth/', include('rest_framework.urls')),

    # Monitoring
    path('', include('django_prometheus.urls')),
]

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)

if not settings.TESTING:
    urlpatterns += [
        path('__debug__/', include('debug_toolbar.urls')),
    ]
