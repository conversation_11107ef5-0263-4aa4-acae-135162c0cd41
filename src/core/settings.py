"""
Django settings for crm_backend project.

Generated by 'django-admin startproject' using Django 4.2.2.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.2/ref/settings/
"""
import logging
import os
import sys
from datetime import timedelta
from pathlib import Path

import sentry_sdk
from sentry_sdk.integrations.django import DjangoIntegration

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.environ.get('SECRET_KEY')

AUTH_USER_MODEL = 'accounts.User'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = os.environ.get('DEBUG', 'False') == 'True'

ALLOWED_HOSTS = (
    os.environ.get('ALLOWED_HOSTS').split(',')
    if os.environ.get('ALLOWED_HOSTS')
    else []
)

# Application definition

DEFAULT_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
]

EXTERNAL_APPS = [
    'corsheaders',
    'debug_toolbar',
    'django_filters',
    'drf_spectacular',
    'rest_framework',
    'admin_reorder',
    'django_prometheus',
    'nested_inline',
    'rest_framework_api_key',
    'simple_history',
]

PROMETHEUS_EXPORT_MIGRATIONS = False

LOCAL_APPS = [
    'accounts',
    'base',
    'only_fans_models',
    'shifts',
    'only_fans_db',
    'fans_sessions',
    'ads',
    'only_fans_models_plan',
    'finance',
    'refresh_fans_models',
    'client_management',
    'peopleforce',
    'donors_budget',
    'content_request',
    'request_sign'
]
INTERNAL_IPS = [
    '127.0.0.1',
]
MIDDLEWARE = [
    'django_prometheus.middleware.PrometheusBeforeMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'admin_reorder.middleware.ModelAdminReorder',
    'debug_toolbar.middleware.DebugToolbarMiddleware',
    'base.middlewares.UserCookieMiddleWare',
    'django_prometheus.middleware.PrometheusAfterMiddleware',
    'simple_history.middleware.HistoryRequestMiddleware',
]
INSTALLED_APPS = DEFAULT_APPS + EXTERNAL_APPS + LOCAL_APPS

# CORS
CORS_ALLOWED_ORIGINS = (
    os.environ.get('CORS_ALLOWED_ORIGINS').split(',')
    if os.environ.get('CORS_ALLOWED_ORIGINS')
    else []
)
CORS_ALLOW_ALL_ORIGINS = bool(os.environ.get('CORS_ALLOW_ALL_ORIGINS', False))

# CSRF
CSRF_TRUSTED_ORIGINS = (
    os.environ.get('CSRF_TRUSTED_ORIGINS').split(',')
    if os.environ.get('CSRF_TRUSTED_ORIGINS')
    else []
)

ROOT_URLCONF = 'core.urls'
# ROOT_URLCONF = "graphite.urls_prometheus_wrapper"

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'core.wsgi.application'

DATABASE_ROUTERS = [
    'core.db_routers.OnlyFansDBRouter',
]

# DATABASE FOR TESTING
TEST_DATABASE = {
    'ENGINE': 'django.db.backends.postgresql',
    'NAME': os.environ.get('POSTGRES_TEST_DB', 'postgres'),
    'USER': os.environ.get('POSTGRES_TEST_USER', 'postgres'),
    'PASSWORD': os.environ.get('POSTGRES_TEST_PASSWORD', 'postgres'),
    'HOST': os.environ.get('POSTGRES_TEST_HOST', 'db'),  # set in docker-compose.yml
    'PORT': 5432,
}
# DB ROUTERS FOR TESTING
TEST_DATABASE_ROUTERS = [
    'core.db_routers.OnlyFansDBTestRouter',
]

TESTING = "test" in sys.argv or "test_coverage" in sys.argv

if TESTING:
    PASSWORD_HASHERS = ['django.contrib.auth.hashers.MD5PasswordHasher']
    DATABASES = {
        'default': TEST_DATABASE,
    }
    DATABASE_ROUTERS = TEST_DATABASE_ROUTERS
    logging.disable(logging.WARNING)
    INSTALLED_APPS.remove("debug_toolbar")
    MIDDLEWARE.remove("debug_toolbar.middleware.DebugToolbarMiddleware")
else:
    MIGRATION_MODULES = {
        'only_fans_db': None,
    }

    DATABASES = {
        'default': {
            'ENGINE': 'django_prometheus.db.backends.postgresql',
            'NAME': os.environ.get('POSTGRES_DB', None),
            'USER': os.environ.get('POSTGRES_USER', None),
            'PASSWORD': os.environ.get('POSTGRES_PASSWORD', None),
            'HOST': os.environ.get('POSTGRES_HOST', 'localhost'),
            'PORT': os.environ.get('POSTGRES_PORT', 5432),
        },
        'only_fans_db': {
            'ENGINE': 'django_prometheus.db.backends.postgresql',
            'NAME': 'onlyfans',
            'USER': os.environ.get('POSTGRES_USER', None),
            'PASSWORD': os.environ.get('POSTGRES_PASSWORD', None),
            'HOST': os.environ.get('POSTGRES_HOST', 'localhost'),
            'PORT': os.environ.get('POSTGRES_PORT', 5432),
        },
    }

# LOGGING

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{name} {levelname} {asctime} {module} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'telegram': {
            'level': 'ERROR',
            'class': 'core.logging_handlers.TelegramHandler',
            'formatter': 'verbose',
        },
    },
    'loggers': {
        '': {
            'handlers': ['telegram'],
            'level': 'ERROR',
            'propagate': True,
        },
    },
}

# redis cache settings
REDIS_HOST_NAME = os.environ.get('REDIS_HOST_NAME', 'localhost')
REDIS_PORT = os.environ.get('REDIS_PORT', 6379)
REDIS_CACHE_DB = os.environ.get('REDIS_CACHE_DB', 5)
REDIS_PASSWORD = os.environ.get('REDIS_PASSWORD', None)

if REDIS_PASSWORD:
    REDIS_URL = f'redis://:{REDIS_PASSWORD}@{REDIS_HOST_NAME}:{REDIS_PORT}'
else:
    REDIS_URL = f'redis://{REDIS_HOST_NAME}:{REDIS_PORT}'

REDIS_CACHE_URL = f'{REDIS_URL}/{REDIS_CACHE_DB}'

CACHE_TTL = 60 * 15

CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.redis.RedisCache',
        'LOCATION': REDIS_CACHE_URL,
        'TIMEOUT': 60 * 60,
    }
}

CELERY_BROKER_URL = os.environ.get('CELERY_BROKER_URL', 'redis://localhost:6379/1')
CELERY_RESULT_BACKEND = os.environ.get(
    'CELERY_RESULT_BACKEND', 'redis://localhost:6379/2'
)
CELERY_LOG_LEVEL = 'INFO'

FIXTURE_DIRS = [
    'base',
]

# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'Europe/Kiev'

USE_I18N = True

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/
STORAGES = {
    'default': {
        'BACKEND': 'django.core.files.storage.FileSystemStorage',
    },
    'staticfiles': {
        'BACKEND': 'django.contrib.staticfiles.storage.StaticFilesStorage',
    },
    'dropbox': {
        'BACKEND': 'storages.backends.dropbox.DropboxStorage',
        'OPTIONS': {
            'oauth2_access_token': os.environ.get('DROPBOX_ACCESS_TOKEN', 'fake'),
            'oauth2_refresh_token': os.environ.get('DROPBOX_REFRESH_TOKEN', 'fake'),
            'app_secret': os.environ.get('DROPBOX_APP_SECRET', 'fake'),
            'app_key': os.environ.get('DROPBOX_APP_KEY', 'fake'),
        },
    },
    'overwrite_file_system_storage': {
        'BACKEND': 'base.storages.FileSystemOverwriteStorage',
    },
}

STATIC_URL = '/static/'
STATIC_ROOT = '/opt/app/staticfiles'

MEDIA_URL = '/media/'
MEDIA_ROOT = '/opt/app/media'

# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

DEBUG_TOOLBAR_CONFIG = {
    'UPDATE_ON_FETCH': True,
    'OBSERVE_REQUEST_CALLBACK': lambda request: DEBUG,
    'SHOW_TOOLBAR_CALLBACK': lambda request: DEBUG,
}

# DRF Spectacular settings
SPECTACULAR_SETTINGS = {
    'TITLE': 'Sparks CRM',
    'DESCRIPTION': 'Sparks CRM 2.0',
    'VERSION': '1.0.0',
    'COMPONENT_SPLIT_PATCH': True,
    'COMPONENT_SPLIT_REQUEST': True,
    'SERVE_PUBLIC': False,
    'SERVE_AUTHENTICATION': ['rest_framework.authentication.BasicAuthentication'],
    'SERVE_PERMISSIONS': ['rest_framework.permissions.IsAdminUser'],
    'SCHEMA_PATH_PREFIX': '/api/v[0-9]',
    'SWAGGER_UI_SETTINGS': {
        'docExpansion': 'list',
        'filter': True,
        'tagsSorter': 'alpha',
    },
    'SERVE_INCLUDE_SCHEMA': False,
    'ENUM_NAME_OVERRIDES': {'ReviewResult': 'finance.models.ReviewResult'},
    'SWAGGER_UI_DIST': 'https://cdn.jsdelivr.net/npm/swagger-ui-dist@5.11.5',
}

REST_FRAMEWORK = {
    'DEFAULT_THROTTLE_CLASSES': [
        'rest_framework.throttling.AnonRateThrottle',
        'rest_framework.throttling.UserRateThrottle',
    ],
    'DEFAULT_THROTTLE_RATES': {'anon': '10/min', 'user': '80/min'},
    'DEFAULT_RENDERER_CLASSES': [
        'rest_framework.renderers.JSONRenderer',
    ],
    'DEFAULT_PARSER_CLASSES': [
        'rest_framework.parsers.JSONParser',
        'rest_framework.parsers.MultiPartParser',
    ],
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    ],
    'DEFAULT_SCHEMA_CLASS': 'drf_spectacular.openapi.AutoSchema',
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
    'DEFAULT_FILTER_BACKENDS': ('base.filter_backends.CustomFilterBackend',),
    'EXCEPTION_HANDLER': 'core.exception_handlers.exception_handler',  # noqa
}

# simple JWT
# https://django-rest-framework-simplejwt.readthedocs.io/en/latest/index.html

SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=60),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=2),
    'ROTATE_REFRESH_TOKENS': True,
}

# admin reorder settings
# https://pypi.org/project/django-modeladmin-reorder/
ADMIN_REORDER = (
    'sites',
    {
        'app': 'rest_framework_api_key',
        'models': ({'model': 'rest_framework_api_key.APIKey', 'label': 'API Keys'},),
    },
    {
        'app': 'accounts',
        'models': (
            {'model': 'accounts.UserTeamLeadProxy', 'label': 'Team Leads'},
            {'model': 'accounts.UserSeniorOperatorProxy', 'label': 'Senior Operators'},
            {'model': 'accounts.UserOperatorProxy', 'label': 'Operators'},
            {'model': 'accounts.UserRole', 'label': 'UserRoles'},
            {'model': 'accounts.User', 'label': 'Users'},
            {'model': 'accounts.UserMarketerProxy', 'label': 'Marketers'},
            {'model': 'accounts.UserHOMProxy', 'label': 'Heads of Marketing'},
            {'model': 'accounts.UserHOFProxy', 'label': 'Heads of Finance'},
            {'model': 'accounts.UserFinancierProxy', 'label': 'Financiers'},
            {'model': 'accounts.UserSMMProxy', 'label': 'SMM specialists'},
            {'model': 'accounts.UserHOCMProxy', 'label': 'Head of CM'},
            {'model': 'accounts.UserClientManagerProxy', 'label': 'Client Manager'},
            {
                'model': 'accounts.UserHOBDProxy',
                'label': 'Head of Business Development',
            },
            {'model': 'accounts.UserBusinessDevProxy', 'label': 'Business Dev'},
            {'model': 'accounts.UserBusinessUnitProxy', 'label': 'Business Unit'},
        ),
    },
    {
        'app': 'only_fans_models',
        'models': (
            {'model': 'only_fans_models.ModelCategory', 'label': 'Model Category'},
            {'model': 'only_fans_models.OnlyFansModel', 'label': 'Models'},
            {'model': 'only_fans_models.BalanceProfit', 'label': 'Balance Profit'},
            {'model': 'only_fans_models.ModelStatus', 'label': 'Model Status'},
            {'model': 'only_fans_models.ModelPageType', 'label': 'Model Page Type'},
        ),
    },
    {
        'app': 'shifts',
        'models': (
            {'model': 'shifts.ShiftNumber', 'label': 'Shift Numbers'},
            {'model': 'shifts.Shift', 'label': 'Shifts'},
            {'model': 'shifts.ScheduleTeam', 'label': 'Schedule Teams'},
            {'model': 'shifts.ShiftIndex', 'label': 'Shift Indexes'},
            {'model': 'shifts.ScheduleMonitoring', 'label': 'Schedule Monitoring'},
            {'model': 'shifts.DBSale', 'label': 'DB Sales'},
            {'model': 'shifts.ShiftSale', 'label': 'Shift Sales'},
            {
                'model': 'shifts.TeamLeadABConfig',
                'label': 'Team Lead Alarm Bot Configs',
            },
            {'model': 'shifts.TeamLeadABChat', 'label': 'Team Lead Alarm Bot Chats'},
        ),
    },
    {
        'app': 'fans_sessions',
        'models': ({'model': 'fans_sessions.Session', 'label': 'Sessions'},),
    },
    {
        'app': 'ads',
        'models': (
            {'model': 'ads.PaymentMethod', 'label': 'Payment Methods'},
            {'model': 'ads.PlatformType', 'label': 'Platform Types'},
            {'model': 'ads.Promo', 'label': 'Promos'},
            {'model': 'ads.Donor', 'label': 'Donors'},
            {'model': 'ads.DonorType', 'label': 'Donor Types'},
            {'model': 'ads.DonorOption', 'label': 'Donor Options'},
            {'model': 'ads.Status', 'label': 'Statuses'},
            {'model': 'ads.BlackList', 'label': 'Black List'},
            {'model': 'ads.Ads', 'label': 'Ads'},
            {'model': 'ads.AdsTracking', 'label': 'Ads Tracking'},
            {'model': 'ads.PromotionCount', 'label': 'Promotion Counts'},
            {'model': 'ads.ProblemStatus', 'label': 'Problem Statuses'},
            {'model': 'ads.AdsSwapLog', 'label': 'Ads Swap Logs'},
            {'model': 'ads.AdsDraft', 'label': 'Ads Drafts'},
        ),
    },
    {
        'app': 'only_fans_models_plan',
        'models': (
            {'model': 'only_fans_models_plan.Period', 'label': 'Periods'},
            {
                'model': 'only_fans_models_plan.MarketingBudgetPeriod',
                'label': 'Marketing Budget Periods',
            },
            {
                'model': 'only_fans_models_plan.MarketingBudgetAlertUser',
                'label': 'Marketing Budget Alerts',
            },
        ),
    },
    {
        'app': 'finance',
        'models': (
            {'model': 'finance.PaymentCurrency', 'label': 'Payment Currencies'},
            {
                'model': 'finance.PaymentCryptoNetwork',
                'label': 'Payment Crypto Networks',
            },
            {'model': 'finance.PaymentSystem', 'label': 'Payment Systems'},
            {'model': 'finance.PaymentExpenseItem', 'label': 'Payment Expense Items'},
            {'model': 'finance.PaymentTool', 'label': 'Payment Tools'},
            {'model': 'finance.PaymentDepartment', 'label': 'Payment Departments'},
            {'model': 'finance.RequestPayment', 'label': 'Request Payments'},
            {'model': 'finance.PayoutReview', 'label': 'Payout Reviews'},
            {'model': 'finance.PayoutAddress', 'label': 'Payout Addresses'},
            {'model': 'finance.TotalDebtHistory', 'label': 'Total Debt Histories'},
            {'model': 'finance.MonthDebtTracking', 'label': 'Month Debt Trackings'},
            {'model': 'finance.CashflowExpenseItem', 'label': 'Cashflow Expense Items'},
            {
                'model': 'finance.CashflowExpenseSubItem',
                'label': 'Cashflow Expense Sub Items',
            },
            {'model': 'finance.RequestCashflow', 'label': 'Cashflow Requests'},
            {'model': 'finance.CashflowIncomeItem', 'label': 'Cashflow Income Items'},
            {
                'model': 'finance.RequestCashflowIncome',
                'label': 'Cashflow Income Requests',
            },
            {'model': 'finance.Budget', 'label': 'Budgets'},
            {'model': 'finance.BudgetExpense', 'label': 'Budget Expenses'},
        ),
    },
    {
        'app': 'refresh_fans_models',
        'models': (
            {'model': 'refresh_fans_models.RefreshTask', 'label': 'Refresh Tasks'},
            {'model': 'refresh_fans_models.Sale', 'label': 'Sales'},
        ),
    },
    {
        'app': 'client_management',
        'models': (
            {'model': 'client_management.Country', 'label': 'Countries'},
            {'model': 'client_management.Nationality', 'label': 'Nationalities'},
            {'model': 'client_management.Language', 'label': 'Languages'},
            {'model': 'client_management.Social', 'label': 'Socials'},
            {'model': 'client_management.ModelInfo', 'label': 'Models Info'},
            {
                'model': 'client_management.CalendarEventType',
                'label': 'Calendar Event Types',
            },
            {
                'model': 'client_management.CalendarEventRepeatType',
                'label': 'Calendar Event Repeat Types',
            },
            {'model': 'client_management.CalendarEvent', 'label': 'Calendar Events'},
            {'model': 'client_management.LiveFeed', 'label': 'LiveFeeds'},
        ),
    },
    {
        'app': 'peopleforce',
        'models': ({'model': 'peopleforce.Employee', 'label': 'Employees'},),
    },
    {
        'app': 'donors_budget',
        'models': ({'model': 'donors_budget.DonorBudgetPeriod', 'label': 'Periods'},),
    },
    {
        'app': 'content_request',
        'models': (
            {'model': 'content_request.ContentRequest', 'label': 'Content Requests'},
            {'model': 'content_request.ContentRequestChatConfig', 'label': 'Chat Configs'},
            {'model': 'content_request.Chat', 'label': 'Chats'},
        ),
    },
    {
        'app': 'request_sign',
        'models': (
            {'model': 'request_sign.Hashtag', 'label': 'Hashtags'},
            {'model': 'request_sign.RequestSign', 'label': 'Request Signs'},
            {'model': 'request_sign.SignResult', 'label': 'Sign Results'},
        ),
    },
)

SESSION_COOKIE_AGE = 60 * 60 * 24 * 14

MVP_URL = os.getenv('MVP_URL', 'http://host.docker.internal:8001/')
MVP_API_KEY = os.getenv('MVP_API_KEY', 'xNYNEDcr.iiwVkYXeuA9bxCGFL7o1xLjhv6OvscTu')

# API USED FOR SYNCHRONISATION ONLY FANS DB (fans_models update)
OF_DB_SYNCHRO_URL = os.getenv('OF_DB_SYNCHRO_URL')
OF_DB_SYNCHRO_TOKEN = os.getenv('OF_DB_SYNCHRO_TOKEN')
RUN_REFRESH_FANS_MODELS = os.getenv('RUN_REFRESH_FANS_MODELS', 'False') == 'True'

# CUSTOM HEADER FOR API KEY
API_KEY_CUSTOM_HEADER = 'HTTP_X_API_KEY'

# GOOGLE SHEETS
REQUEST_PAYMENT_GOOGLE_SHEET_ID = os.getenv('REQUEST_PAYMENT_GOOGLE_SHEET_ID')
REQUEST_CASHFLOW_GOOGLE_SHEET_ID = os.getenv('REQUEST_CASHFLOW_GOOGLE_SHEET_ID')
REQUEST_CASHFLOW_INCOME_GOOGLE_SHEET_ID = os.getenv(
    'REQUEST_CASHFLOW_INCOME_GOOGLE_SHEET_ID'
)

# TELEGRAM_BOT
TRACKER_BOT_TOKEN = os.getenv('TRACKER_BOT_TOKEN')
BUGS_BOT_TOKEN = os.getenv('BUGS_BOT_TOKEN')
BUGS_CHAT_ID = os.getenv('BUGS_CHAT_ID')

# PEOPLE_FORCE
PEOPLEFORCE_URL = os.getenv('PEOPLEFORCE_URL')
PEOPLEFORCE_API_KEY = os.getenv('PEOPLEFORCE_API_KEY')

# PAYOUTS
PARSE_FROM_PAYOUTS_DATE = None

# TOTAL SPEND COPY TABLE SNAPSHOTS
TOTAL_SPEND_COPY_TABLE_SNAPSHOT_API_KEY = os.getenv(
    'TOTAL_SPEND_COPY_TABLE_SNAPSHOT_API_KEY'
)

# DJANGO SIMPLE HISTORY
SIMPLE_HISTORY_REVERT_DISABLED = True

# CONTENT REQUEST
CONTENT_REQUEST_BOT_TOKEN = os.getenv('CONTENT_REQUEST_BOT_TOKEN')

USE_X_FORWARDED_HOST = True
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')

# SENTRY
ENV_NAME = os.environ.get('ENV_NAME')
print('===========')
print(ENV_NAME, 'ENV_NAME')
print('===========')
if ENV_NAME == 'prod':
    print('sentry active!')
    sentry_sdk.init(
        dsn='https://<EMAIL>/4505584440573952',
        integrations=[DjangoIntegration()],
        # Set traces_sample_rate to 1.0 to capture 100%
        # of transactions for performance monitoring.
        # We recommend adjusting this value in production.
        traces_sample_rate=1.0,
        # If you wish to associate users to errors (assuming you are using
        # django.contrib.auth) you may enable sending PII data.
        send_default_pii=True,
    )

try:
    from .local_settings import *  # noqa
except ImportError:
    pass
