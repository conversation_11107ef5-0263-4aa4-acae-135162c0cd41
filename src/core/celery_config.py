config = {
    'database_engine_options': {'echo': False},
    'worker_max_memory_per_child': 256 * 1024,
    'task_acks_late': True,
    'task_annotations': {
        # socket events
        'shifts.tasks.send_socket_event_shift_started': {
            'queue': 'socket_events',
            'delivery_mode': 'transient'
        },
        'shifts.tasks.send_socket_event_shift_ended': {
            'queue': 'socket_events',
            'delivery_mode': 'transient'
        },
        'shifts.tasks.send_socket_event_shift_updated': {
            'queue': 'socket_events',
            'delivery_mode': 'transient',
            'ignore_result': True,
            'expires': 300
        },
        'shifts.tasks.send_socket_event_progress_shifts_updated': {
            'queue': 'socket_events',
            'delivery_mode': 'transient',
            'ignore_result': True,
            'expires': 300
        },
        'shifts.tasks.send_socket_event_shift_created': {
            'queue': 'socket_events',
            'delivery_mode': 'transient'
        },
        'shifts.tasks.send_socket_event_shift_deleted': {
            'queue': 'socket_events',
            'delivery_mode': 'transient'
        },
        'finance.tasks.send_socket_event_request_payment_created': {
            'queue': 'socket_events',
            'delivery_mode': 'transient'
        },
        'finance.tasks.send_socket_event_request_payment_updated': {
            'queue': 'socket_events',
            'delivery_mode': 'transient'
        },
        'finance.tasks.send_socket_event_payout_review_created': {
            'queue': 'socket_events',
            'delivery_mode': 'transient'
        },
        'finance.tasks.send_socket_event_payout_review_updated': {
            'queue': 'socket_events',
            'delivery_mode': 'transient'
        },
        'finance.tasks.send_socket_event_payout_review_deleted': {
            'queue': 'socket_events',
            'delivery_mode': 'transient'
        },
        'finance.tasks.send_socket_event_request_cashflow_created': {
            'queue': 'socket_events',
            'delivery_mode': 'transient'
        },
        'finance.tasks.send_socket_event_request_cashflow_updated': {
            'queue': 'socket_events',
            'delivery_mode': 'transient'
        },
        'finance.tasks.send_socket_event_request_cashflow_deleted': {
            'queue': 'socket_events',
            'delivery_mode': 'transient'
        },

        # cleaner
        'only_fans_db.tasks.list_messages_temp_cleaner': {
            'queue': 'cleaner',
            'delivery_mode': 'transient'
        },

        # services_queue

        # 'fans_sessions.tasks.collecting_fans_sessions': {
        #     'queue': 'services_queue'
        # },
        # 'shifts.tasks.write_operator_profits': {
        #     'queue': 'services_queue'
        # },
        'shift.tasks.check_shifts_without_result_stats': {
            'queue': 'services_queue'
        },
        'accounts.tasks.update_telegram_operator': {
            'queue': 'services_queue'
        },
        'accounts.tasks.create_telegram_operator': {
            'queue': 'services_queue'
        },
        'accounts.tasks.delete_telegram_operator': {
            'queue': 'services_queue'
        },
        'ads.tasks.update_ads_stats': {
            'queue': 'services_queue',
            'delivery_mode': 'transient'
        },
        'only_fans_models_plan.tasks.update_plan_fact_results': {
            'queue': 'services_queue',
            'delivery_mode': 'transient'
        },
        'only_fans_models_plan.tasks.update_specific_plan_fact_results': {
            'queue': 'services_queue'
        },
        'ads.tasks.create_ads_tracking_daily_datas': {
            'queue': 'services_queue'
        },
        'ads.tasks.write_link_logging': {
            'queue':  'services_queue'
        },
        'ads.tasks.write_promotions_count': {
            'queue': 'services_queue'
        },
        'shifts.tasks.update_day_before_yesterday_shifts_data': {
            'queue': 'services_queue'
        },
        'refresh_fans_models.tasks.refresh': {
            'queue': 'services_queue'
        },
        'refresh_fans_models.tasks.retry_refresh': {
            'queue': 'services_queue'
        },
        'peopleforce.tasks.synchronize_people_force': {
            'queue': 'services_queue',
            'delivery_mode': 'transient'
        },
        'finance.tasks.collect_new_payouts': {
            'queue': 'services_queue',
            'delivery_mode': 'transient'
        },
        'finance.tasks.write_total_debt_history': {
            'queue': 'services_queue',
        },
        'finance.tasks.write_total_income_fans_history': {
            'queue': 'services_queue',
        },
        'shifts.tasks.create_externals_shifts': {
            'queue': 'services_queue'
        },
        'ads.tasks.update_ads_friends_status': {
            'queue': 'services_queue'
        },
        'shifts.tasks.update_shift_stats_by_shift_id': {
            'queue': 'services_queue'
        },
        'shifts.tasks.update_shift_stats_for_progress_shifts': {
            'queue': 'services_queue'
        },
        'ads.tasks.write_ads_daily_data': {
            'queue': 'services_queue'
        },
        'ads.tasks.write_ads_models_fans_income_costs': {
            'queue': 'services_queue'
        },
        'shifts.tasks.db_sales_synchronization': {
            'queue': 'services_queue'
        },
        'finance.tasks.create_total_spend_copy_table_snapshots': {
            'queue': 'services_queue'
        },
        'ads.tasks.switch_ads_new_donor_promos': {
            'queue': 'services_queue'
        },
        'finance.tasks.write_model_donor_debt_history': {
            'queue': 'services_queue'
        },

        # notifications
        'finance.tasks.send_request_payment_notification': {
            'queue': 'notifications'
        },
        'base.tasks.send_bug_notification': {
            'queue': 'notifications',
            'delivery_mode': 'transient'
        },
        'only_fans_models_plan.tasks.send_marketing_budget_alerts': {
            'queue': 'notifications',
            'delivery_mode': 'transient'
        }
    },
    'accept_content': ['json', 'application/x-python-serialize'],
    'task_serializer': 'json',
    'result_serializer': 'json',
    'event_serializer': 'json',
    'result_expires': 7200,
    'task_compression': 'gzip',
    'result_compression': 'gzip',
    'task_default_queue': 'celery_tasks',
    'redis_max_connections': 2,
    'broker_transport_options': {
        'max_connections': 2
    },
    'broker_pool_limit': 2,
}
