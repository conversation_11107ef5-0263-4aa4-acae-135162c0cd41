from django.contrib import admin

from fans_sessions.models import Session


@admin.register(Session)
class SessionAdmin(admin.ModelAdmin):
    fields = (
        'session_start',
        'session_end',
        'fan_id',
        'model_id',
        'active_chat',
        'date_payment',
        'time_to_paid',
        'messages_to_paid',
        'count_incoming_messages',
        'count_outgoing_messages',
        'sale_type'
    )
    list_display = (
        'fan_id',
        'model_id',
        'session_start',
        'session_end',
        'count_incoming_messages',
        'count_outgoing_messages',
        'active_chat',
        'date_payment',
        'time_to_paid',
        'messages_to_paid',
        'sale_type'
    )
