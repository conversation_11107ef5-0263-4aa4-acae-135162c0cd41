import datetime
from datetime import timedelta

from django.utils import timezone

from core.celery import BaseTaskWithRetry
from core.celery import app as celery_app
from fans_sessions.services import SessionService
from only_fans_db.services import OnlyFansDBDataCollector
from only_fans_models.services import OnlyFansModelService


@celery_app.task(bind=BaseTaskWithRetry)
def collecting_fans_sessions(self):
    """
    Task for collecting fans sessions
    """
    date = timezone.now().date() - timedelta(days=1)
    start_time = timezone.datetime.combine(date, datetime.time.min)
    end_time = timezone.datetime.combine(date, datetime.time.max)
    models_worked_yesterday = OnlyFansModelService.get_models_on_shifts_by_time_range(
        start_time=start_time,
        end_time=end_time
    )
    data_collector = OnlyFansDBDataCollector(
        start_time=start_time,
        end_time=end_time,
        shifts_models=models_worked_yesterday
    )
    session_service = SessionService(data_collector.messages_data, data_collector.sales_data)
    created_sessions = session_service.create_sessions()

    return f'Created {len(created_sessions)} sessions'
