from django.db import models
from django.utils.translation import gettext_lazy as _

from base.models import TimeStampedUUIDModel


class Session(TimeStampedUUIDModel):
    session_start = models.DateTimeField(
        help_text=_('Session start time'),
        blank=True,
        null=True
    )
    session_end = models.DateTimeField(
        help_text=_('Session end time'),
        blank=True,
        null=True
    )
    fan_id = models.IntegerField(
        help_text=_('Fan ID'),
    )
    model_id = models.IntegerField(
        help_text=_('Model ID'),
    )
    active_chat = models.BooleanField(
        help_text=_('Chat is active if number of messages in session is more than 3'),
        default=False
    )
    date_payment = models.DateTimeField(
        help_text=_('First payment date in session'),
        blank=True,
        null=True
    )
    time_to_paid = models.DurationField(
        help_text=_('Time to first payment in session from session start'),
        blank=True,
        null=True
    )
    messages_to_paid = models.IntegerField(
        help_text=_('Number of messages to first payment in session from session start'),
        blank=True,
        null=True
    )
    count_incoming_messages = models.IntegerField(
        help_text=_('Number of incoming messages from fan in session'),
    )
    count_outgoing_messages = models.IntegerField(
        help_text=_('Number of outgoing messages from model in session'),
    )
    sale_type = models.IntegerField(
        help_text=_('Sales type'),
        blank=True,
        null=True
    )
