# Generated by Django 4.2.2 on 2023-08-10 10:28

import uuid

import django.utils.timezone
from django.db import migrations, models

import base.models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Session",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "session_start",
                    models.DateTimeField(
                        blank=True, help_text="Session start time", null=True
                    ),
                ),
                (
                    "session_end",
                    models.DateTimeField(
                        blank=True, help_text="Session end time", null=True
                    ),
                ),
                ("fan_id", models.IntegerField(help_text="Fan ID")),
                ("model_id", models.IntegerField(help_text="Model ID")),
                (
                    "active_chat",
                    models.Bo<PERSON>anField(
                        default=False,
                        help_text="Chat is active if number of messages in session is more than 3",
                    ),
                ),
                (
                    "date_payment",
                    models.DateTimeField(
                        blank=True, help_text="First payment date in session", null=True
                    ),
                ),
                (
                    "time_to_paid",
                    models.DurationField(
                        blank=True,
                        help_text="Time to first payment in session from session start",
                        null=True,
                    ),
                ),
                (
                    "messages_to_paid",
                    models.IntegerField(
                        blank=True,
                        help_text="Number of messages to first payment in session from session start",
                        null=True,
                    ),
                ),
                (
                    "count_incoming_messages",
                    models.IntegerField(
                        help_text="Number of incoming messages from fan in session"
                    ),
                ),
                (
                    "count_outgoing_messages",
                    models.IntegerField(
                        help_text="Number of outgoing messages from model in session"
                    ),
                ),
                (
                    "sale_type",
                    models.IntegerField(blank=True, help_text="Sales type", null=True),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
    ]
