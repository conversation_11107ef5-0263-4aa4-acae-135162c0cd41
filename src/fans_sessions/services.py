import datetime

from fans_sessions.models import Session


class SessionService:
    """
    Service for generating fans sessions during a given date
    """

    MODEL = Session
    MIN_MESSAGES_ACTIVE_SESSION = 3
    MAX_TIMEDELTA_BETWEEN_MESSAGES = datetime.timedelta(hours=2)
    SEARCH_SALES_TYPES = [2, 3]

    def __init__(
            self,
            messages_data: dict[int, list],
            sales_data: dict[int, list],
    ) -> None:
        self.messages_data = messages_data
        self.sales_data = sales_data

    def get_last_previous_session(
        self, message_date: datetime, fan_id: int, model_id: int
    ) -> MODEL | None:
        """
        Get last session from previous day

        Args:
            message_date: date of message
            fan_id: fan id
            model_id: model id
        """
        search_time_range = (message_date - self.MAX_TIMEDELTA_BETWEEN_MESSAGES, message_date)

        try:
            return self.MODEL.objects.filter(
                fan_id=fan_id,
                model_id=model_id,
                session_end__range=search_time_range,
            ).latest('session_end')
        except self.MODEL.DoesNotExist:
            return

    @staticmethod
    def _group_messages_by_fans(messages: list[dict]) -> dict[int, list[dict]]:
        result = {}

        for message in messages:
            try:
                if message['is_mass_message']:
                    continue

                fan_id = message['fan_id']

                if fan_id not in result:
                    result[fan_id] = []

                result[fan_id].append(message)

            except KeyError:
                continue

        return result

    def __update_last_previous_session(
        self, last_previous_session: MODEL, messages: list[dict]
    ) -> None:
        """
        Update last previous session

        Args:
            last_previous_session: last previous session
            messages: messages
        """
        count_messages_to_remove = 0
        count_incoming_messages = 0
        count_outgoing_messages = 0
        outgoing_messages_time_list = []

        for i, message in enumerate(messages):

            if message['is_incoming']:

                if (
                    message['message_time'] - last_previous_session.session_end
                    > self.MAX_TIMEDELTA_BETWEEN_MESSAGES
                ):
                    break

                last_previous_session.session_end = message['message_time']
                count_incoming_messages += 1

            else:
                count_outgoing_messages += 1
                outgoing_messages_time_list.append(message['message_time'])

            count_messages_to_remove += 1

        if count_messages_to_remove > 0 and last_previous_session.date_payment is None:
            data_with_sale = self.handle_session_has_sale(
                {
                    'session_start': last_previous_session.session_start,
                    'session_end': last_previous_session.session_end,
                    'outgoing_messages_time_list': outgoing_messages_time_list,
                    'fan_id': last_previous_session.fan_id,
                    'model_id': last_previous_session.model_id,
                }
            )

            if data_with_sale is not None and data_with_sale.get('date_payment') is not None:
                last_previous_session.date_payment = data_with_sale['date_payment']
                last_previous_session.time_to_paid = data_with_sale['time_to_paid']
                last_previous_session.messages_to_paid = data_with_sale[
                    'messages_to_paid'
                ]
                last_previous_session.sale_type = data_with_sale['sale_type']

        last_previous_session.count_incoming_messages += count_incoming_messages
        last_previous_session.count_outgoing_messages += count_outgoing_messages

        if (
            not last_previous_session.active_chat
            and last_previous_session.count_incoming_messages >= self.MIN_MESSAGES_ACTIVE_SESSION
        ):
            last_previous_session.active_chat = True

        last_previous_session.save()

        for _ in range(count_messages_to_remove):
            messages.pop(0)

    @staticmethod
    def _get_initial_session_data(model_id: int, message: dict) -> dict:
        """
        Get initial session data

        Args:
            model_id(int): model id
            message(dict): message
        """
        return {
            'session_start': message['message_time'],
            'session_end': message['message_time'],
            'count_incoming_messages': 1,
            'outgoing_messages_time_list': [],
            'active_chat': False,
            'date_payment': None,
            'time_to_paid': None,
            'messages_to_paid': None,
            'fan_id': message['fan_id'],
            'model_id': model_id,
            'count_outgoing_messages': 0,
            'sale_type': None,
            'not_saved': True,
        }

    @staticmethod
    def _get_first_incoming_message(
            messages: list[dict]
    ) -> dict:
        """
        Get first incoming message and its index

        Args:
            messages(list[dict]): messages
        """
        index = None
        incoming_message = None

        for i, message in enumerate(messages):

            if message['is_incoming']:
                incoming_message = message
                break

            index = i

        if incoming_message and index is not None:
            for _ in range(index + 1):
                messages.pop(0)

        return incoming_message

    def handle_session_has_sale(
            self,
            session_data: dict,
    ) -> dict | None:
        """
        Handle session has sale

        Args:
            session_data(dict): session data
        """
        model_sales = self.sales_data.get(session_data['model_id'])

        if not model_sales:
            return

        sales_during_session = list(
            filter(
                lambda sale: (
                                     session_data.get('session_start')
                                     <= sale.get('trans_date')
                                     <= session_data.get('session_end')
                             )
                and sale.get('fan_id') == session_data.get('fan_id')
                and sale.get('type') in self.SEARCH_SALES_TYPES,
                model_sales
            )
        )

        if sales_during_session:
            first_sale = sales_during_session[0]
            session_data['date_payment'] = first_sale['trans_date']
            session_data['time_to_paid'] = first_sale['trans_date'] - session_data['session_start']
            session_data['sale_type'] = first_sale['type']

            if session_data['date_payment'] > session_data['session_end']:
                session_data['session_end'] = session_data['date_payment']

            session_data['messages_to_paid'] = 0

            for message_time in session_data['outgoing_messages_time_list']:
                if message_time <= session_data['date_payment']:
                    session_data['messages_to_paid'] += 1
                else:
                    break

        return session_data

    @staticmethod
    def __remove_fields_from_session_data(session_data: dict, fields: list[str]) -> None:
        """
        Remove fields from session data

        Args:
            session_data(dict): session data
            fields(list[str]): fields
        """
        for field in fields:
            session_data.pop(field)

    def __create_session_object(
            self,
            session_data: dict,
    ) -> MODEL:
        """
        Create Session object

        Args:
            session_data(dict): session data
        """
        if (
                not session_data['active_chat']
                and session_data['count_incoming_messages']
                >= self.MIN_MESSAGES_ACTIVE_SESSION
        ):
            session_data['active_chat'] = True

        self.handle_session_has_sale(session_data)
        self.__remove_fields_from_session_data(session_data, ['not_saved', 'outgoing_messages_time_list'])

        return self.MODEL(**session_data)

    def _generate_sessions_for_single_fan(
        self,
        model_id: int,
        fan_messages: list[dict]
    ) -> list[MODEL]:
        """
        Generate sessions for a single fan

        Args:
            model_id(int): model id
            fan_messages(list[dict]): fan messages
        """
        sessions = []
        current_message = self._get_first_incoming_message(fan_messages)

        if current_message is None:
            return sessions

        session_data = self._get_initial_session_data(model_id, current_message)

        if len(fan_messages) == 1:
            session = self.__create_session_object(session_data)
            sessions.append(session)

            return sessions

        for i, message in enumerate(fan_messages[1:]):
            try:

                if message['is_incoming']:
                    if (
                        message['message_time'] - current_message['message_time']
                        <= self.MAX_TIMEDELTA_BETWEEN_MESSAGES
                    ):
                        session_data['session_end'] = message['message_time']
                        session_data['count_incoming_messages'] += 1

                    else:
                        session = self.__create_session_object(session_data)
                        sessions.append(session)

                        session_data = self._get_initial_session_data(model_id, message)
                        continue
                else:
                    session_data['count_outgoing_messages'] += 1
                    session_data['outgoing_messages_time_list'].append(
                        message['message_time']
                    )

                current_message = message

            except KeyError:
                continue

        if session_data.get('not_saved'):
            session = self.__create_session_object(session_data)
            sessions.append(session)

        return sessions

    def generate_sessions(self) -> list[MODEL]:
        """
        Generate sessions objects from messages data
        """
        all_sessions = []

        for model_id, messages in self.messages_data.items():
            grouped_messages_by_fans = self._group_messages_by_fans(messages)

            for fan_id, fan_messages in grouped_messages_by_fans.items():
                last_previous_session = self.get_last_previous_session(
                    fan_messages[0]['message_time'], fan_id, model_id
                )

                if last_previous_session:
                    self.__update_last_previous_session(
                        last_previous_session, fan_messages
                    )

                if fan_messages:
                    fan_sessions = self._generate_sessions_for_single_fan(model_id, fan_messages)

                    if fan_sessions:
                        all_sessions.extend(fan_sessions)

        return all_sessions

    def create_sessions(self) -> list[MODEL]:
        """
        Create sessions objects from messages data and save them to database
        """
        sessions = self.generate_sessions()

        if sessions:

            return self.MODEL.objects.bulk_create(sessions)
