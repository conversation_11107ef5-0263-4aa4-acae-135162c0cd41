import requests
from django.http import StreamingHttpResponse
from rest_framework import (
    generics,
    mixins,
    views,
    viewsets,
)
from rest_framework.permissions import AllowAny
from rest_framework_api_key.permissions import HasAP<PERSON><PERSON><PERSON>

from content_request.filters import <PERSON><PERSON><PERSON><PERSON><PERSON>
from content_request.models import Chat, ContentRequest
from content_request.serializers import ContentRequestChatSerializer, ContentRequestSerializer
from content_request.services import TelegramContentRequestFileProxyService


class ContentRequestViewSet(mixins.CreateModelMixin, viewsets.GenericViewSet):
    """
    API endpoint that allows to manage ContentRequest objects
    """
    queryset = ContentRequest.objects.all()
    serializer_class = ContentRequestSerializer
    permission_classes = [HasAPIKey]


class ChatListView(generics.ListAPIView):
    """
    API endpoint that allows to list ContentRequestChatConfig objects
    """
    queryset = Chat.objects.all()
    serializer_class = ContentRequestChatSerializer
    permission_classes = [HasAPIKey]
    filterset_class = ChatFilter


class ProxyFileView(views.APIView):
    """
    API endpoint that allows to proxy file requests
    """
    permission_classes = [AllowAny]

    def get(self, request, file_id, *args, **kwargs):
        service = TelegramContentRequestFileProxyService()
        file_path, file_url = service.get_file_path_and_url(file_id=file_id)

        tg_response = requests.get(file_url, stream=True)
        tg_response.raise_for_status()

        content_type = tg_response.headers.get("Content-Type", "application/octet-stream")
        filename = file_path.split("/")[-1]

        response = StreamingHttpResponse(
            streaming_content=tg_response.iter_content(chunk_size=8192),
            content_type=content_type
        )
        response["Content-Disposition"] = f'inline; filename="{filename}"'

        return response
