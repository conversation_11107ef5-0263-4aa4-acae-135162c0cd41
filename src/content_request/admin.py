from django.contrib import admin

from content_request.models import (
    Chat,
    ContentRequest,
    ContentRequestChatConfig,
)


@admin.register(ContentRequest)
class ContentRequestAdmin(admin.ModelAdmin):
    pass


class ChatInline(admin.TabularInline):
    model = Chat
    extra = 1


@admin.register(ContentRequestChatConfig)
class ContentRequestChatConfigAdmin(admin.ModelAdmin):
    inlines = (ChatInline,)
