from rest_framework import serializers

from content_request.models import Chat, ContentRequest
from content_request.services import ContentRequestService


class ContentRequestSerializer(serializers.ModelSerializer):
    """
    Serializer for a content request
    """
    file_ids = serializers.ListField(
        child=serializers.CharField(),
        write_only=True,
        required=False,
        allow_null=True
    )

    class Meta:
        model = ContentRequest
        fields = (
            "tg_username",
            "tg_id",
            "department",
            "only_fans_model",
            "business_unit",
            "team_lead",
            "request_type",
            "deadline",
            "description",
            "tg_chat_group_id",
            "tg_chat_group_thread_id",
            "file_ids"
        )

    def create(self, validated_data):
        site_url = self.context['request'].build_absolute_uri('/')

        service = ContentRequestService()
        content_request = service.create_content_request(
            site_url=site_url,
            validated_data=validated_data
        )

        return content_request


class ContentRequestChatSerializer(serializers.ModelSerializer):
    """
    Serializer for a content request chats
    """
    class Meta:
        model = Chat
        fields = (
            "id",
            "name",
            "tg_chat_group_id",
            "tg_chat_group_thread_id",
        )
