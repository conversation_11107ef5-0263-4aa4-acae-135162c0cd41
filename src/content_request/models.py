from django.db import models
from django.utils.translation import gettext_lazy as _

from base.models import TimeStampedModel, TimeStampedUUIDModel


class ContentRequest(TimeStampedModel):
    """
    Model for content requests
    """

    class DepartmentChoices(models.TextChoices):
        SALES = 'Sales', _('Sales')
        SMM = 'SMM', _('SMM')

    class RequestType(models.TextChoices):
        CONTENT_PLAN = 'Content plan', _('Content plan')
        STREAM = 'Stream', _('Stream')

    tg_username = models.CharField(max_length=63, help_text=_('Tg username'))
    tg_id = models.BigIntegerField(help_text=_('Tg id'))
    department = models.CharField(max_length=63, choices=DepartmentChoices.choices)
    only_fans_model = models.ForeignKey(
        'only_fans_models.OnlyFansModel',
        on_delete=models.PROTECT,
        help_text=_('OnlyFansModel'),
        related_name='content_requests'
    )
    business_unit = models.ForeignKey(
        'accounts.User',
        on_delete=models.PROTECT,
        help_text=_('Business unit'),
        limit_choices_to={'role__name': 'business_unit'},
        related_name='bu_content_requests'
    )
    team_lead = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        help_text=_('Team lead'),
        limit_choices_to={'role__name': 'team_lead'},
        null=True,
        related_name='tl_content_requests'
    )
    request_type = models.CharField(max_length=63, choices=RequestType.choices)
    deadline = models.DateField(help_text=_('Deadline'))
    description = models.TextField(help_text=_('Description'))
    tg_chat_group_id = models.BigIntegerField(help_text=_('Tg chat group id'))
    tg_chat_group_thread_id = models.IntegerField(help_text=_('Tg chat group thread'))
    image_links = models.JSONField(help_text=_('Image links'), blank=True, null=True)

    class Meta:
        ordering = ['-created_at']
        db_table = 'content_request'

    def __str__(self):
        return f'{self.tg_username} - {self.only_fans_model} - {self.department} - {self.request_type}'


class ContentRequestChatConfig(TimeStampedUUIDModel):
    """
    Model for content request chat config
    """
    business_unit = models.OneToOneField(
        'accounts.User',
        on_delete=models.CASCADE,
        help_text=_('Business unit'),
        limit_choices_to={'role__name': 'business_unit'},
        related_name='chat_config'
    )

    def __str__(self):
        return f'{self.business_unit}'


class Chat(TimeStampedUUIDModel):
    """
    Model for content request chat
    """
    class NameChoices(models.TextChoices):
        SMM = 'SMM', _('SMM')
        SALES = 'Sales', _('Sales')
        STREAM = 'Stream', _('Stream')

    chat_config = models.ForeignKey(
        'content_request.ContentRequestChatConfig',
        on_delete=models.CASCADE,
        help_text=_('Chat config'),
        related_name='chats'
    )
    tg_chat_group_id = models.BigIntegerField(help_text=_('Tg chat group id'))
    tg_chat_group_thread_id = models.IntegerField(help_text=_('Tg chat group thread'))
    name = models.CharField(max_length=255, help_text=_('Chat name'), choices=NameChoices.choices)

    class Meta:
        ordering = ['name']
        constraints = [
            models.UniqueConstraint(
                fields=['chat_config', 'name'],
                name='unique_chat_name'
            ),
            models.UniqueConstraint(
                fields=['chat_config', 'tg_chat_group_id', 'tg_chat_group_thread_id'],
                name='unique_chat_tg_id'
            )
        ]

    def __str__(self):
        return f'{self.chat_config.business_unit} - {self.name}'
