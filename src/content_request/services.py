import logging
from urllib.parse import urljoin

import requests
from django.conf import settings
from django.urls import reverse

from content_request.exceptions import TelegramContentRequestFileProxyException
from content_request.models import ContentRequest

logger = logging.getLogger(__name__)


class ContentRequestService:
    """
    Service for managing content requests
    """
    def generate_image_links(self, site_url: str, file_ids: list[str]) -> list[str]:
        """
        Generate image links

        Args:
            site_url (str): Site url
            file_ids (list[str]): List of file ids

        Returns:
            list[str]: List of image links
        """
        return [
            urljoin(site_url, reverse('content-requests:proxy-file', args=[file_id]))
            for file_id in file_ids
        ]

    def create_content_request(self, site_url: str, validated_data: dict) -> ContentRequest:
        """
        Create a content request

        Args:
            site_url (str): Site url
            validated_data (dict): Validated data

        Returns:
            ContentRequest: Created content request
        """
        file_ids = validated_data.pop('file_ids', [])
        image_links = None

        if file_ids:
            image_links = self.generate_image_links(site_url, file_ids)

        content_request = ContentRequest.objects.create(
            **validated_data,
            image_links=image_links
        )

        return content_request


class TelegramContentRequestFileProxyService:
    """
    Service for proxying file requests from Telegram
    """

    def get_file_path_and_url(self, file_id: str) -> tuple[str, str]:
        """
        Get file url from Telegram

        Args:
            file_id (str): File id from Telegram

        Returns:
            str: File url
        """
        file_info_url = f"https://api.telegram.org/bot{settings.CONTENT_REQUEST_BOT_TOKEN}/getFile?file_id={file_id}"
        file_info_resp = requests.get(file_info_url)

        file_info_resp.raise_for_status()
        file_info_json = file_info_resp.json()

        if not file_info_json.get("ok"):
            logger.error(f"Failed to get file info from Telegram. Response: {file_info_json}")

            raise TelegramContentRequestFileProxyException(file_info_json.get("description"))

        file_path: str = file_info_json["result"]["file_path"]
        file_url: str = f"https://api.telegram.org/file/bot{settings.CONTENT_REQUEST_BOT_TOKEN}/{file_path}"

        return file_path, file_url
