from django.urls import path
from rest_framework import routers

from content_request.views import (
    ChatListView,
    ContentRequestViewSet,
    ProxyFileView,
)

app_name = 'content-requests'

router = routers.DefaultRouter()
router.register(
    '',
    ContentRequestViewSet,
    basename='content-requests'
)

urlpatterns = router.urls

urlpatterns += [
    path('chats/', ChatListView.as_view(), name='chats'),
    path('proxy-file/<str:file_id>/', ProxyFileView.as_view(), name='proxy-file'),
]
