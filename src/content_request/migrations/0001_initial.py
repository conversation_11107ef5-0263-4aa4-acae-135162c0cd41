# Generated by Django 4.2.2 on 2025-06-11 20:32

import base.models
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("only_fans_models", "0013_historicalonlyfansmodel"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="ContentRequest",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "tg_username",
                    models.CharField(help_text="Tg username", max_length=63),
                ),
                ("tg_id", models.BigIntegerField(help_text="Tg id")),
                (
                    "department",
                    models.CharField(
                        choices=[("Sales", "Sales"), ("SMM", "SMM")], max_length=63
                    ),
                ),
                (
                    "request_type",
                    models.CharField(
                        choices=[
                            ("Content plan", "Content plan"),
                            ("Stream", "Stream"),
                        ],
                        max_length=63,
                    ),
                ),
                ("deadline", models.DateField(help_text="Deadline")),
                ("description", models.TextField(help_text="Description")),
                (
                    "tg_chat_group_id",
                    models.BigIntegerField(help_text="Tg chat group id"),
                ),
                (
                    "tg_chat_group_thread_id",
                    models.IntegerField(help_text="Tg chat group thread"),
                ),
                (
                    "image_links",
                    models.JSONField(blank=True, help_text="Image links", null=True),
                ),
                (
                    "business_unit",
                    models.ForeignKey(
                        help_text="Business unit",
                        limit_choices_to={"role__name": "business_unit"},
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="bu_content_requests",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "only_fans_model",
                    models.ForeignKey(
                        help_text="OnlyFansModel",
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="content_requests",
                        to="only_fans_models.onlyfansmodel",
                    ),
                ),
                (
                    "team_lead",
                    models.ForeignKey(
                        help_text="Team lead",
                        limit_choices_to={"role__name": "team_lead"},
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="tl_content_requests",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "content_request",
                "ordering": ["-created_at"],
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
    ]
