# Generated by Django 4.2.2 on 2025-06-16 12:55

import base.models
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("content_request", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="ContentRequestChatConfig",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "business_unit",
                    models.OneToOneField(
                        help_text="Business unit",
                        limit_choices_to={"role__name": "business_unit"},
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="chat_config",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.CreateModel(
            name="Chat",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "tg_chat_group_id",
                    models.BigIntegerField(help_text="Tg chat group id"),
                ),
                (
                    "tg_chat_group_thread_id",
                    models.IntegerField(help_text="Tg chat group thread"),
                ),
                (
                    "name",
                    models.CharField(
                        choices=[
                            ("SMM", "SMM"),
                            ("Sales", "Sales"),
                            ("Stream", "Stream"),
                        ],
                        help_text="Chat name",
                        max_length=255,
                    ),
                ),
                (
                    "chat_config",
                    models.ForeignKey(
                        help_text="Chat config",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="chats",
                        to="content_request.contentrequestchatconfig",
                    ),
                ),
            ],
            options={
                "ordering": ["name"],
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.AddConstraint(
            model_name="chat",
            constraint=models.UniqueConstraint(
                fields=("chat_config", "name"), name="unique_chat_name"
            ),
        ),
        migrations.AddConstraint(
            model_name="chat",
            constraint=models.UniqueConstraint(
                fields=("chat_config", "tg_chat_group_id", "tg_chat_group_thread_id"),
                name="unique_chat_tg_id",
            ),
        ),
    ]
