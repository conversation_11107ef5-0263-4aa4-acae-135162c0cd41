from django.contrib.auth import get_user_model
from django_filters import rest_framework as filters

from content_request.models import Chat

User = get_user_model()


class ChatFilter(filters.FilterSet):
    """
    Filter for Chat
    """
    business_unit = filters.ModelChoiceFilter(
        field_name='chat_config__business_unit',
        queryset=User.objects.filter(role__name='business_unit')
    )

    class Meta:
        model = Chat
        fields = ['business_unit']
