from django.urls import include, path
from rest_framework import routers

from finance.views import (
    AveragePurchaseOnModelView,
    BudgetPeriodViewSet,
    BudgetViewSet,
    CashflowDepartmentViewSet,
    CashflowExpenseItemViewSet,
    CashflowExpenseSubItemViewSet,
    CashflowIncomeItemViewSet,
    CashflowIncomeRequestViewSet,
    CashflowRequestViewSet,
    CategoriesSummary,
    CategoriesSummaryCopy,
    DebtTrackingListCopyView,
    DebtTrackingListView,
    DebtViewSet,
    ForecastViewSet,
    FuturePurchaseViewSet,
    FutureTotalPurchaseView,
    IncomeFansTrackingListCopyView,
    IncomeFansTrackingListView,
    PaymentCryptoNetworkViewSet,
    PaymentCurrencyViewSet,
    PaymentDepartmentListView,
    PaymentExpenseItemViewSet,
    PaymentSystemViewSet,
    PaymentToolViewSet,
    PayoutAddressViewSet,
    PayoutReviewViewSet,
    RequestCashflowCreatorListView,
    RequestPaymentCreatorListView,
    RequestPaymentViewSet,
    TodayReportCopyView,
    TodayReportView,
    TotalDebtHistoryViewSet,
    TotalSpendViewSet,
)

router = routers.DefaultRouter()

router.register('total-spend', TotalSpendViewSet, basename='total-spend')
router.register('debts', DebtViewSet, basename='debts')
router.register('future-purchase', FuturePurchaseViewSet, basename='future-purchase')
router.register('forecast', ForecastViewSet, basename='forecast')
router.register('total-debts-history', TotalDebtHistoryViewSet, basename='total-debts-history')

# Payments
router.register(
    'payments/expense-items',
    PaymentExpenseItemViewSet,
    basename='payment-expense-items',
)
router.register(
    'payments/currencies', PaymentCurrencyViewSet, basename='payment-currencies'
)
router.register(
    'payments/crypto-networks',
    PaymentCryptoNetworkViewSet,
    basename='payment-crypto-networks',
)
router.register('payments/systems', PaymentSystemViewSet, basename='payment-systems')
router.register('payments/tools', PaymentToolViewSet, basename='payment-tools')
router.register('payments/requests', RequestPaymentViewSet, basename='payment-requests')

# Payouts
router.register(
    'payouts/reviews', PayoutReviewViewSet, basename='payout-reviews'
),
router.register(
    'payouts/addresses', PayoutAddressViewSet, basename='payout-addresses'
),

# Cashflow
router.register('cashflow/expense-items', CashflowExpenseItemViewSet, basename='cashflow-expense-items',)
router.register('cashflow/expense-sub-items', CashflowExpenseSubItemViewSet, basename='cashflow-expense-sub-items',)
router.register('cashflow/departments', CashflowDepartmentViewSet, basename='cashflow-departments',)
router.register('cashflow/requests', CashflowRequestViewSet, basename='cashflow-requests',)
router.register('cashflow/income-items', CashflowIncomeItemViewSet, basename='cashflow-income-items',)
router.register('cashflow/income-requests', CashflowIncomeRequestViewSet, basename='cashflow-income-requests',)

# Budgets
router.register('budget/periods', BudgetPeriodViewSet, basename='budget-periods')
router.register('budget/budgets', BudgetViewSet, basename='budget-budgets')

urlpatterns = [
    path('future-total-purchase/', FutureTotalPurchaseView.as_view(), name='future-total-purchase'),
    path('average-purchase-on-model/', AveragePurchaseOnModelView.as_view(), name='average-purchase-on-model'),
    path('today-report/', TodayReportView.as_view(), name='today-report'),
    path('today-report/copy/', TodayReportCopyView.as_view(), name='today-report-copy'),
    path('categories-summary/', CategoriesSummary.as_view(), name='categories-summary'),
    path('categories-summary/copy/', CategoriesSummaryCopy.as_view(), name='categories-summary'),
    path('debts-tracking/', DebtTrackingListView.as_view(), name='debts-tracking'),
    path('debts-tracking/copy/', DebtTrackingListCopyView.as_view(), name='debts-tracking-copy'),
    path('income-fans-tracking/', IncomeFansTrackingListView.as_view(), name='income-fans-tracking'),
    path('income-fans-tracking/copy/', IncomeFansTrackingListCopyView.as_view(), name='income-fans-tracking-copy'),
    path('payments/creators/', RequestPaymentCreatorListView.as_view(), name='payment-creator-list'),
    path('payments/departments/', PaymentDepartmentListView.as_view(), name='payment-department-list'),
    path('cashflow/creators/', RequestCashflowCreatorListView.as_view(), name='cashflow-creator-list'),
    path('', include(router.urls)),
]
