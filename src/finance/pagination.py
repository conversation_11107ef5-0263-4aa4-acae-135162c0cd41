from base.pagination import FiftyPerPagePagination


class PayoutReviewPagination(FiftyPerPagePagination):
    def get_paginated_response(self, data):
        """
        Redefine method to add total_sum value to response
        """
        response = super().get_paginated_response(data)
        response.data['total_sum'] = data.serializer.context.get('total_sum', 0)

        return response

    def get_paginated_response_schema(self, schema):
        """
        Redefine method to get correct docs schema
        """
        response_schema = super().get_paginated_response_schema(schema)
        response_schema['properties']['total_sum'] = {
            'type': 'number',
            'example': 1000
        }

        return response_schema
