import datetime
from typing import Type

from django.contrib.auth import get_user_model
from django.db.models import Model
from django.utils import timezone
from rest_framework.serializers import Serializer

from base.services import WebSocketEventSender
from core.celery import BaseTaskWithRetry, app
from finance.models import (
    PayoutReview,
    RequestCashflow,
    RequestPayment,
)
from finance.notifications import TelegramRequestPaymentNotificationService
from finance.serializers import (
    CashflowListSerializer,
    PayoutReviewListSerializer,
    RequestPaymentListSerializer,
)
from finance.services import (
    ModelDonorDebtHistoryService,
    PayoutReviewService,
    TotalDebtHistoryService,
    TotalIncomeFansHistoryService,
    TotalSpendCopyTableSnapshotService,
)

User = get_user_model()


@app.task(bind=BaseTaskWithRetry)
def send_request_payment_notification(
    self, request_payment_id: int, finance_review_result: str
):
    request_payment = RequestPayment.objects.get(id=request_payment_id)

    notificator = TelegramRequestPaymentNotificationService()
    notificator.notify_request_payment_creator(request_payment, finance_review_result)

    return f"Notification sent to user: {request_payment.created_by_tg_id}"


def get_data_for_socket_event(
        instance_model: Type[Model],
        instance_id: str,
        serializer_class: Type[Serializer],
) -> dict:
    instance = instance_model.objects.get(id=instance_id)
    serializer = serializer_class(instance)

    user_ids = User.objects.filter(role__name__in=['financier', 'hof']).values_list('id', flat=True)

    return {
        'users_ids': [str(user_id) for user_id in user_ids],
        'data': serializer.data,
    }


@app.task(bind=BaseTaskWithRetry)
def send_socket_event_request_payment_created(self, request_payment_id: str):
    data = get_data_for_socket_event(
        instance_model=RequestPayment,
        instance_id=request_payment_id,
        serializer_class=RequestPaymentListSerializer
    )

    with WebSocketEventSender(url_path='requests-payments/created/') as ws:
        ws.send_event(data=data)

    return f'RequestPayment {request_payment_id} event created sent'


@app.task(bind=BaseTaskWithRetry)
def send_socket_event_request_payment_updated(self, request_payment_id: str):
    data = get_data_for_socket_event(
        instance_model=RequestPayment,
        instance_id=request_payment_id,
        serializer_class=RequestPaymentListSerializer
    )

    with WebSocketEventSender(url_path='requests-payments/updated/') as ws:
        ws.send_event(data=data)

    return f'RequestPayment {request_payment_id} event updated sent'


@app.task(bind=BaseTaskWithRetry)
def collect_new_payouts(self):
    days_from_now = 7

    date_from = timezone.now().date() - timezone.timedelta(days=days_from_now)
    created_payout_ids = PayoutReviewService().collect_new_payouts_from_only_fans_db(date_from=date_from)

    if created_payout_ids:
        for payout_id in created_payout_ids:
            send_socket_event_payout_review_created.delay(payout_id)

    return f'Created {len(created_payout_ids)} new payouts'


@app.task(bind=BaseTaskWithRetry)
def send_socket_event_payout_review_created(self, payout_review_id: str):
    data = get_data_for_socket_event(
        instance_model=PayoutReview,
        instance_id=payout_review_id,
        serializer_class=PayoutReviewListSerializer
    )

    with WebSocketEventSender(url_path='payout-reviews/created/') as ws:
        ws.send_event(data=data)

    return f'PayoutReview {payout_review_id} event created sent'


@app.task(bind=BaseTaskWithRetry)
def send_socket_event_payout_review_updated(self, payout_review_id: str):
    data = get_data_for_socket_event(
        instance_model=PayoutReview,
        instance_id=payout_review_id,
        serializer_class=PayoutReviewListSerializer
    )

    with WebSocketEventSender(url_path='payout-reviews/updated/') as ws:
        ws.send_event(data=data)

    return f'PayoutReview {payout_review_id} event updated sent'


@app.task(bind=BaseTaskWithRetry)
def send_socket_event_payout_review_deleted(self, payout_review_id: str):
    with WebSocketEventSender(url_path='payout-reviews/deleted/') as ws:
        user_ids = User.objects.filter(role__name__in=['financier', 'hof']).values_list('id', flat=True)
        ws.send_event(
            data={"data": {"id": payout_review_id}, 'users_ids': [str(user_id) for user_id in user_ids]}
        )

    return f'PayoutReview {payout_review_id} event deleted sent'


@app.task(bind=BaseTaskWithRetry)
def write_total_debt_history(self):
    today_date = timezone.now().date()
    total_debt_history = TotalDebtHistoryService.write_total_debt_history_by_date(debts_date=today_date)

    if total_debt_history is None:
        return f'Total debt history for {today_date} already exists'

    return f'Created total debt history for {today_date}'


@app.task(bind=BaseTaskWithRetry)
def write_total_income_fans_history(self):
    today_date = timezone.now().date()
    income_fans_history = TotalIncomeFansHistoryService.write_income_fans_history_by_date(today_date)

    if income_fans_history is None:
        return f'Total income fans history for {today_date} already exists'

    return f'Created income fans history for {today_date}'


@app.task(bind=BaseTaskWithRetry)
def send_socket_event_request_cashflow_created(self, request_cashflow_id: str):
    data = get_data_for_socket_event(
        instance_model=RequestCashflow,
        instance_id=request_cashflow_id,
        serializer_class=CashflowListSerializer
    )

    with WebSocketEventSender(url_path='request-cashflows/created/') as ws:
        ws.send_event(data=data)

    return f'RequestCashflow {request_cashflow_id} event created sent'


@app.task(bind=BaseTaskWithRetry)
def send_socket_event_request_cashflow_updated(self, request_cashflow_id: str):
    data = get_data_for_socket_event(
        instance_model=RequestCashflow,
        instance_id=request_cashflow_id,
        serializer_class=CashflowListSerializer
    )

    with WebSocketEventSender(url_path='request-cashflows/updated/') as ws:
        ws.send_event(data=data)

    return f'RequestCashflow {request_cashflow_id} event updated sent'


@app.task(bind=BaseTaskWithRetry)
def send_socket_event_request_cashflow_deleted(self, request_cashflow_id: str):
    with WebSocketEventSender(url_path='request-cashflows/deleted/') as ws:
        user_ids = User.objects.filter(role__name__in=['financier', 'hof']).values_list('id', flat=True)
        ws.send_event(
            data={"data": {"id": request_cashflow_id}, 'users_ids': [str(user_id) for user_id in user_ids]}
        )

    return f'RequestCashflow {request_cashflow_id} event deleted sent'


def is_last_day_of_month(date: datetime.date) -> bool:
    tomorrow = date + datetime.timedelta(days=1)

    return tomorrow.day == 1


@app.task(bind=BaseTaskWithRetry)
def create_total_spend_copy_table_snapshots(self):
    current_date = timezone.now().date()

    if not is_last_day_of_month(current_date):

        return "Today is not the last day of the month. Task will not execute."

    service = TotalSpendCopyTableSnapshotService()
    service.create_total_spend_copy_table_snapshots(current_date)

    return f'Created total spend copy table snapshots for {current_date}'


@app.task(bind=BaseTaskWithRetry)
def write_model_donor_debt_history(self):
    today_date = timezone.now().date()

    service = ModelDonorDebtHistoryService()
    model_donor_debt_history = service.write_model_donor_debt_history_by_date(today_date)

    if model_donor_debt_history is None:
        return f'Model donor debt history for {today_date} not created'

    return f'Created model donor debt history for {today_date}'
