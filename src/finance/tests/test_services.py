import datetime
from datetime import date, timedelta
from decimal import Decimal
from unittest.mock import MagicMock, patch

from dateutil.relativedelta import relativedelta
from django.db.models import Sum
from django.test import TestCase
from django.utils import timezone

from finance.dtos import DebtTrackingDTO, MonthDataDTO
from finance.models import (
    Budget,
    BudgetExpense,
    BudgetPeriod,
    CashflowExpenseItem,
    CashflowExpenseSubItem,
    MonthDebtTracking,
    PaymentCurrency,
    PaymentDepartment,
    PaymentSystem,
    RequestCashflow,
    TotalDebtHistory,
)
from finance.serializers import CashflowCreateSerializer
from finance.services import (
    BudgetPeriodService,
    BudgetService,
    RequestCashflowService,
    TotalDebtHistoryService,
)


class TotalDebtHistoryServiceTest(TestCase):

    def setUp(self):
        self.service = TotalDebtHistoryService()
        self.start_date = date(2024, 1, 1)
        self.end_date = date(2024, 1, 31)

        self.total_debt_histories = [
            TotalDebtHistory.objects.create(date=self.start_date + timedelta(days=i), debt_amount=100 * i)
            for i in range(14)
        ]

        for i, total_debt_history in enumerate(self.total_debt_histories):
            for j in range(12):
                MonthDebtTracking.objects.create(
                    month_date=self.start_date - relativedelta(year=1) + relativedelta(months=j + 1),
                    month_debt_amount=50 * j * (i + 1),
                    total_debt_history=total_debt_history
                )

    def test_get_debt_tracking_dtos(self):
        queryset = TotalDebtHistory.objects.all()
        dtos = self.service.get_debt_tracking_dtos(queryset, self.start_date, self.end_date)

        self.assertEqual(len(dtos), 5)
        for index, dto in enumerate(dtos):
            self.assertIsInstance(dto, DebtTrackingDTO)
            self.assertEqual(dto.number, index + 1)

            month_data_list = dto.month_data_dtos

            if not month_data_list:
                self.assertEqual(dto.total_debt, 0)
                self.assertEqual(dto.total_delta, 0)
                continue

            self.assertEqual(len(month_data_list), 12)
            for i, month_data in enumerate(month_data_list):
                self.assertIsInstance(month_data, MonthDataDTO)
                if index == 0:
                    self.assertEqual(month_data.delta, 0)
                else:
                    self.assertEqual(
                        month_data.delta,
                        month_data.month_debt_amount - dtos[0].month_data_dtos[i].month_debt_amount
                    )

            self.assertEqual(
                dto.total_debt,
                MonthDebtTracking.objects.filter(
                    total_debt_history__date=dto.parse_date
                ).aggregate(
                    total=Sum('month_debt_amount')
                )['total']
            )
            self.assertEqual(
                dto.total_delta,
                sum(month_data_dto.delta for month_data_dto in dto.month_data_dtos) if dto.month_data_dtos else 0
            )


class TestCashflowService(TestCase):
    def setUp(self):
        self.department = PaymentDepartment.objects.create(
            name="Test Department",
            map_value="test_department"
        )
        self.usd_currency = PaymentCurrency.objects.create(name="USD")
        self.uah_currency = PaymentCurrency.objects.create(name="UAH")
        self.expense_item = CashflowExpenseItem.objects.create(
            name="Expense Item",
        )
        self.expense_sub_item = CashflowExpenseSubItem.objects.create(
            name="Expense Sub Item",
        )
        self.payment_system = PaymentSystem.objects.create(
            name="System"
        )

    def test_create(self):
        data = {
            "cashflow_expense_item": "Expense Item",
            "cashflow_expense_sub_item": "Expense Sub Item",
            "currency": "UAH",
            "amount": 1000.88,
            "payment_system": "System",
            "department": self.department.name,
            "model_nickname": "nickname",
            "created_by": "Bob bobovich",
            "payment_details": "details",
            "payment_description": "descr",
            "financier": "Bob bobovich"
        }
        serializer = CashflowCreateSerializer(data=data)
        serializer.is_valid(raise_exception=True)

        currency_converter = MagicMock()
        currency_converter.convert.return_value = Decimal('999999.99')
        budget_expense_service = MagicMock()
        budget_expense_service.create.return_value = BudgetExpense.objects.create(
            date=datetime.date.today(),
            payment_date=datetime.date.today(),
            expense_item="Expense Item",
            amount_usd=Decimal('999999.99'),
            department=self.department,
            department_name=self.department.name,
            payment_system="System",
            status=BudgetExpense.Status.DONE,
            cashflow_expense_sub_item="Expense Sub Item",
            finance_review_result="accept"
        )
        sheet_service = MagicMock()

        request_cashflow_service = RequestCashflowService(
            currency_converter=currency_converter,
            budget_expense_service=budget_expense_service,
            sheet_service=sheet_service
        )
        result = request_cashflow_service.create(
            **serializer.validated_data,
        )

        self.assertIsInstance(result, RequestCashflow)
        self.assertEqual(result.amount_usd, Decimal('999999.99'))
        self.assertIsNotNone(result.payment_date)
        currency_converter.convert.assert_called_once_with(
            from_currency="UAH",
            to_currency="USD",
            amount=Decimal('1000.88')
        )
        budget_expense_service.create.assert_called_once_with(
            date=result.date,
            payment_date=result.payment_date,
            enrollment_date=result.enrollment_date,
            expense_item=data["cashflow_expense_item"],
            amount_usd=Decimal('999999.99'),
            department=self.department,
            payment_system=data["payment_system"],
            status=BudgetExpense.Status.DONE,
            cashflow_expense_sub_item=data["cashflow_expense_sub_item"],
            is_wage_fund=False,
            description=data['payment_description'],
            finance_review_result="accept",
        )
        self.assertEqual(result.amount, Decimal('1000.88'))
        self.assertEqual(result.amount_usd, Decimal('999999.99'))
        sheet_service.add_row.assert_called_once_with(result)

    def test_update(self):
        request_cashflow = MagicMock()
        request_cashflow.currency = self.usd_currency
        request_cashflow.save.return_value = None

        currency_converter = MagicMock()
        currency_converter.convert.return_value = Decimal('999999.99')
        budget_expense_service = MagicMock()
        sheet_service = MagicMock()
        request_cashflow_service = RequestCashflowService(
            currency_converter=currency_converter,
            budget_expense_service=budget_expense_service,
            sheet_service=sheet_service
        )
        request_cashflow_service.update(request_cashflow, amount=Decimal('1000.88'))
        currency_converter.convert.assert_called_once_with(
            from_currency=request_cashflow.currency.name,
            to_currency="USD",
            amount=Decimal('1000.88')
        )
        budget_expense_service.update.assert_called_once()
        request_cashflow.save.assert_called_once()
        self.assertEqual(request_cashflow.amount_usd, currency_converter.convert.return_value)
        self.assertEqual(request_cashflow.amount, Decimal('1000.88'))
        sheet_service.update_row.assert_called_once()

    def test_copy(self):
        data = {
            "cashflow_expense_item": self.expense_item,
            "cashflow_expense_sub_item": self.expense_sub_item,
            "currency": self.usd_currency,
            "amount": Decimal('1000'),
            "payment_system": self.payment_system,
            "department": self.department,
            "model_nickname": "nickname",
            "created_by": "Bob bobovich",
            "payment_details": "details",
            "payment_description": "descr",
            "financier": "Bob bobovich",
        }
        source_request_cashflow = RequestCashflow(
            **data
        )
        currency_converter = MagicMock()
        currency_converter.convert.return_value = Decimal('1000')
        sheet_service = MagicMock()
        request_cashflow_service = RequestCashflowService(
            currency_converter=currency_converter,
            sheet_service=sheet_service
        )
        user = MagicMock()
        user.full_name = "Tom tomovich"
        result = request_cashflow_service.copy_request_cashflow(source_request_cashflow, user=user)

        self.assertEqual(result.created_by, user.full_name)
        self.assertEqual(result.financier, user.full_name)
        self.assertEqual(result.amount, 0)

        today_date = timezone.now().date()
        self.assertEqual(result.date, today_date)
        self.assertEqual(result.payment_date, today_date)
        self.assertEqual(result.enrollment_date, today_date)


class TestBudgetPeriodService(TestCase):
    def setUp(self):
        self.department = PaymentDepartment.objects.create(
            name="Test Department",
            map_value="test_department"
        )
        self.cashflow_wage_expense_item = CashflowExpenseItem.objects.create(
            name="Wage Expense Item",
            is_wage_fund=True
        )
        self.cashflow_non_wage_expense_item = CashflowExpenseItem.objects.create(
            name="Expense Item",
            is_wage_fund=False
        )

    def test_create(self):
        budget_service = MagicMock()
        service = BudgetPeriodService(budget_service=budget_service)
        data = {
            "start_date": "2024-11-01",
            "end_date": "2024-11-01",
            "budgets": [
                {
                    "expected_plan": 1000,
                    "expected_wage_fund": 500,
                    "department": str(self.department.id)
                }
            ],
            "name": "string"
        }

        period = service.create(**data)

        budget_service.bulk_create.assert_called_once_with(
            period=period,
            validated_data=[
                {
                    "expected_plan": 1000,
                    "expected_wage_fund": 500,
                    "department": str(self.department.id)
                }
            ]
        )
        self.assertEqual(period.start_date, "2024-11-01")
        self.assertEqual(period.end_date, "2024-11-01")
        self.assertEqual(period.name, "string")


class TestBudgetService(TestCase):

    def setUp(self):
        self.department1 = PaymentDepartment.objects.create(
            name="Test Department",
            map_value="test_department"
        )
        self.department2 = PaymentDepartment.objects.create(
            name="Test Department 2",
            map_value="test_department_2"
        )
        self.budget_period = BudgetPeriod.objects.create(
            start_date="2024-10-01",
            end_date="2024-10-31",
            name="Budget Period"
        )
        self.budget_data = {
            "expected_plan": 1000,
            "expected_wage_fund": 500,
            "department": self.department1,
            "period": self.budget_period
        }

    def test_bulk_create(self):
        validated_data = [
            {"expected_plan": 1200, "expected_wage_fund": 600, "department": self.department1},
            {"expected_plan": 800, "expected_wage_fund": 400, "department": self.department2}
        ]

        budgets = BudgetService.bulk_create(period=self.budget_period, validated_data=validated_data)

        self.assertEqual(len(budgets), 2)
        self.assertEqual(budgets[0].expected_plan, 1200)
        self.assertEqual(budgets[1].expected_plan, 800)

    def test_link_new_budget_expenses_to_budget(self):
        self.budget = Budget.objects.create(
            **self.budget_data
        )
        budget_expense = BudgetExpense.objects.create(
            date="2024-10-01",
            payment_date="2024-10-15",
            enrollment_date="2024-10-15",
            expense_item="Expense item",
            amount_usd=1000,
            department=self.department1,
            department_name=self.department1.name,
            payment_system="System",
            status=BudgetExpense.Status.DONE,
        )

        number_linked_expenses = BudgetService.link_new_budget_expenses_to_budget(budget=self.budget)

        self.assertEqual(number_linked_expenses, 1)
        budget_expense.refresh_from_db()
        self.assertEqual(budget_expense.budget, self.budget)

    def test_create_budget(self):
        budget = BudgetService.create(**self.budget_data)

        self.assertEqual(budget.period, self.budget_period)
        self.assertEqual(budget.expected_plan, 1000)
        self.assertEqual(budget.expected_wage_fund, 500)

    @patch.object(BudgetService, 'link_new_budget_expenses_to_budget')
    def test_update_budget(self, mock_link_new_expenses):
        budget = Budget.objects.create(
            **self.budget_data
        )
        new_data = {
            "expected_plan": 1500,
            "expected_wage_fund": 700
        }

        updated_budget = BudgetService.update(instance=budget, **new_data)

        self.assertEqual(updated_budget.expected_plan, 1500)
        self.assertEqual(updated_budget.expected_wage_fund, 700)

        mock_link_new_expenses.assert_called_once_with(budget=updated_budget)
