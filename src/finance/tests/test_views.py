import datetime
import json
import uuid
from decimal import Decimal
from unittest import mock

from django.contrib.auth import get_user_model
from django.urls import reverse
from django.utils import timezone
from rest_framework import status

from ads.models import (
    Ads,
    Promo,
    Status,
)
from base.services import CurrencyConverter
from base.tests.mixins import BaseCRMTest
from finance.models import (
    BudgetExpense,
    CashflowExpenseItem,
    PaymentCurrency,
    PaymentDepartment,
    PaymentExpenseItem,
    PaymentSystem,
    PaymentTool,
    PayoutAddress,
    PayoutReview,
    RequestCashflow,
    RequestPayment,
    ReviewResult,
    TotalDebtHistory,
)


class TestFinanceViews(BaseCRMTest):
    def setUp(self):
        super().setUp()
        self.client.force_authenticate(user=self.user_hof)

    def test_read_permissions(self):
        for user in [
            self.user_operator,
            self.user_team_lead,
            self.user_senior_operator,
        ]:
            self.client.force_authenticate(user=user)
            response = self.client.get(reverse('total-spend-list'))
            self.assertEqual(response.status_code, 403)

            response = self.client.get(reverse('total-spend-copy-table'))
            self.assertEqual(response.status_code, 403)

        for user in [
            self.user_hof,
            self.user_financier,
            self.user_hom,
            self.user_marketer,
            self.user_superuser
        ]:
            self.client.force_authenticate(user=user)
            response = self.client.get(reverse('total-spend-list'))
            self.assertEqual(response.status_code, 200)

            response = self.client.get(reverse('total-spend-copy-table'))
            self.assertEqual(response.status_code, 200)

    def test_cache_total_spend(self):
        cache_key_total_spend = f'finance-total-spend_{self.user_hof.id}_'
        self.assertIsNone(self.cache_service.get_cache_data(cache_key_total_spend))
        response = self.client.get(reverse('total-spend-list'))
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIsNotNone(self.cache_service.get_cache_data(cache_key_total_spend))

        cache_key_total_spend_copy_table = (
            f'finance-total-spend-copy-table_{self.user_hof.id}_'
        )
        response = self.client.get(reverse('total-spend-copy-table'))
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIsNotNone(
            self.cache_service.get_cache_data(cache_key_total_spend_copy_table)
        )

        self.create_only_fans_model(1)
        self.assertIsNone(self.cache_service.get_cache_data(cache_key_total_spend))
        self.assertIsNone(
            self.cache_service.get_cache_data(cache_key_total_spend_copy_table)
        )

    def test_by_buy_date_filter(self):
        model = self.create_only_fans_model(1)
        Ads.objects.create(
            date=datetime.date(day=1, month=12, year=2023),
            only_fans_model=model,
            cost=1000,
        )
        ads_second = Ads.objects.create(
            date=datetime.date(day=2, month=12, year=2023),
            only_fans_model=model,
            buy_date=datetime.date(day=1, month=12, year=2023),
            cost=2000,
        )

        response = self.client.get(reverse('total-spend-list'))
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.json()), 2)

        response = self.client.get(
            reverse('total-spend-list'),
            data={
                'by_buy_date': 'true',
                'date_after': '2023-12-01',
                'date_before': '2023-12-01',
            },
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.json()), 1)
        self.assertEqual(
            response.json()[0]['date'], ads_second.buy_date.strftime('%Y-%m-%d')
        )
        self.assertEqual(
            Decimal(response.json()[0]['promos'][0]['cost']), ads_second.cost_result
        )

        response = self.client.get(
            reverse('total-spend-list'),
            data={
                'by_buy_date': 'true',
                'date_after': '2023-12-02',
            },
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.json()), 0)

        response = self.client.get(
            reverse('total-spend-list'),
            data={
                'by_buy_date': 'true',
                'date_before': '2023-11-30',
            },
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.json()), 0)

    def test_by_refund_date_filter(self):
        model = self.create_only_fans_model(1)
        Ads.objects.create(
            date=datetime.date(day=1, month=12, year=2023),
            only_fans_model=model,
            cost=1000,
        )
        ads_second = Ads.objects.create(
            date=datetime.date(day=2, month=12, year=2023),
            only_fans_model=model,
            refund_date=datetime.date(day=1, month=12, year=2023),
            refund_cost=2000,
            cost=4000,
        )

        response = self.client.get(reverse('total-spend-list'))
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.json()), 2)

        response = self.client.get(
            reverse('total-spend-list'),
            data={
                'by_refund_date': 'true',
                'date_after': '2023-12-01',
                'date_before': '2023-12-01',
            },
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.json()), 1)
        self.assertEqual(
            response.json()[0]['date'], ads_second.refund_date.strftime('%Y-%m-%d')
        )
        self.assertEqual(
            Decimal(response.json()[0]['promos'][0]['cost']), ads_second.refund_cost
        )

        response = self.client.get(
            reverse('total-spend-list'),
            data={
                'by_refund_date': 'true',
                'date_after': '2023-12-02',
            },
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.json()), 0)

        response = self.client.get(
            reverse('total-spend-list'),
            data={
                'by_refund_date': 'true',
                'date_before': '2023-11-30',
            },
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.json()), 0)

    def test_by_buy_date_and_refund_date_both_set(self):
        response = self.client.get(
            reverse('total-spend-list'),
            data={
                'by_refund_date': 'true',
                'by_buy_date': 'true',
            },
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_forecast_permissions(self):
        for user in self.users:
            self.client.force_authenticate(user)
            response = self.client.get(reverse('forecast-list'))

            if user in {
                self.user_hof,
                self.user_superuser,
                self.user_hom,
                self.user_marketer,
                self.user_team_lead,
                self.user_senior_operator
            }:
                self.assertEqual(response.status_code, 200)

                continue

            self.assertEqual(response.status_code, 403)

    def test_total_spend_promo_filter(self):
        promo_1 = Promo.objects.create(
            name='test_promo_1'
        )
        promo_2 = Promo.objects.create(
            name='test_promo_2'
        )
        model = self.create_only_fans_model(1)
        Ads.objects.create(
            date=datetime.date(day=1, month=12, year=2023),
            only_fans_model=model,
            cost=100,
        )
        Ads.objects.create(
            date=datetime.date(day=2, month=12, year=2023),
            only_fans_model=model,
            refund_date=datetime.date(day=1, month=12, year=2023),
            refund_cost=500,
            cost=1000,
            promo=promo_1
        )
        Ads.objects.create(
            date=datetime.date(day=3, month=12, year=2023),
            only_fans_model=model,
            refund_date=datetime.date(day=1, month=12, year=2023),
            refund_cost=2000,
            cost=4000,
            promo=promo_2
        )

        response = self.client.get(reverse('total-spend-list'))
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.json()), 3)

        response = self.client.get(reverse('total-spend-list'), data={'promos': str(promo_1.id)})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.json()), 1)
        self.assertEqual(
            response.json()[0]['date'], '2023-12-02'
        )
        self.assertEqual(
            Decimal(response.json()[0]['promos'][0]['cost']), Decimal('500.00')
        )

        response = self.client.get(reverse('total-spend-list'), data={'promos': f'{promo_1.id},{promo_2.id}'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.json()), 2)
        self.assertEqual(
            Decimal(response.json()[0]['promos'][0]['cost']), Decimal('500.00')
        )
        self.assertEqual(
            Decimal(response.json()[1]['promos'][0]['cost']), Decimal('2000.00')
        )

        # test promo_ids validation
        random_uuid = uuid.uuid4()
        response = self.client.get(reverse('total-spend-list'), data={'promos': f'{promo_1.id},{promo_2.id},{random_uuid}'})
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn(str(random_uuid), response.json()['promos'])

    def test_debts_status_filter(self):
        ads_problem = Ads.objects.create(
            date=timezone.now() - timezone.timedelta(days=10),
            only_fans_model=self.create_only_fans_model(1),
            cost=1000,
            fans_count=1000,
            fans_delta=1000,
            status=Status.objects.create(name='problem'),
        )
        ads_progress = Ads.objects.create(
            date=timezone.now() - timezone.timedelta(days=10),
            only_fans_model=self.create_only_fans_model(2),
            cost=500,
            fans_count=500,
            fans_delta=500,
            status=Status.objects.create(name='progress'),
        )

        response = self.client.get(reverse('debts-list'), data={'statuses': 'problem'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()['total'], ads_problem.fans_count)

        response = self.client.get(reverse('debts-list'))
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()['total'], ads_progress.fans_count)


class TestPaymentsViews(BaseCRMTest):
    def setUp(self):
        super().setUp()
        self.client.force_authenticate(user=self.user_hof)

    @staticmethod
    def _create_request_payment(**data):
        request_payment = RequestPayment.objects.create(
            **data,
            budget_expense=BudgetExpense.objects.create(
                date=timezone.now().date(),
                expense_item="test",
                department_name="test",
                payment_system="test"
            )
        )
        return request_payment

    def test_request_payment_permissions(self):
        for user in [
            self.user_operator,
            self.user_team_lead,
            self.user_senior_operator,
            self.user_superuser,
            self.user_hom,
            self.user_marketer,
            self.user_smm,
        ]:
            self.client.force_authenticate(user=user)
            response = self.client.get(reverse('payment-requests-list'))
            self.assertEqual(response.status_code, 403)

        for user in [self.user_hof, self.user_financier]:
            self.client.force_authenticate(user=user)
            response = self.client.get(reverse('payment-requests-list'))
            self.assertEqual(response.status_code, 200)

    @mock.patch('finance.services.RequestPaymentSheetService')
    @mock.patch.object(CurrencyConverter, 'convert', lambda *args, **kwargs: 999)
    def test_request_payment_update(
        self,
        mocked_google_sheet,
    ):
        with self.captureOnCommitCallbacks(execute=False) as callbacks:
            payment_request = self._create_request_payment(
                created_by='creator',
                created_by_tg_id=1111,
                head='head',
                department='department',
                payment_system='payment_system',
                payment_description='payment_description',
                payment_amount=1000,
                payment_details='payment_details',
                payment_expense_item=PaymentExpenseItem.objects.get_or_create(name="ExpenseItem")[0]
            )
            payment_request.head_review_result = 'accept'
            payment_request.save()
            payment_request.refresh_from_db()

            update_data = {
                'finance_review_result': 'accept',
                'payment_transactions': [
                    {
                        'payment_tool': str(
                            PaymentTool.objects.create(
                                name='payment_tool',
                            ).id
                        ),
                        'amount': 700,
                    },
                    {
                        'payment_tool': str(
                            PaymentTool.objects.create(
                                name='new_payment_tool',
                            )
                        ),
                        'amount': 300,
                    },
                ],
            }

            update_data = json.dumps(update_data)

            for user in [
                self.user_operator,
                self.user_team_lead,
                self.user_senior_operator,
                self.user_superuser,
                self.user_hom,
                self.user_marketer,
                self.user_smm,
            ]:
                self.client.force_authenticate(user=user)
                response = self.client.patch(
                    reverse('payment-requests-detail', args=[payment_request.id]),
                    update_data,
                    content_type='application/json',
                )
                self.assertEqual(response.status_code, 403)

            self.client.force_authenticate(user=self.user_financier)
            self.client.post(
                reverse('payment-requests-toggle-reviewer', args=[payment_request.id])
            )

            response = self.client.patch(
                reverse('payment-requests-detail', args=[payment_request.id]),
                update_data,
                content_type='application/json',
            )
            self.assertEqual(response.status_code, 200)
            self.assertEqual(response.json()['finance_review_result'], 'accept')
            self.assertIsNotNone(response.json()['finance_review_date'])
            self.assertEqual(response.json()['financier'], self.user_financier.full_name)
            self.assertEqual(len(response.json()['payment_transactions']), 2)

            new_update_data = {
                'payment_transactions': [
                    {
                        'payment_tool': str(
                            PaymentTool.objects.get(
                                name='payment_tool',
                            ).id
                        ),
                        'amount': 500,
                    },
                    {
                        'payment_tool': str(
                            PaymentTool.objects.get(
                                name='new_payment_tool',
                            )
                        ),
                        'amount': 500,
                    },
                ],
            }

            new_update_data = json.dumps(new_update_data)

            response = self.client.patch(
                reverse('payment-requests-detail', args=[payment_request.id]),
                new_update_data,
                content_type='application/json',
            )

            self.assertEqual(response.status_code, 200)
            self.assertEqual(len(response.json()['payment_transactions']), 2)

            for payment_transaction in response.json()['payment_transactions']:
                self.assertEqual(payment_transaction['amount'], 500)

            new_expense_item = PaymentExpenseItem.objects.create(name='new_test_item')
            new_currency = PaymentCurrency.objects.create(name='new_test_curency')
            new_system = PaymentSystem.objects.create(name='new_test_system')
            new_update_data = {
                'payment_amount': 999,
                'payment_expense_item': new_expense_item.name,
                'payment_currency': new_currency.name,
                'payment_system': new_system.name,
                'payment_details': 'new_test_details',
                'payment_comment': 'new_test_comment',
                'payment_description': 'new_test_description',
            }
            new_update_data = json.dumps(new_update_data)
            response = self.client.patch(
                reverse('payment-requests-detail', args=[payment_request.id]),
                new_update_data,
                content_type='application/json',
            )

            self.assertEqual(response.status_code, 200)
            self.assertEqual(response.json()['payment_amount'], 999)
            self.assertEqual(response.json()['payment_expense_item'], new_expense_item.name)
            self.assertEqual(response.json()['payment_currency'], new_currency.name)
            self.assertEqual(response.json()['payment_system'], new_system.name)

            mocked_google_sheet.return_value.update_row.assert_called()
        self.assertNotEquals(len(callbacks), 0)

    def test_request_payment_list_filters(self):
        self._create_request_payment(
            created_by='creator_first',
            created_by_tg_id=1111,
            head='head',
            department='department',
            payment_system='payment_system_first',
            payment_description='payment_description',
            payment_amount=1000,
            payment_details='payment_details',
            head_review_result='accept',
            finance_review_result='accept',
            payment_expense_item=PaymentExpenseItem.objects.get_or_create(name="ExpenseItem")[0]
        )
        self._create_request_payment(
            created_by='creator_second',
            created_by_tg_id=2222,
            head='head',
            department='department',
            payment_system='payment_system_second',
            payment_description='payment_description',
            payment_amount=1000,
            payment_details='payment_details_new',
            head_review_result='accept',
            finance_review_result='reject',
            payment_expense_item=PaymentExpenseItem.objects.get_or_create(name="ExpenseItem")[0]
        )

        response = self.client.get(
            reverse('payment-requests-list'), {'created_by': 'creator_first'}
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.json()['results']), 1)
        response = self.client.get(
            reverse('payment-requests-list'),
            {'created_by': 'creator_first,creator_second'},
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.json()['results']), 2)

        response = self.client.get(
            reverse('payment-requests-list'), {'payment_system': 'payment_system_first'}
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.json()['results']), 1)
        response = self.client.get(
            reverse('payment-requests-list'),
            {'payment_system': 'payment_system_first,payment_system_second'},
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.json()['results']), 2)

        response = self.client.get(
            reverse('payment-requests-list'), {'search': 'payment_details'}
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.json()['results']), 2)

        response = self.client.get(
            reverse('payment-requests-list'), {'search': 'payment_details_new'}
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.json()['results']), 1)

    def test_toggle_reviewer(self):
        payment_request = self._create_request_payment(
            created_by='creator',
            created_by_tg_id=1111,
            head='head',
            department='department',
            payment_system='payment_system',
            payment_description='payment_description',
            payment_amount=1000,
            payment_details='payment_details',
            head_review_result='accept',
            payment_expense_item=PaymentExpenseItem.objects.get_or_create(name="ExpenseItem")[0]
        )
        self.client.force_authenticate(user=self.user_financier)
        response = self.client.post(
            reverse('payment-requests-toggle-reviewer', args=[payment_request.id])
        )
        self.assertEqual(response.status_code, 200)
        payment_request.refresh_from_db()
        self.assertEqual(payment_request.finance_reviewer, self.user_financier)

        new_user_financier = get_user_model().objects.create(
            email='<EMAIL>',
            first_name='first_name',
            last_name='last_name',
            role=self.financier_role
        )
        self.client.force_authenticate(user=new_user_financier)
        response = self.client.post(
            reverse('payment-requests-toggle-reviewer', args=[payment_request.id])
        )
        self.assertEqual(response.status_code, 403)

        self.client.force_authenticate(user=self.user_financier)
        response = self.client.post(
            reverse('payment-requests-toggle-reviewer', args=[payment_request.id])
        )
        self.assertEqual(response.status_code, 200)
        payment_request.refresh_from_db()
        self.assertIsNone(payment_request.finance_reviewer)

        self.client.force_authenticate(user=new_user_financier)
        response = self.client.post(
            reverse('payment-requests-toggle-reviewer', args=[payment_request.id])
        )
        self.assertEqual(response.status_code, 200)
        payment_request.refresh_from_db()
        self.assertEqual(payment_request.finance_reviewer, new_user_financier)

    @mock.patch('finance.services.RequestPaymentSheetService')
    @mock.patch.object(CurrencyConverter, 'convert', lambda *args, **kwargs: Decimal('555.55'))
    def test_transactions_amount_not_more_than_payment_amount(self, mocked_sheets: mock.MagicMock):
        payment_request = self._create_request_payment(
            created_by='creator',
            created_by_tg_id=1111,
            head='head',
            department='department',
            payment_system='payment_system',
            payment_description='payment_description',
            payment_amount=1000,
            payment_details='payment_details',
            head_review_result='accept',
            finance_reviewer=self.user_financier,
            payment_expense_item=PaymentExpenseItem.objects.get_or_create(name="ExpenseItem")[0]
        )
        self.client.force_authenticate(user=self.user_financier)
        update_data = {
            'finance_review_result': 'accept',
            'payment_transactions': [
                {
                    'payment_tool': str(
                        PaymentTool.objects.get_or_create(
                            name='payment_tool',
                        )
                    ),
                    'amount': 700,
                },
                {
                    'payment_tool': str(
                        PaymentTool.objects.get_or_create(
                            name='new_payment_tool',
                        )
                    ),
                    'amount': 301,
                },
            ],
        }
        update_data = json.dumps(update_data)
        response = self.client.patch(
            reverse('payment-requests-detail', args=[payment_request.id]),
            update_data,
            content_type='application/json',
        )
        self.assertEqual(response.status_code, 400)

        update_data = {
            'payment_transactions': [
                {
                    'payment_tool': str(
                        PaymentTool.objects.get_or_create(
                            name='payment_tool',
                        )
                    ),
                    'amount': 700,
                },
                {
                    'payment_tool': str(
                        PaymentTool.objects.get_or_create(
                            name='new_payment_tool',
                        )
                    ),
                    'amount': 300,
                },
            ],
        }
        update_data = json.dumps(update_data)
        response = self.client.patch(
            reverse('payment-requests-detail', args=[payment_request.id]),
            update_data,
            content_type='application/json',
        )
        self.assertEqual(response.status_code, 200)

    @mock.patch('finance.services.RequestPaymentSheetService')
    @mock.patch.object(CurrencyConverter, 'convert', lambda *args, **kwargs: Decimal('555.55'))
    def test_request_payment_model_nickname_could_be_updated(self, mocked_sheets: mock.MagicMock):
        payment_request = self._create_request_payment(
            created_by='creator',
            created_by_tg_id=1111,
            head='head',
            department='department',
            payment_system='payment_system',
            payment_description='payment_description',
            payment_amount=1000,
            payment_details='payment_details',
            head_review_result='accept',
            finance_reviewer=self.user_financier,
            model_nickname='model_nickname',
            payment_expense_item=PaymentExpenseItem.objects.get_or_create(name="ExpenseItem")[0]
        )
        self.client.force_authenticate(user=self.user_financier)

        update_data = {
            'model_nickname': 'new_model_nickname'
        }
        update_data = json.dumps(update_data)
        response = self.client.patch(
            reverse('payment-requests-detail', args=[payment_request.id]),
            update_data,
            content_type='application/json',
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()['model_nickname'], 'new_model_nickname')

    @mock.patch('finance.services.RequestPaymentSheetService')
    @mock.patch.object(CurrencyConverter, 'convert', lambda *args, **kwargs: Decimal('555.55'))
    def test_department_could_be_updated(self, mocked_sheets: mock.MagicMock):
        payment_request = self._create_request_payment(
            created_by='creator',
            created_by_tg_id=1111,
            head='head',
            department='department',
            payment_system='payment_system',
            payment_description='payment_description',
            payment_amount=1000,
            payment_details='payment_details',
            head_review_result='accept',
            finance_reviewer=self.user_financier,
            model_nickname='model_nickname',
            payment_expense_item=PaymentExpenseItem.objects.get_or_create(name="ExpenseItem")[0]
        )

        self.client.force_authenticate(user=self.user_financier)
        update_data = {
            'department': 'new_department'
        }
        response = self.client.patch(
            reverse('payment-requests-detail', args=[payment_request.id]),
            json.dumps(update_data),
            content_type='application/json',
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()['department'], 'new_department')

    def test_search_case_insensitive(self):
        payment_request = self._create_request_payment(
            created_by='creator',
            created_by_tg_id=1111,
            head='head',
            department='department',
            payment_system='payment_system',
            payment_description='payment_description',
            payment_amount=1000,
            payment_details='payment_details',
            head_review_result='accept',
            finance_reviewer=self.user_financier,
            model_nickname='model_nickname',
            payment_expense_item=PaymentExpenseItem.objects.get_or_create(name="ExpenseItem")[0]
        )
        self.client.force_authenticate(user=self.user_financier)

        response = self.client.get(
            reverse('payment-requests-list'), {'search': 'head'}
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()['results'][0]['id'], payment_request.id)

        response = self.client.get(
            reverse('payment-requests-list'), {'search': 'Head'}
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()['results'][0]['id'], payment_request.id)

    @mock.patch('finance.services.RequestPaymentSheetService')
    @mock.patch.object(CurrencyConverter, 'convert', lambda *args, **kwargs: Decimal('555.55'))
    def test_create_payment(self, *args, **kwargs):
        payment_currency, _ = PaymentCurrency.objects.get_or_create(name="UAH")
        payment_department, _ = PaymentDepartment.objects.get_or_create(map_value="finance", name="Finance")
        payment_expense_item, _ = PaymentExpenseItem.objects.get_or_create(name="expense item test")
        payment_request_data = {
            'created_by': 'creator',
            'created_by_tg_id': 12358965425,
            'head': 'head',
            'department': 'finance',
            'payment_system': 'payment_system',
            'payment_description': 'payment_description',
            'payment_amount': 1000.99,
            'payment_details': 'payment_details',
            'head_review_result': 'accept',
            'finance_reviewer': str(self.user_financier.id),
            'model_nickname': 'model_nickname',
            'payment_currency': payment_currency.name,
            'payment_expense_item': payment_expense_item.name
        }

        response = self.client.post(
            reverse('payment-requests-list'),
            data=json.dumps(payment_request_data),
            content_type='application/json',
            headers=self.api_key_headers
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        payment_request = RequestPayment.objects.get(id=response.json()['id'])
        self.assertEqual(payment_request.payment_department, payment_department)
        self.assertEqual(payment_request.amount_usd, Decimal('555.55'))
        self.assertEqual(payment_request.payment_amount, Decimal('1000.99'))

    def test_request_payment_delete(self):
        request_payment = self._create_request_payment(
            created_by='creator',
            created_by_tg_id=111,
            head='head',
            department='department',
            payment_system='payment_system',
            payment_description='payment_description',
            payment_amount=1000,
            payment_details='payment_details',
            head_review_result='accept',
            finance_reviewer=self.user_financier,
            model_nickname='model_nickname',
            payment_expense_item=PaymentExpenseItem.objects.get_or_create(name="ExpenseItem")[0]
        )

        response = self.client.delete(
            reverse('payment-requests-detail', args=[request_payment.id])
        )
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertFalse(BudgetExpense.objects.filter(id=request_payment.budget_expense_id).exists())


class TestPayoutsViews(BaseCRMTest):
    def setUp(self):
        super().setUp()
        self.client.force_authenticate(user=self.user_hof)

    def test_payout_reviews_list_permissions(self):
        for user in [
            self.user_operator,
            self.user_team_lead,
            self.user_senior_operator,
            self.user_superuser,
            self.user_hom,
            self.user_marketer,
            self.user_smm,
        ]:
            self.client.force_authenticate(user=user)
            response = self.client.get(reverse('payout-reviews-list'))
            self.assertEqual(response.status_code, 403)

        for user in [self.user_hof, self.user_financier]:
            self.client.force_authenticate(user=user)
            response = self.client.get(reverse('payout-reviews-list'))
            self.assertEqual(response.status_code, 200)

    def test_create_custom_payout_review(self):
        self.client.force_authenticate(user=self.user_financier)
        payout_review_data = {
            'payout_creation_date': timezone.now(),
            'amount': 1000,
            'only_fans_model': str(self.create_only_fans_model(1).id),
            'payout_address': 'payout address',
        }
        with self.assertRaises(PayoutAddress.DoesNotExist):
            PayoutAddress.objects.get(address_name=payout_review_data['payout_address'])

        with self.captureOnCommitCallbacks(execute=False) as callbacks:
            response = self.client.post(
                reverse('payout-reviews-list'), data=payout_review_data
            )
            self.assertEqual(response.status_code, 201)
            self.assertIsNone(
                response.json()['payout_id']
            )
            self.assertEqual(
                response.json()['amount'],
                payout_review_data['amount']
            ),
            self.assertEqual(
                response.json()['only_fans_model']['id'],
                payout_review_data['only_fans_model']
            )
            self.assertIsNotNone(
                response.json()['finance_review_date']
            )
            self.assertEqual(
                response.json()['finance_review_result'],
                ReviewResult.ACCEPT
            )
            self.assertEqual(
                response.json()['payout_address']['address_name'],
                payout_review_data['payout_address']
            )
            self.assertEqual(
                response.json()['financier'],
                str(self.user_financier.full_name)
            )
            self.assertEqual(
                response.json()['finance_reviewer']['id'], str(self.user_financier.id)
            )
        self.assertEqual(len(callbacks), 1)

    def test_update_payout_review(self):
        autogenerated_payout_review = PayoutReview.objects.create(
            payout_creation_date=timezone.now(),
            amount=1000,
            only_fans_model=self.create_only_fans_model(1),
            payout_id=123456
        )
        self.client.force_authenticate(user=self.user_financier)
        response = self.client.patch(
            reverse('payout-reviews-detail', args=[autogenerated_payout_review.id]),
            data={
                'payout_id': 999,
                'payout_address': 'payout address',
                'finance_review_result': ReviewResult.ACCEPT
            }
        )
        self.assertEqual(response.status_code, 403)
        response = self.client.post(
            reverse('payout-reviews-toggle-reviewer', args=[autogenerated_payout_review.id])
        )
        self.assertEqual(response.status_code, 200)
        with self.captureOnCommitCallbacks(execute=False) as callbacks:
            update_data = {
                    'payout_id': 999,
                    'amount': 9999999,
                    'payout_address': 'payout address',
                    'finance_review_result': ReviewResult.ACCEPT
                }
            response = self.client.patch(
                reverse('payout-reviews-detail', args=[autogenerated_payout_review.id]),
                data=update_data
            )
            self.assertEqual(response.status_code, 200)
            self.assertNotEqual(response.json()['payout_id'], update_data['payout_id'])
            self.assertNotEqual(response.json()['amount'], update_data['amount'])
            self.assertEqual(response.json()['payout_address']['address_name'], 'payout address')
            self.assertEqual(response.json()['finance_review_result'], ReviewResult.ACCEPT)
            self.assertEqual(response.json()['financier'], self.user_financier.full_name)
        self.assertEqual(len(callbacks), 1)

        custom_payout_review = PayoutReview.objects.create(
            payout_creation_date=timezone.now(),
            amount=1000,
            only_fans_model=self.create_only_fans_model(2),
        )
        response = self.client.post(
            reverse('payout-reviews-toggle-reviewer', args=[custom_payout_review.id])
        )
        self.assertEqual(response.status_code, 200)
        with self.captureOnCommitCallbacks(execute=False) as callbacks:
            update_data = {
                'amount': 222222,
                'payout_address': 'payout address',
                'finance_review_result': ReviewResult.ACCEPT
            }
            response = self.client.patch(
                reverse('payout-reviews-detail', args=[custom_payout_review.id]),
                data=update_data
            )
            self.assertEqual(response.status_code, 200)
            self.assertEqual(response.json()['amount'], update_data['amount'])
            self.assertEqual(response.json()['payout_address']['address_name'], 'payout address')
            self.assertEqual(response.json()['finance_review_result'], ReviewResult.ACCEPT)
            self.assertEqual(response.json()['financier'], self.user_financier.full_name)
        self.assertEqual(len(callbacks), 1)

    def test_delete_payout_review(self):
        # autogenerated couldn't be deleted
        autogenerated_payout_review = PayoutReview.objects.create(
            payout_creation_date=timezone.now(),
            amount=1000,
            only_fans_model=self.create_only_fans_model(1),
            payout_id=123456
        )
        response = self.client.delete(reverse('payout-reviews-detail',  args=[autogenerated_payout_review.id]))
        self.assertEqual(response.status_code, 400)

        # custom could be deleted
        custom_payout_review = PayoutReview.objects.create(
            payout_creation_date=timezone.now(),
            amount=1000,
            only_fans_model=self.create_only_fans_model(2),
        )
        response = self.client.delete(reverse('payout-reviews-detail', args=[custom_payout_review.id]))
        self.assertEqual(response.status_code, 204)

    def test_payout_search(self):
        only_fans_model = self.create_only_fans_model(1)
        payout_address = PayoutAddress.objects.create(address_name="real address name")
        payout_review = PayoutReview.objects.create(
            payout_creation_date=timezone.now(),
            amount=1000,
            only_fans_model=only_fans_model,
            payout_id=123,
            payout_address=payout_address
        )
        PayoutReview.objects.create(
            payout_creation_date=timezone.now(),
            amount=55555,
            only_fans_model=self.create_only_fans_model(2),
            payout_id=999,
            payout_address=PayoutAddress.objects.create(address_name="fake address")
        )

        response_search_by_payout_id = self.client.get(
            reverse('payout-reviews-list')
        )
        self.assertEqual(response_search_by_payout_id.status_code, 200)
        self.assertEqual(response_search_by_payout_id.json()['count'], 2)

        for search_item in [
            str(payout_review.payout_id),
            only_fans_model.username_of,
            payout_review.amount,
            payout_address.address_name
        ]:
            response_search_by_payout_id = self.client.get(
                reverse('payout-reviews-list'),
                data={'search': search_item}
            )
            self.assertEqual(response_search_by_payout_id.status_code, 200)
            self.assertEqual(response_search_by_payout_id.json()['count'], 1)
            self.assertEqual(response_search_by_payout_id.json()['results'][0]['id'], str(payout_review.id))

    def test_payout_review_filter(self):
        accept_payout_review = PayoutReview.objects.create(
            payout_creation_date='2024-01-01',
            amount=1000,
            only_fans_model=self.create_only_fans_model(1),
            finance_review_result=ReviewResult.ACCEPT
        )
        reject_payout_review = PayoutReview.objects.create(
            payout_creation_date='2024-01-02',
            amount=1000,
            only_fans_model=self.create_only_fans_model(2),
            finance_review_result=ReviewResult.REJECT
        )
        review_payout_review = PayoutReview.objects.create(
            payout_creation_date='2024-01-03',
            amount=1000,
            only_fans_model=self.create_only_fans_model(3),
            finance_review_result=ReviewResult.REVIEW
        )

        for item in [
            (ReviewResult.ACCEPT, accept_payout_review),

            (ReviewResult.REJECT, reject_payout_review),
            (ReviewResult.REVIEW, review_payout_review),
        ]:
            response = self.client.get(
                reverse('payout-reviews-list'),
                data={'finance_review_result': item[0]}
            )
            self.assertEqual(response.status_code, 200)
            self.assertEqual(response.json()['count'], 1)
            self.assertEqual(response.json()['results'][0]['id'], str(item[1].id))

        date_request = self.client.get(
            reverse('payout-reviews-list'),
            data={'payout_creation_date_after': '2024-01-02', 'payout_creation_date_before': '2024-01-02'}
        )
        self.assertEqual(date_request.status_code, 200)
        self.assertEqual(date_request.json()['count'], 1)
        self.assertEqual(date_request.json()['results'][0]['id'], str(reject_payout_review.id))

    def test_total_debt_history_view_list(self):
        debts_data = (
            ('2024-01-01', 100000),
            ('2024-01-02', 200000),
            ('2024-01-03', 300000),
        )
        for date, debt_amount in debts_data:
            TotalDebtHistory.objects.create(
                date=date,
                debt_amount=debt_amount
            )
        response = self.client.get(reverse('total-debts-history-list'))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()['count'], 3)

        response = self.client.get(
            reverse('total-debts-history-list'),
            data={
                'date_before': '2024-01-02',
                'date_after': '2024-01-02'
            }
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()['count'], 1)
        self.assertEqual(response.json()['results'][0]['date'], '2024-01-02')
        self.assertEqual(response.json()['results'][0]['debt_amount'], 200000)

    def test_total_sum_in_payout_review_list_response(self):
        for i in range(1, 4):
            PayoutReview.objects.create(
                payout_creation_date=f'2024-01-0{i}',
                amount=1000,
                only_fans_model=self.create_only_fans_model(i),
                finance_review_result=ReviewResult.ACCEPT
            )
        response = self.client.get(reverse('payout-reviews-list'))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()['total_sum'], 3000)

    @mock.patch('finance.services.post_bulk_update.send')
    def test_mass_provide(self, mocked_signal):
        payout_reviews = [
            PayoutReview.objects.create(
                payout_creation_date=timezone.now(),
                amount=1000,
                only_fans_model=self.create_only_fans_model(i),
                payout_id=i
            )
            for i in range(1, 10)
        ]
        response = self.client.post(
            reverse('payout-reviews-mass-provide'),
            data={
                'payout_reviews': [str(payout_review.id) for payout_review in payout_reviews],
                'payout_address': 'payout_address'
            }
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()), 9)
        for payout_review in payout_reviews:
            payout_review.refresh_from_db()
            self.assertEqual(payout_review.finance_review_result, ReviewResult.ACCEPT)
            self.assertEqual(payout_review.payout_address.address_name, 'payout_address')

        mocked_signal.assert_called_once()


class TestCashFlowViews(BaseCRMTest):
    def setUp(self):
        super().setUp()
        self.client.force_authenticate(user=self.user_hof)

        self.expense_item = CashflowExpenseItem.objects.create(name='expense item')
        self.department = PaymentDepartment.objects.create(name='department')
        self.payment_system = PaymentSystem.objects.create(name='payment system')
        self.payment_currency = PaymentCurrency.objects.create(name='USD')

    def test_request_cashflow_permissions(self):
        for user in [
            self.user_operator,
            self.user_team_lead,
            self.user_senior_operator,
            self.user_superuser,
            self.user_hom,
            self.user_marketer,
            self.user_smm,
        ]:
            self.client.force_authenticate(user=user)
            response = self.client.get(reverse('cashflow-requests-list'))
            self.assertEqual(response.status_code, 403)

        for user in [self.user_hof, self.user_financier]:
            self.client.force_authenticate(user=user)
            response = self.client.get(reverse('cashflow-requests-list'))
            self.assertEqual(response.status_code, 200)

    def test_request_cashflow_delete(self):
        request_cashflow = RequestCashflow.objects.create(
            date='2024-01-01',
            payment_date='2024-01-01',
            enrollment_date='2024-01-01',
            cashflow_expense_item=self.expense_item,
            amount_usd=100,
            department=self.department,
            payment_system=self.payment_system,
            currency=self.payment_currency,
            amount=100,
            created_by='creator',
            payment_details='details',
            budget_expense=BudgetExpense.objects.create(
                date='2024-01-01',
                expense_item="test",
                department_name="test",
                payment_system="test"
            )
        )

        response = self.client.delete(
            reverse('cashflow-requests-detail', args=[request_cashflow.id])
        )
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertFalse(BudgetExpense.objects.filter(id=request_cashflow.budget_expense_id).exists())
