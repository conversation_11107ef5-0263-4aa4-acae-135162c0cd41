from drf_spectacular.types import OpenApiTypes
from drf_spectacular.utils import OpenApiParameter

from finance.serializers import CopyDataSerializer, TotalSpendSerializer

TOTAL_SPEND_COPY_TABLE_SCHEMA = {
    "parameters": [
        OpenApiParameter(
            name='date_after',
            description='Filter rows by date after',
            required=False,
            type=OpenApiTypes.DATE,
        ),
        OpenApiParameter(
            name='date_before',
            description='Filter rows by date before',
            required=False,
            type=OpenApiTypes.DATE,
        ),
        OpenApiParameter(
            name='by_buy_date',
            description='Group data by buy_date',
            required=False,
            type=OpenApiTypes.BOOL,
        ),
        OpenApiParameter(
            name='by_refund_date',
            description='Group data by refund_date',
            required=False,
            type=OpenApiTypes.BOOL,
        ),
        OpenApiParameter(
            name='by_date_counter',
            description='Group data by date_counter',
            required=False,
            type=OpenApiTypes.BOOL,
        ),
        OpenApiParameter(
            name='by_date_phantom_cost',
            description='Group by date, use phantom_cost instead of cost_result',
            required=False,
            type=OpenApiTypes.BOOL,
        ),
        OpenApiParameter(
            name='by_date_counter_phantom_cost',
            description='Group by date_counter, use phantom_cost instead of cost_result',
            required=False,
            type=OpenApiTypes.BOOL,
        ),
        OpenApiParameter(
            name='promos',
            description='Filter rows by promo ids',
            required=False,
            type=OpenApiTypes.UUID,
            many=True,
        ),
        OpenApiParameter(
            name='by_date_counter_friends_stories',
            description='Group by date_counter + by date for promos Friends for model and Stories for models and New donor friends and New donor stories',
            required=False,
            type=OpenApiTypes.BOOL,
        ),
    ],
    "description": "Endpoint for getting negative romi data.",
    "summary": "Get negative romi data",
    "responses": CopyDataSerializer,
}

TOTAL_SPEND_SCHEMA = {
    'responses': {200: TotalSpendSerializer(many=True)},
    'parameters': [
        OpenApiParameter(
            name='date_after',
            description='Filter rows by date after',
            required=False,
            type=OpenApiTypes.DATE,
        ),
        OpenApiParameter(
            name='date_before',
            description='Filter rows by date before',
            required=False,
            type=OpenApiTypes.DATE,
        ),
        OpenApiParameter(
            name='by_buy_date',
            description='Group data by buy_date',
            required=False,
            type=OpenApiTypes.BOOL,
        ),
        OpenApiParameter(
            name='by_refund_date',
            description='Group data by refund_date',
            required=False,
            type=OpenApiTypes.BOOL,
        ),
        OpenApiParameter(
            name='by_date_counter',
            description='Group data by date_counter',
            required=False,
            type=OpenApiTypes.BOOL,
        ),
        OpenApiParameter(
            name='by_date_phantom_cost',
            description='Group by date, use phantom_cost instead of cost_result',
            required=False,
            type=OpenApiTypes.BOOL,
        ),
        OpenApiParameter(
            name='by_date_counter_phantom_cost',
            description='Group by date_counter, use phantom_cost instead of cost_result',
            required=False,
            type=OpenApiTypes.BOOL,
        ),
        OpenApiParameter(
            name='promos',
            description='Filter rows by promo ids (comma-separated UUIDs)',
            required=False,
            type=OpenApiTypes.UUID,
            many=True,
            explode=False,
            style='form',
        ),
        OpenApiParameter(
            name='by_date_counter_friends_stories',
            description='Group by date_counter + by date for promos Friends for model and Stories for models and New donor friends and New donor stories',
            required=False,
            type=OpenApiTypes.BOOL,
        ),
    ],
}
