from django.contrib import admin

from finance.models import (
    Budget,
    BudgetExpense,
    CashflowExpenseItem,
    CashflowExpenseSubItem,
    CashflowIncomeItem,
    MonthDebtTracking,
    MonthModelDebtTracking,
    PaymentCryptoNetwork,
    PaymentCurrency,
    PaymentDepartment,
    PaymentExpenseItem,
    PaymentSystem,
    PaymentTool,
    PaymentTransaction,
    PayoutAddress,
    PayoutReview,
    RequestCashflow,
    RequestCashflowIncome,
    RequestPayment,
    TotalDebtHistory,
)


@admin.register(PaymentCurrency)
class CurrencyAdmin(admin.ModelAdmin):
    pass


@admin.register(PaymentCryptoNetwork)
class PaymentCryptoNetworkAdmin(admin.ModelAdmin):
    pass


@admin.register(PaymentSystem)
class PaymentSystemAdmin(admin.ModelAdmin):
    pass


@admin.register(PaymentExpenseItem)
class ExpenseItemAdmin(admin.ModelAdmin):
    pass


@admin.register(PaymentTool)
class PaymentToolAdmin(admin.ModelAdmin):
    pass


@admin.register(PaymentDepartment)
class ModelNameAdmin(admin.ModelAdmin):
    pass


class PaymentTransactionInline(admin.TabularInline):
    model = PaymentTransaction
    can_delete = True
    extra = 1


@admin.register(RequestPayment)
class RequestPaymentAdmin(admin.ModelAdmin):
    inlines = (PaymentTransactionInline,)


@admin.register(PayoutAddress)
class PayoutAddressAdmin(admin.ModelAdmin):
    actions = ["inactivate_addresses", "activate_addresses"]

    @admin.action(description="Inactivate selected addresses")
    def inactivate_addresses(self, request, queryset):
        queryset.update(is_active=False)

        self.message_user(request, "Addresses inactivated successfully")

    @admin.action(description="Activate selected addresses")
    def activate_addresses(self, request, queryset):
        queryset.update(is_active=True)

        self.message_user(request, "Addresses activated successfully")


@admin.register(PayoutReview)
class PayoutReviewAdmin(admin.ModelAdmin):
    search_fields = ('financier', 'payout_address', 'payout_id')

    def get_queryset(self, request):
        queryset = super().get_queryset(request)

        return queryset.select_related('payout_address', 'only_fans_model')


class MonthModelDebtTrackingInline(admin.TabularInline):
    model = MonthModelDebtTracking
    can_delete = False
    extra = 0

    def has_change_permission(self, request, obj=None):
        return False

    def has_add_permission(self, request, obj=None):
        return False


@admin.register(MonthDebtTracking)
class MonthDebtTrackingAdmin(admin.ModelAdmin):
    inlines = (MonthModelDebtTrackingInline,)

    def get_queryset(self, request):
        queryset = super().get_queryset(request)

        return queryset.select_related('total_debt_history').prefetch_related('month_model_debt_trackings')

    def has_change_permission(self, request, obj=None):
        return False

    def has_add_permission(self, request, obj=None):
        return False


class MonthDebtTrackingInline(admin.TabularInline):
    model = MonthDebtTracking
    can_delete = False
    extra = 0
    show_change_link = True

    def has_change_permission(self, request, obj=None):
        return False

    def has_add_permission(self, request, obj=None):
        return False


@admin.register(TotalDebtHistory)
class TotalDebtHistoryAdmin(admin.ModelAdmin):
    inlines = (MonthDebtTrackingInline,)

    def get_queryset(self, request):
        queryset = super().get_queryset(request)

        return queryset.prefetch_related('month_debt_trackings')

    def has_change_permission(self, request, obj=None):
        return False

    def has_add_permission(self, request, obj=None):
        return False


class BudgetExpenseInline(admin.TabularInline):
    model = BudgetExpense
    can_delete = False
    extra = 0

    def has_add_permission(self, request, obj=None):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return False


@admin.register(Budget)
class BudgetAdmin(admin.ModelAdmin):
    inlines = (BudgetExpenseInline,)

    def get_queryset(self, request):
        queryset = super().get_queryset(request)

        return queryset.prefetch_related('department', 'period', 'budget_expenses')


@admin.register(RequestCashflow)
class RequestCashflowAdmin(admin.ModelAdmin):
    def get_queryset(self, request):
        queryset = super().get_queryset(request)

        return queryset.select_related(
            'department',
            'payment_system',
            'currency',
            'cashflow_expense_item',
            'cashflow_expense_sub_item'
        )


@admin.register(CashflowExpenseItem)
class CashflowExpenseItemAdmin(admin.ModelAdmin):
    pass


@admin.register(CashflowExpenseSubItem)
class CashflowExpenseSubItemAdmin(admin.ModelAdmin):
    pass


@admin.register(BudgetExpense)
class BudgetExpenseAdmin(admin.ModelAdmin):
    pass


@admin.register(CashflowIncomeItem)
class CashflowIncomeItemAdmin(admin.ModelAdmin):
    pass


@admin.register(RequestCashflowIncome)
class RequestCashflowIncomeAdmin(admin.ModelAdmin):
    def get_queryset(self, request):
        queryset = super().get_queryset(request)

        return queryset.select_related(
            'department',
            'payment_system',
            'currency',
            'cashflow_income_item',
        )
