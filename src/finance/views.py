from datetime import timedelta

from django.contrib.auth import get_user_model
from django.db.models import (
    Prefetch,
    Q,
    Sum,
    Value,
)
from django.db.models.functions import Concat
from django.utils import timezone
from django.utils.dateparse import parse_date
from django_filters.rest_framework import DjangoFilterBackend
from drf_spectacular.types import OpenApiTypes
from drf_spectacular.utils import OpenApiParameter, extend_schema
from rest_framework import status
from rest_framework.decorators import action
from rest_framework.exceptions import ValidationError
from rest_framework.filters import SearchFilter
from rest_framework.generics import GenericAPIView
from rest_framework.mixins import (
    DestroyModelMixin,
    ListModelMixin,
    UpdateModelMixin,
)
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.viewsets import GenericViewSet, ModelViewSet
from rest_framework_api_key.permissions import HasAPIKey

from ads.models import Ads, Promo
from ads.services import AdsService
from base.crm_cache.decorators import cache_response
from base.filters import SearchForDjangoCryptography
from base.mixins import BaseViewMethodsMixin, ListSerializerResponseMixin
from base.pagination import FiftyPerPagePagination
from base.permissions import (
    IsHofOrFinancier,
    IsSuperUserOrHOMorMarketerOrHOFOrFinancier,
    create_role_permission,
)
from finance.filters import (
    DebtTrackingFilter,
    IncomeFansTrackingFilter,
    PayoutReviewFilter,
    RequestCashflowFilter,
    RequestCashflowIncomeFilter,
    RequestPaymentFilter,
    TotalDebtHistoryFilter,
)
from finance.models import (
    Budget,
    BudgetExpense,
    BudgetPeriod,
    CashflowExpenseItem,
    CashflowExpenseSubItem,
    CashflowIncomeItem,
    PaymentCryptoNetwork,
    PaymentCurrency,
    PaymentDepartment,
    PaymentExpenseItem,
    PaymentSystem,
    PaymentTool,
    PayoutAddress,
    PayoutReview,
    RequestCashflow,
    RequestCashflowIncome,
    RequestPayment,
    ReviewResult,
    TotalDebtHistory,
    TotalIncomeFansHistory,
)
from finance.pagination import PayoutReviewPagination
from finance.permissions import IsFinanceReviewer
from finance.serializers import (
    BudgetCreateSerializer,
    BudgetListSerializer,
    BudgetPeriodCreateSerializer,
    BudgetPeriodDetailSerializer,
    BudgetPeriodListSerializer,
    BudgetPeriodUpdateSerializer,
    BudgetUpdateSerializer,
    CashflowCreateSerializer,
    CashflowDepartmentSerializer,
    CashflowExpenseItemSerializer,
    CashflowExpenseSubItemSerializer,
    CashflowIncomeCreateSerializer,
    CashflowIncomeItemSerializer,
    CashflowIncomeListSerializer,
    CashflowIncomeUpdateSerializer,
    CashflowListSerializer,
    CashflowUpdateSerializer,
    CategoriesSummaryCopySerializer,
    CategoriesSummarySerializer,
    CopyDataSerializer,
    DebtsListSerializer,
    DebtsSummarySerializer,
    DebtTrackingDTOSerializer,
    ForecastSerializer,
    ForecastTableViewSerializer,
    FuturePurchaseListSerializer,
    IncomeFansTrackingDTOSerializer,
    PaymentCryptoNetworkSerializer,
    PaymentCurrencySerializer,
    PaymentDepartmentListSerializer,
    PaymentExpenseItemSerializer,
    PaymentSystemSerializer,
    PaymentToolSerializer,
    PayoutAddressSerializer,
    PayoutReviewAutogeneratedUpdateSerializer,
    PayoutReviewCustomCreateSerializer,
    PayoutReviewCustomUpdateSerializer,
    PayoutReviewListSerializer,
    PayoutReviewMassProvideSerializer,
    PurchaseSerializer,
    RequestCashflowCreatorListSerializer,
    RequestPaymentCreateSerializer,
    RequestPaymentCreatorListSerializer,
    RequestPaymentListSerializer,
    TodayReportSerializer,
    TotalDebtHistorySerializer,
    TotalSpendSerializer,
)
from finance.services import (
    PayoutReviewService,
    RequestCashflowIncomeService,
    RequestCashflowService,
    RequestPaymentService,
    TotalDebtHistoryService,
    TotalIncomeFansHistoryService,
)
from finance.spectecular_schemas import TOTAL_SPEND_COPY_TABLE_SCHEMA, TOTAL_SPEND_SCHEMA


class TotalSpendViewSet(BaseViewMethodsMixin, ListModelMixin, GenericViewSet):
    """
    This class is responsible for retrieving and filtering data related to total spend on ads.
    """

    action_serializers = {
        'list': TotalSpendSerializer,
    }
    permission_classes = (IsSuperUserOrHOMorMarketerOrHOFOrFinancier | HasAPIKey,)

    def get_queryset(self, *args, **kwargs):
        date_filter_parameter = self.get_date_filter_parameter(
            self.request.query_params
        )
        date_after = self.request.query_params.get('date_after')
        date_before = self.request.query_params.get('date_before')
        promo_ids = self._get_promos_filter(self.request.query_params)

        queryset = Ads.objects.filter(
                only_fans_model__index_number__isnull=False,
                department=Ads.DepartmentChoices.MARKETING,
            ).select_related(
                'only_fans_model', 'promo'
            )

        if date_filter_parameter != "date_counter":
            date_lookup = {f"{date_filter_parameter}__isnull": False}

            if date_after:
                date_lookup[f"{date_filter_parameter}__gte"] = date_after

            if date_before:
                date_lookup[f"{date_filter_parameter}__lte"] = date_before

            queryset = queryset.filter(
                **date_lookup,
            ).order_by(
                    date_filter_parameter
                )
        else:
            queryset = queryset.filter(
                Q(
                    date_counter__gte=date_after,
                    date_counter__lte=date_before
                )
                | Q(
                    date_counter__isnull=True,
                    date_counter_extra__gte=date_after,
                    date_counter_extra__lte=date_before
                )
                | Q(
                    date_counter__isnull=True,
                    date_counter_extra__isnull=True,
                    date__gte=date_after,
                    date__lte=date_before
                )
            ).order_by(
                "date_counter", "date_counter_extra", "date"
            )

        if date_filter_parameter == 'refund_date':
            queryset = queryset.filter(
                refund_cost__isnull=False
            )
        elif date_filter_parameter == 'buy_date':
            queryset = queryset.filter(
                cost__isnull=False
            )
        else:
            queryset = queryset.filter(
                cost_result__isnull=False
            )

        if promo_ids:
            queryset = queryset.filter(promo_id__in=promo_ids)

        return queryset.distinct()

    @staticmethod
    def get_date_filter_parameter(query_params: dict) -> str:
        """
        Returns the filter parameter based on the query parameters.

        Args:
            query_params(dict): dict with request query params
        """
        filter_keys = [
            'by_buy_date',
            'by_refund_date',
            'by_date_counter',
            'by_date_phantom_cost',
            'by_date_counter_phantom_cost',
            'by_date_counter_friends_stories',
        ]

        count_date_keys = 0
        for key in filter_keys:
            if key in query_params:
                count_date_keys += 1

            if count_date_keys > 1:
                raise ValidationError(
                    f'Only one of the parameters {filter_keys} can be set.'
                )

        for key in filter_keys:
            if query_params.get(key, 'false').lower() == 'true':

                return key.replace('by_', '').replace('_phantom_cost', '').replace('_friends_stories', '')

        return 'date'

    @staticmethod
    def _check_by_phantom_cost(query_params: dict) -> bool:
        return (
                query_params.get('by_date_phantom_cost', 'false').lower() == 'true'
                or query_params.get('by_date_counter_phantom_cost', 'false').lower() == 'true'
        )

    @staticmethod
    def _check_by_friends_stories(query_params: dict) -> bool:
        return query_params.get('by_date_counter_friends_stories', 'false').lower() == 'true'

    @staticmethod
    def _get_promos_filter(query_params: dict) -> list[str] | None:
        """
        Returns the promo_ids based on the 'promos' filter.

        Args:
            query_params(dict): dict with request query params
        """

        promos = query_params.get('promos')

        if promos:
            promo_ids = set(promos.split(','))

            # validate promo_ids
            existing_promo_ids = set(Promo.objects.filter(id__in=promo_ids).values_list('id', flat=True).distinct())

            if len(existing_promo_ids) != len(promo_ids):
                missing_ids = set(promo_ids) - existing_promo_ids
                raise ValidationError(
                    {'promos': f'Promo with id(s) {", ".join(missing_ids)} does not exist'}
                )

            return list(promo_ids)

        return None

    @cache_response('finance-total-spend')
    @extend_schema(**TOTAL_SPEND_SCHEMA)
    def list(self, request, *args, **kwargs):
        date_filter_parameter = self.get_date_filter_parameter(request.query_params)
        by_phantom_cost = self._check_by_phantom_cost(query_params=request.query_params)
        by_friends_stories = self._check_by_friends_stories(query_params=request.query_params)
        promo_ids = self._get_promos_filter(request.query_params)

        if by_friends_stories and by_phantom_cost:
            raise ValidationError(
                'Only one of the parameters by_date_counter_phantom_cost and by_date_counter_friends_stories can be set.'
            )

        if by_friends_stories and promo_ids:
            raise ValidationError(
                'by_date_counter_friends_stories and promos filters are not compatible.'
            )

        ads_list = list(self.get_queryset())
        data = AdsService.get_total_spend_data(
            ads_list=ads_list,
            date_filter_parameter=date_filter_parameter,
            by_phantom_cost=by_phantom_cost,
            by_friends_stories=by_friends_stories,
        )
        serializer = self.get_serializer(data=data, many=True)
        serializer.is_valid(raise_exception=True)

        return Response(serializer.data)

    @cache_response('finance-total-spend-copy-table')
    @extend_schema(**TOTAL_SPEND_COPY_TABLE_SCHEMA)
    @action(detail=False, methods=['GET'], url_path='copy-table')
    def copy_table(self, request):
        response = {'copy_data': ''}

        min_date = request.query_params.get('date_after')
        max_date = request.query_params.get('date_before')
        date_filter_parameter = self.get_date_filter_parameter(request.query_params)
        by_phantom_cost = self._check_by_phantom_cost(query_params=request.query_params)
        by_friends_stories = self._check_by_friends_stories(query_params=request.query_params)
        promo_ids = self._get_promos_filter(request.query_params)

        if by_friends_stories and by_phantom_cost:
            raise ValidationError(
                'Only one of the parameters by_date_counter_phantom_cost and by_date_counter_friends_stories can be set.'
            )

        if by_friends_stories and promo_ids:
            raise ValidationError(
                'by_date_counter_friends_stories and promos filters are not compatible.'
            )

        ads_list = list(self.get_queryset())

        if not ads_list:

            return Response(response)

        response["copy_data"] = AdsService.get_total_spend_table_data_string(
            ads_list=ads_list,
            min_date=min_date,
            max_date=max_date,
            promo_ids=promo_ids,
            date_filter_parameter=date_filter_parameter,
            by_phantom_cost=by_phantom_cost,
            by_friends_stories=by_friends_stories,
        )

        return Response(response)


class FutureTotalPurchaseView(APIView):
    """
    View for collection costs in defined intervals
    """

    permission_classes = [IsSuperUserOrHOMorMarketerOrHOFOrFinancier]

    @extend_schema(responses=PurchaseSerializer)
    def get(self, request):
        ads_list = list(
            Ads.objects.filter(
                cost_result__isnull=False,
                buy_date__isnull=False,
                department=Ads.DepartmentChoices.MARKETING,
                date__gte=timezone.now()
                - timedelta(max(AdsService.FINANCE_DATA_INTERVALS)),
            )
            .order_by(
                '-date',
            )
            .only('cost_result', 'date', 'buy_date')
        )

        total_purchase_dict = AdsService.get_future_total_purchase(ads_list=ads_list)

        return Response(total_purchase_dict)


class AveragePurchaseOnModelView(APIView):
    """
    View for collection average cost results per model in defined intervals
    """

    permission_classes = [IsSuperUserOrHOMorMarketerOrHOFOrFinancier]

    @extend_schema(responses=PurchaseSerializer)
    def get(self, request):
        ads_list = list(
            Ads.objects.filter(
                cost_result__isnull=False,
                buy_date__isnull=False,
                department=Ads.DepartmentChoices.MARKETING,
                date__gte=timezone.now()
                - timedelta(max(AdsService.FINANCE_DATA_INTERVALS)),
            )
            .order_by(
                '-date',
            )
            .select_related('platform_type')
            .only(
                'cost_result',
                'date',
                'buy_date',
                'only_fans_model_id',
                'platform_type__name',
            )
        )

        average_purchase_dict = AdsService.get_average_purchase_on_model(
            ads_list=ads_list
        )

        return Response(average_purchase_dict)


class DebtViewSet(BaseViewMethodsMixin, GenericViewSet):
    action_serializers = {
        'list': DebtsListSerializer,
        'copy': CopyDataSerializer,
        'summary': DebtsSummarySerializer,
        'copy_summary': CopyDataSerializer,
    }
    permission_classes = [IsSuperUserOrHOMorMarketerOrHOFOrFinancier]

    def get_queryset(self):
        today_date = timezone.now().date()
        statuses = self.request.query_params.getlist(
            'statuses',
            default=AdsService.DEBT_QUERYSET_FILTER_STATUSES
        )

        queryset = Ads.objects.filter(
            status__name__in=statuses,
            fans_delta__isnull=False,
            fans_delta__gt=0,
            cost_result__isnull=False,
            date__lte=today_date,
            department=Ads.DepartmentChoices.MARKETING,
        ).select_related('only_fans_model').order_by('date')

        return queryset

    @extend_schema(
        parameters=[
            OpenApiParameter('statuses', OpenApiTypes.STR, many=True, required=False, enum=['problem'])
        ]
    )
    def list(self, request, *args, **kwargs):
        ads_list = list(self.get_queryset())
        debts = AdsService.get_debts(ads_list)
        total = sum(debts.amount for debts in debts)
        serializer = self.get_serializer(
            {
                'debts': debts,
                'total': total,
            }
        )

        return Response(serializer.data)

    @extend_schema(
        parameters=[
            OpenApiParameter('statuses', OpenApiTypes.STR, many=True, required=False, enum=['problem'])
        ]
    )
    @action(detail=False, methods=['GET'], url_path='copy')
    def copy(self, request, *args, **kwargs):
        """
        Copy debts data
        """
        ads_list = list(self.get_queryset())
        debts_string = AdsService.get_debts_string(ads_list)
        serializer = self.get_serializer(data={'copy_data': debts_string})
        serializer.is_valid(raise_exception=True)

        return Response(serializer.data)

    @extend_schema(
        parameters=[
            OpenApiParameter('statuses', OpenApiTypes.STR, many=True, required=False, enum=['problem'])
        ]
    )
    @extend_schema(responses=DebtsSummarySerializer(many=True))
    @action(detail=False, methods=['GET'], url_path='summary')
    def summary(self, request, *args, **kwargs):
        """
        Get debts summary data (detail data by model)
        """
        ads_list = list(self.get_queryset())
        debts = AdsService.get_debts_summary(ads_list)
        serializer = self.get_serializer(debts, many=True)

        return Response(serializer.data)

    @extend_schema(
        parameters=[
            OpenApiParameter('statuses', OpenApiTypes.STR, many=True, required=False, enum=['problem'])
        ]
    )
    @action(detail=False, methods=['GET'], url_path='summary/copy')
    def copy_summary(self, request, *args, **kwargs):
        """
        Copy debts summary data
        """
        ads_list = list(self.get_queryset())
        debts_string = AdsService.get_debts_summary_string(ads_list)
        serializer = self.get_serializer(data={'copy_data': debts_string})
        serializer.is_valid(raise_exception=True)

        return Response(serializer.data)


class FuturePurchaseViewSet(BaseViewMethodsMixin, GenericViewSet):
    queryset = Ads.objects.filter(
        cost_result__isnull=False,
        department=Ads.DepartmentChoices.MARKETING,
    ).order_by('date')

    action_serializers = {
        'list': FuturePurchaseListSerializer,
        'copy': CopyDataSerializer,
    }

    permission_classes = [IsSuperUserOrHOMorMarketerOrHOFOrFinancier]

    def get_queryset(self):
        queryset = super().get_queryset().filter(date__gte=timezone.now())

        if self.action == 'by_donor_marketer':
            queryset = queryset.filter(
                Q(donor__isnull=False) | Q(marketer__isnull=False)
            )

        return queryset

    def list(self, request, *args, **kwargs):
        ads_list = list(self.get_queryset())
        purchases = AdsService.get_future_purchases(ads_list)
        total = sum(purchases.amount for purchases in purchases)
        serializer = self.get_serializer({'purchases': purchases, 'total': total})

        return Response(serializer.data)

    @action(detail=False, methods=['GET'], url_path='copy')
    def copy(self, request, *args, **kwargs):
        ads_list = list(self.get_queryset())
        future_purchases_string = AdsService.get_future_purchases_string(ads_list)
        serializer = self.get_serializer({'copy_data': future_purchases_string})

        return Response(serializer.data)


class TodayReportView(APIView):
    permission_classes = [IsSuperUserOrHOMorMarketerOrHOFOrFinancier]

    @extend_schema(responses=TodayReportSerializer)
    def get(self, request, *args, **kwargs):
        """
        Get today report
        """
        data = AdsService.get_today_report_data()

        return Response(data)


class TodayReportCopyView(APIView):
    permission_classes = [IsSuperUserOrHOMorMarketerOrHOFOrFinancier]

    @extend_schema(responses=CopyDataSerializer)
    def get(self, request, *args, **kwargs):
        """
        Copy today report data
        """
        data = AdsService.get_today_report_data_string()

        return Response(data)


class CategoriesSummary(APIView):

    permission_classes = [IsSuperUserOrHOMorMarketerOrHOFOrFinancier]

    @extend_schema(responses=CategoriesSummarySerializer)
    def get(self, request, *args, **kwargs):
        """
        Get categories summary
        """
        data = AdsService.get_categories_summary_data()

        return Response(data)


class CategoriesSummaryCopy(APIView):
    permission_classes = [IsSuperUserOrHOMorMarketerOrHOFOrFinancier]

    @extend_schema(responses=CategoriesSummaryCopySerializer)
    def get(self, request, *args, **kwargs):
        """
        Copy categories summary data
        """
        data = AdsService.get_categories_summary_data_string()

        return Response(data)


class ForecastViewSet(BaseViewMethodsMixin, GenericViewSet):
    permission_classes = [
        create_role_permission(
            allowed_roles=[
                'superuser',
                'hom',
                'marketer',
                'hof',
                'team_lead',
                'senior_operator'
            ]
        )
    ]
    action_serializers = {
        'list': ForecastSerializer,
        'table_view': ForecastTableViewSerializer,
        'table_view_copy': CopyDataSerializer,
    }

    def get_queryset(self):
        today_date = timezone.now().date()
        queryset = Ads.objects.filter(department=Ads.DepartmentChoices.MARKETING)

        if self.action == 'list':
            queryset = (
                queryset.filter(
                    Q(status__name='progress') | Q(date__gte=today_date),
                    cost_result__isnull=False,
                    cost_result__gt=0,
                )
                .select_related('status', 'only_fans_model')
                .order_by('date')
                .only(
                    'date',
                    'cost_result',
                    'fans_count',
                    'fans_delta',
                    'fans_per_day',
                    'probable_end_date',
                    'only_fans_model__nickname',
                    'status__name',
                    'claims_count',
                )
            )
        elif self.action in ['table_view', 'table_view_copy']:
            queryset = (
                queryset.filter(
                    status__name='progress',
                    probable_end_date__isnull=False,
                    probable_end_date__gte=today_date,
                )
                .select_related('status', 'only_fans_model')
                .order_by('probable_end_date')
                .only(
                    'fans_per_day',
                    'probable_end_date',
                    'only_fans_model__nickname',
                    'status__name',
                )
            )

        return queryset

    def list(self, request, *args, **kwargs):
        ads_list = list(self.get_queryset())
        forecast_data = AdsService.get_forecast_data(ads_list)
        serializer = self.get_serializer(forecast_data, many=True)

        return Response(serializer.data)

    @extend_schema(responses=ForecastTableViewSerializer(many=True))
    @action(detail=False, methods=['GET'], url_path='table-view')
    def table_view(self, request, *args, **kwargs):
        ads_list = list(self.get_queryset())
        forecast_data = AdsService.get_forecast_table_view_data(ads_list)
        serializer = self.get_serializer(forecast_data, many=True)

        return Response(serializer.data)

    @action(detail=False, methods=['GET'], url_path='table-view/copy')
    def table_view_copy(self, request, *args, **kwargs):
        ads_list = list(self.get_queryset())
        forecast_data_string = AdsService.get_forecast_table_view_data_string(ads_list)
        serializer = self.get_serializer({'copy_data': forecast_data_string})

        return Response(serializer.data)


# ---------------------------------------RequestPayment-------------------------------------
@extend_schema(tags=['finance-payment-requests'])
class PaymentExpenseItemViewSet(ListModelMixin, GenericViewSet):
    """
    List all Expense Items
    """

    permission_classes = [HasAPIKey | IsHofOrFinancier]
    serializer_class = PaymentExpenseItemSerializer
    queryset = PaymentExpenseItem.objects.filter(is_active=True)


@extend_schema(tags=['finance-payment-requests'])
class PaymentCurrencyViewSet(ListModelMixin, GenericViewSet):
    """
    List all Payment Currency
    """

    permission_classes = [HasAPIKey | IsHofOrFinancier]
    serializer_class = PaymentCurrencySerializer
    queryset = PaymentCurrency.objects.filter(is_active=True)


@extend_schema(tags=['finance-payment-requests'])
class PaymentCryptoNetworkViewSet(ListModelMixin, GenericViewSet):
    """
    List all Payment Crypto Networks
    """

    permission_classes = [HasAPIKey | IsHofOrFinancier]
    serializer_class = PaymentCryptoNetworkSerializer
    queryset = PaymentCryptoNetwork.objects.filter(is_active=True)


@extend_schema(tags=['finance-payment-requests'])
class PaymentSystemViewSet(ListModelMixin, GenericViewSet):
    """
    List all Payment System
    """

    permission_classes = [HasAPIKey | IsHofOrFinancier]
    serializer_class = PaymentSystemSerializer
    queryset = PaymentSystem.objects.filter(is_active=True)


@extend_schema(tags=['finance-payment-requests'])
class PaymentToolViewSet(ListModelMixin, GenericViewSet):
    """
    List all Payment Tools
    """

    permission_classes = [IsHofOrFinancier]
    serializer_class = PaymentToolSerializer
    queryset = PaymentTool.objects.filter(is_active=True)


@extend_schema(
    tags=['finance-payment-requests'],
    responses={status.HTTP_200_OK: RequestPaymentCreatorListSerializer},
)
class RequestPaymentCreatorListView(APIView):
    """
    List distinct creators from RequestPayment objects as strings
    """

    permission_classes = [IsHofOrFinancier]

    def get(self, request, *args, **kwargs):
        creators = (
            RequestPayment.objects.values_list('created_by', flat=True)
            .distinct('created_by')
            .order_by('created_by')
        )

        return Response(creators, status=status.HTTP_200_OK)


@extend_schema(tags=['finance-payment-requests'])
class PaymentDepartmentListView(APIView):
    """
    List all Payment Department
    """
    permission_classes = [IsHofOrFinancier]

    @extend_schema(responses={status.HTTP_200_OK: PaymentDepartmentListSerializer})
    def get(self, request, *args, **kwargs):
        departments = PaymentDepartment.objects.values_list('map_value', flat=True).order_by('map_value')
        serializer = PaymentDepartmentListSerializer(departments)

        return Response(serializer.data, status=status.HTTP_200_OK)


@extend_schema(tags=['finance-payment-requests'])
class RequestPaymentViewSet(BaseViewMethodsMixin, ModelViewSet):
    """
    API endpoint that allows to manage RequestPayment objects
    """

    serializer_class = RequestPaymentListSerializer
    permission_classes = [IsHofOrFinancier]
    action_permissions = {
        'list': [HasAPIKey | IsHofOrFinancier],
        'retrieve': [HasAPIKey | IsHofOrFinancier],
        'create': [HasAPIKey],
        'update': [HasAPIKey | IsHofOrFinancier & IsFinanceReviewer],
        'partial_update': [HasAPIKey | IsHofOrFinancier & IsFinanceReviewer],
        'toggle_reviewer': [IsHofOrFinancier]
    }

    action_serializers = {
        'list': RequestPaymentListSerializer,
        'retrieve': RequestPaymentListSerializer,
        'create': RequestPaymentCreateSerializer,
        'update': RequestPaymentListSerializer,
        'partial_update': RequestPaymentListSerializer,
        'toggle_reviewer': RequestPaymentListSerializer
    }

    pagination_class = FiftyPerPagePagination

    filter_backends = [DjangoFilterBackend, SearchForDjangoCryptography]
    filterset_class = RequestPaymentFilter
    search_fields = [
        'id',
        'created_by',
        'head',
        'financier',
        'department',
        'payment_system',
        'payment_expense_item__name',
        'payment_comment',
        'model_nickname',
        'payment_description',
        'payment_currency__name',
        'payment_amount',
        'payment_transactions__payment_tool__name',
        'payment_details',
    ]

    def get_queryset(self):
        queryset = RequestPayment.objects.all()

        if self.action == 'list':
            return (
                RequestPayment.objects.filter(head_review_result=ReviewResult.ACCEPT)
                .select_related('payment_currency', 'payment_expense_item', 'finance_reviewer')
                .prefetch_related('payment_transactions__payment_tool')
            )

        return queryset

    @extend_schema(responses=RequestPaymentListSerializer, request=None)
    @action(detail=True, methods=['POST'], url_path='toggle-reviewer')
    def toggle_reviewer(self, request, pk=None):
        """
        Start review for RequestPayment
        """
        request_payment = self.get_object()
        user = request.user
        instance = RequestPaymentService.toggle_reviewer(instance=request_payment, reviewer=user)
        serializer = self.get_serializer(instance)

        return Response(serializer.data)


# -------------------------------------------------PayoutReview--------------------------------------------------
@extend_schema(tags=['finance-payout-reviews'])
class PayoutReviewViewSet(ListSerializerResponseMixin, BaseViewMethodsMixin, ModelViewSet):
    """
    API endpoint that allows to manage RequestPayment objects
    """
    permission_classes = [IsHofOrFinancier]
    action_permissions = {
        'list': [IsHofOrFinancier],
        'retrieve': [IsHofOrFinancier],
        'create': [IsHofOrFinancier],
        'update': [IsHofOrFinancier & IsFinanceReviewer],
        'partial_update': [HasAPIKey | IsHofOrFinancier & IsFinanceReviewer],
        'toggle_reviewer': [IsHofOrFinancier],
        'destroy':  [IsHofOrFinancier],
        'mass_provide': [IsHofOrFinancier]
    }
    queryset = PayoutReview.objects.filter(
        only_fans_model__isnull=False
    ).select_related(
        'only_fans_model', 'payout_address', 'finance_reviewer'
    )
    LIST_SERIALIZER_CLASS = PayoutReviewListSerializer
    pagination_class = PayoutReviewPagination
    filter_backends = [DjangoFilterBackend, SearchFilter]
    filterset_class = PayoutReviewFilter
    search_fields = [
        'payout_id',
        'only_fans_model__username_of',
        'amount',
        'payout_address__address_name'
    ]

    def get_serializer_class(self):
        if self.action == 'create':
            return PayoutReviewCustomCreateSerializer

        elif self.action in ['update', 'partial_update']:
            obj = self.get_object()

            if obj.is_autogenerated:
                return PayoutReviewAutogeneratedUpdateSerializer
            else:
                return PayoutReviewCustomUpdateSerializer

        elif self.action == 'mass_provide':
            return PayoutReviewMassProvideSerializer

        return PayoutReviewListSerializer

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        total_sum = sum(item.amount for item in queryset)
        page = self.paginate_queryset(queryset)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            serializer.context['total_sum'] = total_sum

            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)

        return Response(serializer.data)

    @extend_schema(responses=PayoutReviewListSerializer)
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)

    @extend_schema(responses=PayoutReviewListSerializer, request=PayoutReviewCustomUpdateSerializer)
    def update(self, request, *args, **kwargs):
        return super().update(request, *args, **kwargs)

    @extend_schema(responses=PayoutReviewListSerializer, request=PayoutReviewCustomUpdateSerializer)
    def partial_update(self, request, *args, **kwargs):
        return super().partial_update(request, *args, **kwargs)

    @extend_schema(responses=PayoutReviewListSerializer, request=None)
    @action(detail=True, methods=['POST'], url_path='toggle-reviewer')
    def toggle_reviewer(self, request, pk=None):
        """
        Start review for RequestPayment
        """
        obj = self.get_object()
        user = request.user
        instance = PayoutReviewService.toggle_reviewer(instance=obj, reviewer=user)
        serializer = self.get_serializer(instance)

        return Response(serializer.data)

    def perform_destroy(self, instance):
        if instance.is_autogenerated:
            raise ValidationError('Cannot delete autogenerated instance')
        else:
            instance.delete()

    @extend_schema(responses=PayoutReviewListSerializer, request=PayoutReviewMassProvideSerializer)
    @action(detail=False, methods=['POST'], url_path='mass-provide')
    def mass_provide(self, request, *args, **kwargs):
        """
        Mass provide for PayoutReview objects
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        updated_payout_reviews = PayoutReviewService.mass_provide(
            payout_reviews=serializer.validated_data['payout_reviews'],
            user=request.user,
            payout_address=serializer.validated_data['payout_address']
        )
        response_serializer = self.LIST_SERIALIZER_CLASS(updated_payout_reviews, many=True)

        return Response(response_serializer.data)


@extend_schema(tags=['finance-payout-reviews'])
class PayoutAddressViewSet(ListModelMixin, GenericViewSet):
    """
    API endpoint that allows to list PayoutAddress objects
    """
    serializer_class = PayoutAddressSerializer
    permission_classes = [IsHofOrFinancier]
    queryset = PayoutAddress.objects.filter(is_active=True)


# ---------------------------------------------History---------------------------------------------
@extend_schema(tags=['finance-history'])
class TotalDebtHistoryViewSet(ListModelMixin, GenericViewSet):
    """
    API endpoint that allows to list TotalDebtHistory objects
    """
    serializer_class = TotalDebtHistorySerializer
    permission_classes = [IsSuperUserOrHOMorMarketerOrHOFOrFinancier]
    queryset = TotalDebtHistory.objects.all()
    pagination_class = FiftyPerPagePagination
    filterset_class = TotalDebtHistoryFilter


@extend_schema(tags=['finance-history'])
class DebtTrackingListView(GenericAPIView):
    """
    API endpoint that allows to list Deb Tracking data, in date range based on every monday
    """

    permission_classes = [IsSuperUserOrHOMorMarketerOrHOFOrFinancier]
    filterset_class = DebtTrackingFilter
    queryset = TotalDebtHistory.objects.filter(
        month_debt_trackings__isnull=False
    ).prefetch_related(
        'month_debt_trackings'
    ).order_by(
        'date'
    ).distinct()
    serializer_class = DebtTrackingDTOSerializer

    @extend_schema(
        parameters=[
            OpenApiParameter('date_before', type=OpenApiTypes.DATE, required=True),
            OpenApiParameter('date_after', type=OpenApiTypes.DATE, required=True)
        ]
    )
    def get(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        date_before = parse_date(request.query_params['date_before'])
        date_after = parse_date(request.query_params['date_after'])
        debt_tacking_dtos = TotalDebtHistoryService().get_debt_tracking_dtos(
            queryset=queryset,
            end_date=date_before,
            start_date=date_after
        )
        serializer = self.get_serializer(debt_tacking_dtos, many=True)

        return Response(serializer.data)


@extend_schema(tags=['finance-history'])
class DebtTrackingListCopyView(DebtTrackingListView):
    """
    API endpoint that allows to list Debt Tracking data in string representation, in date range based on every monday
    """
    serializer_class = CopyDataSerializer

    @extend_schema(
        parameters=[
            OpenApiParameter('date_before', type=OpenApiTypes.DATE, required=True),
            OpenApiParameter('date_after', type=OpenApiTypes.DATE, required=True)
        ]
    )
    def get(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        date_before = parse_date(request.query_params['date_before'])
        date_after = parse_date(request.query_params['date_after'])
        sting_data = TotalDebtHistoryService().get_debt_tracking_string(
            queryset=queryset,
            end_date=date_before,
            start_date=date_after
        )
        serializer = self.get_serializer({'copy_data': sting_data})

        return Response(serializer.data)


@extend_schema(tags=['finance-history'])
class IncomeFansTrackingListView(GenericAPIView):
    """
    API endpoint that allows to list Income Fans data, in date range based on every monday
    """

    permission_classes = [IsSuperUserOrHOMorMarketerOrHOFOrFinancier]
    filterset_class = IncomeFansTrackingFilter
    queryset = TotalIncomeFansHistory.objects.filter(
        month_income_fans_trackings__isnull=False
    ).prefetch_related(
        'month_income_fans_trackings'
    ).order_by(
        'date'
    ).distinct()
    serializer_class = IncomeFansTrackingDTOSerializer

    @extend_schema(
        parameters=[
            OpenApiParameter('date_before', type=OpenApiTypes.DATE, required=True),
            OpenApiParameter('date_after', type=OpenApiTypes.DATE, required=True)
        ]
    )
    def get(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        date_before = parse_date(request.query_params['date_before'])
        date_after = parse_date(request.query_params['date_after'])
        income_fans_tacking_dtos = TotalIncomeFansHistoryService().get_income_fans_tracking_dtos(
            queryset=queryset,
            end_date=date_before,
            start_date=date_after
        )
        serializer = self.get_serializer(income_fans_tacking_dtos, many=True)

        return Response(serializer.data)


@extend_schema(tags=['finance-history'])
class IncomeFansTrackingListCopyView(IncomeFansTrackingListView):
    """
    API endpoint that allows to list Income Fans Tracking data in string representation, in date range based on every monday
    """
    serializer_class = CopyDataSerializer

    @extend_schema(
        parameters=[
            OpenApiParameter('date_before', type=OpenApiTypes.DATE, required=True),
            OpenApiParameter('date_after', type=OpenApiTypes.DATE, required=True)
        ]
    )
    def get(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        date_before = parse_date(request.query_params['date_before'])
        date_after = parse_date(request.query_params['date_after'])
        sting_data = TotalIncomeFansHistoryService().get_income_fans_tracking_string(
            queryset=queryset,
            end_date=date_before,
            start_date=date_after
        )
        serializer = self.get_serializer({'copy_data': sting_data})

        return Response(serializer.data)


# -------------------------------------------------Cashflow-------------------------------------------------
@extend_schema(tags=['finance-cashflow'])
class CashflowExpenseItemViewSet(ListModelMixin, GenericViewSet):
    """
    List all Cashflow Expense Items
    """

    permission_classes = [HasAPIKey | IsHofOrFinancier]
    serializer_class = CashflowExpenseItemSerializer
    queryset = CashflowExpenseItem.objects.filter(is_active=True)


@extend_schema(tags=['finance-cashflow'])
class CashflowExpenseSubItemViewSet(ListModelMixin, GenericViewSet):
    """
    List all Cashflow Expense Sub Items
    """

    permission_classes = [HasAPIKey | IsHofOrFinancier]
    serializer_class = CashflowExpenseSubItemSerializer
    queryset = CashflowExpenseSubItem.objects.filter(is_active=True)


@extend_schema(tags=['finance-cashflow'])
class CashflowDepartmentViewSet(ListModelMixin, GenericViewSet):
    """
    List all Cashflow Departments
    """

    permission_classes = [HasAPIKey | IsHofOrFinancier]
    serializer_class = CashflowDepartmentSerializer
    queryset = PaymentDepartment.objects.order_by('name')


@extend_schema(
    tags=['finance-cashflow'],
    responses={status.HTTP_200_OK: RequestCashflowCreatorListSerializer},
)
class RequestCashflowCreatorListView(APIView):
    """
    List distinct creators from RequestCashflow objects as strings
    """
    def get(self, request, *args, **kwargs):
        creators_list = get_user_model().objects.filter(
            role__name__in=('financier', 'hof')
        ).annotate(
            full_name=Concat(
                'first_name',
                Value(' '),
                'last_name',
            )
        ).values_list(
            'full_name',
            flat=True
        ).distinct()

        return Response(creators_list)


@extend_schema(tags=['finance-cashflow'])
class CashflowRequestViewSet(
    ListSerializerResponseMixin,
    BaseViewMethodsMixin,
    ModelViewSet
):
    """
    API endpoints that allows to manage CashflowRequest objects
    """
    action_permissions = {
        'create': [HasAPIKey | IsHofOrFinancier],
        'list': [IsHofOrFinancier],
        'update': [IsHofOrFinancier],
        'partial_update': [IsHofOrFinancier],
        'retrieve': [IsHofOrFinancier],
        'copy': [IsHofOrFinancier]
    }
    action_serializers = {
        'create': CashflowCreateSerializer,
        'update': CashflowUpdateSerializer,
        'partial_update': CashflowUpdateSerializer,
        'list': CashflowListSerializer,
        'retrieve': CashflowListSerializer,
    }
    queryset = RequestCashflow.objects.select_related(
        'department',
        'cashflow_expense_item',
        'cashflow_expense_sub_item',
        'payment_system',
        'currency'
    ).order_by(
        '-id'
    )
    pagination_class = FiftyPerPagePagination
    filter_backends = [DjangoFilterBackend, SearchForDjangoCryptography]
    filterset_class = RequestCashflowFilter
    search_fields = [
        'id',
        'date',
        'cashflow_expense_item',
        'cashflow_expense_sub_item',
        'currency',
        'amount',
        'department',
        'model_nickname',
        'created_by',
        'payment_description',
        'payment_details',
        'payment_system',
    ]

    @extend_schema(
        responses=CashflowListSerializer
    )
    @action(detail=True, methods=['POST'])
    def copy(self, request, *args, **kwargs):
        """
        Copy an existing RequestCashflow instance.
        """
        existing_request_cashflow = self.get_object()
        new_cashflow = RequestCashflowService().copy_request_cashflow(
            source_cashflow=existing_request_cashflow,
            user=request.user
        )
        response_serializer = CashflowListSerializer(new_cashflow)

        return Response(response_serializer.data, status=status.HTTP_201_CREATED)


@extend_schema(tags=['finance-cashflow-incomes'])
class CashflowIncomeItemViewSet(ListModelMixin, GenericViewSet):
    """
    List all Cashflow Income Items
    """
    permission_classes = [HasAPIKey | IsHofOrFinancier]
    serializer_class = CashflowIncomeItemSerializer
    queryset = CashflowIncomeItem.objects.filter(is_active=True)


@extend_schema(tags=['finance-cashflow-incomes'])
class CashflowIncomeRequestViewSet(
    ListSerializerResponseMixin,
    BaseViewMethodsMixin,
    ModelViewSet
):
    """
    API endpoints that allows to manage CashflowIncomeRequest objects
    """
    action_permissions = {
        'create': [HasAPIKey | IsHofOrFinancier],
        'list': [IsHofOrFinancier],
        'update': [IsHofOrFinancier],
        'partial_update': [IsHofOrFinancier],
        'retrieve': [IsHofOrFinancier],
        'copy': [IsHofOrFinancier]
    }
    action_serializers = {
        'create': CashflowIncomeCreateSerializer,
        'update': CashflowIncomeUpdateSerializer,
        'partial_update': CashflowIncomeUpdateSerializer,
        'list': CashflowIncomeListSerializer,
        'retrieve': CashflowIncomeListSerializer,
    }
    queryset = RequestCashflowIncome.objects.select_related(
        'department',
        'cashflow_income_item',
        'payment_system',
        'currency'
    ).order_by(
        '-id'
    )
    pagination_class = FiftyPerPagePagination
    filter_backends = [DjangoFilterBackend, SearchForDjangoCryptography]
    filterset_class = RequestCashflowIncomeFilter
    search_fields = [
        'id',
        'date',
        'cashflow_income_item',
        'currency',
        'amount',
        'department',
        'created_by',
        'payment_description',
        'income_details',
        'payment_system',
    ]

    @extend_schema(
        responses=CashflowIncomeListSerializer
    )
    @action(detail=True, methods=['POST'])
    def copy(self, request, *args, **kwargs):
        """
        Copy an existing RequestCashflowIncome instance.
        """
        existing_request_cashflow_income = self.get_object()
        new_cashflow = RequestCashflowIncomeService().copy_request_cashflow_income(
            source_cashflow_income=existing_request_cashflow_income,
            user=request.user
        )
        response_serializer = CashflowIncomeListSerializer(new_cashflow)

        return Response(response_serializer.data, status=status.HTTP_201_CREATED)


# -----------------------------------------------------Budget-------------------------------------------------
@extend_schema(tags=['finance-budget'])
class BudgetPeriodViewSet(BaseViewMethodsMixin, ModelViewSet):
    """
    API endpoints that allows to manage BudgetPeriod objects
    """
    permission_classes = [IsHofOrFinancier]
    action_serializers = {
        'create': BudgetPeriodCreateSerializer,
        'update': BudgetPeriodUpdateSerializer,
        'partial_update': BudgetPeriodUpdateSerializer,
        'list': BudgetPeriodListSerializer,
        'retrieve':  BudgetPeriodDetailSerializer,
    }

    def get_queryset(self):
        budget_with_annotations = Budget.objects.annotate(
            budget_fact=Sum(
                'budget_expenses__amount_usd',
                filter=Q(
                    (
                        Q(budget_expenses__request_cashflows__isnull=False)
                        | Q(budget_expenses__request_payments__isnull=False)
                    ),
                    budget_expenses__status=BudgetExpense.Status.DONE,
                )
            ),
            wage_fund_fact=Sum(
                'budget_expenses__amount_usd',
                filter=Q(
                    (
                        Q(budget_expenses__request_cashflows__isnull=False)
                        | Q(budget_expenses__request_payments__isnull=False)
                    ),
                    budget_expenses__request_cashflows__cashflow_expense_item__is_wage_fund=True,
                    budget_expenses__status=BudgetExpense.Status.DONE,
                ),
            )
        ).select_related(
            'department'
        )

        return BudgetPeriod.objects.prefetch_related(
            Prefetch('budgets', queryset=budget_with_annotations)
        )

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        instance = self.get_queryset().get(pk=serializer.instance.id)
        response_serializer = BudgetPeriodDetailSerializer(instance=instance)

        return Response(response_serializer.data, status=status.HTTP_201_CREATED)

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        response_serializer = BudgetPeriodDetailSerializer(instance=self.get_object())

        return Response(response_serializer.data, status=status.HTTP_200_OK)


@extend_schema(tags=['finance-budget'])
class BudgetViewSet(
    BaseViewMethodsMixin,
    UpdateModelMixin,
    DestroyModelMixin,
    GenericViewSet
):
    """
    API endpoints that allows to manage Budget objects
    """
    permission_classes = [IsHofOrFinancier]
    queryset = Budget.objects.annotate(
            budget_fact=Sum(
                'budget_expenses__amount_usd',
                filter=Q(
                    (
                        Q(budget_expenses__request_cashflows__isnull=False)
                        | Q(budget_expenses__request_payments__isnull=False)
                    ),
                    budget_expenses__status=BudgetExpense.Status.DONE,
                )
            ),
            wage_fund_fact=Sum(
                'budget_expenses__amount_usd',
                filter=Q(
                    (
                        Q(budget_expenses__request_cashflows__isnull=False)
                        | Q(budget_expenses__request_payments__isnull=False)
                    ),
                    budget_expenses__request_cashflows__cashflow_expense_item__is_wage_fund=True,
                    budget_expenses__status=BudgetExpense.Status.DONE,
                )
            )
        ).select_related(
            'department'
        )
    action_serializers = {
        'create': BudgetCreateSerializer,
        'update': BudgetUpdateSerializer,
        'partial_update': BudgetUpdateSerializer,
        'list': BudgetListSerializer,
    }

    @extend_schema(responses=BudgetListSerializer)
    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        instance = self.get_queryset().get(pk=serializer.instance.id)
        response_serializer = BudgetListSerializer(instance=instance)

        return Response(response_serializer.data, status=status.HTTP_201_CREATED)

    @extend_schema(responses=BudgetListSerializer)
    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        response_serializer = BudgetListSerializer(instance=self.get_object())

        return Response(response_serializer.data, status=status.HTTP_200_OK)
