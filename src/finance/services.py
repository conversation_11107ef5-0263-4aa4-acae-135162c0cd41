import logging
from collections import defaultdict
from datetime import (
    date,
    datetime,
    timedelta,
)
from decimal import Decimal

import gspread
import httpx
from django.conf import settings
from django.contrib.auth import get_user_model
from django.db import IntegrityError, transaction
from django.db.models import Q, QuerySet
from django.utils import timezone
from rest_framework.exceptions import PermissionDenied

from ads.models import Ads
from ads.services import AdsService
from base.exceptions import CurrencyConversionException, CurrencyNotFoundException
from base.mixins import GoogleMonthSheetMixin
from base.services import CurrencyConverter, GoogleSheetsManager
from base.signals import post_bulk_update
from finance.dtos import (
    DebtTrackingDTO,
    IncomeFansTrackingDTO,
    MonthDataDTO,
    MonthIncomeFansDataDTO,
)
from finance.exceptions import GoogleSheetAPIError, GoogleSheetCellNotFound
from finance.models import (
    Budget,
    BudgetExpense,
    BudgetPeriod,
    CopyTableSnapshot,
    ModelDonorDebtHistory,
    ModelDonorDebtHistoryDetail,
    MonthDebtTracking,
    MonthIncomeFansTracking,
    MonthModelDebtTracking,
    PaymentDepartment,
    PaymentTransaction,
    PayoutAddress,
    PayoutReview,
    RequestCashflow,
    RequestCashflowIncome,
    RequestPayment,
    ReviewResult,
    TotalDebtHistory,
    TotalIncomeFansHistory,
)
from only_fans_db.models import PayoutRequests
from only_fans_models.models import OnlyFansModel

logger = logging.getLogger(__name__)
User = get_user_model()


class ToggleFianceReviewMixin:
    """
    Mixin for toggle finance reviewer
    """

    @classmethod
    def toggle_reviewer(cls, instance, reviewer: User):
        assert hasattr(cls, 'MODEL'), 'MODEL is required'

        with transaction.atomic():
            instance = cls.MODEL.objects.select_for_update().get(id=instance.id)

            if not instance.finance_reviewer:
                instance.finance_reviewer = reviewer

            elif instance.finance_reviewer == reviewer:
                instance.finance_reviewer = None

            elif instance.finance_reviewer != reviewer:
                raise PermissionDenied("You are not current reviewer")

            instance.save()

        return instance


class BudgetService:
    """
    Service for managing Budgets
    """

    @staticmethod
    def link_new_budget_expenses_to_budget(budget: Budget) -> int:
        """
        Link new BudgetExpenses to the Budget instance.

        Args:
            budget (Budget): The Budget instance to link BudgetExpenses to.

        Returns:
            list[BudgetExpense]: The list of linked BudgetExpenses.
        """
        return BudgetExpense.objects.filter(
            (
                Q(
                    payment_date__gte=budget.period.start_date,
                    payment_date__lte=budget.period.end_date,
                    enrollment_date__isnull=True,
                    payment_date__isnull=False,
                )
                | Q(
                    enrollment_date__gte=budget.period.start_date,
                    enrollment_date__lte=budget.period.end_date,
                    enrollment_date__isnull=False,
                )
            ),
            department=budget.department,
            budget__isnull=True,
            status=BudgetExpense.Status.DONE,
        ).update(budget=budget)

    @classmethod
    def bulk_create(
        cls, period: BudgetPeriod, validated_data: list[dict]
    ) -> list[Budget]:
        """
        Bulk create Budget instances.

        Args:
            period (BudgetPeriod): The BudgetPeriod instance for which to create budgets.
            validated_data (list[dict]): The list of validated data for creating budgets.

        Returns:
            list[Budget]: The list of created Budget instances.
        """
        with transaction.atomic():
            budgets = Budget.objects.bulk_create(
                [Budget(**data, period=period) for data in validated_data],
                batch_size=50,
            )
            for budget in budgets:
                cls.link_new_budget_expenses_to_budget(budget=budget)

        return budgets

    @classmethod
    def create(cls, period: BudgetPeriod, **validated_data) -> Budget:
        """
        Create a new Budget instance.

        Args:
            period (BudgetPeriod): The BudgetPeriod instance for which to create the budget.
            **validated_data: The validated data for creating the budget.

        Returns:
            Budget: The created Budget instance.
        """
        with transaction.atomic():
            budget = Budget.objects.create(period=period, **validated_data)
            cls.link_new_budget_expenses_to_budget(budget=budget)

        return budget

    @classmethod
    def update(cls, instance: Budget, **validated_data) -> Budget:
        """
        Update an existing Budget instance.

        Args:
            instance (Budget): The Budget instance to update.
            **validated_data: The validated data for updating the Budget instance.

        Returns:
            Budget: The updated Budget instance.
        """
        with transaction.atomic():
            for key, value in validated_data.items():
                setattr(instance, key, value)

            instance.save()
            cls.link_new_budget_expenses_to_budget(budget=instance)

        return instance


class BudgetPeriodService:
    """
    Service for managing BudgetPeriods
    """

    def __init__(self, budget_service: BudgetService | None = None):
        self.budget_service = budget_service or BudgetService()

    def _create_budgets(self, period: BudgetPeriod, budgets: list[dict]) -> None:
        """
        Create budgets for a given BudgetPeriod.

        Args:
            period (BudgetPeriod): The BudgetPeriod instance for which to create budgets.
            budgets (list[dict]): The list of budgets to create.

        Returns:
            None
        """
        self.budget_service.bulk_create(period=period, validated_data=budgets)

    def create(self, **validated_data) -> BudgetPeriod:
        """
        Create a new BudgetPeriod instance.

        Args:
            **validated_data: The validated data for creating the BudgetPeriod instance.

        Returns:
            BudgetPeriod: The created BudgetPeriod instance.
        """

        with transaction.atomic():
            budgets = validated_data.pop('budgets', [])
            period = BudgetPeriod.objects.create(**validated_data)

            self.budget_service.bulk_create(period=period, validated_data=budgets)

        return period

    def update(self, instance: BudgetPeriod, **validated_data) -> BudgetPeriod:
        """
        Update an existing BudgetPeriod instance.

        Args:
            instance (BudgetPeriod): The BudgetPeriod instance to update.
            **validated_data: The validated data for updating the BudgetPeriod instance.

        Returns:
            BudgetPeriod: The updated BudgetPeriod instance.
        """
        with transaction.atomic():
            if 'budgets' in validated_data:
                budgets = validated_data.pop('budgets', [])
                instance.budgets.all().delete()
                self.budget_service.bulk_create(period=instance, validated_data=budgets)

            for key, value in validated_data.items():
                setattr(instance, key, value)
            instance.save()

        return instance


class BudgetExpenseService:
    """
    Service for managing Budget Expenses
    """

    @staticmethod
    def find_budget(
        enrollment_date: date, payment_date: date, department: PaymentDepartment
    ) -> Budget | None:
        """
        Find the budget for a given date.

        Args:
            enrollment_date (date): The enrollment date.
            payment_date (date): The payment date.
            department (PaymentDepartment): The department for which to find the budget.

        Returns:
            Budget | None: The budget for the given date and department, or None if not found.
        """
        date_ = enrollment_date or payment_date

        if not date_:
            return

        budget_date = date_.replace(day=1)

        return Budget.objects.filter(
            period__start_date__lte=budget_date,
            period__end_date__gte=budget_date,
            department=department,
        ).first()

    @classmethod
    def create(
        cls,
        date: date,
        payment_date: date,
        expense_item: str,
        amount_usd: Decimal | float | int,
        payment_system: str,
        status: BudgetExpense.Status,
        department: PaymentDepartment,
        finance_review_result: str,
        cashflow_expense_sub_item: str | None = None,
        enrollment_date: date | None = None,
        is_wage_fund: bool = False,
        description: str | None = None,
    ) -> BudgetExpense:
        """
        Create a new Budget Expense instance.

        Args:
            date (date): The date of the expense.
            payment_date (date): The payment date of the expense.
            expense_item (str): The expense item.
            amount_usd (float): The amount in USD.
            payment_system (str): The payment system.
            status (BudgetExpense.Status): The status of the expense.
            department (PaymentDepartment): The department for the expense.
            finance_review_result (ReviewResult): The finance review result.
            cashflow_expense_sub_item (str | None, optional): The cashflow expense sub-item. Defaults to None.
            enrollment_date (date | None, optional): The enrollment date. Defaults to None.
            is_wage_fund (bool, optional): Whether the expense is for a wage fund. Defaults to False.
            description (str | None, optional): The description of the expense. Defaults to None.
        Returns:
            BudgetExpense: The created Budget Expense instance.
        """
        budget = cls.find_budget(
            enrollment_date=enrollment_date,
            payment_date=payment_date,
            department=department,
        )

        return BudgetExpense.objects.create(
            date=date,
            payment_date=payment_date,
            expense_item=expense_item,
            amount_usd=amount_usd,
            department=department,
            department_name=getattr(department, 'name', 'undefined'),
            payment_system=payment_system,
            status=status,
            cashflow_expense_sub_item=cashflow_expense_sub_item,
            budget=budget,
            enrollment_date=enrollment_date,
            is_wage_fund=is_wage_fund,
            description=description,
            finance_review_result=finance_review_result,
        )

    @classmethod
    def update(
        cls,
        instance: BudgetExpense,
        payment_date: date,
        expense_item: str,
        amount_usd: Decimal,
        payment_system: str,
        status: BudgetExpense.Status,
        department: PaymentDepartment,
        finance_review_result: str,
        cashflow_expense_sub_item: str | None = None,
        enrollment_date: date | None = None,
        is_wage_fund: bool = False,
        description: str | None = None,
    ) -> BudgetExpense:
        """
        Update an existing Budget Expense instance.

        Args:
            instance (BudgetExpense): The Budget Expense instance to update.
            payment_date (date): The new payment date of the expense.
            expense_item (str): The new expense item.
            amount_usd (float): The new amount in USD.
            payment_system (str): The new payment system.
            status (BudgetExpense.Status): The new status of the expense.
            department (PaymentDepartment): The new department for the expense.
            finance_review_result (ReviewResult): The new finance review result.
            cashflow_expense_sub_item (str | None, optional): The new cashflow expense sub-item. Defaults to None.
            enrollment_date (date | None, optional): The new enrollment date. Defaults to None.
            is_wage_fund (bool, optional): Whether the expense is for a wage fund. Defaults to False.
            description (str | None, optional): The description of the expense. Defaults to None.

        Returns:
            BudgetExpense: The updated Budget Expense instance.
        """
        budget = cls.find_budget(
            enrollment_date=enrollment_date,
            payment_date=payment_date,
            department=department,
        )

        instance.payment_date = payment_date
        instance.enrollment_date = enrollment_date
        instance.expense_item = expense_item
        instance.amount_usd = amount_usd
        instance.department = department
        instance.department_name = getattr(department, 'name', 'undefined')
        instance.payment_system = payment_system
        instance.status = status
        instance.cashflow_expense_sub_item = cashflow_expense_sub_item
        instance.budget = budget
        instance.is_wage_fund = is_wage_fund
        instance.description = description
        instance.finance_review_result = finance_review_result

        instance.save()

        return instance


class RequestPaymentService(ToggleFianceReviewMixin):
    MODEL = RequestPayment
    CONVERT_CURRENCY = "USD"

    def convert_currency(self, request_payment: RequestPayment) -> Decimal:
        """
        Convert payment amount to USD based on payment currency

        Args:
            request_payment (RequestPayment): RequestPayment instance
        """
        payment_currency = request_payment.payment_currency
        payment_amount = request_payment.payment_amount
        value = payment_amount

        if payment_currency and payment_amount:
            try:
                currency_converter = CurrencyConverter()
                value = currency_converter.convert(
                    payment_currency.name.upper(),
                    self.CONVERT_CURRENCY,
                    payment_amount,
                )
                value = round(value, 2)

            except (CurrencyNotFoundException, CurrencyConversionException) as e:
                logger.error(f"Currency conversion failed: {e}", exc_info=True)

        return value

    def get_payment_department_or_none(
        self, request_payment: RequestPayment
    ) -> PaymentDepartment | None:
        """
        Get payment department or none based on request_payment.department

        Args:
            request_payment (RequestPayment): RequestPayment instance
        """
        try:
            return PaymentDepartment.objects.get(
                map_value__iexact=request_payment.department.strip()
            )
        except PaymentDepartment.DoesNotExist:
            return None

    @staticmethod
    def _get_payment_date(finance_review_result: str, finance_review_date: datetime):
        """
        Get payment date based on finance review result and finance review date

        Args:
            finance_review_result (ReviewResult): finance review result
            finance_review_date (datetime): finance review date

        Returns:
            datetime: payment date
        """
        if finance_review_result == ReviewResult.ACCEPT:
            return finance_review_date

        return None

    @staticmethod
    def _get_status(finance_review_result: str):
        """
        Get status based on finance review result

        Args:
            finance_review_result (str): finance review result

        Returns:
            BudgetExpense.Status: status
        """
        if finance_review_result == ReviewResult.ACCEPT:
            return BudgetExpense.Status.DONE

        return BudgetExpense.Status.NOT_DONE

    def _create_or_update_budget_expense(self, request_payment: RequestPayment):
        """
        Create budget expense based on request payment

        Args:
            request_payment (RequestPayment): RequestPayment instance
        """
        if (
            hasattr(request_payment, 'budget_expense')
            and request_payment.budget_expense
        ):
            self._update_budget_expense(request_payment)
        else:
            self._create_budget_expense(request_payment)

    def _create_budget_expense(self, request_payment: RequestPayment):
        """
        Create budget expense based on request payment

        Args:
            request_payment (RequestPayment): RequestPayment instance
        """
        payment_date = self._get_payment_date(
            request_payment.finance_review_result,
            request_payment.finance_review_date,
        )
        status = self._get_status(request_payment.finance_review_result)

        request_payment.budget_expense = BudgetExpenseService.create(
            date=request_payment.date or timezone.now().date(),
            payment_date=payment_date,
            expense_item=getattr(request_payment.payment_expense_item, 'name', None),
            amount_usd=request_payment.amount_usd,
            payment_system=request_payment.payment_system,
            status=status,
            department=request_payment.payment_department,
            description=request_payment.payment_description,
            finance_review_result=request_payment.finance_review_result,
        )

    def _update_budget_expense(self, request_payment: RequestPayment):
        """
        Update budget expense based on request payment

        Args:
            request_payment (RequestPayment): RequestPayment instance
        """
        payment_date = self._get_payment_date(
            request_payment.finance_review_result,
            request_payment.finance_review_date,
        )
        status = self._get_status(request_payment.finance_review_result)

        BudgetExpenseService.update(
            instance=request_payment.budget_expense,
            payment_date=payment_date,
            expense_item=getattr(request_payment.payment_expense_item, 'name', None),
            amount_usd=request_payment.amount_usd,
            payment_system=request_payment.payment_system,
            status=status,
            department=request_payment.payment_department,
            description=request_payment.payment_description,
            finance_review_result=request_payment.finance_review_result,
        )

    def create(self, validated_data: dict) -> RequestPayment:
        """
        Create new RequestPayment instance

        Args:
            validated_data (dict): validated data
        """
        new_request_payment = RequestPayment(**validated_data)
        new_request_payment.amount_usd = self.convert_currency(new_request_payment)
        new_request_payment.payment_department = self.get_payment_department_or_none(
            new_request_payment
        )

        with transaction.atomic():
            self._create_budget_expense(new_request_payment)
            new_request_payment.save()

        return new_request_payment

    def update_payment_transactions(
        self, instance: MODEL, payment_transactions: list[dict]
    ) -> None:
        PaymentTransaction.objects.filter(request_payment=instance).delete()

        for payment_transaction in payment_transactions:
            payment_transaction.pop('id', None)
            PaymentTransaction.objects.create(
                request_payment=instance, **payment_transaction
            )

    def update(self, instance: MODEL, validated_data: dict, user: User) -> MODEL:
        with transaction.atomic():
            if 'payment_transactions' in validated_data:
                payment_transactions = validated_data.pop('payment_transactions', [])
                self.update_payment_transactions(instance, payment_transactions)

            if 'finance_review_result' in validated_data:
                finance_review_result = validated_data.pop('finance_review_result')

                if (
                    getattr(instance, 'finance_review_result', None)
                    != finance_review_result
                ):
                    instance.finance_review_result = finance_review_result
                    instance.finance_review_date = timezone.now()
                    instance.financier = user.full_name

            if 'head_review_result' in validated_data:
                head_review_result = validated_data.pop('head_review_result')

                if getattr(instance, 'head_review_result', None) != head_review_result:
                    instance.head_review_result = head_review_result
                    instance.head_review_date = timezone.now()

            for attr, value in validated_data.items():
                setattr(instance, attr, value)

            instance.amount_usd = self.convert_currency(instance)
            instance.payment_department = self.get_payment_department_or_none(instance)

            if instance.head_review_result == ReviewResult.ACCEPT:
                RequestPaymentSheetService().update_row(
                    instance, create_if_not_exist=True
                )
                instance.google_result = True

            self._create_or_update_budget_expense(request_payment=instance)

            instance.save()

            return instance


class RequestPaymentSheetService(GoogleMonthSheetMixin, GoogleSheetsManager):
    """
    Service for managing Request Payments in the shift
    """

    HEADERS = [
        "Номер запроса",
        "Дата запроса",
        "Дата утверждения запроса",
        "Запрос отавил",
        "Запрос утвердил",
        "Отдел",
        "Платежная система",
        "Статья расхода",
        "Никнейм модели",
        "Валюта",
        "Сумма",
        "Реквизиты",
        "Примечание платежа",
        "Ссылка Jira",
        "Описание платежа",
        "Финансист",
        "Дата обработки заявки",
        "Статус обработки заявки",
        "Способ оплаты",
        "Сумма оплаты",
        "Дополнительные источники оплаты",
        "Cумма USD",
        "Сеть",
    ]
    SHEET_ID = settings.REQUEST_PAYMENT_GOOGLE_SHEET_ID
    CONFIG_FILE_PATH = "var/google_api_config.json"
    REQUEST_PAYMENT_ID_COLUMN = 1

    def __init__(self):
        if not self.SHEET_ID:
            raise ValueError("SHEET_ID is not set")

        super().__init__(self.CONFIG_FILE_PATH, self.SHEET_ID)

    @staticmethod
    def _add_payment_transactions_to_row(request_payment: RequestPayment, row: list):
        payment_transactions = request_payment.payment_transactions.order_by(
            'created_at'
        ).select_related('payment_tool')

        if payment_transactions:
            row.append(payment_transactions[0].payment_tool.name)
            row.append(str(payment_transactions[0].amount).replace('.', ','))

            if len(payment_transactions) > 1:
                row.append(
                    ' + '.join(
                        f'{transaction.payment_tool.name} {str(transaction.amount).replace(".", ",")}'
                        for transaction in payment_transactions[1:]
                    )
                )
            else:
                row.append('')
        else:
            row.extend([''] * 3)

    def generate_row_data(self, request_payment: RequestPayment) -> list:
        result = []

        for attr in [
            'id',
            'date',
            'head_review_date',
            'created_by',
            'head',
            'department',
            'payment_system',
            ('payment_expense_item', 'name'),
            'model_nickname',
            ('payment_currency', 'name'),
            'payment_amount',
            'payment_details',
            'payment_comment',
            'jira_link',
            'payment_description',
            'financier',
            'finance_review_date',
            'finance_review_result',
        ]:
            if isinstance(attr, tuple):
                value = request_payment

                for attr_part in attr:
                    try:
                        value = getattr(value, attr_part)
                    except AttributeError:
                        value = ''

                        break
            else:
                value = getattr(request_payment, attr, None)

                if isinstance(value, datetime):
                    value = value.strftime('%d.%m.%Y')
                elif isinstance(value, Decimal):
                    value = str(value).replace('.', ',')

            result.append(str(value) if value else '')

        # write transactions
        self._add_payment_transactions_to_row(
            request_payment=request_payment, row=result
        )

        if request_payment.amount_usd:
            result.append(str(request_payment.amount_usd).replace('.', ','))

        if request_payment.payment_crypto_network:
            result.append(request_payment.payment_crypto_network.name)

        return result

    def add_row(self, request_payment: RequestPayment) -> None:
        try:
            sheet = self.get_month_sheet(request_payment.date)
            row_data = self.generate_row_data(request_payment)
            sheet.append_row(row_data, value_input_option='USER_ENTERED')
        except gspread.exceptions.APIError as error:
            raise GoogleSheetAPIError(error)

    def update_row(
        self, request_payment: RequestPayment, create_if_not_exist: bool = False
    ) -> None:
        try:
            sheet = self.get_month_sheet(request_payment.date)

            cell = sheet.find(
                str(request_payment.id), in_column=self.REQUEST_PAYMENT_ID_COLUMN
            )

            if not cell:
                if create_if_not_exist:
                    return self.add_row(request_payment)

                raise GoogleSheetCellNotFound()

            row_number = cell.row
            row_data = self.generate_row_data(request_payment)
            row_range = f"A{row_number}"
            sheet.update([row_data], row_range, value_input_option='USER_ENTERED')
        except gspread.exceptions.APIError as error:
            raise GoogleSheetAPIError(error)


class PayoutReviewService(ToggleFianceReviewMixin):
    """
    Service for managing Payout Reviews
    """

    MODEL = PayoutReview
    START_PARSE_DATE = settings.PARSE_FROM_PAYOUTS_DATE or timezone.now().date()
    assert isinstance(
        START_PARSE_DATE, date
    ), "PARSE_FROM_PAYOUTS_DATE should be a date"

    def get_latest_payout_review_date(self) -> date | None:
        latest_payout_review = (
            self.MODEL.objects.filter(
                payout_id__isnull=False,
            )
            .order_by('-payout_creation_date')
            .first()
        )

        return (
            latest_payout_review.payout_creation_date.date()
            if latest_payout_review
            else None
        )

    @staticmethod
    def _get_external_model_ids() -> list[int]:
        """
        Get models with external team_lead

        :return: List of model_ids
        :retype: list[int]
        """
        return list(
            OnlyFansModel.objects.filter(
                team_lead__external=True, team_lead__isnull=False
            )
            .values_list('model_id', flat=True)
            .distinct()
        )

    def get_new_payouts_from_db(
        self, date_from: date | None = None
    ) -> QuerySet(PayoutRequests):
        """
        Get new payouts from database

        :param date_from: date from which to get payouts
        :return: QuerySet of new payouts
        :rtype: QuerySet
        """
        if not date_from:
            date_from = timezone.now().date()

        external_model_ids = self._get_external_model_ids()

        return PayoutRequests.objects.filter(
            ~Q(model_id__in=external_model_ids),
            created_at__gte=date_from,
        ).order_by('created_at')

    @staticmethod
    def get_only_fans_models_by_payout_ids(
        payout_ids: list[str],
    ) -> dict[str, OnlyFansModel]:
        """
        Get only fans model by payout_ids

        :param payout_ids: list of payout review ids
        :return: dict of only fans models
        :rtype: dict[str, OnlyFansModel]
        """
        only_fans_models = OnlyFansModel.objects.filter(model_id__in=payout_ids)

        return {model.model_id: model for model in only_fans_models}

    def collect_new_payouts_from_only_fans_db(
        self, date_from: date | None = None
    ) -> list[str]:
        """
        Collect new payouts from OnlyFans database

        :param date_from: date from which to get payouts
        :return: list of payout review ids
        :rtype: list[str]
        """
        now = timezone.now()
        new_payouts = self.get_new_payouts_from_db(date_from=date_from)
        only_fans_models_dict = self.get_only_fans_models_by_payout_ids(
            [new_payout.model_id for new_payout in new_payouts]
        )

        payout_reviews = [
            self.MODEL(
                payout_id=new_payout.invoice_id,
                payout_creation_date=new_payout.created_at,
                amount=new_payout.amount,
                only_fans_model=only_fans_models_dict.get(new_payout.model_id),
                model_id=new_payout.model_id,
            )
            for new_payout in new_payouts
        ]
        self.MODEL.objects.bulk_create(
            payout_reviews, ignore_conflicts=True, batch_size=100
        )

        return [
            str(id_)
            for id_ in self.MODEL.objects.filter(created_at__gte=now)
            .order_by('created_at')
            .values_list('id', flat=True)
        ]

    @staticmethod
    def update(instance: MODEL, validated_data: dict, user: User) -> PayoutReview:
        for attr, value in validated_data.items():
            if attr == 'finance_review_result' and value != getattr(
                instance, 'finance_review_result'
            ):
                instance.finance_review_result = value
                instance.finance_review_date = timezone.now()
                instance.financier = user.full_name
            setattr(instance, attr, value)

        instance.save()

        return instance

    def create(self, validated_data: dict, user: User) -> PayoutReview:
        return self.MODEL.objects.create(
            **validated_data,
            finance_review_result=ReviewResult.ACCEPT,
            finance_review_date=timezone.now(),
            financier=user.full_name,
            finance_reviewer=user,
        )

    @classmethod
    def mass_provide(
        cls,
        payout_reviews: list[PayoutReview],
        payout_address: PayoutAddress,
        user: User,
    ) -> list[PayoutReview]:
        """
        Updates a batch of `PayoutReview` instances with finance review details, payout address,
        and triggers a bulk update.

        Args:
            payout_reviews (list[PayoutReview]): List of `PayoutReview` instances to update.
            payout_address (PayoutAddress): The payout address to assign.
            user (User): The user performing the review.

        Returns:
            list[PayoutReview]: The updated list of `PayoutReview` instances.
        """
        for payout_review in payout_reviews:
            payout_review.finance_review_result = ReviewResult.ACCEPT
            payout_review.finance_review_date = timezone.now()
            payout_review.financier = user.full_name
            payout_review.finance_reviewer = user
            payout_review.payout_address = payout_address

        PayoutReview.objects.bulk_update(
            payout_reviews,
            fields=[
                'finance_review_result',
                'finance_review_date',
                'financier',
                'finance_reviewer',
                'payout_address',
            ],
            batch_size=100,
        )
        post_bulk_update.send(sender=PayoutReview, instances=payout_reviews)

        return payout_reviews


class TotalDebtHistoryService:
    """
    Service for managing Total Debts
    """

    MODEL = TotalDebtHistory

    @classmethod
    def write_total_debt_history_by_date(cls, debts_date: date) -> MODEL | None:
        """
        Write total debt history by date

        :param debts_date: date for which to calculate total debt
        :type debts_date: date
        :return: TotalDebtHistory instance
        :rtype: TotalDebtHistory | None
        """
        if cls.MODEL.objects.filter(date=debts_date).exists():
            return

        current_month_date = debts_date.replace(day=1)

        ads_list = list(
            Ads.objects.filter(
                status__name__in=AdsService.DEBT_QUERYSET_FILTER_STATUSES,
                fans_delta__isnull=False,
                fans_delta__gt=0,
                cost_result__isnull=False,
                date__lte=debts_date,
                department=Ads.DepartmentChoices.MARKETING,
            ).select_related('only_fans_model').order_by('date')
        )
        debts = AdsService.get_debts(ads_list)
        total_debt_amount = sum(debt.amount for debt in debts)
        total_fans_debt_amount = sum(debt.fans_amount for debt in debts)

        with transaction.atomic():
            total_debt_history = cls.MODEL.objects.create(
                date=debts_date,
                debt_amount=total_debt_amount,
                fans_debt_amount=total_fans_debt_amount,
            )

            model_month_debt_trackings = []

            for debt in debts:
                if debt.month != current_month_date:
                    month_debt_tracking = MonthDebtTracking.objects.create(
                        month_date=debt.month,
                        month_debt_amount=debt.amount,
                        month_fans_debt_amount=debt.fans_amount,
                        total_debt_history=total_debt_history,
                    )

                    for model_debt in debt.models_debts.values():
                        model_month_debt_tracking = MonthModelDebtTracking(
                            month_debt_tracking=month_debt_tracking,
                            only_fans_model=model_debt.only_fans_model,
                            debt_amount=model_debt.amount,
                            fans_debt_amount=model_debt.fans_amount,
                        )
                        model_month_debt_trackings.append(model_month_debt_tracking)

            MonthModelDebtTracking.objects.bulk_create(
                model_month_debt_trackings, batch_size=500, ignore_conflicts=True
            )

        return total_debt_history

    @staticmethod
    def get_suitable_days(
        start_date: datetime.date, end_date: datetime.date, day_of_the_week: int = 0
    ) -> list[datetime.date]:
        """
        Extract tuesdays from date range

        :param start_date: start date
        :type start_date: datetime.date
        :param end_date: end date
        :type end_date: datetime.date
        :param day_of_the_week
        :type day_of_the_week: int
        :return: list of tuesdays
        :rtype: list[datetime.date]
        """
        assert day_of_the_week in range(7), 'day_of_the_week should be in range(7)'

        if start_date > end_date:
            start_date, end_date = end_date, start_date

        current_date = start_date
        while current_date.weekday() != day_of_the_week:
            current_date += timedelta(days=1)

        suitable_days = []
        while current_date <= end_date:
            suitable_days.append(current_date)
            current_date += timedelta(days=7)

        return suitable_days

    def get_debt_tracking_dtos(
        self,
        queryset: QuerySet[MODEL],
        start_date: date,
        end_date: date,
        day_of_the_week: int = 0,
    ) -> list[DebtTrackingDTO]:
        """
        Get debt tracking dtos

        :param queryset: queryset of TotalDebtHistory
        :type queryset: QuerySet[MODEL]
        :param start_date: start date
        :type start_date: date
        :param end_date: end date
        :type end_date: date
        :param day_of_the_week
        :type day_of_the_week: int from 0 to 6
        :return: list of DebtTrackingDTO
        :rtype: list[DebtTrackingDTO]
        """
        assert start_date and end_date, 'start_date and end_date should be provided'

        suitable_days = self.get_suitable_days(start_date, end_date, day_of_the_week)
        queryset = queryset.filter(date__in=suitable_days).order_by('date')

        debt_tracking_dtos = []
        first_week_data = {}
        found_tuesdays = []
        for index, total_debt_history in enumerate(queryset):
            debt_tracking_dto = DebtTrackingDTO(
                number=suitable_days.index(total_debt_history.date) + 1,
                parse_date=total_debt_history.date,
            )
            for month_debt_tracking in total_debt_history.month_debt_trackings.order_by(
                'month_date'
            ):
                month_data_dto = MonthDataDTO(
                    month_date=month_debt_tracking.month_date,
                    month_debt_amount=month_debt_tracking.month_debt_amount,
                )

                if index == 0:
                    first_week_data[
                        month_debt_tracking.month_date
                    ] = month_debt_tracking.month_debt_amount
                else:
                    month_data_dto.delta = (
                        month_debt_tracking.month_debt_amount
                        - first_week_data.get(month_debt_tracking.month_date, 0)
                    )

                debt_tracking_dto.add_month_data(month_data_dto)
            debt_tracking_dtos.append(debt_tracking_dto)
            found_tuesdays.append(debt_tracking_dto.parse_date)

        for day in suitable_days:
            if day not in found_tuesdays:
                debt_tracking_dtos.insert(
                    suitable_days.index(day),
                    DebtTrackingDTO(
                        number=suitable_days.index(day) + 1, parse_date=day
                    ),
                )

        return debt_tracking_dtos

    def get_debt_tracking_string(
        self,
        queryset: QuerySet[MODEL],
        start_date: date,
        end_date: date,
        day_of_the_week: int = 0,
    ) -> str:
        """
        Get debt tracking string

        :param queryset: queryset of TotalDebtHistory
        :type queryset: QuerySet[MODEL]
        :param start_date: start date
        :type start_date: date
        :param end_date: end date
        :type end_date: date
        :param day_of_the_week
        :type day_of_the_week: int from 0 to 6
        :return: debt tracking string
        :rtype: str
        """
        debt_tracking_dtos = self.get_debt_tracking_dtos(
            queryset, start_date, end_date, day_of_the_week
        )
        debt_tracking_dtos = [dto for dto in debt_tracking_dtos if dto.month_data_dtos]

        if not debt_tracking_dtos:
            return ''

        string = (
            '\t\t\t\t'.join(
                f'{dto.number} week {dto.parse_date.strftime("%B")}'
                for dto in debt_tracking_dtos
            )
            + '\n'
        )

        dto_with_max_length_month_data = max(
            debt_tracking_dtos, key=lambda x: len(x.month_data_dtos)
        )

        for i in range(len(dto_with_max_length_month_data.month_data_dtos)):
            for dto in debt_tracking_dtos:
                try:
                    month_data_dto = dto.month_data_dtos[i]
                except IndexError:
                    continue
                string += (
                    f'{month_data_dto.month_date}\t'
                    f'{month_data_dto.month_debt_amount}\t'
                    f'{month_data_dto.delta}\t\t'
                )
            string += '\n'

        return string


class TotalIncomeFansHistoryService:
    """
    Service for managing Income Fans History
    """

    MODEL = TotalIncomeFansHistory

    @classmethod
    def write_income_fans_history_by_date(cls, income_fans_date: date) -> MODEL | None:
        """
        Write total debt history by date

        :param income_fans_date: date for which to calculate total debt
        :type income_fans_date: date
        :return: TotalDebtHistory instance
        :rtype: TotalDebtHistory | None
        """
        if cls.MODEL.objects.filter(date=income_fans_date).exists():
            return

        current_month_date = income_fans_date.replace(day=1)

        ads_list = list(
            Ads.objects.filter(
                (Q(phantom_cost__isnull=False) | Q(cost_result__isnull=False)),
                fans_count__isnull=False,
                fans_count__gt=0,
                date__lte=income_fans_date,
                department=Ads.DepartmentChoices.MARKETING,
                claims_count__isnull=False,
                claims_count__gt=0,
            ).order_by('date')
        )

        income_fans = AdsService.get_income_fans_dtos(ads_list)
        total = sum(income_fan.income_amount for income_fan in income_fans)

        with transaction.atomic():
            income_fans_history = cls.MODEL.objects.create(
                date=income_fans_date, income_amount=total
            )
            month_income_fans_trackings = [
                MonthIncomeFansTracking(
                    month_date=income_fan.month_date,
                    month_income_amount=income_fan.income_amount,
                    income_fans_history=income_fans_history,
                )
                for income_fan in income_fans
                if income_fan.month_date != current_month_date
            ]
            MonthIncomeFansTracking.objects.bulk_create(
                month_income_fans_trackings, batch_size=500, ignore_conflicts=True
            )

        return income_fans_history

    @staticmethod
    def get_suitable_days(
        start_date: datetime.date, end_date: datetime.date, day_of_the_week: int = 0
    ) -> list[datetime.date]:
        """
        Extract tuesdays from date range

        :param start_date: start date
        :type start_date: datetime.date
        :param end_date: end date
        :type end_date: datetime.date
        :param day_of_the_week
        :type day_of_the_week: int
        :return: list of tuesdays
        :rtype: list[datetime.date]
        """
        assert day_of_the_week in range(7), 'day_of_the_week should be in range(7)'

        if start_date > end_date:
            start_date, end_date = end_date, start_date

        current_date = start_date
        while current_date.weekday() != day_of_the_week:
            current_date += timedelta(days=1)

        suitable_days = []
        while current_date <= end_date:
            suitable_days.append(current_date)
            current_date += timedelta(days=7)

        return suitable_days

    def get_income_fans_tracking_dtos(
        self,
        queryset: QuerySet[MODEL],
        start_date: date,
        end_date: date,
        day_of_the_week: int = 0,
    ) -> list[IncomeFansTrackingDTO]:
        """
        Get Income Fans tracking dtos

        :param queryset: queryset of TotalIncomeFansHistory
        :type queryset: QuerySet[MODEL]
        :param start_date: start date
        :type start_date: date
        :param end_date: end date
        :type end_date: date
        :param day_of_the_week
        :type day_of_the_week: int from 0 to 6
        :return: list of IncomeFansTrackingDTO
        :rtype: list[IncomeFansTrackingDTO]
        """
        assert start_date and end_date, 'start_date and end_date should be provided'

        suitable_days = self.get_suitable_days(start_date, end_date, day_of_the_week)
        queryset = queryset.filter(date__in=suitable_days).order_by('date')

        income_fans_tracking_dtos = []
        first_week_data = {}
        found_tuesdays = []
        for index, total_income_fans_history in enumerate(queryset):
            income_fans_tracking_dto = IncomeFansTrackingDTO(
                number=suitable_days.index(total_income_fans_history.date) + 1,
                parse_date=total_income_fans_history.date,
            )
            for (
                month_income_fans_tracking
            ) in total_income_fans_history.month_income_fans_trackings.order_by(
                'month_date'
            ):
                month_data_dto = MonthIncomeFansDataDTO(
                    month_date=month_income_fans_tracking.month_date,
                    month_income_amount=month_income_fans_tracking.month_income_amount,
                )

                if index == 0:
                    first_week_data[
                        month_income_fans_tracking.month_date
                    ] = month_income_fans_tracking.month_income_amount
                else:
                    month_data_dto.delta = (
                        month_income_fans_tracking.month_income_amount
                        - first_week_data.get(month_income_fans_tracking.month_date, 0)
                    )

                income_fans_tracking_dto.add_month_data(month_data_dto)
            income_fans_tracking_dtos.append(income_fans_tracking_dto)
            found_tuesdays.append(income_fans_tracking_dto.parse_date)

        for day in suitable_days:
            if day not in found_tuesdays:
                income_fans_tracking_dtos.insert(
                    suitable_days.index(day),
                    IncomeFansTrackingDTO(
                        number=suitable_days.index(day) + 1, parse_date=day
                    ),
                )

        return income_fans_tracking_dtos

    def get_income_fans_tracking_string(
        self,
        queryset: QuerySet[MODEL],
        start_date: date,
        end_date: date,
        day_of_the_week: int = 0,
    ) -> str:
        """
        Get income fans tracking string

        :param queryset: queryset of TotalIncomeFansHistory
        :type queryset: QuerySet[MODEL]
        :param start_date: start date
        :type start_date: date
        :param end_date: end date
        :type end_date: date
        :param day_of_the_week
        :type day_of_the_week: int from 0 to 6
        :return: income fans tracking string
        :rtype: str
        """
        income_fans_tracking_dtos = self.get_income_fans_tracking_dtos(
            queryset, start_date, end_date, day_of_the_week
        )
        income_fans_tracking_dtos = [
            dto for dto in income_fans_tracking_dtos if dto.month_data_dtos
        ]

        if not income_fans_tracking_dtos:
            return ''

        string = (
            '\t\t\t\t'.join(
                f'{dto.number} week {dto.parse_date.strftime("%B")}'
                for dto in income_fans_tracking_dtos
            )
            + '\n'
        )

        dto_with_max_length_month_data = max(
            income_fans_tracking_dtos, key=lambda x: len(x.month_data_dtos)
        )

        for i in range(len(dto_with_max_length_month_data.month_data_dtos)):
            for dto in income_fans_tracking_dtos:
                try:
                    month_data_dto = dto.month_data_dtos[i]
                except IndexError:
                    continue
                string += (
                    f'{month_data_dto.month_date}\t'
                    f'{month_data_dto.month_income_amount}\t'
                    f'{month_data_dto.delta}\t\t'
                )
            string += '\n'

        return string


class RequestCashflowService:
    """
    Service for managing Request Cashflows
    """

    CONVERT_CURRENCY = 'USD'

    def __init__(
        self, currency_converter=None, budget_expense_service=None, sheet_service=None
    ) -> None:
        self.currency_converter = currency_converter or CurrencyConverter()
        self.budget_expense_service = budget_expense_service or BudgetExpenseService()
        self.sheet_service = sheet_service or RequestCashflowSheetService()

    def convert_currency(self, amount: Decimal, from_currency: str) -> Decimal:
        """
        Convert a given amount from a specified currency to USD.

        Args:
            amount (Decimal): The amount to convert.
            from_currency (str): The currency code of the amount to convert.

        Returns:
            float: The converted amount in USD.
        """
        value = amount

        try:
            value = self.currency_converter.convert(
                from_currency=from_currency,
                to_currency=self.CONVERT_CURRENCY,
                amount=amount,
            )
            value = round(value, 2)
        except (CurrencyNotFoundException, CurrencyConversionException) as e:
            logger.error(f"Currency conversion failed: {e}", exc_info=True)

        return value

    @staticmethod
    def _get_budget_expense_status(request_cashflow_status: str):
        """
        Get the corresponding Budget Expense status based on the Request Cashflow status.

        Args:
            request_cashflow_status (RequestCashflow.Status): The Request Cashflow status.

        Returns:
            BudgetExpense.Status: The corresponding Budget Expense status.
        """
        if request_cashflow_status == ReviewResult.ACCEPT:
            return BudgetExpense.Status.DONE
        elif request_cashflow_status == ReviewResult.REJECT:
            return BudgetExpense.Status.NOT_DONE

    def _create_or_update_budget_expense(
        self, request_cashflow: RequestCashflow
    ) -> None:
        """
        Create or update a Budget Expense instance based on the provided Request Cashflow.

        Args:
            request_cashflow (RequestCashflow): The Request Cashflow instance.
        """
        if (
            hasattr(request_cashflow, 'budget_expense')
            and request_cashflow.budget_expense
        ):
            self._update_budget_expense(request_cashflow)
        else:
            self._create_budget_expense(request_cashflow)

    def _create_budget_expense(self, request_cashflow: RequestCashflow) -> None:
        """
        Create  a Budget Expense instance based on the provided Request Cashflow.

        Args:
            request_cashflow (RequestCashflow): The Request Cashflow instance.

        Returns:
            BudgetExpense: The created or updated Budget Expense instance.
        """
        request_cashflow.budget_expense = self.budget_expense_service.create(
            date=request_cashflow.date,
            payment_date=request_cashflow.payment_date,
            enrollment_date=request_cashflow.enrollment_date,
            expense_item=request_cashflow.cashflow_expense_item.name,
            amount_usd=request_cashflow.amount_usd,
            department=request_cashflow.department,
            payment_system=request_cashflow.payment_system.name,
            status=self._get_budget_expense_status(request_cashflow.status),
            cashflow_expense_sub_item=getattr(
                request_cashflow.cashflow_expense_sub_item, 'name', None
            ),
            is_wage_fund=request_cashflow.cashflow_expense_item.is_wage_fund,
            description=request_cashflow.payment_description,
            finance_review_result=request_cashflow.status,
        )

    def _update_budget_expense(self, request_cashflow: RequestCashflow) -> None:
        """
        Create or update a Budget Expense instance based on the provided Request Cashflow.

        Args:
            request_cashflow (RequestCashflow): The Request Cashflow instance.

        Returns:
            BudgetExpense: The created or updated Budget Expense instance.
        """
        self.budget_expense_service.update(
            instance=request_cashflow.budget_expense,
            payment_date=request_cashflow.payment_date,
            enrollment_date=request_cashflow.enrollment_date,
            expense_item=request_cashflow.cashflow_expense_item.name,
            amount_usd=request_cashflow.amount_usd,
            department=request_cashflow.department,
            payment_system=request_cashflow.payment_system.name,
            status=self._get_budget_expense_status(request_cashflow.status),
            cashflow_expense_sub_item=getattr(
                request_cashflow.cashflow_expense_sub_item, 'name', None
            ),
            is_wage_fund=request_cashflow.cashflow_expense_item.is_wage_fund,
            description=request_cashflow.payment_description,
            finance_review_result=request_cashflow.status,
        )

    def create(self, **validated_data) -> RequestCashflow:
        """
        Create a new Request Cashflow instance.

        This method initializes a Request Cashflow with the provided validated data,
        converts its amount to USD, and creates an associated Budget Expense entry.
        The operation is wrapped in an atomic transaction to ensure consistency.

        Args:
            **validated_data: Arbitrary keyword arguments representing the validated
                              data required to initialize a RequestCashflow instance.

        Returns:
            RequestCashflow: The created Request Cashflow instance with the amount in USD.
        """

        new_request_cashflow = RequestCashflow(**validated_data)
        new_request_cashflow.amount_usd = self.convert_currency(
            amount=new_request_cashflow.amount,
            from_currency=new_request_cashflow.currency.name,
        )

        with transaction.atomic():
            self._create_budget_expense(request_cashflow=new_request_cashflow)
            new_request_cashflow.save()
            self.sheet_service.add_row(new_request_cashflow)

        return new_request_cashflow

    def update(
        self,
        instance: RequestCashflow,
        **validated_data,
    ) -> RequestCashflow:
        """
        Update an existing Request Cashflow instance.

        This method updates the provided Request Cashflow instance with the validated data,
        converts its amount to USD, and updates the associated Budget Expense entry.
        The operation is wrapped in an atomic transaction to ensure consistency.

        Args:
            instance (RequestCashflow): The Request Cashflow instance to be updated.
            **validated_data: Arbitrary keyword arguments representing the validated
                              data to update the Request Cashflow instance.

        Returns:
            RequestCashflow: The updated Request Cashflow instance with the amount in USD.
        """
        old_enrollment_date = None
        if (
            instance.enrollment_date
            and validated_data.get('enrollment_date')
            and instance.enrollment_date != validated_data.get('enrollment_date')
        ):
            old_enrollment_date = instance.enrollment_date

        for key, value in validated_data.items():
            setattr(instance, key, value)

        instance.amount_usd = self.convert_currency(
            amount=instance.amount,
            from_currency=instance.currency.name,
        )

        with transaction.atomic():
            self._create_or_update_budget_expense(request_cashflow=instance)
            instance.save()
            self.sheet_service.update_row(
                request_cashflow=instance,
                old_enrollment_date=old_enrollment_date,
                create_if_not_exists=True,
            )

        return instance

    def copy_request_cashflow(
        self, source_cashflow: RequestCashflow, user: User
    ) -> RequestCashflow:
        """
        Copy a Request Cashflow instance.

        This method creates a new Request Cashflow instance based on the provided source

        Args:
            source_cashflow (RequestCashflow): The source Request Cashflow instance to copy.
            user (User): request created_by

        Returns:
            RequestCashflow: The copied Request Cashflow instance.
        """
        today_date = timezone.now().date()

        new_cashflow_data = {
            'date': today_date,
            'payment_date': today_date,
            'enrollment_date': today_date,
            'created_by': user.full_name,
            'financier': user.full_name,
            'department': source_cashflow.department,
            'payment_system': source_cashflow.payment_system,
            'cashflow_expense_item': source_cashflow.cashflow_expense_item,
            'cashflow_expense_sub_item': source_cashflow.cashflow_expense_sub_item,
            'model_nickname': source_cashflow.model_nickname,
            'currency': source_cashflow.currency,
            'amount': Decimal(0),
            'status': source_cashflow.status,
            'payment_details': source_cashflow.payment_details,
            'payment_description': source_cashflow.payment_description,
        }

        return self.create(**new_cashflow_data)


class RequestCashflowSheetService(GoogleMonthSheetMixin, GoogleSheetsManager):
    """
    Service for managing RequestCashflow sheet
    """

    HEADERS = [
        'Номер запроса',
        'Дата запроса',
        'Дата платежа',
        'Дата начисления',
        'Автор запроса',
        'Финансист',
        'Отдел',
        'Платежная система',
        'Статья расхода',
        'Подстатья расхода',
        'Никнейм модели',
        'Валюта',
        'Сумма',
        'Статус обработки',
        'Способ оплаты',
        'Cумма USD',
        'ФОТ',
        'Описание',
    ]
    SHEET_ID = settings.REQUEST_CASHFLOW_GOOGLE_SHEET_ID
    CONFIG_FILE_PATH = "var/google_api_config.json"
    REQUEST_CASHFLOW_ID_COLUMN = 1

    def __init__(self):
        if not self.SHEET_ID:
            raise ValueError("SHEET_ID is not set")

        super().__init__(self.CONFIG_FILE_PATH, self.SHEET_ID)

    @staticmethod
    def generate_row_data(request_cashflow: RequestCashflow) -> list:
        result = []

        for attr in [
            'id',
            'date',
            'payment_date',
            'enrollment_date',
            'created_by',
            'financier',
            'department',
            'payment_system',
            'cashflow_expense_item',
            'cashflow_expense_sub_item',
            'model_nickname',
            'currency',
            'amount',
            'status',
            'payment_details',
            'amount_usd',
        ]:
            value = getattr(request_cashflow, attr, None)

            if isinstance(value, datetime):
                value = value.strftime('%d.%m.%Y')
            elif isinstance(value, Decimal):
                value = str(value).replace('.', ',')

            result.append(str(value) if value else '')

        if (
            hasattr(request_cashflow, 'cashflow_expense_item')
            and request_cashflow.cashflow_expense_item
        ):
            result.append(request_cashflow.cashflow_expense_item.is_wage_fund)
        else:
            result.append('')

        result.append(request_cashflow.payment_description)

        return result

    def add_row(self, request_cashflow: RequestCashflow) -> None:
        try:
            sheet = self.get_month_sheet(request_cashflow.enrollment_date)
            row_data = self.generate_row_data(request_cashflow)
            sheet.append_row(row_data, value_input_option='USER_ENTERED')
        except gspread.exceptions.APIError as error:
            raise GoogleSheetAPIError(error)

    def _delete_row(
        self, request_cashflow: RequestCashflow, old_enrollment_date: date
    ) -> None:
        try:
            sheet = self.get_month_sheet(old_enrollment_date)
            cell = sheet.find(
                str(request_cashflow.id), in_column=self.REQUEST_CASHFLOW_ID_COLUMN
            )

            if not cell:
                raise GoogleSheetCellNotFound()

            row_number = cell.row
            sheet.delete_rows(row_number)
        except gspread.exceptions.APIError as error:
            raise GoogleSheetAPIError(error)

    def update_row(
        self,
        request_cashflow: RequestCashflow,
        old_enrollment_date: date = None,
        create_if_not_exists: bool = False,
    ) -> None:
        try:
            if old_enrollment_date:
                try:
                    self._delete_row(request_cashflow, old_enrollment_date)
                except GoogleSheetCellNotFound:
                    pass

                return self.add_row(request_cashflow)

            sheet = self.get_month_sheet(request_cashflow.enrollment_date)
            cell = sheet.find(
                str(request_cashflow.id), in_column=self.REQUEST_CASHFLOW_ID_COLUMN
            )

            if not cell:
                if create_if_not_exists:
                    return self.add_row(request_cashflow)

                raise GoogleSheetCellNotFound()

            row_number = cell.row
            row_data = self.generate_row_data(request_cashflow)
            row_range = f"A{row_number}"
            sheet.update([row_data], row_range, value_input_option='USER_ENTERED')
        except gspread.exceptions.APIError as error:
            raise GoogleSheetAPIError(error)


class RequestCashflowIncomeService:
    """
    Service for managing Request Cashflows
    """

    CONVERT_CURRENCY = 'USD'

    def __init__(
        self,
        currency_converter=None,
        sheet_service=None,
    ) -> None:
        self.currency_converter = currency_converter or CurrencyConverter()
        self.sheet_service = sheet_service or RequestCashflowIncomeSheetService()

    def convert_currency(self, amount: Decimal, from_currency: str) -> Decimal:
        """
        Convert a given amount from a specified currency to USD.

        Args:
            amount (Decimal): The amount to convert.
            from_currency (str): The currency code of the amount to convert.

        Returns:
            float: The converted amount in USD.
        """
        value = amount

        try:
            value = self.currency_converter.convert(
                from_currency=from_currency,
                to_currency=self.CONVERT_CURRENCY,
                amount=amount,
            )
            value = round(value, 2)
        except (CurrencyNotFoundException, CurrencyConversionException) as e:
            logger.error(f"Currency conversion failed: {e}", exc_info=True)

        return value

    def create(self, **validated_data) -> RequestCashflowIncome:
        """
        Create a new Request Cashflow Incoe instance.

        This method initializes a Request Cashflow with the provided validated data,
        converts its amount to USD.
        The operation is wrapped in an atomic transaction to ensure consistency.

        Args:
            **validated_data: Arbitrary keyword arguments representing the validated
                              data required to initialize a RequestCashflow instance.

        Returns:
            RequestCashflowIncome: The created Request Cashflow Income instance with the amount in USD.
        """

        new_request_cashflow_income = RequestCashflowIncome(**validated_data)
        new_request_cashflow_income.amount_usd = self.convert_currency(
            amount=new_request_cashflow_income.amount,
            from_currency=new_request_cashflow_income.currency.name,
        )

        with transaction.atomic():
            new_request_cashflow_income.save()
            self.sheet_service.add_row(new_request_cashflow_income)

        return new_request_cashflow_income

    def update(
        self,
        instance: RequestCashflowIncome,
        **validated_data,
    ) -> RequestCashflowIncome:
        """
        Update an existing Request Cashflow Income instance.

        This method updates the provided Request Cashflow instance with the validated data,
        converts its amount to USD.
        The operation is wrapped in an atomic transaction to ensure consistency.

        Args:
            instance (RequestCashflowIncome): The Request Cashflow instance to be updated.
            **validated_data: Arbitrary keyword arguments representing the validated
                              data to update the Request Cashflow instance.

        Returns:
            RequestCashflowIncome: The updated Request Cashflow instance with the amount in USD.
        """
        old_enrollment_date = None
        if (
            instance.enrollment_date
            and validated_data.get('enrollment_date')
            and instance.enrollment_date != validated_data.get('enrollment_date')
        ):
            old_enrollment_date = instance.enrollment_date

        for key, value in validated_data.items():
            setattr(instance, key, value)

        instance.amount_usd = self.convert_currency(
            amount=instance.amount,
            from_currency=instance.currency.name,
        )

        with transaction.atomic():
            instance.save()
            self.sheet_service.update_row(
                request_cashflow_income=instance,
                old_enrollment_date=old_enrollment_date,
                create_if_not_exists=True,
            )

        return instance

    def copy_request_cashflow_income(
        self, source_cashflow_income: RequestCashflowIncome, user: User
    ) -> RequestCashflowIncome:
        """
        Copy a Request Cashflow Income instance.

        This method creates a new Request Cashflow instance based on the provided source

        Args:
            source_cashflow_income (RequestCashflowIncome): The source Request Cashflow Income instance to copy.
            user (User): request created_by

        Returns:
            RequestCashflowIncome: The copied Request Cashflow instance.
        """
        today_date = timezone.now().date()

        new_cashflow_data = {
            'date': today_date,
            'payment_date': today_date,
            'enrollment_date': today_date,
            'created_by': user.full_name,
            'financier': user.full_name,
            'department': source_cashflow_income.department,
            'payment_system': source_cashflow_income.payment_system,
            'cashflow_income_item': source_cashflow_income.cashflow_income_item,
            'currency': source_cashflow_income.currency,
            'amount': Decimal(0),
            'status': source_cashflow_income.status,
            'income_details': source_cashflow_income.income_details,
            'payment_description': source_cashflow_income.payment_description,
        }

        return self.create(**new_cashflow_data)


class RequestCashflowIncomeSheetService(GoogleMonthSheetMixin, GoogleSheetsManager):
    """
    Service for managing RequestCashflowIncomes sheet
    """

    HEADERS = [
        'Номер запроса',
        'Дата запроса',
        'Дата платежа',
        'Дата начисления',
        'Автор запроса',
        'Финансист',
        'Отдел',
        'Платежная система',
        'Статья дохода',
        'Валюта',
        'Сумма',
        'Статус обработки',
        'Cумма USD',
        'Описание',
    ]
    SHEET_ID = settings.REQUEST_CASHFLOW_INCOME_GOOGLE_SHEET_ID
    CONFIG_FILE_PATH = "var/google_api_config.json"
    ID_COLUMN = 1

    def __init__(self):
        if not self.SHEET_ID:
            raise ValueError("SHEET_ID is not set")

        super().__init__(self.CONFIG_FILE_PATH, self.SHEET_ID)

    @staticmethod
    def generate_row_data(request_cashflow_income: RequestCashflowIncome) -> list:
        result = []

        for attr in [
            'id',
            'date',
            'payment_date',
            'enrollment_date',
            'created_by',
            'financier',
            'department',
            'payment_system',
            'cashflow_income_item',
            'currency',
            'amount',
            'status',
            'amount_usd',
            'payment_description',
        ]:
            value = getattr(request_cashflow_income, attr, None)

            if isinstance(value, datetime):
                value = value.strftime('%d.%m.%Y')
            elif isinstance(value, Decimal):
                value = str(value).replace('.', ',')

            result.append(str(value) if value else '')

        return result

    def add_row(self, request_cashflow_income: RequestCashflowIncome) -> None:
        try:
            sheet = self.get_month_sheet(request_cashflow_income.enrollment_date)
            row_data = self.generate_row_data(request_cashflow_income)
            sheet.append_row(row_data, value_input_option='USER_ENTERED')
        except gspread.exceptions.APIError as error:
            raise GoogleSheetAPIError(error)

    def _delete_row(
        self, request_cashflow_income: RequestCashflowIncome, old_enrollment_date: date
    ) -> None:
        try:
            sheet = self.get_month_sheet(old_enrollment_date)
            cell = sheet.find(str(request_cashflow_income.id), in_column=self.ID_COLUMN)

            if not cell:
                raise GoogleSheetCellNotFound()

            row_number = cell.row
            sheet.delete_rows(row_number)
        except gspread.exceptions.APIError as error:
            raise GoogleSheetAPIError(error)

    def update_row(
        self,
        request_cashflow_income: RequestCashflowIncome,
        old_enrollment_date: date = None,
        create_if_not_exists: bool = False,
    ) -> None:
        try:
            if old_enrollment_date:
                try:
                    self._delete_row(request_cashflow_income, old_enrollment_date)
                except GoogleSheetCellNotFound:
                    pass

                return self.add_row(request_cashflow_income)

            sheet = self.get_month_sheet(request_cashflow_income.enrollment_date)
            cell = sheet.find(str(request_cashflow_income.id), in_column=self.ID_COLUMN)

            if not cell:
                if create_if_not_exists:
                    return self.add_row(request_cashflow_income)

                raise GoogleSheetCellNotFound()

            row_number = cell.row
            row_data = self.generate_row_data(request_cashflow_income)
            row_range = f"A{row_number}"
            sheet.update([row_data], row_range, value_input_option='USER_ENTERED')
        except gspread.exceptions.APIError as error:
            raise GoogleSheetAPIError(error)


class TotalSpendCopyTableSnapshotService:
    URL = 'https://ofcrm.biz/api/v1/finance/total-spend/copy-table/'
    HEADERS = {
        'X-API-KEY': settings.TOTAL_SPEND_COPY_TABLE_SNAPSHOT_API_KEY
    }

    @classmethod
    def _get_request_total_spend_copy_table(
        cls,
        client:  httpx.Client,
        start_date: str,
        end_date: str,
        specific_filter: str | None = None,
        promo_id: str | None = None
    ) -> httpx.Response:
        params = {
            'date_after': start_date,
            'date_before': end_date,
        }

        if specific_filter:
            params[specific_filter] = True

        if promo_id:
            params['promo_id'] = promo_id

        response = client.get(
            cls.URL,
            params=params,
            headers=cls.HEADERS
        )

        return response

    @staticmethod
    def _create_total_spend_copy_table_snapshot(
        current_date: date,
        date_filter: str,
        promo_filter: str,
        start_date: str,
        end_date: str,
        string: str
    ) -> CopyTableSnapshot | None:
        try:
            obj = CopyTableSnapshot.objects.create(
                created_at=current_date,
                date_filter=date_filter,
                promo_filter=promo_filter,
                start_date=start_date,
                end_date=end_date,
                string=string
            )

            logger.info(f'CopyTableSnapshot created for {current_date} {promo_filter}')

            return obj
        except Exception as e:
            logger.error(f'Error creating CopyTableSnapshot for {current_date} {promo_filter}: {e}')

    @classmethod
    def create_single_total_spend_copy_table_snapshot(
        cls,
        current_date: date,
        client: httpx.Client,
        start_date: str,
        end_date: str,
        date_filter: CopyTableSnapshot.DateFilterChoices,
        promo_filter: CopyTableSnapshot.PromoFilterChoices,
        specific_filter: str = None,
        promo_id: str = None,
    ):
        response = cls._get_request_total_spend_copy_table(
            client=client,
            start_date=start_date,
            end_date=end_date,
            specific_filter=specific_filter,
            promo_id=promo_id
        )

        if response.status_code == 200:
            cls._create_total_spend_copy_table_snapshot(
                current_date=current_date,
                date_filter=date_filter,
                promo_filter=promo_filter,
                start_date=start_date,
                end_date=end_date,
                string=response.text
            )

    @classmethod
    def create_total_spend_copy_table_snapshots(
        cls,
        current_date: date
    ) -> None:
        """
        Create CopyTableSnapshot for Total Spend
        """
        start_date = '2024-01-01'
        end_date = f'{current_date.year}-12-31'

        friends_promo_id = '3806372a-cfb2-4db0-b6cf-c5efb0a1c018'
        gg_promo_id = 'e07de862-b2ad-434e-8919-eb6f0f299591'

        with httpx.Client() as client:
            # date + all promos
            cls.create_single_total_spend_copy_table_snapshot(
                current_date=current_date,
                client=client,
                start_date=start_date,
                end_date=end_date,
                date_filter=CopyTableSnapshot.DateFilterChoices.DATE,
                promo_filter=CopyTableSnapshot.PromoFilterChoices.ALL_PROMOS,
            )

            # date + friends
            cls.create_single_total_spend_copy_table_snapshot(
                current_date=current_date,
                client=client,
                start_date=start_date,
                end_date=end_date,
                date_filter=CopyTableSnapshot.DateFilterChoices.DATE,
                promo_filter=CopyTableSnapshot.PromoFilterChoices.FRIENDS_FOR_MODEL,
                promo_id=friends_promo_id
            )

            # date + gg
            cls.create_single_total_spend_copy_table_snapshot(
                current_date=current_date,
                client=client,
                start_date=start_date,
                end_date=end_date,
                date_filter=CopyTableSnapshot.DateFilterChoices.DATE,
                promo_filter=CopyTableSnapshot.PromoFilterChoices.GG,
                promo_id=gg_promo_id
            )

            # buy_date + all
            cls.create_single_total_spend_copy_table_snapshot(
                current_date=current_date,
                client=client,
                start_date=start_date,
                end_date=end_date,
                date_filter=CopyTableSnapshot.DateFilterChoices.BUY_DATE,
                promo_filter=CopyTableSnapshot.PromoFilterChoices.ALL_PROMOS,
                specific_filter='by_buy_date'
            )

            # buy_date + friends
            cls.create_single_total_spend_copy_table_snapshot(
                current_date=current_date,
                client=client,
                start_date=start_date,
                end_date=end_date,
                date_filter=CopyTableSnapshot.DateFilterChoices.BUY_DATE,
                promo_filter=CopyTableSnapshot.PromoFilterChoices.FRIENDS_FOR_MODEL,
                specific_filter='by_buy_date',
                promo_id=friends_promo_id
            )

            # buy_date + gg
            cls.create_single_total_spend_copy_table_snapshot(
                current_date=current_date,
                client=client,
                start_date=start_date,
                end_date=end_date,
                date_filter=CopyTableSnapshot.DateFilterChoices.BUY_DATE,
                promo_filter=CopyTableSnapshot.PromoFilterChoices.GG,
                specific_filter='by_buy_date',
                promo_id=gg_promo_id
            )

            # phantom_cost + all
            cls.create_single_total_spend_copy_table_snapshot(
                current_date=current_date,
                client=client,
                start_date=start_date,
                end_date=end_date,
                date_filter=CopyTableSnapshot.DateFilterChoices.DATE_PHANTOM_COST,
                promo_filter=CopyTableSnapshot.PromoFilterChoices.ALL_PROMOS,
                specific_filter='by_date_phantom_cost'
            )

            # phantom_cost + friends
            cls.create_single_total_spend_copy_table_snapshot(
                current_date=current_date,
                client=client,
                start_date=start_date,
                end_date=end_date,
                date_filter=CopyTableSnapshot.DateFilterChoices.DATE_PHANTOM_COST,
                promo_filter=CopyTableSnapshot.PromoFilterChoices.FRIENDS_FOR_MODEL,
                specific_filter='by_date_phantom_cost',
                promo_id=friends_promo_id
            )

            # phantom_cost + gg
            cls.create_single_total_spend_copy_table_snapshot(
                current_date=current_date,
                client=client,
                start_date=start_date,
                end_date=end_date,
                date_filter=CopyTableSnapshot.DateFilterChoices.DATE_PHANTOM_COST,
                promo_filter=CopyTableSnapshot.PromoFilterChoices.GG,
                specific_filter='by_date_phantom_cost',
                promo_id=gg_promo_id
            )


class ModelDonorDebtHistoryService:
    """
    Service for managing ModelDonorDebtHistory
    """

    def write_model_donor_debt_history_by_date(self, debts_date: date | None = None) -> list[ModelDonorDebtHistory] | None:
        """
        Write model donor debt history by date

        Args:
            debts_date (date): date for which to calculate model donor debt

        Returns:
            list[ModelDonorDebtHistory] | None: ModelDonorDebtHistory instances or None if already exists
        """
        debts_date = debts_date or timezone.now().date()

        current_month_date = debts_date.replace(day=1)

        queryset = Ads.objects.filter(
                status__name__in=AdsService.DEBT_QUERYSET_FILTER_STATUSES,
                donor__isnull=False,
                fans_delta__isnull=False,
                fans_delta__gt=0,
                cost_result__isnull=False,
                date__lte=debts_date,
                department=Ads.DepartmentChoices.MARKETING,
            ).select_related('only_fans_model').order_by('date')

        if not queryset:
            return None

        debts = defaultdict(dict)

        for ads in queryset:
            if (
                not getattr(ads, 'fans_count', None)
                or not getattr(ads, 'cost_result', None)
                or not getattr(ads, 'fans_delta', None)
            ):
                continue

            ads_date = ads.date.replace(day=1)

            if ads_date > current_month_date:
                continue

            donor_id = ads.donor_id
            model_id = ads.only_fans_model_id

            debts[model_id].setdefault(ads_date, {})
            debts[model_id][ads_date].setdefault(donor_id, {'debt_amount': 0, 'fans_debt_amount': 0})
            debts[model_id][ads_date][donor_id]['debt_amount'] += ads.fans_delta * ads.cost_result / ads.fans_count
            debts[model_id][ads_date][donor_id]['fans_debt_amount'] += ads.fans_delta

        model_donor_debt_histories = []
        for model_id, model_debts in debts.items():
            with transaction.atomic():
                try:
                    model_donor_debt_history = ModelDonorDebtHistory.objects.create(
                        date=debts_date,
                        only_fans_model_id=model_id,
                    )
                    model_donor_debt_histories.append(model_donor_debt_history)
                except IntegrityError:
                    logger.info(f'ModelDonorDebtHistory for {current_month_date} {model_id} already exists')
                    continue

                donor_debt_details = []
                for month_date, month_debts in model_debts.items():
                    for donor_id, donor_debt in month_debts.items():
                        donor_debt_details.append(
                            ModelDonorDebtHistoryDetail(
                                month_date=month_date,
                                model_donor_debt_history=model_donor_debt_history,
                                donor_id=donor_id,
                                debt_amount=donor_debt['debt_amount'],
                                fans_debt_amount=donor_debt['fans_debt_amount'],
                            )
                        )

                ModelDonorDebtHistoryDetail.objects.bulk_create(
                    donor_debt_details, batch_size=500, ignore_conflicts=True
                )

        return model_donor_debt_histories
