from drf_spectacular.utils import OpenApiExample, extend_schema_serializer
from rest_framework import serializers
from rest_framework.exceptions import ValidationError
from rest_framework.relations import SlugR<PERSON>ted<PERSON>ield
from rest_framework.request import Request
from rest_framework.validators import UniqueTogetherValidator

from accounts.serializers import PureUserSerializer
from ads.models import Ads
from base.serializiers import CreatableSlugRelatedField, CreatableSlugRelatedFieldIgnoreExtraSpaces
from base.tools import get_user_from_context
from finance.models import (
    Budget,
    BudgetPeriod,
    CashflowExpenseItem,
    CashflowExpenseSubItem,
    CashflowIncomeItem,
    PaymentCryptoNetwork,
    PaymentCurrency,
    PaymentDepartment,
    PaymentExpenseItem,
    PaymentSystem,
    PaymentTool,
    PaymentTransaction,
    PayoutAddress,
    PayoutReview,
    RequestCashflow,
    RequestCashflowIncome,
    RequestPayment,
    ReviewResult,
    TotalDebtHistory,
)
from finance.services import (
    BudgetPeriodService,
    BudgetService,
    PayoutReviewService,
    RequestCashflowIncomeService,
    RequestCashflowService,
    RequestPaymentService,
)
from only_fans_models.serializers import OnlyFansModelsSerializer


class NestedPromoSerializer(serializers.ModelSerializer):
    """
    Nested ads summary serializer
    """

    nickname = serializers.CharField(source='only_fans_model.nickname')
    order = serializers.IntegerField(
        source='only_fans_model.index_number', allow_null=True
    )
    cost = serializers.DecimalField(
        source='cost_result', max_digits=10, decimal_places=2
    )

    class Meta:
        model = Ads
        fields = ('nickname', 'order', 'cost')


class TotalSpendSerializer(serializers.Serializer):
    """
    Total spend serializer
    """

    date = serializers.DateField()
    promos = NestedPromoSerializer(many=True)


class CopyDataSerializer(serializers.Serializer):
    """
    Total spend copy table serializer
    """

    copy_data = serializers.CharField(max_length=100000)


class PointSerializer(serializers.Serializer):
    """
    Serializer for a Point object, used to convert Point objects to JSON representation and vice versa.
    """

    interval = serializers.IntegerField()
    cost_result = serializers.DecimalField(max_digits=10, decimal_places=2)


class PurchaseSerializer(serializers.Serializer):
    """
    A serializer class for converting the FutureTotalPurchase model instances into JSON format.
    """

    points = PointSerializer(many=True)
    yesterday_differ = serializers.ChoiceField(
        choices=['positive', 'negative', 'equal']
    )


@extend_schema_serializer(
    examples=[
        OpenApiExample(
            'Example response',
            value={
                'month': '01.2023',
                'amount': 100,
            },
            response_only=True,
        )
    ]
)
class MonthAmountSerializer(serializers.Serializer):
    """
    DebtListSerializer
    """

    month = serializers.DateField(format='%m.%Y')
    amount = serializers.IntegerField()


class TodayReportSerializer(serializers.Serializer):
    """
    TodayReportSerializer
    """

    model = serializers.CharField(max_length=255)
    future_purchase = serializers.IntegerField()
    currency = serializers.DecimalField(max_digits=10, decimal_places=2)
    debt_fans = serializers.IntegerField()
    debt = serializers.IntegerField()
    total = serializers.IntegerField()


class NameValueSerializer(serializers.Serializer):
    """
    NameValueSerializer
    """

    name = serializers.CharField(max_length=255)
    value = serializers.IntegerField()


class NestedCategoriesSummarySerializer(serializers.Serializer):
    """
    NestedFuturePurchaseByDonorMarketerSerializer
    """

    by_donor = NameValueSerializer(many=True)
    by_marketer = NameValueSerializer(many=True)


class NestedCategoriesSummaryCopySerializer(serializers.Serializer):
    """
    NestedCategoriesSummaryCopySerializer
    """

    by_donor = serializers.CharField(max_length=100000)
    by_marketer = serializers.CharField(max_length=100000)


class CategoriesSummarySerializer(serializers.Serializer):
    """
    FuturePurchaseByDonorMarketerSerializer
    """

    future_purchases = NestedCategoriesSummarySerializer()
    debts = NestedCategoriesSummarySerializer()
    total = NestedCategoriesSummarySerializer()


class CategoriesSummaryCopySerializer(serializers.Serializer):
    """
    FuturePurchaseByDonorMarketerSerializer
    """

    future_purchases = NestedCategoriesSummaryCopySerializer()
    debts = NestedCategoriesSummaryCopySerializer()
    total = NestedCategoriesSummaryCopySerializer()


class NestedDebtsSummarySerializer(serializers.Serializer):
    """
    NestedDebtsSummarySerializer
    """

    model = serializers.CharField(max_length=255)
    index = serializers.IntegerField()
    value = serializers.DecimalField(max_digits=10, decimal_places=2)


class DebtsSummarySerializer(serializers.Serializer):
    """
    DebtsSummarySerializer
    """

    month = serializers.DateField(format='%m.%Y')
    debts = NestedDebtsSummarySerializer(many=True)


class DebtsListSerializer(serializers.Serializer):
    """
    DebtsListSerializer
    """

    debts = MonthAmountSerializer(many=True)
    total = serializers.IntegerField()


class FuturePurchaseListSerializer(serializers.Serializer):
    """
    FuturePurchaseListSerializer
    """

    purchases = MonthAmountSerializer(many=True)
    total = serializers.IntegerField()


class ForecastSerializer(TodayReportSerializer):
    """
    ForecastSerializer
    """

    fans_per_day = serializers.DecimalField(
        max_digits=10, decimal_places=2, coerce_to_string=False
    )
    forecast_of_days = serializers.IntegerField()
    fans_forecast_of_months = MonthAmountSerializer(many=True)
    count_active_promo = serializers.IntegerField()
    completion = serializers.IntegerField()
    currency = serializers.DecimalField(max_digits=10, decimal_places=0)
    debt_no_last_month = serializers.IntegerField()


class NestedForecastTableViewSerializer(NestedDebtsSummarySerializer):
    """
    NestedForecastTableViewSerializer
    """

    pass


class ForecastTableViewSerializer(serializers.Serializer):
    """
    ForecastTableViewSerializer
    """

    month = serializers.DateField(format='%m.%Y')
    forecast = NestedForecastTableViewSerializer(many=True)


# ------------------------------------------REQUEST PAYMENT----------------------------------------------------
class PaymentExpenseItemSerializer(serializers.ModelSerializer):
    """
    PaymentExpenseItem serializer
    """

    class Meta:
        model = PaymentExpenseItem
        fields = ('id', 'name', 'reviewer_tg_id')


class PaymentCurrencySerializer(serializers.ModelSerializer):
    """
    PaymentCurrency serializer
    """

    class Meta:
        model = PaymentCurrency
        fields = ('id', 'name')


class PaymentSystemSerializer(serializers.ModelSerializer):
    """
    PaymentSystem serializer
    """

    class Meta:
        model = PaymentSystem
        fields = ('id', 'name')


class PaymentToolSerializer(serializers.ModelSerializer):
    """
    PaymentTool serializer
    """

    class Meta:
        model = PaymentTool
        fields = ('id', 'name')


class PaymentCryptoNetworkSerializer(serializers.ModelSerializer):
    """
    PaymentCryptoNetwork serializer
    """

    class Meta:
        model = PaymentCryptoNetwork
        fields = ('id', 'name')


class RequestPaymentCreateSerializer(serializers.ModelSerializer):
    """
    Create RequestPayment serializer
    """
    payment_expense_item = SlugRelatedField(
        slug_field='name', queryset=PaymentExpenseItem.objects.all()
    )
    payment_currency = SlugRelatedField(
        slug_field='name', queryset=PaymentCurrency.objects.all()
    )
    payment_crypto_network = SlugRelatedField(
        slug_field='name',
        queryset=PaymentCryptoNetwork.objects.all(),
        allow_empty=True,
        allow_null=True,
        required=False
    )

    class Meta:
        model = RequestPayment
        fields = (
            'id',
            'created_by',
            'created_by_tg_id',
            'created_by_tg_username',
            'head',
            'department',
            'head_review_result',
            'head_review_date',
            'payment_system',
            'payment_expense_item',
            'payment_description',
            'payment_comment',
            'model_nickname',
            'payment_currency',
            'payment_amount',
            'payment_details',
            'payment_crypto_network',
            'jira_link',
        )

    def create(self, validated_data):
        return RequestPaymentService().create(
            validated_data
        )


class PaymentTransactionSerializer(serializers.ModelSerializer):
    """
    PaymentTransaction serializer
    """

    payment_tool = CreatableSlugRelatedField(
        slug_field='name',
        queryset=PaymentTool.objects.all(),
    )
    amount = serializers.DecimalField(
        max_digits=10, decimal_places=2, coerce_to_string=False
    )

    class Meta:
        model = PaymentTransaction
        fields = ('id', 'payment_tool', 'amount')


class RequestPaymentListSerializer(serializers.ModelSerializer):
    """
    List RequestPayment serializer
    """

    payment_expense_item = CreatableSlugRelatedField(
        allow_null=True,
        slug_field='name',
        queryset=PaymentExpenseItem.objects.all(),
    )
    payment_currency = CreatableSlugRelatedField(
        allow_null=True,
        slug_field='name',
        queryset=PaymentCurrency.objects.all(),
    )
    payment_crypto_network = CreatableSlugRelatedField(
        slug_field='name',
        queryset=PaymentCryptoNetwork.objects.all(),
        allow_null=True,
        required=False
    )
    payment_transactions = PaymentTransactionSerializer(many=True)
    payment_amount = serializers.DecimalField(
        max_digits=10, decimal_places=2, coerce_to_string=False
    )
    finance_reviewer = PureUserSerializer()

    class Meta:
        model = RequestPayment
        fields = (
            'id',
            'google_result',
            'head_review_date',
            'head_review_result',
            'date',
            'created_by',
            'created_by_tg_id',
            'head',
            'department',
            'model_nickname',
            'payment_comment',
            'payment_description',
            'payment_currency',
            'payment_amount',
            'payment_expense_item',
            'payment_details',
            'financier',
            'finance_review_result',
            'finance_review_date',
            'payment_transactions',
            'payment_system',
            'finance_reviewer',
            'payment_crypto_network',
            'created_by_tg_link',
            'jira_link',
        )
        read_only_fields = (
            'date',
            'created_by',
            'created_by_tg_id',
            'head',
            'head_review_date',
            'financier',
            'finance_review_date',
            'finance_reviewer',
        )

    def validate_finance_review_result(self, value):
        current_finance_review_result = (
            self.instance.finance_review_result if self.instance else None
        )

        if current_finance_review_result != ReviewResult.REVIEW:
            if value and value not in [
                ReviewResult.ACCEPT,
                ReviewResult.REJECT,
            ]:
                raise ValidationError(
                    {
                        'finance_review_result': f'Value could be only {ReviewResult.ACCEPT.value} '
                        f'or {ReviewResult.REJECT.value} '
                        f'if current finance_review_result is not {ReviewResult.REVIEW.value}'
                    }
                )
        return value

    def validate(self, attrs):
        if self.instance:
            if payment_transactions := attrs.get('payment_transactions'):
                amount = attrs.get('payment_amount') or self.instance.payment_amount
                payment_transactions_amount = sum(
                    payment_transaction['amount']
                    for payment_transaction in payment_transactions
                )

                if payment_transactions_amount > amount:
                    raise ValidationError(
                        {
                            'payment_transactions': 'Transactions amount cannot be more than the payment amount'
                        }
                    )

        return attrs

    def update(self, instance, validated_data):
        user = get_user_from_context(self.context)

        instance = RequestPaymentService().update(
            instance=instance,
            validated_data=validated_data,
            user=user,
        )

        return instance


class RequestPaymentCreatorListSerializer(serializers.ListSerializer):
    """
    RequestPaymentCreatorListSerializer
    """

    child = serializers.CharField(max_length=255)


class PaymentDepartmentListSerializer(serializers.ListSerializer):
    """
    RequestPaymentDepartmentListSerializer
    """

    child = serializers.CharField(max_length=255)


# ---------------------------------------------------PayoutReview-----------------------------------------------
class PayoutAddressSerializer(serializers.ModelSerializer):
    """
    PayoutAddress serializer
    """
    class Meta:
        model = PayoutAddress
        fields = ('id', 'address_name')


class PayoutReviewListSerializer(serializers.ModelSerializer):
    """
    PayoutReview serializer
    """
    only_fans_model = OnlyFansModelsSerializer()
    payout_address = PayoutAddressSerializer()
    amount = serializers.DecimalField(
        max_digits=10, decimal_places=2, coerce_to_string=False
    )
    finance_reviewer = PureUserSerializer()

    class Meta:
        model = PayoutReview
        fields = (
            'id',
            'payout_id',
            'payout_creation_date',
            'amount',
            'only_fans_model',
            'finance_review_date',
            'finance_review_result',
            'financier',
            'payout_address',
            'finance_reviewer'
        )

        read_only_fields = (
            'payout_id',
            'payout_creation_date',
            'amount',
            'only_fans_model',
            'finance_review_date',
            'finance_review_result',
            'financier',
            'payout_address',
        )


class PayoutReviewCustomCreateSerializer(serializers.ModelSerializer):
    """
    PayoutReview create serializer for custom instances
    """
    payout_address = CreatableSlugRelatedFieldIgnoreExtraSpaces(
        slug_field='address_name', queryset=PayoutAddress.objects.all()
    )

    class Meta:
        model = PayoutReview
        fields = (
            'payout_creation_date',
            'amount',
            'only_fans_model',
            'payout_address',
        )

    def create(self, validated_data):
        request: Request = self.context.get('request')
        return PayoutReviewService().create(
            validated_data=validated_data,
            user=request.user,
        )


class PayoutReviewCustomUpdateSerializer(serializers.ModelSerializer):
    payout_address = CreatableSlugRelatedFieldIgnoreExtraSpaces(
        slug_field='address_name', queryset=PayoutAddress.objects.all()
    )

    class Meta:
        model = PayoutReview
        fields = (
            'payout_creation_date',
            'amount',
            'only_fans_model',
            'payout_address',
            'finance_review_result'
        )

    def update(self, instance, validated_data):
        request: Request = self.context.get('request')
        instance = PayoutReviewService().update(
            instance=instance,
            validated_data=validated_data,
            user=request.user,
        )

        return instance


class PayoutReviewAutogeneratedUpdateSerializer(serializers.ModelSerializer):
    """
    PayoutReview update serializer for autogenerated instances
    """
    payout_address = CreatableSlugRelatedFieldIgnoreExtraSpaces(
        slug_field='address_name', queryset=PayoutAddress.objects.all()
    )

    class Meta:
        model = PayoutReview
        fields = (
            'payout_address',
            'finance_review_result'
        )

    def update(self, instance, validated_data):
        request: Request = self.context.get('request')
        instance = PayoutReviewService().update(
            instance=instance,
            validated_data=validated_data,
            user=request.user,
        )

        return instance


class PayoutReviewMassProvideSerializer(serializers.Serializer):
    payout_reviews = serializers.PrimaryKeyRelatedField(many=True, queryset=PayoutReview.objects.all())
    payout_address = CreatableSlugRelatedFieldIgnoreExtraSpaces(
        slug_field='address_name', queryset=PayoutAddress.objects.all()
    )

    class Meta:
        fields = (
            'ids',
            'payout_address',
            'finance_review_result'
        )


# ----------------------------------Debt History---------------------------------
class TotalDebtHistorySerializer(serializers.ModelSerializer):
    """
    TotalDebtHistory serializer
    """

    class Meta:
        model = TotalDebtHistory
        fields = ('id', 'date', 'debt_amount')


class DateTrackingDTOSerializer(serializers.Serializer):
    month_date = serializers.DateField()
    month_debt_amount = serializers.IntegerField()
    delta = serializers.IntegerField()


class DebtTrackingDTOSerializer(serializers.Serializer):
    number = serializers.IntegerField()
    parse_date = serializers.DateField()
    total_debt = serializers.IntegerField()
    total_delta = serializers.IntegerField()
    month_data = DateTrackingDTOSerializer(many=True, source='month_data_dtos', allow_null=True)


class DateIncomeFansTrackingDTOSerializer(serializers.Serializer):
    month_date = serializers.DateField()
    month_income_amount = serializers.IntegerField()
    delta = serializers.IntegerField()


class IncomeFansTrackingDTOSerializer(serializers.Serializer):
    number = serializers.IntegerField()
    parse_date = serializers.DateField()
    total_income_amount = serializers.IntegerField()
    total_delta = serializers.IntegerField()
    month_data = DateIncomeFansTrackingDTOSerializer(many=True, source='month_data_dtos', allow_null=True)


# -----------------------------------------------Cashflow---------------------------------
class CashflowExpenseItemSerializer(serializers.ModelSerializer):
    """
    CashflowExpenseItem serializer
    """
    class Meta:
        model = CashflowExpenseItem
        fields = ('id', 'name')


class CashflowExpenseSubItemSerializer(serializers.ModelSerializer):
    """
    CashflowExpenseSubItem serializer
    """
    class Meta:
        model = CashflowExpenseSubItem
        fields = ('id', 'name')


class CashflowDepartmentSerializer(serializers.ModelSerializer):
    """
    CashflowDepartment serializer
    """
    class Meta:
        model = PaymentDepartment
        fields = ('id', 'name')


class CashflowCreateSerializer(serializers.ModelSerializer):
    """
    Cashflow create serializer
    """
    cashflow_expense_item = SlugRelatedField(
        slug_field='name', queryset=CashflowExpenseItem.objects.all()
    )
    cashflow_expense_sub_item = SlugRelatedField(
        slug_field='name',
        queryset=CashflowExpenseSubItem.objects.all(),
        required=False,
        allow_null=True,
        allow_empty=True
    )
    currency = SlugRelatedField(
        slug_field='name', queryset=PaymentCurrency.objects.all()
    )
    department = SlugRelatedField(
        slug_field='name', queryset=PaymentDepartment.objects.all()
    )
    payment_system = SlugRelatedField(
        slug_field='name', queryset=PaymentSystem.objects.all()
    )
    created_by = serializers.CharField(
        max_length=255,
        required=False,
        allow_null=True,
        allow_blank=True
    )

    class Meta:
        model = RequestCashflow
        fields = (
            'id',
            'date',
            'payment_date',
            'enrollment_date',
            'cashflow_expense_item',
            'cashflow_expense_sub_item',
            'currency',
            'amount',
            'department',
            'model_nickname',
            'created_by',
            'payment_description',
            'payment_details',
            'payment_system',
            'financier'
        )

    def create(self, validated_data):
        user = get_user_from_context(context=self.context)

        if not validated_data.get('created_by'):
            validated_data['created_by'] = user.full_name

        if not validated_data.get('financier'):
            validated_data['financier'] = validated_data['created_by']

        return RequestCashflowService().create(
            **validated_data,
        )


class CashflowUpdateSerializer(serializers.ModelSerializer):
    """
    Cashflow update serializer
    """
    cashflow_expense_item = SlugRelatedField(
        slug_field='name', queryset=CashflowExpenseItem.objects.all()
    )
    cashflow_expense_sub_item = SlugRelatedField(
        slug_field='name',
        queryset=CashflowExpenseSubItem.objects.all(),
        required=False,
        allow_null=True,
        allow_empty=True
    )
    currency = SlugRelatedField(
        slug_field='name', queryset=PaymentCurrency.objects.all()
    )
    department = SlugRelatedField(
        slug_field='name', queryset=PaymentDepartment.objects.all()
    )
    payment_system = SlugRelatedField(
        slug_field='name', queryset=PaymentSystem.objects.all()
    )

    class Meta:
        model = RequestCashflow
        fields = (
            'date',
            'payment_date',
            'enrollment_date',
            'cashflow_expense_item',
            'cashflow_expense_sub_item',
            'currency',
            'amount',
            'department',
            'model_nickname',
            'payment_description',
            'payment_details',
            'payment_system',
            'status',
            'created_by',
            'financier'
        )

    def update(self, instance, validated_data):
        return RequestCashflowService().update(
            instance=instance,
            **validated_data,
        )


class CashflowListSerializer(serializers.ModelSerializer):
    """
    Cashflow list serializer
    """
    cashflow_expense_item = SlugRelatedField(slug_field='name', read_only=True)
    cashflow_expense_sub_item = SlugRelatedField(slug_field='name', allow_null=True, read_only=True)
    currency = SlugRelatedField(slug_field='name', read_only=True)
    department = SlugRelatedField(slug_field='name', read_only=True)
    payment_system = SlugRelatedField(slug_field='name', read_only=True)

    class Meta:
        model = RequestCashflow
        fields = (
            'id',
            'date',
            'payment_date',
            'enrollment_date',
            'cashflow_expense_item',
            'cashflow_expense_sub_item',
            'currency',
            'amount',
            'department',
            'model_nickname',
            'created_by',
            'payment_description',
            'payment_details',
            'payment_system',
            'amount_usd',
            'status',
            'updated_at',
            'financier'
        )
        read_only_fields = fields


class RequestCashflowCreatorListSerializer(serializers.ListSerializer):
    """
    RequestCashflowCreatorListSerializer
    """

    child = serializers.CharField(max_length=255)


class CashflowIncomeItemSerializer(serializers.ModelSerializer):
    """
    CashflowIncomeItem serializer
    """
    class Meta:
        model = CashflowIncomeItem
        fields = ('id', 'name')


class CashflowIncomeCreateSerializer(serializers.ModelSerializer):
    """
    Cashflow Income create serializer
    """
    cashflow_income_item = SlugRelatedField(
        slug_field='name', queryset=CashflowIncomeItem.objects.all()
    )
    currency = SlugRelatedField(
        slug_field='name', queryset=PaymentCurrency.objects.all()
    )
    department = SlugRelatedField(
        slug_field='name', queryset=PaymentDepartment.objects.all()
    )
    payment_system = SlugRelatedField(
        slug_field='name', queryset=PaymentSystem.objects.all()
    )
    created_by = serializers.CharField(
        max_length=255,
        required=False,
        allow_null=True,
        allow_blank=True
    )

    class Meta:
        model = RequestCashflowIncome
        fields = (
            'id',
            'date',
            'payment_date',
            'enrollment_date',
            'cashflow_income_item',
            'currency',
            'amount',
            'department',
            'created_by',
            'payment_description',
            'income_details',
            'payment_system',
            'financier'
        )

    def create(self, validated_data):
        user = get_user_from_context(context=self.context)

        if not validated_data.get('created_by'):
            validated_data['created_by'] = user.full_name

        if not validated_data.get('financier'):
            validated_data['financier'] = validated_data['created_by']

        return RequestCashflowIncomeService().create(
            **validated_data,
        )


class CashflowIncomeUpdateSerializer(serializers.ModelSerializer):
    """
    Cashflow Income update serializer
    """
    cashflow_income_item = SlugRelatedField(
        slug_field='name', queryset=CashflowIncomeItem.objects.all()
    )
    currency = SlugRelatedField(
        slug_field='name', queryset=PaymentCurrency.objects.all()
    )
    department = SlugRelatedField(
        slug_field='name', queryset=PaymentDepartment.objects.all()
    )
    payment_system = SlugRelatedField(
        slug_field='name', queryset=PaymentSystem.objects.all()
    )

    class Meta:
        model = RequestCashflowIncome
        fields = (
            'date',
            'payment_date',
            'enrollment_date',
            'cashflow_income_item',
            'currency',
            'amount',
            'department',
            'payment_description',
            'income_details',
            'payment_system',
            'status',
            'created_by',
            'financier'
        )

    def update(self, instance, validated_data):
        return RequestCashflowIncomeService().update(
            instance=instance,
            **validated_data,
        )


class CashflowIncomeListSerializer(serializers.ModelSerializer):
    """
    Cashflow Income list serializer
    """
    cashflow_income_item = SlugRelatedField(slug_field='name', read_only=True)
    currency = SlugRelatedField(slug_field='name', read_only=True)
    department = SlugRelatedField(slug_field='name', read_only=True)
    payment_system = SlugRelatedField(slug_field='name', read_only=True)

    class Meta:
        model = RequestCashflowIncome
        fields = (
            'id',
            'date',
            'payment_date',
            'enrollment_date',
            'cashflow_income_item',
            'currency',
            'amount',
            'department',
            'created_by',
            'payment_description',
            'income_details',
            'payment_system',
            'amount_usd',
            'status',
            'updated_at',
            'financier'
        )
        read_only_fields = fields


# ----------------------------------------------Budget--------------------------------------------------------
class BudgetSerializer(serializers.ModelSerializer):
    """
        Serializer for creating Budget instances.
    """
    class Meta:
        model = Budget
        fields = (
            'id',
            'expected_plan',
            'expected_wage_fund',
            'department',
        )

    def validate(self, attrs):
        expected_plan = attrs.get('expected_plan', 0)
        expected_wage_fund = attrs.get('expected_wage_fund', 0)

        if expected_plan < expected_wage_fund:
            raise serializers.ValidationError(
                {
                    'expected_plan': 'Expected plan should be more than expected wage fund',
                    'expected_wage_fund': 'Expected plan should be more than expected wage fund'
                }
            )

        return attrs


class BudgetPeriodCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating BudgetPeriod instances.
    """
    budgets = BudgetSerializer(many=True,  required=False)

    class Meta:
        model = BudgetPeriod
        fields = ('id', 'start_date', 'end_date', 'budgets', 'name')
        validators = [
            UniqueTogetherValidator(
                queryset=BudgetPeriod.objects.all(),
                fields=['start_date', 'end_date']
            )
        ]

    def validate(self, attrs):
        start_date = attrs.get('start_date')
        end_date = attrs.get('end_date')

        if start_date and end_date and start_date > end_date:
            raise serializers.ValidationError('Start date must be less than end date')

        return attrs

    def create(self, validated_data):
        return BudgetPeriodService().create(**validated_data)


class BudgetPeriodUpdateSerializer(BudgetPeriodCreateSerializer):
    """
    Serializer for  updating BudgetPeriod instances.
    """
    def update(self, instance, validated_data):
        return BudgetPeriodService().update(instance=instance, **validated_data)


class BudgetPeriodListSerializer(serializers.ModelSerializer):
    """
    BudgetPeriod List Serializer

    Serializer class for serializing and deserializing BudgetPeriod objects.
    """
    class Meta:
        model = BudgetPeriod
        fields = ('id', 'start_date', 'end_date', 'name')


class BudgetListSerializer(serializers.ModelSerializer):
    """
    Budget Nested Serializer

    Serializer class for serializing and deserializing Budget objects.
    """
    department = CashflowDepartmentSerializer(read_only=True)
    budget_fact = serializers.SerializerMethodField()
    wage_fund_fact = serializers.SerializerMethodField()

    class Meta:
        model = Budget
        fields = (
            'id',
            'expected_plan',
            'expected_wage_fund',
            'department',
            'budget_fact',
            'wage_fund_fact'
        )

    def get_budget_fact(self, obj):
        return getattr(obj, 'budget_fact', 0) or 0

    def get_wage_fund_fact(self, obj):
        return getattr(obj, 'wage_fund_fact', 0) or 0


class BudgetPeriodDetailSerializer(serializers.ModelSerializer):
    """
    BudgetPeriod Detail Serializer

    Serializer class for serializing and deserializing BudgetPeriod objects.
    """
    budgets = BudgetListSerializer(many=True, read_only=True)

    class Meta:
        model = BudgetPeriod
        fields = ('id', 'start_date', 'end_date', 'name', 'budgets')


class BudgetCreateSerializer(BudgetSerializer):
    """
    Budget create serializer
    """
    class Meta:
        model = Budget
        fields = (
            'id',
            'period',
            'expected_plan',
            'expected_wage_fund',
            'department',
        )

    def create(self, validated_data):
        return BudgetService.create(**validated_data)


class BudgetUpdateSerializer(BudgetSerializer):
    """
    Budget update serializer
    """
    class Meta:
        model = Budget
        fields = (
            'id',
            'period',
            'expected_plan',
            'expected_wage_fund',
            'department',
        )

    def update(self, instance, validated_data):
        return BudgetService.update(instance=instance, **validated_data)


class CurrentTimeSerializer(serializers.Serializer):
    """
    Serializer for the current time.
    """
    kyiv = serializers.DateTimeField()
    utc = serializers.DateTimeField()
