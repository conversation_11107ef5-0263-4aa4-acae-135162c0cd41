from django.conf import settings

from base.services import TelegramBotService
from finance.models import RequestPayment, ReviewResult


class TelegramRequestPaymentNotificationService(TelegramBotService):
    """
    Service for sending notifications based on Request Payment objects
    """

    def __init__(self):
        if not settings.TRACKER_BOT_TOKEN:
            raise ValueError('TRACKER_BOT_TOKEN is not set')

        super().__init__(settings.TRACKER_BOT_TOKEN)

    @staticmethod
    def generate_status_message(
            request_payment: RequestPayment,
            finance_review_result: str
    ) -> str:
        message = f'Заявка id: {request_payment.id}\n'

        if finance_review_result == ReviewResult.ACCEPT:
            message += 'Статус: Заявка оплачена'

        elif finance_review_result == ReviewResult.REJECT:
            message += 'Статус: Заявка отклонена'

        return message

    def notify_request_payment_creator(
        self, request_payment: RequestPayment, finance_review_result: str
    ):
        if (
            request_payment.head_review_result == ReviewResult.ACCEPT
            and finance_review_result in [ReviewResult.ACCEPT, ReviewResult.REJECT]
        ):
            message = self.generate_status_message(request_payment, finance_review_result)
            self.send_message(
                chat_id=request_payment.created_by_tg_id,
                text=message,
            )
