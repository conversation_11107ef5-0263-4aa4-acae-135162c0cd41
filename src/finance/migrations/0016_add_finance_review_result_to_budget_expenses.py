# Generated by Django 4.2.2 on 2024-11-20 16:07

from django.db import migrations
from django.db.migrations import RunPython


def add_finance_review_result(apps, schema_editor):
    """
    Create a default payment department if it doesn't exist.
    """
    BudgetExpense = apps.get_model('finance', 'BudgetExpense')

    data_to_update = []
    for budget_expense in BudgetExpense.objects.all():
        if hasattr(budget_expense, 'request_payments'):
            budget_expense.finance_review_result = budget_expense.request_payments.finance_review_result
        elif hasattr(budget_expense, 'request_cashflows'):
            budget_expense.finance_review_result = budget_expense.request_cashflows.status

        data_to_update.append(budget_expense)

    BudgetExpense.objects.bulk_update(data_to_update, ['finance_review_result'], batch_size=100)

class Migration(migrations.Migration):

    dependencies = [
        ("finance", "0015_budgetexpense_finance_review_result_and_more"),
    ]

    operations = [
        RunPython(add_finance_review_result, migrations.RunPython.noop)
    ]
