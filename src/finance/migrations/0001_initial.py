# Generated by Django 4.2.2 on 2024-02-21 15:24

import uuid

import django.db.models.deletion
import django.utils.timezone
import django_cryptography.fields
from django.db import migrations, models

import base.models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="PaymentCurrency",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(help_text="Name", max_length=255)),
                (
                    "is_active",
                    models.BooleanField(default=True, help_text="Activation"),
                ),
            ],
            options={
                "ordering": ["name"],
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.CreateModel(
            name="PaymentExpenseItem",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(help_text="Name", max_length=255)),
                (
                    "is_active",
                    models.BooleanField(default=True, help_text="Activation"),
                ),
            ],
            options={
                "ordering": ["name"],
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.CreateModel(
            name="PaymentSystem",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(help_text="Name", max_length=255)),
                (
                    "is_active",
                    models.BooleanField(default=True, help_text="Activation"),
                ),
            ],
            options={
                "ordering": ["name"],
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.CreateModel(
            name="PaymentTool",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(help_text="Name", max_length=255)),
                (
                    "is_active",
                    models.BooleanField(default=True, help_text="Activation"),
                ),
            ],
            options={
                "ordering": ["name"],
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.CreateModel(
            name="PaymentTransaction",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2, help_text="Amount", max_digits=10
                    ),
                ),
                (
                    "payment_tool",
                    models.ForeignKey(
                        help_text="Payment tool",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="payment_transactions",
                        to="finance.paymenttool",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.CreateModel(
            name="RequestPayment",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "date",
                    models.DateTimeField(auto_now_add=True, help_text="Creating date"),
                ),
                (
                    "created_by",
                    models.CharField(help_text="Created by name", max_length=255),
                ),
                ("created_by_tg_id", models.BigIntegerField()),
                (
                    "head_review_date",
                    models.DateTimeField(
                        blank=True, help_text="Head review date", null=True
                    ),
                ),
                (
                    "head_review_result",
                    models.CharField(
                        blank=True,
                        choices=[("accept", "Accepted"), ("reject", "Rejected")],
                        help_text="Head review result",
                        max_length=255,
                        null=True,
                    ),
                ),
                ("head", models.CharField(help_text="Head name", max_length=255)),
                (
                    "finance_review_date",
                    models.DateTimeField(
                        blank=True, help_text="Finance review date", null=True
                    ),
                ),
                (
                    "finance_review_result",
                    models.CharField(
                        blank=True,
                        choices=[("accept", "Provided"), ("reject", "Canceled")],
                        help_text="Finance review result",
                        max_length=255,
                        null=True,
                    ),
                ),
                (
                    "financier",
                    models.CharField(
                        blank=True,
                        help_text="Financier name",
                        max_length=255,
                        null=True,
                    ),
                ),
                (
                    "department",
                    models.CharField(help_text="Department name", max_length=255),
                ),
                (
                    "payment_system",
                    models.CharField(help_text="Payment system name", max_length=255),
                ),
                (
                    "payment_comment",
                    models.TextField(blank=True, help_text="Comment", null=True),
                ),
                (
                    "model_nickname",
                    models.CharField(
                        blank=True,
                        help_text="Model nickname",
                        max_length=255,
                        null=True,
                    ),
                ),
                ("payment_description", models.TextField(help_text="Description")),
                (
                    "google_result",
                    models.BooleanField(
                        blank=True,
                        help_text="Indicate that row created in google spreadsheets",
                        null=True,
                    ),
                ),
                (
                    "payment_amount",
                    models.DecimalField(
                        decimal_places=2, default=0, help_text="Amount", max_digits=10
                    ),
                ),
                (
                    "payment_details",
                    django_cryptography.fields.encrypt(
                        models.CharField(help_text="Details", max_length=255)
                    ),
                ),
                (
                    "payment_currency",
                    models.ForeignKey(
                        help_text="Currency",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="payment_requests",
                        to="finance.paymentcurrency",
                    ),
                ),
                (
                    "payment_expense_item",
                    models.ForeignKey(
                        help_text="Expense item",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="payment_requests",
                        to="finance.paymentexpenseitem",
                    ),
                ),
                (
                    "payment_tools",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Payment tools",
                        related_name="payment_requests",
                        through="finance.PaymentTransaction",
                        to="finance.paymenttool",
                    ),
                ),
            ],
            options={
                "ordering": ["-date"],
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.AddField(
            model_name="paymenttransaction",
            name="request_payment",
            field=models.ForeignKey(
                help_text="Request payment",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="payment_transactions",
                to="finance.requestpayment",
            ),
        ),
    ]
