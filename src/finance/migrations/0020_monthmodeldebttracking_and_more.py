# Generated by Django 4.2.2 on 2025-02-18 16:36

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("only_fans_models", "0012_subprofile"),
        ("finance", "0019_monthdebttracking_month_fans_debt_amount_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="MonthModelDebtTracking",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "debt_amount",
                    models.IntegerField(default=0, help_text="Debt amount"),
                ),
                (
                    "fans_debt_amount",
                    models.IntegerField(default=0, help_text="Fans debt amount"),
                ),
                (
                    "month_debt_tracking",
                    models.ForeignKey(
                        help_text="Month debt tracking",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="month_model_debt_trackings",
                        to="finance.monthdebttracking",
                    ),
                ),
                (
                    "only_fans_model",
                    models.ForeignKey(
                        help_text="OnlyFans model",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="month_model_debt_trackings",
                        to="only_fans_models.onlyfansmodel",
                    ),
                ),
            ],
            options={
                "ordering": ["only_fans_model"],
            },
        ),
        migrations.AddConstraint(
            model_name="monthmodeldebttracking",
            constraint=models.UniqueConstraint(
                fields=("only_fans_model", "month_debt_tracking"),
                name="unique_month_model_debt_tracking",
            ),
        ),
    ]
