# Generated by Django 4.2.2 on 2024-04-02 15:19

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("finance", "0004_requestpayment_finance_reviewer_and_more"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="paymentcurrency",
            name="name",
            field=models.Char<PERSON>ield(help_text="Name", max_length=255, unique=True),
        ),
        migrations.AlterField(
            model_name="paymentexpenseitem",
            name="name",
            field=models.CharField(help_text="Name", max_length=255, unique=True),
        ),
        migrations.AlterField(
            model_name="paymentsystem",
            name="name",
            field=models.Char<PERSON>ield(help_text="Name", max_length=255, unique=True),
        ),
        migrations.AlterField(
            model_name="paymenttool",
            name="name",
            field=models.Char<PERSON>ield(help_text="Name", max_length=255, unique=True),
        ),
    ]
