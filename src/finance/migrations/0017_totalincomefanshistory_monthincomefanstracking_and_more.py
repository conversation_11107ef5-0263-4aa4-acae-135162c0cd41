# Generated by Django 4.2.2 on 2024-12-19 14:41

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("finance", "0016_add_finance_review_result_to_budget_expenses"),
    ]

    operations = [
        migrations.CreateModel(
            name="TotalIncomeFansHistory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "date",
                    models.DateField(db_index=True, help_text="Date", unique=True),
                ),
                ("income_amount", models.IntegerField()),
            ],
            options={
                "ordering": ["-date"],
            },
        ),
        migrations.CreateModel(
            name="MonthIncomeFansTracking",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("month_date", models.DateField(help_text="Month date")),
                ("month_income_amount", models.IntegerField()),
                (
                    "income_fans_history",
                    models.ForeignKey(
                        help_text="Income fans history",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="month_income_fans_trackings",
                        to="finance.totalincomefanshistory",
                    ),
                ),
            ],
            options={
                "ordering": ["month_date"],
            },
        ),
        migrations.AddConstraint(
            model_name="monthincomefanstracking",
            constraint=models.UniqueConstraint(
                fields=("month_date", "income_fans_history"),
                name="unique_month_income_fans_tracking",
            ),
        ),
    ]
