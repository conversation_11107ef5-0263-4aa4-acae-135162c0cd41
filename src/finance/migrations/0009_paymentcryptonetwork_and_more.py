# Generated by Django 4.2.2 on 2024-09-16 20:05

import base.models
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("finance", "0008_monthdebttracking_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="PaymentCryptoNetwork",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "name",
                    models.CharField(help_text="Name", max_length=255, unique=True),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, help_text="Activation"),
                ),
            ],
            options={
                "ordering": ["name"],
                "abstract": False,
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.AddField(
            model_name="requestpayment",
            name="payment_crypto_network",
            field=models.ForeignKey(
                blank=True,
                help_text="Crypto network",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="request_payments",
                to="finance.paymentcryptonetwork",
            ),
        ),
    ]
