# Generated by Django 4.2.2 on 2024-11-01 15:37

import base.models
import datetime
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import django_cryptography.fields
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("finance", "0009_paymentcryptonetwork_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="Budget",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "expected_plan",
                    models.PositiveIntegerField(help_text="Expected plan"),
                ),
                (
                    "expected_wage_fund",
                    models.PositiveIntegerField(help_text="Expected wage fund"),
                ),
            ],
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.CreateModel(
            name="BudgetExpense",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("date", models.DateField(help_text="Date")),
                (
                    "payment_date",
                    models.DateField(blank=True, help_text="Payment date", null=True),
                ),
                (
                    "enrollment_date",
                    models.DateField(
                        blank=True, help_text="Enrollment date", null=True
                    ),
                ),
                (
                    "expense_item",
                    models.CharField(help_text="Expense item", max_length=255),
                ),
                (
                    "cashflow_expense_sub_item",
                    models.CharField(
                        blank=True,
                        help_text="Expense sub item",
                        max_length=255,
                        null=True,
                    ),
                ),
                (
                    "amount_usd",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Amount in USD",
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "department_name",
                    models.CharField(help_text="Department", max_length=255),
                ),
                (
                    "payment_system",
                    models.CharField(help_text="Payment system", max_length=255),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[("done", "Done"), ("not_done", "Not done")],
                        help_text="Status",
                        max_length=255,
                    ),
                ),
            ],
            options={
                "ordering": ["-date"],
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.CreateModel(
            name="BudgetPeriod",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("start_date", models.DateField(help_text="Start date of the period")),
                ("end_date", models.DateField(help_text="End date of the period")),
                (
                    "name",
                    models.CharField(
                        blank=True,
                        help_text="Name of the period",
                        max_length=255,
                        null=True,
                    ),
                ),
            ],
            options={
                "db_table": "finance_budget_period",
                "ordering": ["-start_date"],
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.CreateModel(
            name="CashflowExpenseItem",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "name",
                    models.CharField(help_text="Name", max_length=255, unique=True),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, help_text="Activation"),
                ),
                ("is_wage_fund", models.BooleanField(db_index=True, default=False)),
            ],
            options={
                "ordering": ["name"],
                "abstract": False,
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.CreateModel(
            name="CashflowExpenseSubItem",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "name",
                    models.CharField(help_text="Name", max_length=255, unique=True),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, help_text="Activation"),
                ),
            ],
            options={
                "ordering": ["name"],
                "abstract": False,
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.CreateModel(
            name="PaymentDepartment",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "name",
                    models.CharField(help_text="Name", max_length=255, unique=True),
                ),
                (
                    "map_value",
                    models.CharField(
                        help_text="Map value(name of the app from MVP",
                        max_length=255,
                        unique=True,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.AddField(
            model_name="requestpayment",
            name="amount_usd",
            field=models.DecimalField(
                decimal_places=2, default=0, help_text="Amount in USD", max_digits=10
            ),
        ),
        migrations.CreateModel(
            name="RequestCashflow",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "payment_date",
                    models.DateField(
                        default=datetime.date.today, help_text="Payment date"
                    ),
                ),
                (
                    "enrollment_date",
                    models.DateField(
                        default=datetime.date.today, help_text="Enrollment date"
                    ),
                ),
                (
                    "date",
                    models.DateField(
                        default=datetime.date.today, editable=False, help_text="Date"
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2, help_text="Amount", max_digits=10
                    ),
                ),
                (
                    "model_nickname",
                    models.CharField(
                        blank=True,
                        help_text=" Model nickname",
                        max_length=255,
                        null=True,
                    ),
                ),
                (
                    "created_by",
                    models.CharField(help_text="Payment author", max_length=255),
                ),
                (
                    "payment_details",
                    django_cryptography.fields.encrypt(
                        models.CharField(help_text="Details", max_length=255)
                    ),
                ),
                (
                    "payment_description",
                    models.TextField(blank=True, help_text="Description", null=True),
                ),
                (
                    "amount_usd",
                    models.DecimalField(
                        decimal_places=2, help_text="Amount in USD", max_digits=10
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[("accept", "Accept"), ("reject", "Reject")],
                        default="accept",
                        help_text="Status",
                        max_length=20,
                    ),
                ),
                (
                    "budget_expense",
                    models.OneToOneField(
                        help_text="Budget expense",
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="request_cashflows",
                        to="finance.budgetexpense",
                    ),
                ),
                (
                    "cashflow_expense_item",
                    models.ForeignKey(
                        help_text="Expense item",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="request_cashflows",
                        to="finance.cashflowexpenseitem",
                    ),
                ),
                (
                    "cashflow_expense_sub_item",
                    models.ForeignKey(
                        blank=True,
                        help_text="Expense sub item",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="request_cashflows",
                        to="finance.cashflowexpensesubitem",
                    ),
                ),
                (
                    "currency",
                    models.ForeignKey(
                        help_text="Currency",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="request_cashflows",
                        to="finance.paymentcurrency",
                    ),
                ),
                (
                    "department",
                    models.ForeignKey(
                        help_text="Department",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="request_cashflows",
                        to="finance.paymentdepartment",
                    ),
                ),
                (
                    "payment_system",
                    models.ForeignKey(
                        help_text="Payment system",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="request_cashflows",
                        to="finance.paymentsystem",
                    ),
                ),
            ],
            options={
                "ordering": ["-date"],
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.AddConstraint(
            model_name="budgetperiod",
            constraint=models.UniqueConstraint(
                fields=("start_date", "end_date"), name="finance_budget_unique_period"
            ),
        ),
        migrations.AddField(
            model_name="budgetexpense",
            name="budget",
            field=models.ForeignKey(
                blank=True,
                help_text="Budget",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="budget_expenses",
                to="finance.budget",
            ),
        ),
        migrations.AddField(
            model_name="budgetexpense",
            name="department",
            field=models.ForeignKey(
                help_text="Department",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="budget_expenses",
                to="finance.paymentdepartment",
            ),
        ),
        migrations.AddField(
            model_name="budget",
            name="department",
            field=models.ForeignKey(
                help_text="Department",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="budgets",
                to="finance.paymentdepartment",
            ),
        ),
        migrations.AddField(
            model_name="budget",
            name="period",
            field=models.ForeignKey(
                help_text="Budget period",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="budgets",
                to="finance.budgetperiod",
            ),
        ),
        migrations.AddField(
            model_name="requestpayment",
            name="budget_expense",
            field=models.OneToOneField(
                help_text="Budget expense",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="request_payments",
                to="finance.budgetexpense",
            ),
        ),
        migrations.AddField(
            model_name="requestpayment",
            name="payment_department",
            field=models.ForeignKey(
                blank=True,
                help_text="Department",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="request_payments",
                to="finance.paymentdepartment",
            ),
        ),
        migrations.AddConstraint(
            model_name="budget",
            constraint=models.UniqueConstraint(
                fields=("period", "department"),
                name="unique_finance_budget_period_department",
            ),
        ),
    ]
