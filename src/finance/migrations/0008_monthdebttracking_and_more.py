# Generated by Django 4.2.2 on 2024-08-07 16:10

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("finance", "0007_totaldebthistory"),
    ]

    operations = [
        migrations.CreateModel(
            name="MonthDebtTracking",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("month_date", models.DateField(help_text="Month date")),
                ("month_debt_amount", models.IntegerField()),
                (
                    "total_debt_history",
                    models.ForeignKey(
                        help_text="Total debt history",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="month_debt_trackings",
                        to="finance.totaldebthistory",
                    ),
                ),
            ],
            options={
                "ordering": ["month_date"],
            },
        ),
        migrations.AddConstraint(
            model_name="monthdebttracking",
            constraint=models.UniqueConstraint(
                fields=("month_date", "total_debt_history"),
                name="unique_month_debt_tracking",
            ),
        ),
    ]
