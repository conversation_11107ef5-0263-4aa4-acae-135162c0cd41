# Generated by Django 4.2.2 on 2024-03-19 13:32

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("finance", "0003_alter_requestpayment_finance_review_result_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="requestpayment",
            name="finance_reviewer",
            field=models.ForeignKey(
                blank=True,
                help_text="Current financier reviewer",
                limit_choices_to={"role__name": "financier"},
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="request_payments",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="requestpayment",
            name="payment_currency",
            field=models.ForeignKey(
                help_text="Currency",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="request_payments",
                to="finance.paymentcurrency",
            ),
        ),
        migrations.AlterField(
            model_name="requestpayment",
            name="payment_expense_item",
            field=models.ForeignKey(
                help_text="Expense item",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="request_payments",
                to="finance.paymentexpenseitem",
            ),
        ),
        migrations.AlterField(
            model_name="requestpayment",
            name="payment_tools",
            field=models.ManyToManyField(
                blank=True,
                help_text="Payment tools",
                related_name="request_payments",
                through="finance.PaymentTransaction",
                to="finance.paymenttool",
            ),
        ),
    ]
