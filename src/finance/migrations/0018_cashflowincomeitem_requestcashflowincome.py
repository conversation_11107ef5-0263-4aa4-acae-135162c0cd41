# Generated by Django 4.2.2 on 2025-01-06 14:20

import base.models
import datetime
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import django_cryptography.fields
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("finance", "0017_totalincomefanshistory_monthincomefanstracking_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="CashflowIncomeItem",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "name",
                    models.CharField(help_text="Name", max_length=255, unique=True),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, help_text="Activation"),
                ),
            ],
            options={
                "ordering": ["name"],
                "abstract": False,
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.CreateModel(
            name="RequestCashflowIncome",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "payment_date",
                    models.DateField(
                        default=datetime.date.today, help_text="Payment date"
                    ),
                ),
                (
                    "enrollment_date",
                    models.DateField(
                        default=datetime.date.today, help_text="Enrollment date"
                    ),
                ),
                (
                    "date",
                    models.DateField(
                        default=datetime.date.today, editable=False, help_text="Date"
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2, help_text="Amount", max_digits=10
                    ),
                ),
                (
                    "created_by",
                    models.CharField(help_text="Payment author", max_length=255),
                ),
                (
                    "income_details",
                    django_cryptography.fields.encrypt(
                        models.CharField(help_text="Details", max_length=255)
                    ),
                ),
                (
                    "payment_description",
                    models.TextField(blank=True, help_text="Description", null=True),
                ),
                (
                    "amount_usd",
                    models.DecimalField(
                        decimal_places=2, help_text="Amount in USD", max_digits=10
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("review", "In review"),
                            ("accept", "Accepted"),
                            ("reject", "Rejected"),
                        ],
                        default="accept",
                        help_text="Status",
                        max_length=20,
                    ),
                ),
                (
                    "financier",
                    models.CharField(blank=True, help_text="Financier", max_length=255),
                ),
                (
                    "cashflow_income_item",
                    models.ForeignKey(
                        help_text="Income item",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="request_cashflow_incomes",
                        to="finance.cashflowincomeitem",
                    ),
                ),
                (
                    "currency",
                    models.ForeignKey(
                        help_text="Currency",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="request_cashflow_incomes",
                        to="finance.paymentcurrency",
                    ),
                ),
                (
                    "department",
                    models.ForeignKey(
                        help_text="Department",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="request_cashflow_incomes",
                        to="finance.paymentdepartment",
                    ),
                ),
                (
                    "payment_system",
                    models.ForeignKey(
                        help_text="Payment system",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="request_cashflow_incomes",
                        to="finance.paymentsystem",
                    ),
                ),
            ],
            options={
                "ordering": ["-date"],
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
    ]
