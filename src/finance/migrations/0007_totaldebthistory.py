# Generated by Django 4.2.2 on 2024-07-22 13:39

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("finance", "0006_payoutaddress_payoutreview"),
    ]

    operations = [
        migrations.CreateModel(
            name="TotalDebtHistory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "date",
                    models.DateField(db_index=True, help_text="Date", unique=True),
                ),
                ("debt_amount", models.IntegerField()),
            ],
            options={
                "ordering": ["-date"],
            },
        ),
    ]
