# Generated by Django 4.2.2 on 2024-06-20 17:58
import uuid

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models

import base.models


class Migration(migrations.Migration):

    dependencies = [
        ("only_fans_models", "0012_subprofile"),
        ("finance", "0005_alter_paymentcurrency_name_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="PayoutAddress",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "address_name",
                    models.CharField(help_text="Address", max_length=255, unique=True),
                ),
            ],
            options={
                "ordering": ["address_name"],
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.CreateModel(
            name="PayoutReview",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "payout_id",
                    models.BigIntegerField(
                        blank=True, help_text="Payout ID", null=True, unique=True
                    ),
                ),
                (
                    "payout_creation_date",
                    models.DateTimeField(help_text="Payout creation date"),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2, help_text="Amount", max_digits=10
                    ),
                ),
                (
                    "finance_review_date",
                    models.DateTimeField(
                        blank=True, help_text="Finance review date", null=True
                    ),
                ),
                (
                    "finance_review_result",
                    models.CharField(
                        choices=[
                            ("review", "In review"),
                            ("accept", "Accepted"),
                            ("reject", "Rejected"),
                        ],
                        default="review",
                        help_text="Finance review result",
                        max_length=255,
                    ),
                ),
                (
                    "financier",
                    models.CharField(
                        blank=True,
                        help_text="Financier name",
                        max_length=255,
                        null=True,
                    ),
                ),
                (
                    "model_id",
                    models.BigIntegerField(blank=True, help_text="Model ID", null=True),
                ),
                (
                    "only_fans_model",
                    models.ForeignKey(
                        help_text="OnlyFans model",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="payout_reviews",
                        to="only_fans_models.onlyfansmodel",
                    ),
                ),
                (
                    "payout_address",
                    models.ForeignKey(
                        help_text="Payout address",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="payout_reviews",
                        to="finance.payoutaddress",
                    ),
                ),
                (
                    "finance_reviewer",
                    models.ForeignKey(
                        blank=True,
                        help_text="Current financier reviewer",
                        limit_choices_to={"role__name": "financier"},
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="payout_reviews",
                        to=settings.AUTH_USER_MODEL,
                    )
                ),
            ],
            options={
                "ordering": ["-payout_creation_date"],
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
    ]
