# Generated by Django 4.2.2 on 2025-06-23 13:19

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("ads", "0049_promo_new_donor_budget"),
        ("only_fans_models", "0013_historicalonlyfansmodel"),
        ("finance", "0025_payoutaddress_is_active"),
    ]

    operations = [
        migrations.CreateModel(
            name="ModelDonorDebtHistory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("date", models.DateField(db_index=True, help_text="Date")),
                (
                    "only_fans_model",
                    models.ForeignKey(
                        help_text="OnlyFans model",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="model_donor_debt_histories",
                        to="only_fans_models.onlyfansmodel",
                    ),
                ),
            ],
            options={
                "ordering": ["-date"],
            },
        ),
        migrations.CreateModel(
            name="ModelDonorDebtHistoryDetail",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("month_date", models.DateField(help_text="Month date")),
                ("debt_amount", models.IntegerField(help_text="Debt amount")),
                ("fans_debt_amount", models.IntegerField(help_text="Fans debt amount")),
                (
                    "donor",
                    models.ForeignKey(
                        help_text="Donor",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="model_donor_debt_history_details",
                        to="ads.donor",
                    ),
                ),
                (
                    "model_donor_debt_history",
                    models.ForeignKey(
                        help_text="Model donor debt history",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="model_donor_debt_history_details",
                        to="finance.modeldonordebthistory",
                    ),
                ),
            ],
            options={
                "ordering": ["-month_date"],
            },
        ),
        migrations.AddConstraint(
            model_name="modeldonordebthistorydetail",
            constraint=models.UniqueConstraint(
                fields=("month_date", "model_donor_debt_history", "donor"),
                name="unique_model_donor_debt_history_detail",
            ),
        ),
        migrations.AddConstraint(
            model_name="modeldonordebthistory",
            constraint=models.UniqueConstraint(
                fields=("date", "only_fans_model"),
                name="unique_model_donor_debt_history",
            ),
        ),
    ]
