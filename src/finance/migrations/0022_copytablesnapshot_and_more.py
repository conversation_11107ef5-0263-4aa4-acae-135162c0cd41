# Generated by Django 4.2.2 on 2025-02-26 18:14

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("finance", "0021_requestpayment_created_by_tg_username_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="CopyTableSnapshot",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateField(
                        auto_now_add=True, help_text="Date of the snapshot created"
                    ),
                ),
                (
                    "date_filter",
                    models.CharField(
                        choices=[
                            ("date", "Date"),
                            ("buy_date", "Buy date"),
                            ("date_phantom_cost", "Date phantom cost"),
                        ],
                        help_text="Date filter",
                        max_length=63,
                    ),
                ),
                (
                    "promo_filter",
                    models.CharField(
                        choices=[
                            ("all promos", "All promos"),
                            ("friends for model", "Friends for model"),
                            ("gg", "GG"),
                        ],
                        help_text="Promo filter",
                        max_length=63,
                    ),
                ),
                ("start_date", models.DateField(help_text="Start date")),
                ("end_date", models.DateField(help_text="End date")),
                ("string", models.TextField(help_text="Copy table string")),
            ],
        ),
        migrations.AddConstraint(
            model_name="copytablesnapshot",
            constraint=models.UniqueConstraint(
                fields=(
                    "created_at",
                    "date_filter",
                    "promo_filter",
                    "start_date",
                    "end_date",
                ),
                name="unique_copy_table_snapshot",
            ),
        ),
    ]
