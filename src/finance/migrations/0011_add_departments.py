# Generated by Django 4.2.2 on 2024-10-28 12:02

from django.db import migrations


def create_payment_departments(apps, schema_editor):
    """
    Create a default payment department if it doesn't exist.
    """
    PaymentDepartment = apps.get_model('finance', 'PaymentDepartment')

    data = [
        ("analytic", "Analytics"),
        ("business_dev", "Business Development"),
        ("client_management", "Client Management"),
        ("executive_management_team", "Executive Management Team"),
        ("finance", "Finance"),
        ("hr", "HR"),
        ("it", "IT"),
        ("marketing", "Marketing"),
        ("onboarding", "Onboarding"),
        ("production", "Production"),
        ("sales", "Sales"),
        ("scouting", "Scouting"),
        ("smm", "SMM"),
        ("other", "Other"),
    ]
    for map_value, name in data:
        PaymentDepartment.objects.get_or_create(map_value=map_value, name=name)


class Migration(migrations.Migration):

    dependencies = [
        ("finance", "0010_budget_budgetexpense_budgetperiod_and_more"),
    ]

    operations = [
        migrations.RunPython(create_payment_departments, migrations.RunPython.noop)
    ]
