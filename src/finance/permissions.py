from django.contrib.auth import get_user_model

from base import permissions
from finance.models import ReviewResult


class IsFinanceReviewer(permissions.BasePermission):
    """
    Custom permission to only reviewer has permission to object
    """
    message = 'You are not reviewer, push the flag before action with request payment'
    code = 403

    def has_object_permission(self, request, view, obj):
        """
        Check if the user is reviewer
        """
        user = request.user

        assert hasattr(
            obj, 'finance_reviewer'
        ), "Object must have 'finance_reviewer' attribute"

        if not obj.finance_reviewer:
            return False

        assert isinstance(
            obj.finance_reviewer, get_user_model()
        ), "Attribute 'finance_reviewer' must be a User instance"

        if not user.is_authenticated:
            return False

        if obj.finance_review_result == ReviewResult.REVIEW:
            return obj.finance_reviewer == user

        return True
