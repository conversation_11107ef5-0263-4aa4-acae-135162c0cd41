from datetime import date

from django.db import models
from django.db.models import UniqueConstraint
from django.utils.translation import gettext_lazy as _
from django_cryptography.fields import encrypt

from base.models import TimeStampedModel, TimeStampedUUIDModel


# ---------------------------------REQUEST PAYMENT------------------------------------------------
class BaseNameModel(TimeStampedUUIDModel):
    """
    Abstract base model for entities with a name and activation status.
    """
    name = models.CharField(max_length=255, help_text=_('Name'), unique=True)
    is_active = models.BooleanField(default=True, help_text=_('Activation'))

    class Meta:
        abstract = True
        ordering = ['name']

    def __str__(self):
        return self.name


class PaymentExpenseItem(BaseNameModel):
    """
    Model for expense items
    """
    reviewer_tg_id = models.BigIntegerField(
        help_text=_('Reviewer tg id'),
        null=True,
        blank=True
    )


class PaymentSystem(BaseNameModel):
    """
    Model for payment systems
    """
    pass


class PaymentCurrency(BaseNameModel):
    """
    Model for currencies
    """
    pass


class PaymentTool(BaseNameModel):
    """
    Model for payment tools
    """
    pass


class PaymentCryptoNetwork(BaseNameModel):
    """
    Model for crypto networks
    """
    pass


class PaymentDepartment(TimeStampedUUIDModel):
    """
    Model for payment departments
    """
    name = models.CharField(max_length=255, help_text=_('Name'), unique=True)
    map_value = models.CharField(max_length=255, help_text=_('Map value(name of the app from MVP'), unique=True)

    def __str__(self):
        return self.name


class ReviewResult(models.TextChoices):
    REVIEW = 'review', _('In review')
    ACCEPT = 'accept', _('Accepted')
    REJECT = 'reject', _('Rejected')


class RequestPayment(TimeStampedModel):
    """
    Model for request payments from users to finance department
    """

    date = models.DateTimeField(auto_now_add=True, help_text=_('Creating date'))
    created_by = models.CharField(max_length=255, help_text=_('Created by name'))
    created_by_tg_id = models.BigIntegerField(
        help_text=_('Creator tg id'),
    )
    created_by_tg_username = models.CharField(
        max_length=63,
        help_text=_('Creator tg username'),
        null=True
    )
    head_review_date = models.DateTimeField(
        blank=True, null=True, help_text=_('Head review date')
    )
    head_review_result = models.CharField(
        default=ReviewResult.REVIEW,
        max_length=255,
        choices=ReviewResult.choices,
        help_text=_('Head review result'),
    )
    head = models.CharField(max_length=255, help_text=_('Head name'))
    finance_review_date = models.DateTimeField(
        blank=True, null=True, help_text=_('Finance review date')
    )
    finance_review_result = models.CharField(
        default=ReviewResult.REVIEW,
        max_length=255,
        choices=ReviewResult.choices,
        help_text=_('Finance review result'),
    )
    financier = models.CharField(
        max_length=255, help_text=_('Financier name'), blank=True, null=True
    )
    department = models.CharField(max_length=255, help_text=_('Department name'))
    payment_system = models.CharField(
        max_length=255, help_text=_('Payment system name')
    )
    payment_expense_item = models.ForeignKey(
        'finance.PaymentExpenseItem',
        on_delete=models.SET_NULL,
        null=True,
        help_text=_('Expense item'),
        related_name='request_payments',
    )
    payment_comment = models.TextField(blank=True, null=True, help_text=_('Comment'))
    model_nickname = models.CharField(
        max_length=255, null=True, blank=True, help_text=_('Model nickname')
    )
    payment_description = models.TextField(help_text=_('Description'))
    google_result = models.BooleanField(
        blank=True,
        null=True,
        help_text=_('Indicate that row created in google spreadsheets'),
    )
    payment_currency = models.ForeignKey(
        'finance.PaymentCurrency',
        on_delete=models.SET_NULL,
        null=True,
        help_text=_('Currency'),
        related_name='request_payments',
    )
    payment_crypto_network = models.ForeignKey(
        'finance.PaymentCryptoNetwork',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text=_('Crypto network'),
        related_name='request_payments',
    )
    payment_amount = models.DecimalField(
        max_digits=10, decimal_places=2, default=0, help_text=_('Amount')
    )
    payment_details = encrypt(models.CharField(max_length=255, help_text=_('Details')))
    payment_tools = models.ManyToManyField(
        'finance.PaymentTool',
        through='finance.PaymentTransaction',
        help_text=_('Payment tools'),
        related_name='request_payments',
        blank=True,
    )
    finance_reviewer = models.ForeignKey(
        'accounts.User',
        limit_choices_to={'role__name': 'financier'},
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        help_text=_('Current financier reviewer'),
        related_name='request_payments',
    )
    payment_department = models.ForeignKey(
        'finance.PaymentDepartment',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text=_('Department'),
        related_name='request_payments',
    )
    amount_usd = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        help_text=_('Amount in USD')
    )
    budget_expense = models.OneToOneField(
        'finance.BudgetExpense',
        on_delete=models.CASCADE,
        help_text=_('Budget expense'),
        related_name='request_payments',
        null=True
    )
    jira_link = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        help_text=_('Jira link')
    )

    class Meta:
        ordering = ['-date']

    def __str__(self):
        return f"{self.date} {self.created_by}"

    @property
    def created_by_tg_link(self):
        """
        Link to the creator's tg
        """
        return f'https://t.me/{self.created_by_tg_username}' if self.created_by_tg_username else None


class PaymentTransaction(TimeStampedUUIDModel):
    request_payment = models.ForeignKey(
        'finance.RequestPayment',
        on_delete=models.CASCADE,
        related_name='payment_transactions',
        help_text=_('Request payment'),
    )
    payment_tool = models.ForeignKey(
        'finance.PaymentTool',
        on_delete=models.SET_NULL,
        null=True,
        help_text=_('Payment tool'),
        related_name='payment_transactions',
    )
    amount = models.DecimalField(max_digits=10, decimal_places=2, help_text=_('Amount'))


# --------------------------------------Payout-------------------------------------------------------------
class PayoutAddress(TimeStampedUUIDModel):
    """
    Model for payout addresses
    """
    address_name = models.CharField(max_length=255, help_text=_('Address'), unique=True)
    is_active = models.BooleanField(default=True, help_text=_('Display in dropdown'))

    class Meta:
        ordering = ['address_name']

    def __str__(self):
        return self.address_name


class PayoutReview(TimeStampedUUIDModel):
    """
    Model for payout
    """
    payout_id = models.BigIntegerField(help_text=_('Payout ID'), unique=True, blank=True, null=True)
    payout_creation_date = models.DateTimeField(help_text=_('Payout creation date'))
    amount = models.DecimalField(max_digits=10, decimal_places=2, help_text=_('Amount'))
    only_fans_model = models.ForeignKey(
        'only_fans_models.OnlyFansModel',
        on_delete=models.SET_NULL,
        null=True,
        help_text=_('OnlyFans model'),
        related_name='payout_reviews',
    )
    finance_review_date = models.DateTimeField(
        blank=True, null=True, help_text=_('Finance review date')
    )
    finance_review_result = models.CharField(
        default=ReviewResult.REVIEW,
        max_length=255,
        choices=ReviewResult.choices,
        help_text=_('Finance review result'),
    )
    financier = models.CharField(
        max_length=255, help_text=_('Financier name'), blank=True, null=True
    )
    payout_address = models.ForeignKey(
        'finance.PayoutAddress',
        on_delete=models.SET_NULL,
        null=True,
        help_text=_('Payout address'),
        related_name='payout_reviews',
    )
    model_id = models.BigIntegerField(help_text=_('Model ID'), blank=True, null=True)
    finance_reviewer = models.ForeignKey(
        'accounts.User',
        limit_choices_to={'role__name': 'financier'},
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        help_text=_('Current financier reviewer'),
        related_name='payout_reviews',
    )

    class Meta:
        ordering = ['-payout_creation_date']

    def __str__(self):
        return f"{self.payout_creation_date} {self.only_fans_model}"

    @property
    def is_autogenerated(self):
        return self.payout_id is not None

    def save(
        self, force_insert=False, force_update=False, using=None, update_fields=None
    ):
        if self.only_fans_model and not self.model_id:
            self.model_id = self.only_fans_model.model_id

        return super().save(force_insert, force_update, using, update_fields)


# --------------------------------------------------History-------------------------------------------------------
class TotalDebtHistory(models.Model):
    """
    Model for total debt history
    """
    date = models.DateField(
        help_text=_('Date'),
        unique=True,
        db_index=True
    )
    debt_amount = models.IntegerField()
    fans_debt_amount = models.IntegerField(default=0)

    class Meta:
        ordering = ['-date']

    def __str__(self):
        return f"{self.date} - {self.debt_amount}"


class MonthDebtTracking(models.Model):
    """
    Model for month debt tracking
    """
    month_date = models.DateField(
        help_text=_('Month date'),
    )
    month_debt_amount = models.IntegerField()
    month_fans_debt_amount = models.IntegerField(default=0)
    total_debt_history = models.ForeignKey(
        'finance.TotalDebtHistory',
        on_delete=models.CASCADE,
        help_text=_('Total debt history'),
        related_name='month_debt_trackings',
    )

    class Meta:
        ordering = ['month_date']
        constraints = [
            UniqueConstraint(
                fields=['month_date', 'total_debt_history'], name='unique_month_debt_tracking'
            )
        ]

    def __str__(self):
        return f"{self.month_date} - {self.month_debt_amount}"


class MonthModelDebtTracking(models.Model):
    """
    Model for month model debt tracking
    """
    only_fans_model = models.ForeignKey(
        'only_fans_models.OnlyFansModel',
        on_delete=models.CASCADE,
        help_text=_('OnlyFans model'),
        related_name='month_model_debt_trackings',
    )
    month_debt_tracking = models.ForeignKey(
        MonthDebtTracking,
        on_delete=models.CASCADE,
        help_text=_('Month debt tracking'),
        related_name='month_model_debt_trackings',
    )
    debt_amount = models.IntegerField(
        default=0,
        help_text=_('Debt amount')
    )
    fans_debt_amount = models.IntegerField(
        default=0,
        help_text=_('Fans debt amount')
    )

    class Meta:
        ordering = ['only_fans_model']
        constraints = [
            UniqueConstraint(
                fields=['only_fans_model', 'month_debt_tracking'],
                name='unique_month_model_debt_tracking'
            )
        ]


class TotalIncomeFansHistory(models.Model):
    """
    Model for Income Fans history
    """
    date = models.DateField(
        help_text=_('Date'),
        unique=True,
        db_index=True
    )
    income_amount = models.IntegerField()

    class Meta:
        ordering = ['-date']

    def __str__(self):
        return f"{self.date} - {self.income_amount}"


class MonthIncomeFansTracking(models.Model):
    """
    Model for month income fans tracking
    """
    month_date = models.DateField(
        help_text=_('Month date'),
    )
    month_income_amount = models.IntegerField()
    income_fans_history = models.ForeignKey(
        'finance.TotalIncomeFansHistory',
        on_delete=models.CASCADE,
        help_text=_('Income fans history'),
        related_name='month_income_fans_trackings',
    )

    class Meta:
        ordering = ['month_date']
        constraints = [
            UniqueConstraint(
                fields=['month_date', 'income_fans_history'], name='unique_month_income_fans_tracking'
            )
        ]

    def __str__(self):
        return f"{self.month_date} - {self.month_income_amount}"


# -----------------------------------------------Budget---------------------------------------------------------
class BudgetPeriod(TimeStampedUUIDModel):
    """
    Model for budget period
    """
    start_date = models.DateField(
        help_text=_('Start date of the period')
    )
    end_date = models.DateField(
        help_text=_('End date of the period')
    )
    name = models.CharField(
        max_length=255,
        help_text=_('Name of the period'),
        blank=True,
        null=True
    )

    class Meta:
        ordering = ['-start_date']
        constraints = [
            models.UniqueConstraint(fields=['start_date', 'end_date'], name='finance_budget_unique_period')
        ]
        db_table = 'finance_budget_period'

    def __str__(self):
        if self.name:
            return f'{self.name}'

        return f'{self.start_date} - {self.end_date}'


class Budget(TimeStampedUUIDModel):
    """
    Model for budget
    """
    period = models.ForeignKey(
        BudgetPeriod,
        on_delete=models.CASCADE,
        help_text=_('Budget period'),
        related_name='budgets',
    )
    expected_plan = models.PositiveIntegerField(
        help_text=_('Expected plan'),
    )
    expected_wage_fund = models.PositiveIntegerField(
        help_text=_('Expected wage fund'),
    )
    department = models.ForeignKey(
        PaymentDepartment,
        on_delete=models.SET_NULL,
        null=True,
        help_text=_('Department'),
        related_name='budgets',
    )

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=['period', 'department'],
                name='unique_finance_budget_period_department'
            )
        ]

    def __str__(self):
        return f"{self.period} - {self.department.name}"


class BudgetExpense(TimeStampedUUIDModel):
    """
    Model for budget expense for analytics
    """
    class Status(models.TextChoices):
        DONE = 'done', _('Done')
        NOT_DONE = 'not_done', _('Not done')

    budget = models.ForeignKey(
        Budget,
        on_delete=models.SET_NULL,
        help_text=_('Budget'),
        related_name='budget_expenses',
        null=True,
        blank=True
    )
    date = models.DateField(help_text=_('Date'))
    payment_date = models.DateField(help_text=_('Payment date'), null=True, blank=True)
    enrollment_date = models.DateField(help_text=_('Enrollment date'), null=True, blank=True)
    expense_item = models.CharField(max_length=255, help_text=_('Expense item'))
    cashflow_expense_sub_item = models.CharField(
        max_length=255, help_text=_('Expense sub item'), blank=True, null=True
    )
    amount_usd = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text=_('Amount in USD'),
        null=True,
        blank=True
    )
    department_name = models.CharField(max_length=255, help_text=_('Department'))
    department = models.ForeignKey(
        PaymentDepartment,
        on_delete=models.SET_NULL,
        null=True,
        help_text=_('Department'),
        related_name='budget_expenses',
    )
    payment_system = models.CharField(max_length=255, help_text=_('Payment system'))
    status = models.CharField(
        max_length=255,
        help_text=_('Status'),
        choices=Status.choices
    )
    description = models.TextField(help_text=_('Description'), blank=True, null=True)
    is_wage_fund = models.BooleanField(default=False)
    finance_review_result = models.CharField(
        default=ReviewResult.REVIEW,
        max_length=255,
        choices=ReviewResult.choices,
        help_text=_('Finance review result'),
    )

    class Meta:
        ordering = ['-date']

    def __str__(self):
        return f"{self.date} - {self.expense_item} - {self.status}"


# ------------------------------------------------------Cashflow----------------------------------------------
class CashflowExpenseItem(BaseNameModel):
    """
    Model for cashflow expense items
    """
    is_wage_fund = models.BooleanField(default=False, db_index=True)


class CashflowExpenseSubItem(BaseNameModel):
    """
    Model for cashflow sub items
    """
    pass


class RequestCashflow(TimeStampedModel):
    """
    Model for request cashflow
    """
    payment_date = models.DateField(help_text=_('Payment date'), default=date.today)
    enrollment_date = models.DateField(help_text=_('Enrollment date'), default=date.today)
    date = models.DateField(help_text=_('Date'), default=date.today, editable=False)
    cashflow_expense_item = models.ForeignKey(
        CashflowExpenseItem,
        on_delete=models.SET_NULL,
        null=True,
        help_text=_('Expense item'),
        related_name='request_cashflows',
    )
    cashflow_expense_sub_item = models.ForeignKey(
        CashflowExpenseSubItem,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text=_('Expense sub item'),
        related_name='request_cashflows',
    )
    currency = models.ForeignKey(
        PaymentCurrency,
        on_delete=models.SET_NULL,
        null=True,
        help_text=_('Currency'),
        related_name='request_cashflows',
    )
    amount = models.DecimalField(
        max_digits=10, decimal_places=2, help_text=_('Amount')
    )
    payment_system = models.ForeignKey(
        PaymentSystem,
        on_delete=models.SET_NULL,
        null=True,
        help_text=_('Payment system'),
        related_name='request_cashflows',
    )
    department = models.ForeignKey(
        PaymentDepartment,
        on_delete=models.SET_NULL,
        null=True,
        help_text=_('Department'),
        related_name='request_cashflows',
    )
    model_nickname = models.CharField(
        max_length=255, null=True, blank=True, help_text=_(' Model nickname')
    )
    created_by = models.CharField(max_length=255, help_text=_('Payment author'))
    payment_details = encrypt(models.CharField(max_length=255, help_text=_('Details')))
    payment_description = models.TextField(
        help_text=_('Description'),
        blank=True,
        null=True
    )
    amount_usd = models.DecimalField(
        max_digits=10, decimal_places=2, help_text=_('Amount in USD')
    )
    budget_expense = models.OneToOneField(
        'finance.BudgetExpense',
        on_delete=models.CASCADE,
        help_text=_('Budget expense'),
        related_name='request_cashflows',
    )
    status = models.CharField(
        choices=ReviewResult.choices,
        max_length=20,
        help_text=_('Status'),
        default=ReviewResult.ACCEPT
    )
    financier = models.CharField(max_length=255, help_text=_('Financier'), blank=True)

    class Meta:
        ordering = ['-date']

    def __str__(self):
        return f"{self.date} {self.created_by}"


class CashflowIncomeItem(BaseNameModel):
    """
    Model for cashflow income items
    """
    pass


class RequestCashflowIncome(TimeStampedModel):
    """
    Model for request cashflow incomes
    """
    payment_date = models.DateField(help_text=_('Payment date'), default=date.today)
    enrollment_date = models.DateField(help_text=_('Enrollment date'), default=date.today)
    date = models.DateField(help_text=_('Date'), default=date.today, editable=False)
    cashflow_income_item = models.ForeignKey(
        CashflowIncomeItem,
        on_delete=models.SET_NULL,
        null=True,
        help_text=_('Income item'),
        related_name='request_cashflow_incomes',
    )
    currency = models.ForeignKey(
        PaymentCurrency,
        on_delete=models.SET_NULL,
        null=True,
        help_text=_('Currency'),
        related_name='request_cashflow_incomes',
    )
    amount = models.DecimalField(
        max_digits=10, decimal_places=2, help_text=_('Amount')
    )
    payment_system = models.ForeignKey(
        PaymentSystem,
        on_delete=models.SET_NULL,
        null=True,
        help_text=_('Payment system'),
        related_name='request_cashflow_incomes',
    )
    department = models.ForeignKey(
        PaymentDepartment,
        on_delete=models.SET_NULL,
        null=True,
        help_text=_('Department'),
        related_name='request_cashflow_incomes',
    )
    created_by = models.CharField(max_length=255, help_text=_('Payment author'))
    income_details = encrypt(models.CharField(max_length=255, help_text=_('Details')))
    payment_description = models.TextField(
        help_text=_('Description'),
        blank=True,
        null=True
    )
    amount_usd = models.DecimalField(
        max_digits=10, decimal_places=2, help_text=_('Amount in USD')
    )
    status = models.CharField(
        choices=ReviewResult.choices,
        max_length=20,
        help_text=_('Status'),
        default=ReviewResult.ACCEPT
    )
    financier = models.CharField(max_length=255, help_text=_('Financier'), blank=True)

    class Meta:
        ordering = ['-date']

    def __str__(self):
        return f"{self.date} {self.created_by}"


# -----------------------------------------Copy Table Snapshot------------------------------------------------
class CopyTableSnapshot(models.Model):
    """
    Model for copy table snapshot
    """
    class DateFilterChoices(models.TextChoices):
        DATE = 'date', _('Date')
        BUY_DATE = 'buy_date', _('Buy date')
        DATE_PHANTOM_COST = 'date_phantom_cost', _('Date phantom cost')

    class PromoFilterChoices(models.TextChoices):
        ALL_PROMOS = 'all promos', _('All promos')
        FRIENDS_FOR_MODEL = 'friends for model', _('Friends for model')
        GG = 'gg', _('GG')

    created_at = models.DateField(help_text=_('Date of the snapshot created'), auto_now_add=True)
    date_filter = models.CharField(max_length=63, help_text=_('Date filter'), choices=DateFilterChoices.choices)
    promo_filter = models.CharField(max_length=63, help_text=_('Promo filter'),  choices=PromoFilterChoices.choices)
    start_date = models.DateField(help_text=_('Start date'))
    end_date = models.DateField(help_text=_('End date'))
    string = models.TextField(help_text=_('Copy table string'))

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=['created_at', 'date_filter', 'promo_filter', 'start_date', 'end_date'],
                name='unique_copy_table_snapshot'
            )
        ]


# ------------------------------------------Model Donor Debt History--------------------------------
class ModelDonorDebtHistory(models.Model):
    """
    Model for model donor debt history
    """
    date = models.DateField(
        help_text=_('Date'),
        db_index=True
    )
    only_fans_model = models.ForeignKey(
        'only_fans_models.OnlyFansModel',
        on_delete=models.CASCADE,
        help_text=_('OnlyFans model'),
        related_name='model_donor_debt_histories',
    )

    class Meta:
        ordering = ['-date']
        constraints = [
            UniqueConstraint(
                fields=['date', 'only_fans_model'], name='unique_model_donor_debt_history'
            )
        ]

    def __str__(self):
        return f"{self.date} - {self.only_fans_model}"


class ModelDonorDebtHistoryDetail(models.Model):
    """
    Model for model donor debt history detail
    """
    month_date = models.DateField(
        help_text=_('Month date'),
    )
    model_donor_debt_history = models.ForeignKey(
        ModelDonorDebtHistory,
        on_delete=models.CASCADE,
        help_text=_('Model donor debt history'),
        related_name='model_donor_debt_history_details',
    )
    donor = models.ForeignKey(
        'ads.Donor',
        on_delete=models.CASCADE,
        help_text=_('Donor'),
        related_name='model_donor_debt_history_details',
    )
    debt_amount = models.IntegerField(
        help_text=_('Debt amount')
    )
    fans_debt_amount = models.IntegerField(
        help_text=_('Fans debt amount')
    )

    class Meta:
        constraints = [
            UniqueConstraint(
                fields=['month_date', 'model_donor_debt_history', 'donor'],
                name='unique_model_donor_debt_history_detail'
            )
        ]
        ordering = ['-month_date']

    def __str__(self):
        return f"{self.model_donor_debt_history} - {self.donor}"
