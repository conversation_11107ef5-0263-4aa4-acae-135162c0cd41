from dataclasses import dataclass
from datetime import datetime
from decimal import Decimal


@dataclass
class MonthDataDTO:
    month_date: datetime.date
    month_debt_amount: int
    delta: int = 0


@dataclass
class DebtTrackingDTO:
    number: int
    parse_date: datetime.date
    total_debt: int = 0
    total_delta: int = 0
    month_data_dtos: list[MonthDataDTO] | None = None

    def add_month_data(self, month_data_dto: MonthDataDTO) -> None:
        if self.month_data_dtos is None:
            self.month_data_dtos = []

        self.month_data_dtos.append(month_data_dto)
        self.total_debt += month_data_dto.month_debt_amount
        self.total_delta += month_data_dto.delta


@dataclass
class MonthIncomeFansDataDTO:
    month_date: datetime.date
    month_income_amount: int
    delta: int = 0


@dataclass
class IncomeFansTrackingDTO:
    number: int
    parse_date: datetime.date
    total_income_amount: int = 0
    total_delta: int = 0
    month_data_dtos: list[MonthIncomeFansDataDTO] | None = None

    def add_month_data(self, month_data_dto: MonthIncomeFansDataDTO) -> None:
        if self.month_data_dtos is None:
            self.month_data_dtos = []

        self.month_data_dtos.append(month_data_dto)
        self.total_income_amount += month_data_dto.month_income_amount
        self.total_delta += month_data_dto.delta


@dataclass
class IncomeFansDTO:
    month_date: datetime.date
    income_amount: int = Decimal('0')

    def add_amount(self, amount: Decimal) -> None:
        self.income_amount += amount
