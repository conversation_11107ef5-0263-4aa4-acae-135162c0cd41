from django.db import transaction
from django.db.models.signals import (
    post_delete,
    post_save,
    pre_save,
)
from django.dispatch import receiver

from ads.models import Ads
from base.crm_cache.service import RedisCacheService
from base.signals import post_bulk_update
from finance.models import (
    PayoutReview,
    RequestCashflow,
    RequestPayment,
    ReviewResult,
)
from finance.tasks import (
    send_request_payment_notification,
    send_socket_event_payout_review_created,
    send_socket_event_payout_review_deleted,
    send_socket_event_payout_review_updated,
    send_socket_event_request_cashflow_created,
    send_socket_event_request_cashflow_deleted,
    send_socket_event_request_cashflow_updated,
    send_socket_event_request_payment_created,
    send_socket_event_request_payment_updated,
)
from only_fans_models.models import OnlyFansModel


@receiver([post_save, post_delete], sender=Ads)
@receiver([post_save, post_delete], sender=OnlyFansModel)
def invalidate_cache_buying_ads(sender, instance, *args, **kwargs):
    """
    Signal receiver function that invalidates cache related to finance.

    Args:
    - sender: The sender of the signal.
    - instance: The instance that triggered the signal.
    - args: Additional positional arguments.
    - kwargs: Additional keyword arguments.
    """
    cache_service = RedisCacheService()
    prefixes = ['finance-total-spend', 'finance-total-spend-copy-table']

    for prefix in prefixes:
        cache_service.delete_keys_with_prefix(prefix)


@receiver([pre_save], sender=RequestPayment)
def cache_previous_request_payment(instance: RequestPayment, *args, **kwargs):
    original_head_review_result = None
    original_finance_review_result = None

    try:
        original_request_payment = RequestPayment.objects.get(pk=instance.id)
        original_head_review_result = original_request_payment.head_review_result
        original_finance_review_result = original_request_payment.finance_review_result
    except RequestPayment.DoesNotExist:
        pass  # RequestPayment object does not exist in the database, no need to cache it.

    instance.__original_head_review_result = original_head_review_result
    instance.__original_finance_review_result = original_finance_review_result


@receiver([post_save], sender=RequestPayment)
def invoke_request_payment_socket_event(instance: RequestPayment, created, *args, **kwargs):
    """
    Signal receiver function that sends a socket event to the client.

    Args:
    - instance: The instance that triggered the signal.
    - created: A boolean indicating whether the instance was created.
    - args: Additional positional arguments.
    - kwargs: Additional keyword arguments.
    """
    if instance.head_review_result != ReviewResult.ACCEPT:
        return

    original_head_review_result = getattr(instance, '__original_head_review_result', None)
    original_finance_review_result = getattr(instance, '__original_finance_review_result', None)

    if (
            original_head_review_result == ReviewResult.REVIEW
            and instance.head_review_result == ReviewResult.ACCEPT
    ) or created:
        transaction.on_commit(lambda: send_socket_event_request_payment_created.delay(instance.id))
    elif instance.head_review_result == ReviewResult.ACCEPT:
        transaction.on_commit(lambda: send_socket_event_request_payment_updated.delay(instance.id))

    if original_finance_review_result != instance.finance_review_result:
        transaction.on_commit(
            lambda: send_request_payment_notification.delay(instance.id, instance.finance_review_result)
        )


@receiver([post_save, post_delete, post_bulk_update], sender=PayoutReview)
def invoke_payout_review_socket_event(sender, created=None, instance=None, instances=None, *args, **kwargs):
    """
    Signal receiver function that sends a socket event to the client.

    Args:
    - instance: The instance that triggered the signal.
    - created: A boolean indicating whether the instance was created.
    - args: Additional positional arguments.
    - kwargs: Additional keyword arguments.
    """
    if created is not None and instance is not None:
        if created:
            transaction.on_commit(lambda: send_socket_event_payout_review_created.delay(str(instance.id)))
        else:
            transaction.on_commit(lambda: send_socket_event_payout_review_updated.delay(str(instance.id)))
    elif instances:
        for instance in instances:
            send_socket_event_payout_review_updated.delay(str(instance.id))
    elif instance:
        transaction.on_commit(lambda: send_socket_event_payout_review_deleted.delay(str(instance.id)))


@receiver(post_delete, sender=RequestPayment)
@receiver(post_delete, sender=RequestCashflow)
def post_delete_budget_expense(sender, instance, *args, **kwargs):
    """
    Signal receiver function that deletes a BudgetExpense object.

    Args:
    - sender: The sender of the signal.
    - instance: The instance that triggered the signal.
    - args: Additional positional arguments.
    - kwargs: Additional keyword arguments.
    """
    if hasattr(instance, 'budget_expense') and instance.budget_expense:
        instance.budget_expense.delete()


@receiver([post_save, post_delete], sender=RequestCashflow)
def invoke_request_cashflow_socket_event(sender, created=None, instance=None, *args, **kwargs):
    """
    Signal receiver function that sends a socket event to the client.

    Args:
    - sender: The sender of the signal.
    - instance: The instance that triggered the signal.
    - created: A boolean indicating whether the instance was created.
    - args: Additional positional arguments.
    - kwargs: Additional keyword arguments.
    """
    if created is not None and instance is not None:
        if created:
            transaction.on_commit(lambda: send_socket_event_request_cashflow_created.delay(str(instance.id)))
        else:
            transaction.on_commit(lambda: send_socket_event_request_cashflow_updated.delay(str(instance.id)))
    elif instance:
        transaction.on_commit(lambda: send_socket_event_request_cashflow_deleted.delay(str(instance.id)))
