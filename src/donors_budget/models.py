from django.db import models
from django.utils.translation import gettext_lazy as _

from base.models import TimeStampedUUIDModel


class DonorBudgetPeriod(TimeStampedUUIDModel):
    """
    DonorBudgetPeriod model
    """
    start_date = models.DateField(
        help_text=_('Start date of the period')
    )
    end_date = models.DateField(
        help_text=_('End date of the period')
    )
    name = models.CharField(
        max_length=255,
        help_text=_('Name of the period'),
        blank=True,
        null=True
    )

    class Meta:
        ordering = ['-start_date']
        constraints = [
            models.UniqueConstraint(fields=['start_date', 'end_date'], name='donor_budget_unique_period')
        ]
        db_table = 'donor_budget_period'

    def __str__(self):

        if self.name:
            return f'{self.name}'

        return f'{self.start_date} - {self.end_date}'


class DonorBudgetExpectedResult(TimeStampedUUIDModel):
    """
    Model which defines budget expected results for donors budgets
    """
    class PromoType(models.TextChoices):
        """
        Donor budget expected result type
        """
        ALL = 'all', _('All')
        FRIENDS_FOR_MODEL = 'friends for model', _('Friends for model')

    donor_budget_period = models.ForeignKey(
        DonorBudgetPeriod,
        on_delete=models.CASCADE,
        related_name='donor_budget_expected_results',
        help_text=_('Budget period of budget expected result')
    )
    donor = models.ForeignKey(
        'ads.Donor',
        on_delete=models.SET_NULL,
        null=True,
        related_name='donor_budget_expected_results',
        help_text=_('Donor')
    )
    cost_plan = models.PositiveIntegerField(
        default=0,
        help_text=_('Budget cost plan')
    )
    promo_type = models.CharField(
        max_length=30,
        choices=PromoType.choices,
        default=PromoType.ALL,
        help_text=_('Promo type')
    )

    class Meta:
        ordering = ['donor__name']
        constraints = [
            models.UniqueConstraint(
                fields=['donor_budget_period', 'donor', 'promo_type'],
                name='unique_donor_budget_expected_result'
            )
        ]
        db_table = 'donor_budget_expected_result'

    def __str__(self):
        return f'{self.donor} - {self.cost_plan}'


class DonorBudgetFactResult(TimeStampedUUIDModel):
    """
    Model which defines budget fact results for donor budgets
    """
    donor_budget_expected_result = models.OneToOneField(
        DonorBudgetExpectedResult,
        on_delete=models.CASCADE,
        related_name='donor_budget_fact_result',
        help_text=_('Budget fact result for budget expected result'),
        unique=True
    )
    cost_fact = models.PositiveIntegerField(
        default=0,
        help_text=_('Budget cost fact based on date')
    )
    cost_fact_date_counter = models.PositiveIntegerField(
        default=0,
        help_text=_('Budget cost fact based on date_counter'),
        blank=True,
    )

    class Meta:
        db_table = 'donor_budget_fact_result'

    def __str__(self):
        return f'{self.cost_fact}'
