from dateutil.relativedelta import relativedelta
from rest_framework import serializers
from rest_framework.validators import UniqueTogetherValidator

from ads.serializers import DonorListSerializer
from donors_budget.models import DonorBudgetExpectedResult, DonorBudgetPeriod
from donors_budget.services import DonorBudgetExpectedResultService, DonorBudgetPeriodService


class NestedDonorBudgetExpectedResultSerializer(serializers.ModelSerializer):
    """
    NestedDonorBudgetExpectedResultSerializer class
    """
    donor = DonorListSerializer()
    cost_fact = serializers.SerializerMethodField()

    class Meta:
        model = DonorBudgetExpectedResult
        fields = (
            'id',
            'cost_plan',
            'donor',
            'promo_type',
            'cost_fact'
        )

    def get_cost_fact(self, obj):
        date_filter = self.context['request'].query_params.get('date_filter')

        if not hasattr(obj, 'donor_budget_fact_result') or obj.donor_budget_fact_result is None:
            return 0

        if date_filter == 'date_counter':
            return obj.donor_budget_fact_result.cost_fact_date_counter or 0

        return obj.donor_budget_fact_result.cost_fact or 0


class DonorBudgetPeriodDetailSerializer(serializers.ModelSerializer):
    """
    Serializer class for DonorBudgetPeriod model.
    """
    donor_budget_expected_results = NestedDonorBudgetExpectedResultSerializer(many=True)
    previous_period = serializers.SerializerMethodField()

    class Meta:
        model = DonorBudgetPeriod
        fields = (
            'id',
            'start_date',
            'end_date',
            'donor_budget_expected_results',
            'previous_period',
            'name'
        )

    def get_previous_period(self, obj):
        start_date = obj.start_date - relativedelta(months=1)
        end_date = obj.end_date - relativedelta(months=1)

        previous_period = DonorBudgetPeriod.objects.filter(
            start_date__month=start_date.month,
            end_date__month=end_date.month
        ).first()

        if previous_period:
            return str(previous_period.id)


class DonorBudgetExpectedResultForPeriodSerializer(serializers.ModelSerializer):
    """
    Serializer class for DonorBudgetExpectedResult model related to a specific MarketingBudgetPeriod.
    """
    class Meta:
        model = DonorBudgetExpectedResult
        fields = (
            'cost_plan',
            'donor',
            'promo_type',
        )


class DonorBudgetPeriodCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating and updating DonorBudgetPeriod instances.
    """
    donor_budget_expected_results = DonorBudgetExpectedResultForPeriodSerializer(many=True,  required=False)

    class Meta:
        model = DonorBudgetPeriod
        fields = ('start_date', 'end_date', 'donor_budget_expected_results', 'name')
        validators = [
            UniqueTogetherValidator(
                queryset=DonorBudgetPeriod.objects.all(),
                fields=['start_date', 'end_date']
            )
        ]

    def validate(self, attrs):
        start_date = attrs.get('start_date')
        end_date = attrs.get('end_date')

        if start_date and end_date and start_date > end_date:
            raise serializers.ValidationError('Start date must be less than end date')

        return attrs

    def create(self, validated_data):
        return DonorBudgetPeriodService.create(validated_data)


class DonorBudgetPeriodUpdateSerializer(DonorBudgetPeriodCreateSerializer):
    """
    Serializer for updating DonorBudgetPeriod instances.
    """
    donor_budget_expected_results = DonorBudgetExpectedResultForPeriodSerializer(many=True, required=False)
    promo_type = serializers.ChoiceField(DonorBudgetExpectedResult.PromoType.choices, required=True)

    class Meta:
        model = DonorBudgetPeriod
        fields = ('start_date', 'end_date', 'donor_budget_expected_results', 'name', 'promo_type')
        validators = [
            UniqueTogetherValidator(
                queryset=DonorBudgetPeriod.objects.all(),
                fields=['start_date', 'end_date']
            )
        ]

    def update(self, instance, validated_data):
        return DonorBudgetPeriodService().update(instance=instance, data=validated_data)


class DonorBudgetPeriodListSerializer(serializers.ModelSerializer):
    """
    DonorBudgetPeriodListSerializer

    Serializer class for serializing and deserializing DonorBudgetPeriod objects.
    """
    class Meta:
        model = DonorBudgetPeriod
        fields = ('id', 'start_date', 'end_date', 'name')


class DonorBudgetExpectedResultSerializer(serializers.ModelSerializer):
    """
    Serializer class for DonorBudgetExpectedResult model.
    """
    donor_name = serializers.CharField(
        source='donor.name',
        read_only=True,
        allow_null=True
    )

    class Meta:
        model = DonorBudgetExpectedResult
        fields = (
            'id',
            'donor',
            'donor_budget_period',
            'cost_plan',
            'promo_type',
            'donor_name'
        )


class DonorBudgetExpectedResultCreateUpdateSerializer(DonorBudgetExpectedResultSerializer):
    def create(self, validated_data):
        return DonorBudgetExpectedResultService.create(validated_data)


class CopyDonorBudgetPeriodDetailData(serializers.Serializer):
    """
    Serializer for DonorBudgetPeriodDetail string representation
    """
    copy_data = serializers.CharField(max_length=100000)
