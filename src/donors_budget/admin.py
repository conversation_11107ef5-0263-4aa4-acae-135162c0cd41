from django.contrib import admin
from nested_inline.admin import NestedModelAdmin, NestedStackedInline

from donors_budget.models import (
    DonorBudgetExpectedResult,
    DonorBudgetFactResult,
    DonorBudgetPeriod,
)


class DonorBudgetFactResultInline(NestedStackedInline):
    """
    Inline class for DonorBudgetFactResult model
    """
    model = DonorBudgetFactResult
    extra = 1
    fk_name = 'donor_budget_expected_result'


class DonorBudgetExpectedResultInline(NestedStackedInline):
    """
    Inline class for DonorBudgetExpectedResult model
    """
    model = DonorBudgetExpectedResult
    extra = 1
    fk_name = 'donor_budget_period'
    inlines = [DonorBudgetFactResultInline]


@admin.register(DonorBudgetPeriod)
class BudgetPeriodAdmin(NestedModelAdmin):
    """
    Admin class for managing DonorBudgetPeriod in the admin panel.
    """
    list_display = ['start_date', 'end_date', 'name']
    ordering = ['start_date', 'end_date']
    inlines = [
        DonorBudgetExpectedResultInline,
    ]
