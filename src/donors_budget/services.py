from enum import Enum

from django.db import transaction
from django.db.models import Q, Sum
from django.db.models.functions import Coalesce

from ads.models import Ads
from donors_budget.models import (
    DonorBudgetExpectedResult,
    DonorBudgetFactResult,
    DonorBudgetPeriod,
)


class DonorBudgetService:
    """
    Service class for donor budget related operations
    """

    class DateFilterValue(str, Enum):
        """
        Enum for date filter values
        """

        DATE = "date"
        DATE_COUNTER = "date_counter"

    @classmethod
    def calculate_cost_for_budget_fact_result(
        cls,
        budget_fact_result: DonorBudgetFactResult,
        date_filter_value: DateFilterValue = DateFilterValue.DATE,
    ) -> int:
        """
        Calculate the cost for a given DonorBudgetFactResult instance.

        Args:
            budget_fact_result (BudgetFactResult): The BudgetFactResult instance to calculate the cost for.
            date_filter_value (DateFilterValue, optional): The date filter value to use. Defaults to DateFilterValue.DATE.

        Returns:
            float: The calculated result_cost.
        """
        date_filter = {
            f"{date_filter_value}__gte": budget_fact_result.donor_budget_expected_result.donor_budget_period.start_date,
            f"{date_filter_value}__lte": budget_fact_result.donor_budget_expected_result.donor_budget_period.end_date,
        }

        queryset = Ads.objects.filter(
            donor=budget_fact_result.donor_budget_expected_result.donor,
            department=Ads.DepartmentChoices.MARKETING,
            **date_filter,
        )

        expected_result_promo_type = (
            budget_fact_result.donor_budget_expected_result.promo_type
        )

        if expected_result_promo_type == DonorBudgetExpectedResult.PromoType.ALL:
            promo_type_values = DonorBudgetExpectedResult.PromoType.values
            promo_type_values.remove(DonorBudgetExpectedResult.PromoType.ALL.value)
            q_promo_types = Q()
            for promo_type in promo_type_values:
                q_promo_types |= Q(promo__name__iexact=promo_type)

            queryset = queryset.exclude(q_promo_types)
        else:
            queryset = queryset.filter(promo__name__iexact=expected_result_promo_type)

        total_cost = queryset.aggregate(
            total=Sum(Coalesce('phantom_cost', 'cost_result'))
        )['total']

        return round(total_cost) if total_cost else 0

    @classmethod
    def update_budget_fact_results(cls, ads: Ads) -> int:
        """
        Update donor budget fact results with the given Ads instance.

        Args:
            ads (Ads): The Ads instance to update.

        Returns:
            int: The number of rows affected by the update.
        """
        data_to_update = []

        if not all([ads.cost_result, ads.donor, ads.date]):

            return len(data_to_update)

        previous_date = getattr(ads, '__original_date', None) or ads.date
        previous_donor = (
            getattr(ads, '__original_donor', None) or ads.donor
        )

        budget_fact_results = list(
            DonorBudgetFactResult.objects.filter(
                Q(
                    donor_budget_expected_result__donor_budget_period__start_date__lte=ads.date,
                    donor_budget_expected_result__donor_budget_period__end_date__gte=ads.date,
                    donor_budget_expected_result__donor=ads.donor,
                )
                | Q(
                    donor_budget_expected_result__donor_budget_period__start_date__lte=previous_date,
                    donor_budget_expected_result__donor_budget_period__end_date__gte=previous_date,
                    donor_budget_expected_result__donor=previous_donor,
                )
            )
        )

        if not budget_fact_results:

            return len(data_to_update)

        for budget_fact_result in budget_fact_results:
            budget_fact_result.cost_fact = cls.calculate_cost_for_budget_fact_result(
                budget_fact_result, cls.DateFilterValue.DATE
            )
            budget_fact_result.cost_fact_date_counter = (
                cls.calculate_cost_for_budget_fact_result(
                    budget_fact_result, cls.DateFilterValue.DATE_COUNTER
                )
            )
            data_to_update.append(budget_fact_result)

        return DonorBudgetFactResult.objects.bulk_update(
            data_to_update, fields=['cost_fact', 'cost_fact_date_counter']
        )


class DonorBudgetExpectedResultService(DonorBudgetService):
    """
    Service class for budget expected result related operations
    """

    @classmethod
    def create(cls, data: dict) -> DonorBudgetExpectedResult:
        """
        Create DonorBudgetExpectedResult

        Args:
            data: serializer validated data
        """
        with transaction.atomic():
            new_expected_result = DonorBudgetExpectedResult.objects.create(**data)
            new_fact_result = DonorBudgetFactResult(
                donor_budget_expected_result=new_expected_result
            )
            new_fact_result.cost_fact = cls.calculate_cost_for_budget_fact_result(
                new_fact_result, cls.DateFilterValue.DATE
            )
            new_fact_result.cost_fact_date_counter = (
                cls.calculate_cost_for_budget_fact_result(
                    new_fact_result, cls.DateFilterValue.DATE_COUNTER
                )
            )
            new_fact_result.save()

        return new_expected_result


class DonorBudgetPeriodService(DonorBudgetService):
    """
    Service class for donor budget period related operations
    """

    @classmethod
    def create(cls, data: dict) -> DonorBudgetPeriod:
        """
        Create BudgetPeriod

        Args:
            data: serializer validated data
        """
        with transaction.atomic():

            donor_budget_expected_results = data.pop(
                'donor_budget_expected_results', None
            )
            donor_budget_period = DonorBudgetPeriod.objects.create(**data)

            if not donor_budget_expected_results:
                return donor_budget_period

            for expected_result_data in donor_budget_expected_results:
                expected_result_data[
                    'donor_budget_period'
                ] = donor_budget_period
                DonorBudgetExpectedResultService.create(expected_result_data)

            return donor_budget_period

    @staticmethod
    def update_budget_expected_result(
        budget_expected_result_data: dict,
        budget_expected_result_instance: DonorBudgetExpectedResult,
    ) -> None:
        """
        Update single DonorBudgetExpectedResult
        """
        is_updated = False

        for key, value in budget_expected_result_data.items():

            if getattr(budget_expected_result_instance, key) != value:
                setattr(budget_expected_result_instance, key, value)
                is_updated = True

        if is_updated:
            budget_expected_result_instance.save()

    def update(self, instance: DonorBudgetPeriod, data: dict) -> DonorBudgetPeriod:
        """
        Update DonorBudgetPeriod

        Args:
            instance: DonorBudgetPeriod instance to update
            data: serializer validated data
        """
        with transaction.atomic():
            updated = False

            budget_expected_results = data.pop('donor_budget_expected_results', [])
            promo_type = data.pop(
                'promo_type', DonorBudgetExpectedResult.PromoType.ALL
            )

            for key, value in data.items():
                if getattr(instance, key) != value:
                    setattr(instance, key, value)
                    updated = True

            if updated:
                instance.save()

            if not budget_expected_results:
                return instance

            instance_budget_expected_results = list(
                instance.donor_budget_expected_results.filter(
                    promo_type=promo_type
                )
            )

            for budget_expected_result_data in budget_expected_results:
                appropriate_instance_budget_expected_result = list(
                    filter(
                        (
                            lambda instance_expected_result: budget_expected_result_data.get(
                                'donor'
                            )
                            == instance_expected_result.donor
                        ),
                        instance_budget_expected_results,
                    )
                )

                if appropriate_instance_budget_expected_result:
                    appropriate_instance_budget_expected_result = (
                        appropriate_instance_budget_expected_result[0]
                    )
                    instance_budget_expected_results.remove(
                        appropriate_instance_budget_expected_result
                    )
                    self.update_budget_expected_result(
                        budget_expected_result_data=budget_expected_result_data,
                        budget_expected_result_instance=appropriate_instance_budget_expected_result,
                    )

                else:
                    budget_expected_result_data[
                        'donor_budget_period'
                    ] = instance
                    DonorBudgetExpectedResultService.create(
                        budget_expected_result_data
                    )

            if instance_budget_expected_results:
                instance.donor_budget_expected_results.filter(
                    id__in=[result.id for result in instance_budget_expected_results]
                ).delete()

            return instance

    @staticmethod
    def get_budget_period_detail_string(instance: DonorBudgetPeriod, date_filter: str) -> str:
        """
        Get string representation of donor budget period detail page

        Args:
            instance (DonorBudgetPeriod): DonorBudgetPeriod instance
            date_filter (str): date_filter value "date" or "date_counter"
        """
        headers = ['Model', 'Plan', 'Fact', 'Percent']
        string = '\t'.join(headers) + '\n'

        for expected_result in instance.donor_budget_expected_results.all():
            expected_result: DonorBudgetExpectedResult

            cost_fact = (
                expected_result.donor_budget_fact_result.cost_fact_date_counter
                if date_filter == 'date_counter'
                else expected_result.donor_budget_fact_result.cost_fact
            )

            string += (
                '\t'.join(
                    [
                        expected_result.donor.name,
                        str(expected_result.cost_plan),
                        str(cost_fact),
                        str(round(cost_fact / expected_result.cost_plan * 100)),
                    ]
                )
                + '\n'
            )

        return string
