from rest_framework import routers

from donors_budget.views import DonorBudgetExpectedResultViewSet, DonorBudgetPeriodViewSet

router = routers.DefaultRouter()
router.register(
    'periods',
    DonorBudgetPeriodViewSet,
    basename='donors-budget-period'
)
router.register(
    'expected-results',
    DonorBudgetExpectedResultViewSet,
    basename='donors-budget-expected-result'
)

urlpatterns = router.urls
