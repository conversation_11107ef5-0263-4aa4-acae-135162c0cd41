import json

from rest_framework.reverse import reverse

from ads.models import Ads, Donor
from base.tests.mixins import BaseCRMTest
from donors_budget.models import (
    DonorBudgetExpectedResult,
    DonorBudgetFactResult,
    DonorBudgetPeriod,
)


class TestDonorsBudgetViews(BaseCRMTest):
    def setUp(self):
        super().setUp()
        self.client.force_authenticate(user=self.user_hom)

        self.donor = Donor.objects.create(name='@testdonor')

        self.valid_donor_budget_period_data = {
            'start_date': '2023-12-01',
            'end_date': '2023-12-31',
            'donor_budget_expected_results': [{
                'cost_plan': 3333,
                'donor': str(self.donor.id),
            }]
        }

    def test_read_permissions_donor_budget_period(self):
        for user in self.users:
            self.client.force_authenticate(user=user)
            response = self.client.get(reverse('donors-budget-period-list'))

            if user.role.name in ['hom', 'marketer', 'superuser', 'financier', 'hof']:
                self.assertEqual(response.status_code, 200)
            else:
                self.assertEqual(response.status_code, 403)

    def test_write_permissions_donor_budget_period(self):
        data = {
            'start_date': '2023-12-01',
            'end_date': '2023-12-31',
        }

        for user in self.users:
            self.client.force_authenticate(user=user)
            response = self.client.post(reverse('donors-budget-period-list'), data=data)

            if user.role.name in ['hom']:
                self.assertEqual(response.status_code, 201)
            else:
                self.assertEqual(response.status_code, 403)

    def test_create_donor_budget_period(self):
        data = self.valid_donor_budget_period_data
        self.client.force_authenticate(user=self.user_hom)
        response = self.client.post(
            reverse('donors-budget-period-list'),  data=json.dumps(data), content_type='application/json'
        )
        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.data['start_date'], data['start_date'])
        self.assertEqual(response.data['end_date'], data['end_date'])
        self.assertEqual(
            response.data['donor_budget_expected_results'][0]['cost_plan'],
            data['donor_budget_expected_results'][0]['cost_plan']
        )
        self.assertEqual(
            response.data['donor_budget_expected_results'][0]['donor']['id'],
            data['donor_budget_expected_results'][0]['donor']
        )
        self.assertIn('cost_plan', response.data['donor_budget_expected_results'][0])

        expected_result = DonorBudgetExpectedResult.objects.get(
            id=response.data['donor_budget_expected_results'][0]["id"]
        )
        self.assertTrue(
            DonorBudgetFactResult.objects.filter(donor_budget_expected_result=expected_result).exists()
        )

    def test_ads_budget_fact_results_updated_if_donor_data_changed(self):
        only_fans_model = self.create_only_fans_model(1)

        december_period = DonorBudgetPeriod.objects.create(
            start_date='2023-12-01',
            end_date='2023-12-31',
        )
        december_expected_result = DonorBudgetExpectedResult.objects.create(
            donor=self.donor,
            donor_budget_period=december_period,
            cost_plan=1000
        )
        december_fact_result = DonorBudgetFactResult.objects.create(
            donor_budget_expected_result=december_expected_result,
            cost_fact=0
        )
        ads = Ads.objects.create(
            only_fans_model=only_fans_model,
            date='2023-12-01',
            cost=100,
            donor=self.donor
        )

        december_fact_result.refresh_from_db()
        self.assertEqual(december_fact_result.cost_fact, 100)

        january_period = DonorBudgetPeriod.objects.create(
            start_date='2024-01-01',
            end_date='2024-01-31',
        )
        january_expected_result = DonorBudgetExpectedResult.objects.create(
            donor_budget_period=january_period,
            cost_plan=1000,
            donor=self.donor
        )
        january_fact_result = DonorBudgetFactResult.objects.create(
            donor_budget_expected_result=january_expected_result,
            cost_fact=0
        )

        ads.date = '2024-01-01'
        ads.save()

        january_fact_result.refresh_from_db()
        december_fact_result.refresh_from_db()

        self.assertEqual(january_fact_result.cost_fact, 100)
        self.assertEqual(december_fact_result.cost_fact, 0)

    def test_donors_budget_retrieve_promo_type_filter(self):
        data = {
            'start_date': '2023-12-01',
            'end_date': '2023-12-31',
            'donor_budget_expected_results': [
                {
                    "cost_plan": 3333,
                    "donor": str(self.donor.id),
                    "promo_type": DonorBudgetExpectedResult.PromoType.ALL
                },
                {
                    "cost_plan": 3333,
                    "donor": str(self.donor.id),
                    "promo_type": DonorBudgetExpectedResult.PromoType.FRIENDS_FOR_MODEL
                }
            ]
        }
        self.client.force_authenticate(user=self.user_hom)
        response = self.client.post(
            reverse('donors-budget-period-list'), data=json.dumps(data), content_type='application/json'
        )
        self.assertEqual(response.status_code, 201)

        response = self.client.get(
            reverse('donors-budget-period-detail', kwargs={'pk': response.data['id']})
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data['donor_budget_expected_results']), 1)
        self.assertEqual(
            response.data['donor_budget_expected_results'][0]['promo_type'],
            DonorBudgetExpectedResult.PromoType.ALL
        )

        response = self.client.get(
            reverse('donors-budget-period-detail', kwargs={'pk': response.data['id']}),
            data={'promo_type': 'friends for model'}
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data['donor_budget_expected_results']), 1)
        self.assertEqual(
            response.data['donor_budget_expected_results'][0]['promo_type'],
            DonorBudgetExpectedResult.PromoType.FRIENDS_FOR_MODEL
        )
