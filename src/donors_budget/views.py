from django.db.models import Prefetch
from drf_spectacular.types import OpenApiTypes
from drf_spectacular.utils import OpenApiParameter, extend_schema
from rest_framework import mixins, viewsets
from rest_framework.decorators import action
from rest_framework.generics import get_object_or_404
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet

from base.mixins import BaseViewMethodsMixin, ListSerializerResponseMixin
from base.permissions import (
    IsHofOrFinancier,
    IsSuperUserOrHOMorMarketer,
    WriteOnlyForHOMRole,
)
from donors_budget.models import DonorBudgetExpectedResult, DonorBudgetPeriod
from donors_budget.serializers import (
    CopyDonorBudgetPeriodDetailData,
    DonorBudgetExpectedResultCreateUpdateSerializer,
    DonorBudgetExpectedResultSerializer,
    DonorBudgetPeriodCreateSerializer,
    DonorBudgetPeriodDetailSerializer,
    DonorBudgetPeriodListSerializer,
    DonorBudgetPeriodUpdateSerializer,
    NestedDonorBudgetExpectedResultSerializer,
)
from donors_budget.services import DonorBudgetPeriodService


@extend_schema(tags=['donors-budget - Donor Budget Periods'])
class DonorBudgetPeriodViewSet(BaseViewMethodsMixin, ListSerializerResponseMixin, ModelViewSet):
    serializer_class = DonorBudgetPeriodListSerializer
    permission_classes = (IsSuperUserOrHOMorMarketer | IsHofOrFinancier, WriteOnlyForHOMRole)
    action_serializers = {
        'list': DonorBudgetPeriodListSerializer,
        'retrieve': DonorBudgetPeriodDetailSerializer,
        'create': DonorBudgetPeriodCreateSerializer,
        'update': DonorBudgetPeriodUpdateSerializer,
        'partial_update': DonorBudgetPeriodUpdateSerializer,
        'copy_table': CopyDonorBudgetPeriodDetailData
    }
    LIST_SERIALIZER_CLASS = DonorBudgetPeriodDetailSerializer

    def get_queryset(self):
        queryset = DonorBudgetPeriod.objects.all()

        if self.action in ['retrieve', 'update', 'partial_update', 'copy_table']:
            if self.action in ['retrieve', 'copy_table']:
                promo_type = self.request.query_params.get('promo_type', DonorBudgetExpectedResult.PromoType.ALL)
            else:
                promo_type = self.request.data.get('promo_type', DonorBudgetExpectedResult.PromoType.ALL)

            queryset = queryset.prefetch_related(
                Prefetch(
                    'donor_budget_expected_results',
                    queryset=DonorBudgetExpectedResult.objects.filter(
                        promo_type=promo_type
                    )
                )
            )
            queryset = queryset.prefetch_related(
                'donor_budget_expected_results__donor',
                'donor_budget_expected_results__donor_budget_fact_result'
            )

        return queryset

    @extend_schema(
        request=DonorBudgetPeriodCreateSerializer,
        responses={201: DonorBudgetPeriodDetailSerializer}
    )
    def create(self, request, *args, **kwargs):
        """
        Create a new instance BudgetPeriod with nested BudgetExpectedResult instances
        and returns DonorBudgetPeriod detail serializer
        """
        return super().create(request, *args, **kwargs)

    @extend_schema(
        request=DonorBudgetPeriodUpdateSerializer,
        responses={200: DonorBudgetPeriodDetailSerializer}
    )
    def update(self, request, *args, **kwargs):
        """
        Update instance DonorBudgetPeriod with nested DonorBudgetExpectedResults
        and returns DonorBudgetPeriodDetailSerializer
        """
        partial = kwargs.pop('partial', False)
        instance = get_object_or_404(DonorBudgetPeriod, pk=kwargs['pk'])
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)

        self.perform_update(serializer)

        if getattr(instance, '_prefetched_objects_cache', None):
            # If 'prefetch_related' has been applied to a queryset, we need to
            # forcibly invalidate the prefetch cache on the instance.
            instance._prefetched_objects_cache = {}

        instance = self.get_object()
        serializer = self.LIST_SERIALIZER_CLASS(instance=instance, context=self.get_serializer_context())

        return Response(serializer.data)

    @extend_schema(
        request=DonorBudgetPeriodUpdateSerializer,
        responses={200: DonorBudgetPeriodDetailSerializer}
    )
    def partial_update(self, request, *args, **kwargs):
        """
        Partial update instance DonorBudgetPeriod with nested DonorBudgetExpectedResults
        and returns DonorBudgetPeriodDetailSerializer
        """
        return super().partial_update(request, *args, **kwargs)

    @extend_schema(
        parameters=[
            OpenApiParameter(
                name='promo_type',
                location=OpenApiParameter.QUERY,
                type=OpenApiTypes.STR,
                description='Filter by promo type',
                enum=DonorBudgetExpectedResult.PromoType
            ),
            OpenApiParameter(
                name='date_filter',
                location=OpenApiParameter.QUERY,
                type=OpenApiTypes.STR,
                description='Filter by date or date_counter',
                enum=["date", "date_counter"]
            )
        ]
    )
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

    def list(self, request, *args, **kwargs):
        """
        Redefine method for caching
        """
        return super().list(request, *args, **kwargs)

    @extend_schema(
        parameters=[
            OpenApiParameter(
                name='promo_type',
                location=OpenApiParameter.QUERY,
                type=OpenApiTypes.STR,
                description='Filter by promo type',
                enum=DonorBudgetExpectedResult.PromoType
            ),
            OpenApiParameter(
                name='date_filter',
                location=OpenApiParameter.QUERY,
                type=OpenApiTypes.STR,
                description='Filter by date or date_counter',
                enum=["date", "date_counter"]
            )
        ]
    )
    @action(methods=['get'], detail=True,  url_path='copy-table')
    def copy_table(self, request, *args, **kwargs):
        """
        Get donor budget period detail data string representation
        """
        instance = self.get_object()
        date_filter = request.query_params.get('date_filter', 'date')

        copy_table_string = DonorBudgetPeriodService().get_budget_period_detail_string(
            instance=instance, date_filter=date_filter
        )
        serializer = self.get_serializer(
            {
                'copy_data': copy_table_string
            }
        )

        return Response(serializer.data)


@extend_schema(tags=['donors-budget - Donor Budget Expected Results'])
class DonorBudgetExpectedResultViewSet(
    BaseViewMethodsMixin,
    ListSerializerResponseMixin,
    mixins.UpdateModelMixin,
    mixins.CreateModelMixin,
    mixins.DestroyModelMixin,
    viewsets.GenericViewSet
):
    queryset = DonorBudgetExpectedResult.objects.select_related('donor')
    serializer_class = DonorBudgetExpectedResultSerializer
    permission_classes = (IsSuperUserOrHOMorMarketer | IsHofOrFinancier, WriteOnlyForHOMRole)

    action_serializers = {
        'create': DonorBudgetExpectedResultCreateUpdateSerializer,
        'update': DonorBudgetExpectedResultCreateUpdateSerializer
    }

    LIST_SERIALIZER_CLASS = NestedDonorBudgetExpectedResultSerializer

    @extend_schema(responses={201: NestedDonorBudgetExpectedResultSerializer})
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)

    @extend_schema(responses={200: NestedDonorBudgetExpectedResultSerializer})
    def update(self, request, *args, **kwargs):
        return super().update(request, *args, **kwargs)

    @extend_schema(responses={200: NestedDonorBudgetExpectedResultSerializer})
    def partial_update(self, request, *args, **kwargs):
        return super().partial_update(request, *args, **kwargs)
