# Generated by Django 4.2.2 on 2025-05-16 13:37

import base.models
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("ads", "0047_ads_date_counter_extra_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="DonorBudgetExpectedResult",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "cost_plan",
                    models.PositiveIntegerField(
                        default=0, help_text="Budget cost plan"
                    ),
                ),
                (
                    "promo_type",
                    models.CharField(
                        choices=[
                            ("all", "All"),
                            ("friends for model", "Friends for model"),
                        ],
                        default="all",
                        help_text="Promo type",
                        max_length=30,
                    ),
                ),
            ],
            options={
                "db_table": "donor_budget_expected_result",
                "ordering": ["donor__name"],
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.CreateModel(
            name="DonorBudgetFactResult",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "cost_fact",
                    models.PositiveIntegerField(
                        default=0, help_text="Budget cost fact based on date"
                    ),
                ),
                (
                    "cost_fact_date_counter",
                    models.PositiveIntegerField(
                        blank=True,
                        default=0,
                        help_text="Budget cost fact based on date_counter",
                    ),
                ),
            ],
            options={
                "db_table": "donor_budget_fact_result",
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.CreateModel(
            name="DonorBudgetPeriod",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("start_date", models.DateField(help_text="Start date of the period")),
                ("end_date", models.DateField(help_text="End date of the period")),
                (
                    "name",
                    models.CharField(
                        blank=True,
                        help_text="Name of the period",
                        max_length=255,
                        null=True,
                    ),
                ),
            ],
            options={
                "db_table": "donor_budget_period",
                "ordering": ["-start_date"],
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.AddConstraint(
            model_name="donorbudgetperiod",
            constraint=models.UniqueConstraint(
                fields=("start_date", "end_date"), name="donor_budget_unique_period"
            ),
        ),
        migrations.AddField(
            model_name="donorbudgetfactresult",
            name="donor_budget_expected_result",
            field=models.OneToOneField(
                help_text="Budget fact result for budget expected result",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="donor_budget_fact_result",
                to="donors_budget.donorbudgetexpectedresult",
            ),
        ),
        migrations.AddField(
            model_name="donorbudgetexpectedresult",
            name="donor",
            field=models.ForeignKey(
                help_text="Donor",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="donor_budget_expected_results",
                to="ads.donor",
            ),
        ),
        migrations.AddField(
            model_name="donorbudgetexpectedresult",
            name="donor_budget_period",
            field=models.ForeignKey(
                help_text="Budget period of budget expected result",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="donor_budget_expected_results",
                to="donors_budget.donorbudgetperiod",
            ),
        ),
        migrations.AddConstraint(
            model_name="donorbudgetexpectedresult",
            constraint=models.UniqueConstraint(
                fields=("donor_budget_period", "donor", "promo_type"),
                name="unique_donor_budget_expected_result",
            ),
        ),
    ]
