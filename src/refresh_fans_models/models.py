from django.db import models
from django.utils.translation import gettext_lazy as _

from base.models import TimeStampedModel, TimeStampedUUIDModel


class RefreshTask(TimeStampedUUIDModel):
    """
    RefreshTask model to track the status of the task.
    """
    class StatusChoices(models.TextChoices):
        PROGRESS = 'progress'
        SUCCESS = 'success'
        ERROR = 'error'
        RETRY_LATER = 'retry_later'

    time_sent = models.DateTimeField(
        blank=True,
        null=True,
        help_text=_('Time when the task is sent')
    )
    time_completed = models.DateTimeField(
        blank=True,
        null=True,
        help_text=_('Time when the task is completed')
    )
    status = models.CharField(
        max_length=32,
        choices=StatusChoices.choices,
        default=StatusChoices.PROGRESS,
        help_text=_('Status of the task')
    )
    request_body = models.JSONField(
        blank=True,
        null=True,
        help_text=_('Request body sent for the refresh')
    )
    retries_count = models.IntegerField(
        default=0,
        help_text=_('Number of retries')
    )

    class Meta:
        ordering = ('-time_sent',)

    def __str__(self):
        return f'{self.time_sent} - {self.status}'


class Sale(TimeStampedModel):
    """
    Representation of Sale from onlyfans db
    """
    trans_id = models.CharField(
        primary_key=True,
        max_length=64,
        help_text=_('trans_id from onlyfans db'),
        unique=True
    )
    trans_date = models.DateTimeField(
        blank=True,
        null=True,
        help_text=_('trans_date from onlyfans db')
    )
    refresh_task = models.ForeignKey(
        'refresh_fans_models.RefreshTask',
        on_delete=models.PROTECT,
        related_name='sales',
    )
    fan_id = models.IntegerField()
    model_id = models.IntegerField()
    is_calculated = models.BooleanField(
        default=False,
        help_text=_('Define that sale was used in ads stats')
    )
    insert_date = models.DateTimeField(
        blank=True,
        null=True,
        help_text=_('insert_date from onlyfans db')
    )

    class Meta:
        ordering = ('-trans_date',)

    def __str__(self):
        return f'{self.trans_date} - {self.trans_id}'
