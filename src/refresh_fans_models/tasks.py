import datetime

from core.celery import BaseTaskWithRetry, app
from only_fans_db.models import Sales
from refresh_fans_models.models import RefreshTask, Sale
from refresh_fans_models.services import RefreshTaskService


@app.task(bind=BaseTaskWithRetry)
def refresh(
    self
):
    try:
        last_insert_date = Sale.objects.filter(insert_date__isnull=False).latest('insert_date').insert_date
    except Sale.DoesNotExist:
        last_insert_date = datetime.datetime(2024, 11, 27)

    sales = list(
        Sales.objects.filter(
            insert_date__isnull=False,
            insert_date__gt=last_insert_date,
            amount__isnull=False,
            fan__isnull=False,
            model__isnull=False
        ).order_by(
            'insert_date'
        ).only(
            'trans_id', 'trans_date', 'amount', 'fan', 'model', 'insert_date'
        )
    )

    if not sales:
        return "There are no new sales"

    task = RefreshTaskService().create(sales_from_of_db=sales)

    return (f"Task id: {task.id}\n"
            f"Sales count: {len(sales)}\n"
            "Created!")


@app.task(bind=BaseTaskWithRetry)
def retry_refresh(self):
    tasks_need_retry = RefreshTask.objects.filter(status=RefreshTask.StatusChoices.RETRY_LATER)

    if not tasks_need_retry:
        return "There are no tasks to retry"

    RefreshTaskService().retry_tasks(tasks_need_retry)

    return f"{len(tasks_need_retry)} sent to retry"
