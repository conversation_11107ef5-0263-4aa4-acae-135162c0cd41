# Generated by Django 4.2.2 on 2024-05-27 15:45

from django.db import migrations


def mark_sales_calculated(apps, schema_editor):
    Sale = apps.get_model("refresh_fans_models", "Sale")

    all_sales = list(Sale.objects.all())
    for sale in all_sales:
        sale.is_calculated = True

    Sale.objects.bulk_update(all_sales, ["is_calculated"],  batch_size=500)


def reverse_mark_sales_calculated(apps, schema_editor):
    pass


class Migration(migrations.Migration):

    dependencies = [
        (
            "refresh_fans_models",
            "0002_refreshtask_retries_count_sale_is_calculated_and_more",
        ),
    ]

    operations = [
        migrations.RunPython(mark_sales_calculated, reverse_mark_sales_calculated)
    ]

