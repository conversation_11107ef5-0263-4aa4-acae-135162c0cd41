# Generated by Django 4.2.2 on 2024-03-25 10:22

import uuid

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models

import base.models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="RefreshTask",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "time_sent",
                    models.DateTimeField(
                        blank=True, help_text="Time when the task is sent", null=True
                    ),
                ),
                (
                    "time_completed",
                    models.DateTimeField(
                        blank=True,
                        help_text="Time when the task is completed",
                        null=True,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("progress", "Progress"),
                            ("success", "Success"),
                            ("error", "Error"),
                        ],
                        default="progress",
                        help_text="Status of the task",
                        max_length=32,
                    ),
                ),
                (
                    "request_body",
                    models.JSONField(
                        blank=True,
                        help_text="Request body sent for the refresh",
                        null=True,
                    ),
                ),
            ],
            options={
                "ordering": ("-time_sent",),
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.CreateModel(
            name="Sale",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "trans_id",
                    models.CharField(
                        help_text="trans_id from onlyfans db",
                        max_length=64,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                (
                    "trans_date",
                    models.DateTimeField(
                        blank=True, help_text="trans_date from onlyfans db", null=True
                    ),
                ),
                ("fan_id", models.IntegerField()),
                ("model_id", models.IntegerField()),
                (
                    "refresh_task",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="sales",
                        to="refresh_fans_models.refreshtask",
                    ),
                ),
            ],
            options={
                "ordering": ("-trans_date",),
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
    ]
