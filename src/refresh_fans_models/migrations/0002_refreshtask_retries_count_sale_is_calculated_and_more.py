# Generated by Django 4.2.2 on 2024-05-27 15:45

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("refresh_fans_models", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="refreshtask",
            name="retries_count",
            field=models.IntegerField(default=0, help_text="Number of retries"),
        ),
        migrations.AddField(
            model_name="sale",
            name="is_calculated",
            field=models.BooleanField(
                default=False, help_text="Define that sale was used in ads stats"
            ),
        ),
        migrations.AlterField(
            model_name="refreshtask",
            name="status",
            field=models.CharField(
                choices=[
                    ("progress", "Progress"),
                    ("success", "Success"),
                    ("error", "Error"),
                    ("retry_later", "Retry Later"),
                ],
                default="progress",
                help_text="Status of the task",
                max_length=32,
            ),
        ),
    ]
