from django.shortcuts import get_object_or_404
from django.utils import timezone
from drf_spectacular.utils import extend_schema, inline_serializer
from rest_framework import serializers, status
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_api_key.permissions import <PERSON><PERSON><PERSON><PERSON><PERSON>

from refresh_fans_models.models import RefreshTask
from refresh_fans_models.serializers import RefreshTaskCallbackSerializer


@extend_schema(
    request=RefreshTaskCallbackSerializer,
    responses={
        200: inline_serializer(
            name='RefreshTaskCallbackResponse',
            fields={'message': serializers.CharField()},
        )
    },
)
class RefreshTaskCallbackView(APIView):
    """Endpoint for callback from refresh api"""
    permission_classes = [HasAPIKey]

    def post(self, request):
        serializer = RefreshTaskCallbackSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        task = get_object_or_404(RefreshTask, id=serializer.validated_data['task_id'])
        task.status = serializer.validated_data['status']
        task.time_completed = timezone.now()
        task.save()

        return Response({'message': 'OK'}, status=status.HTTP_200_OK)
