import json

import httpx
from django.conf import settings
from django.db import transaction
from django.utils import timezone

from base.tasks import send_bug_notification
from only_fans_db.models import Sales
from refresh_fans_models.models import RefreshTask, Sale


class RefreshTaskService:
    """
    Service for refreshing fans models
    """
    URL = f'{settings.OF_DB_SYNCHRO_URL}/update/fans'
    HEADERS = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {settings.OF_DB_SYNCHRO_TOKEN}'
    }

    def send_request(self, refresh_data: dict[str, str | list[dict[str, list[str]]]]):
        with httpx.Client() as client:
            try:
                response = client.post(self.URL, json=refresh_data, headers=self.HEADERS, timeout=60)
                response.raise_for_status()
            except httpx.HTTPStatusError as e:
                send_bug_notification.delay(
                    f'Refresh task failed. '
                    f'OF response status code: {e.response.status_code}. '
                    f'OF response text: {e.response.text}'
                    f'REFRESH_DATA: {refresh_data}',
                )
                raise e

    @staticmethod
    def generate_refresh_data(
            task_id: str,
            sales_from_of_db: list[Sales]
    ) -> dict[str, str | list[dict[str, list[str]]]]:
        """
        Generate refresh data from sales from OF
        """
        fans_models_data = {}

        for sale in sales_from_of_db:
            fans_models_data.setdefault(str(sale.model_id), set()).add(str(sale.fan_id))

        return {
            'task_id': str(task_id),
            'models': [
                {model_id: list(fans_ids)}
                for model_id, fans_ids in fans_models_data.items()
            ]

        }

    def create(self, sales_from_of_db: list[Sales]) -> RefreshTask | None:
        if not sales_from_of_db:
            return

        with transaction.atomic():
            task = RefreshTask.objects.create(time_sent=timezone.now())

            refresh_data = self.generate_refresh_data(
                task_id=task.id,
                sales_from_of_db=sales_from_of_db
            )
            self.send_request(refresh_data=refresh_data)

            sales = [
                Sale(
                    trans_id=sale_from_of_db.trans_id,
                    trans_date=sale_from_of_db.trans_date,
                    refresh_task=task,
                    fan_id=sale_from_of_db.fan_id,
                    model_id=sale_from_of_db.model_id,
                    insert_date=sale_from_of_db.insert_date,
                )
                for sale_from_of_db in sales_from_of_db
            ]

            Sale.objects.bulk_create(sales, ignore_conflicts=True, batch_size=500)
            RefreshTask.objects.filter(pk=task.pk).update(request_body=json.dumps(refresh_data))

            return task

    def retry_tasks(self, tasks_list: list[RefreshTask]):
        for task in tasks_list:
            refresh_data = json.loads(task.request_body)

            try:
                self.send_request(refresh_data=refresh_data)
            except httpx.HTTPStatusError:
                continue

            task.retries_count += 1
            task.save()
