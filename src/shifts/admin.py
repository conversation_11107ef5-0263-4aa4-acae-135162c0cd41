from django import forms
from django.contrib import admin
from simple_history.admin import SimpleHistoryAdmin

from base.mixins import ReadOnlyAdminMixin
from shifts.models import (
    DBSale,
    ScheduleMonitoring,
    ScheduleTeam,
    Shift,
    ShiftIndex,
    ShiftNumber,
    ShiftRating,
    ShiftResult,
    ShiftSale,
    TeamLeadABChat,
    TeamLeadABConfig,
)


@admin.register(ShiftIndex)
class ShiftIndexAdmin(admin.ModelAdmin):
    """
    Admin class for managing Shift Indexes in the admin panel.
    """
    pass


@admin.register(ShiftNumber)
class ShiftNumberAdmin(admin.ModelAdmin):
    """
    Admin class for managing Shift Numbers in the admin panel.
    """
    list_display = ('index', 'name', 'time_start', 'time_end')
    list_filter = ('index__number',)
    filter_horizontal = ('team_leads',)
    list_select_related = ('index',)
    search_fields = ('index__number', 'name')


class ShiftResultsInline(admin.TabularInline):
    model = ShiftResult
    extra = 3
    max_num = 0
    can_delete = False


class ShiftRatingInline(admin.TabularInline):
    model = ShiftRating
    extra = 3
    max_num = 0
    can_delete = False
    exclude = ('operator',)


@admin.register(Shift)
class ShiftAdmin(SimpleHistoryAdmin):
    list_display = (
        'shift_date',
        'status',
        'shift_number',
        'team_lead',
        'operator',
        'shift_start',
        'shift_end',
    )
    list_filter = ('team_lead',)
    inlines = (
        ShiftResultsInline,
        ShiftRatingInline,
    )
    autocomplete_fields = ('team_lead', 'operator', 'shift_number')
    search_fields = (
        'team_lead__first_name',
        'team_lead__last_name',
        'operator__first_name',
        'operator__last_name'
    )
    ordering = ('-shift_start', 'team_lead',)

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'shift_number__index', 'team_lead', 'operator'
        )


@admin.register(ScheduleTeam)
class ScheduleTeamAdmin(admin.ModelAdmin):
    """
    Admin class for managing ScheduleTeams
    """
    search_fields = ('only_fans_model__nickname', 'operator__first_name', 'operator__last_name')
    readonly_fields = ('only_fans_model', 'shift_index')

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        if db_field.name == "shift_number":
            kwargs["queryset"] = ShiftNumber.objects.select_related('index')

        return super().formfield_for_foreignkey(db_field, request, **kwargs)

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'shift_number__index',
            'operator',
            'only_fans_model',
            'shift_index'
        )


@admin.register(ScheduleMonitoring)
class ScheduleMonitoringAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    """
    Admin class for managing ScheduleMonitoring
    """
    list_filter = ('shift_result__only_fans_model',)
    list_select_related = ('shift_result__only_fans_model', 'shift_result__shift__shift_number__index')


@admin.register(DBSale)
class DBSaleAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    """
    Admin class for managing DBSale
    """
    list_display = ('trans_id', 'trans_date', 'amount', 'fan_username', 'model_id', 'sale_source', 'sale_type')
    list_filter = ('sale_source', 'sale_type')
    search_fields = ('trans_id', 'fan_username', 'model_id')
    ordering = ('-trans_date',)


@admin.register(ShiftSale)
class ShiftSaleAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    """
    Admin class for managing ShiftSale
    """
    list_display = ('shift', 'db_sale', 'amount')
    list_select_related = ('shift', 'db_sale')


class TeamLeadABChatInline(admin.TabularInline):
    """
    Inline class for managing TeamLeadABChat
    """
    model = TeamLeadABChat
    extra = 1


@admin.register(TeamLeadABConfig)
class TeamLeadABConfigAdmin(admin.ModelAdmin):
    """
    Admin class for managing TeamLeadABConfig
    """
    list_select_related = ('team_lead',)
    list_filter = ('team_lead',)
    inlines = (TeamLeadABChatInline,)

    def get_form(self, request, obj=None, **kwargs):
        kwargs['widgets'] = {
            'shift_numbers_plan': forms.TextInput(attrs={'placeholder': 'Ex. 35-30-35'})
        }
        return super().get_form(request, obj, **kwargs)
