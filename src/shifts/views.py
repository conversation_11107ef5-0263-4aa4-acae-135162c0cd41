from datetime import <PERSON><PERSON><PERSON>

import pytz
from django.db.models import F
from django.utils import timezone
from django_filters.rest_framework import DjangoFilterBackend
from drf_spectacular.types import OpenApiTypes
from drf_spectacular.utils import OpenApiParameter, extend_schema
from rest_framework import (
    mixins,
    status,
    viewsets,
)
from rest_framework.decorators import action
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.viewsets import GenericViewSet
from rest_framework_api_key.permissions import HasAP<PERSON><PERSON><PERSON>

from base.mixins import BaseViewMethodsMixin, ListSerializerResponseMixin
from base.pagination import ThirtyFivePerPagePagination
from base.permissions import (
    IsSpectator,
    IsTeamLeadOrSeniorOperator,
    IsTeamLeadOrSeniorOperatorOrOperator,
    IsTeamLeadOrSeniorOperatorOrOperatorOrSuperUser,
    IsTeamLeadOrSeniorOperatorOrSuperUser,
)
from finance.serializers import CurrentTimeSerializer
from shifts.filters import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ilt<PERSON>
from shifts.models import (
    ScheduleTeam,
    Shift,
    ShiftIndex,
    TeamLeadABChat,
    TeamLeadABConfig,
)
from shifts.permissions import CanDeleteShiftByTeamLeadOrSeniorOperator
from shifts.serializers import (
    AlarmBotTeamLeadChatsSerializer,
    AlarmBotTeamLeadMetricsSerializer,
    PaginatedShiftDateSerializer,
    ScheduleTeamCreateSerializer,
    ScheduleTeamListSerializer,
    ScheduleTeamUpdateSerializer,
    ShiftAlarmBotSerializer,
    ShiftCreateSerializer,
    ShiftIndexSerializer,
    ShiftListHistorySerializer,
    ShiftListSerializer,
    ShiftSerializer,
    ShiftStartEndResponseSerializer,
    ShiftUpdateSerializer,
)
from shifts.services import ShiftEventer, ShiftService
from shifts.spectecular_schemas import SHIFT_HISTORY_TOTAL_SCHEMA


class ShiftViewSet(BaseViewMethodsMixin, viewsets.ModelViewSet):
    """
    API endpoints for shifts.
    """
    serializer_class = ShiftSerializer
    http_method_names = ['get', 'post', 'delete', 'patch']

    action_serializers = {
        'create': ShiftCreateSerializer,
        'list': ShiftListSerializer,
        'update': ShiftUpdateSerializer,
        'partial_update': ShiftUpdateSerializer,
        'history': ShiftListHistorySerializer,
        'alarm_bot': ShiftAlarmBotSerializer
    }

    permission_classes = [IsTeamLeadOrSeniorOperatorOrSuperUser]
    action_permissions = {
        'create': [IsTeamLeadOrSeniorOperator, ],
        'list': [IsTeamLeadOrSeniorOperatorOrOperatorOrSuperUser | IsSpectator],
        'destroy': [CanDeleteShiftByTeamLeadOrSeniorOperator, ],
        'start_shift': [IsTeamLeadOrSeniorOperatorOrOperator, ],
        'end_shift': [IsTeamLeadOrSeniorOperatorOrOperator, ],
        'update': [IsTeamLeadOrSeniorOperator, ],
        'partial_update': [IsTeamLeadOrSeniorOperator, ],
        'history': [IsTeamLeadOrSeniorOperatorOrOperator | IsSpectator],
        'history_total': [IsTeamLeadOrSeniorOperatorOrSuperUser | IsSpectator],
        'alarm_bot': [HasAPIKey]
    }

    def get_queryset(self):
        """
        Get queryset for list action.
        """
        queryset = Shift.objects.all()

        if self.action not in ['list', 'history', 'history_total', 'alarm_bot']:
            return queryset

        action_status = {
            'list': ['progress', 'hold'],
            'history': ['end'],
            'history_total': ['end'],
            'alarm_bot': ['end', 'progress']
        }

        queryset = queryset.filter(
            status__in=action_status[self.action]
        ).select_related(
            'shift_number__index', 'operator', 'team_lead'
        ).prefetch_related(
            'stats__only_fans_model', 'only_fans_models', 'results'
        )

        if self.action == 'alarm_bot':
            today_date = timezone.now().date()

            return queryset.annotate(
                shift_end_delay=F("shift_end") + timedelta(minutes=20)
            ).filter(
                shift_end__date=today_date,
                shift_end_delay__lte=timezone.now(),
                team_lead__external=False
            ).prefetch_related('stats')

        if self.request.user.role.name == 'superuser' and self.action == 'list':
            return queryset.filter(status='progress')

        if self.request.user.role.name == 'team_lead':
            queryset = queryset.filter(team_lead=self.request.user)

        elif self.request.user.role.name == 'senior_operator':
            queryset = queryset.filter(team_lead=self.request.user.parent)

        elif self.request.user.role.name == 'operator':
            queryset = queryset.filter(operator=self.request.user)

        return queryset

    @extend_schema(responses=ShiftAlarmBotSerializer)
    @action(detail=False, methods=['get'], url_path='alarm-bot')
    def alarm_bot(self, request, *args, **kwargs):
        """
        Get shifts for alarm bot.
        """
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)

        return Response(serializer.data)

    @extend_schema(
        request=ShiftCreateSerializer,
        responses=ShiftListSerializer,
    )
    def create(self, request, *args, **kwargs):
        """
        Rewrite create method to get correct response data structure.
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)

        shift_instance = Shift.objects.get(id=serializer.data['id'])
        list_serializer = ShiftListSerializer(shift_instance, context={'request': request})

        headers = self.get_success_headers(serializer.data)

        ShiftEventer(shift=shift_instance).send_event_created()
        return Response(list_serializer.data, status=status.HTTP_201_CREATED, headers=headers)

    @extend_schema(
        request=ShiftUpdateSerializer,
        responses=ShiftListSerializer,
    )
    def update(self, request, *args, **kwargs):
        """
        Rewrite update method to get correct response data structure.
        """
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(
            instance,
            data=request.data,
            partial=partial
        )

        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        if getattr(instance, '_prefetched_objects_cache', None):
            # If 'prefetch_related' has been applied to a queryset, we need to
            # forcibly invalidate the prefetch cache on the instance.
            instance._prefetched_objects_cache = {}

        shift = Shift.objects.get(id=serializer.instance.id)
        list_serializer = ShiftListSerializer(shift)

        return Response(list_serializer.data)

    @extend_schema(
        request=ShiftUpdateSerializer,
        responses=ShiftListSerializer,
    )
    def partial_update(self, request, *args, **kwargs):
        """
        Rewrite partial_update method to add correct schema to the docs.
        """
        return super().partial_update(request, *args, **kwargs)

    def perform_create(self, serializer):
        """
        Set the team lead of the shift.
        """
        if self.request.user.role is None:
            return

        if self.request.user.role.name == 'team_lead':
            serializer.save(team_lead=self.request.user)

        elif self.request.user.role.name == 'senior_operator':
            serializer.save(team_lead=self.request.user.parent)

    @extend_schema(
        methods=['POST'],
        request=None,
        responses={
            200: ShiftStartEndResponseSerializer
        }
    )
    @action(detail=True, methods=['post'], url_path='start-shift')
    def start_shift(self, request, pk=None):
        """
        Start the shift.
        """
        shift = self.get_object()
        data = ShiftService(shift).start_shift()
        ShiftEventer(shift=shift).send_event_started()

        return Response(data, status=status.HTTP_200_OK)

    @extend_schema(
        methods=['POST'],
        request=None,
        responses={
            200: ShiftStartEndResponseSerializer
        }
    )
    @action(detail=True, methods=['post'], url_path='end-shift')
    def end_shift(self, request, pk=None):
        """
        End the shift.
        """
        shift = self.get_object()
        data = ShiftService(shift).end_shift()

        if data.get('message') == 'OK':
            ShiftEventer(shift=shift).send_event_ended()

        return Response(data, status=status.HTTP_200_OK)

    def destroy(self, request, *args, **kwargs):
        ShiftEventer(shift=self.get_object()).send_event_deleted()

        return super().destroy(request, *args, **kwargs)

    @extend_schema(
        parameters=[
            OpenApiParameter('page', OpenApiTypes.INT, OpenApiParameter.QUERY, description='Page number')
        ],
        responses={200: PaginatedShiftDateSerializer()},
    )
    @action(detail=False, methods=['get'], url_path='history')
    def history(self, request, pk=None):
        """
        List of history shifts with custom pagination.
        """
        filter_backend = ShiftsHistoryFilter()
        queryset = filter_backend.filter_queryset(
            queryset=self.get_queryset(),
            request=request,
            view=self
        )

        page_number = request.query_params.get('page') or 1
        serializer = self.get_serializer_class()
        paginated_data = ShiftService.get_paginated_history_data(
            shifts=queryset,
            page_number=page_number,
            serializer=serializer,
        )

        return Response(paginated_data)

    @extend_schema(**SHIFT_HISTORY_TOTAL_SCHEMA)
    @action(detail=False, methods=['get'], url_path='history/total')
    def history_total(self, request, pk=None):
        self.filter_backends = [ShiftsHistoryFilter]
        queryset = self.filter_queryset(
            queryset=self.get_queryset()
        )
        self.pagination_class = ThirtyFivePerPagePagination
        page = self.paginate_queryset(queryset)

        if page is not None:

            return self.get_paginated_response(ShiftService.get_history_total_data(page))

        return Response(ShiftService.get_history_total_data(queryset))


class ShiftIndexViewSet(
    mixins.ListModelMixin,
    GenericViewSet
):
    """
    API endpoints for shift index.
    """
    queryset = ShiftIndex.objects.all()
    serializer_class = ShiftIndexSerializer
    permission_classes = [IsTeamLeadOrSeniorOperatorOrSuperUser]


# ------------------------------------------------------Schedule Teams-------------------------------------------------
class ScheduleTeamViewSet(
    BaseViewMethodsMixin,
    ListSerializerResponseMixin,
    mixins.CreateModelMixin,
    mixins.ListModelMixin,
    mixins.UpdateModelMixin,
    viewsets.GenericViewSet
):
    """
    API endpoints for Schedule Teams
    """
    queryset = ScheduleTeam.objects.select_related(
        'only_fans_model',
        'shift_number',
        'operator',
        'replacement',
        'shift_index'
    )
    permission_classes = [IsTeamLeadOrSeniorOperatorOrSuperUser]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['only_fans_model']
    action_serializers = {
        'list': ScheduleTeamListSerializer,
        'create': ScheduleTeamCreateSerializer,
        'update': ScheduleTeamUpdateSerializer,
        'partial_update': ScheduleTeamUpdateSerializer,
    }

    @extend_schema(responses=ScheduleTeamListSerializer)
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)

    @extend_schema(responses=ScheduleTeamListSerializer)
    def update(self, request, *args, **kwargs):
        return super().update(request, *args, **kwargs)

    @extend_schema(responses=ScheduleTeamListSerializer)
    def partial_update(self, request, *args, **kwargs):
        return super().partial_update(request, *args, **kwargs)


# ------------------------------------------------------Alarm Bot-------------------------------------------------
class AlarmBotViewSet(
    viewsets.ViewSet
):
    """
    API endpoints for Alarm Bot
    """
    permission_classes = [HasAPIKey]
    action_serializers = {
        'team-leads-metrics': AlarmBotTeamLeadMetricsSerializer,
        'team-leads-chats': AlarmBotTeamLeadChatsSerializer,
    }

    @extend_schema(responses=AlarmBotTeamLeadMetricsSerializer)
    @action(detail=False, methods=['get'], url_path='team-leads-metrics')
    def team_leads_metrics(self, request, pk=None):
        """
        Get team leads metrics for alarm bot.
        """
        queryset = TeamLeadABConfig.objects.select_related('team_lead')
        serializer = AlarmBotTeamLeadMetricsSerializer(queryset, many=True)

        return Response(serializer.data)

    @extend_schema(responses=AlarmBotTeamLeadChatsSerializer)
    @action(detail=False, methods=['get'], url_path='team-leads-chats')
    def team_leads_chats(self, request, pk=None):
        """
        Get team leads chats for alarm bot.
        """
        queryset = TeamLeadABChat.objects.select_related('team_lead_ab_config__team_lead', 'shift_number_index')
        serializer = AlarmBotTeamLeadChatsSerializer(queryset, many=True)

        return Response(serializer.data)


# --------------------------------------------Server Time------------------------------------------------------
class CurrentTimeView(APIView):
    """
    API endpoint that allows to get current time
    """
    permission_classes = [AllowAny]
    serializer_class = CurrentTimeSerializer

    def get(self, request, *args, **kwargs):
        serializer = self.serializer_class(
            data={
                'kyiv': timezone.now(),
                'utc': timezone.now().astimezone(pytz.utc)
            }
        )
        serializer.is_valid(raise_exception=True)

        return Response(serializer.data)
