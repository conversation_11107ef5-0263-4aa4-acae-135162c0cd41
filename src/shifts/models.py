import datetime

from django.conf import settings
from django.core.exceptions import ValidationError
from django.db import models
from django.utils.translation import gettext_lazy as _
from simple_history.models import HistoricalRecords

from base.models import TimeStampedModel, TimeStampedUUIDModel


class ShiftIndex(TimeStampedUUIDModel):
    """
    Shift Index Model
    """
    number = models.IntegerField(
        help_text=_('Number'),
        unique=True,
    )

    class Meta:
        ordering = ['number']
        verbose_name = _('Shift index')
        verbose_name_plural = _('Shift indexes')

    def __str__(self):
        return f"{self.number}"


class ShiftNumber(TimeStampedUUIDModel):
    """
    Shift Number Model
    """
    team_leads = models.ManyToManyField(
        'accounts.User',
        help_text=_('Team leads'),
        verbose_name=_('Team leads'),
        related_name='shift_numbers',
        limit_choices_to={'role__name': 'team_lead'},
        blank=True
    )
    name = models.CharField(
        max_length=100,
        help_text=_('Name'),
    )
    index = models.ForeignKey(
        'shifts.ShiftIndex',
        related_name='shift_numbers',
        on_delete=models.SET_NULL,
        null=True,
        help_text=_('Reference to Shift index'),
    )
    time_start = models.TimeField(
        help_text=_('Start time'),
    )
    time_end = models.TimeField(
        help_text=_('End time'),
    )

    def __str__(self):
        return f"{str(self.index)} {self.name}"

    def clean(self):
        if self.time_start == self.time_end:
            raise ValidationError(
                _('Start time can not be equal to end time')
            )


class Shift(TimeStampedUUIDModel):
    """
    Shift Model
    """
    STATUS_CHOICES = [
        ('hold', 'Ожидает начала'),
        ('end', 'Закончена'),
        ('overdue', 'Опоздание'),
        ('progress', 'В работе'),
    ]
    shift_date = models.DateField(
        help_text=_('Shift date')
    )
    status = models.CharField(
        choices=STATUS_CHOICES,
        max_length=100,
        default='hold',
        help_text=_('Status from choices(hold, end, overdue, progress)'),
    )
    shift_number = models.ForeignKey(
        'shifts.ShiftNumber',
        related_name='shifts',
        on_delete=models.SET_NULL,
        null=True,
        help_text=_('Reference to Shift number'),
    )
    shift_start = models.DateTimeField(
        help_text=_('Shift number start time'),
    )
    shift_end = models.DateTimeField(
        help_text=_('Shift number end time'),
    )
    shift_start_fact = models.DateTimeField(
        help_text=_('Shift fact start time'),
        blank=True,
        null=True
    )
    shift_end_fact = models.DateTimeField(
        help_text=_('Shift fact end time'),
        blank=True,
        null=True
    )
    shift_duration = models.DurationField(
        help_text=_('Shift duration'),
        null=True,
        blank=True
    )
    team_lead = models.ForeignKey(
        'accounts.User',
        help_text=_('Team lead'),
        related_name='team_lead_shifts',
        limit_choices_to={'role__name': 'team_lead'},
        on_delete=models.SET_NULL,
        null=True
    )
    operator = models.ForeignKey(
        'accounts.User',
        help_text=_('Operator'),
        related_name='operator_shifts',
        limit_choices_to={'role__name': 'operator'},
        on_delete=models.SET_NULL,
        null=True,
    )
    only_fans_models = models.ManyToManyField(
        'only_fans_models.OnlyFansModel',
        help_text=_('Only fans models'),
        related_name='shifts',
        through='shifts.ShiftResult'
    )
    json_info = models.JSONField(
        help_text=_('Shift result'),
        blank=True,
        null=True
    )
    operator_name = models.CharField(
        max_length=255,
        help_text=_('Operator full name'),
        blank=True,
        null=True
    )
    is_updated = models.BooleanField(
        default=False,
        help_text=_('Information about shift has fully updated statistics'),
    )
    date_updated_stats = models.DateTimeField(
        help_text=_('Date when shift statistics was updated'),
        blank=True,
        null=True
    )
    history = HistoricalRecords()

    class Meta:
        ordering = [
            'shift_start'
        ]

    def __str__(self):
        return f"{self.shift_date} {self.shift_number}"

    @property
    def duration(self):
        """
        Calculate shift duration
        """
        if self.shift_start_fact and self.shift_end_fact:
            return self.shift_end_fact - self.shift_start_fact

        return datetime.timedelta(0)


class ShiftResult(TimeStampedUUIDModel):
    shift = models.ForeignKey(
        'shifts.Shift',
        related_name='results',
        on_delete=models.CASCADE
    )
    only_fans_model = models.ForeignKey(
        'only_fans_models.OnlyFansModel',
        related_name='results',
        on_delete=models.SET_NULL,
        blank=True,
        null=True
    )
    shift_start_fact = models.DateTimeField(
        help_text=_('Shift Result fact start time'),
        blank=True,
        null=True
    )
    shift_end_fact = models.DateTimeField(
        help_text=_('Shift Result fact end time'),
        blank=True,
        null=True
    )

    def __str__(self):
        return str(self.shift)


class ShiftRating(TimeStampedUUIDModel):
    shift = models.ForeignKey(
        'shifts.Shift',
        related_name='ratings',
        on_delete=models.SET_NULL,
        null=True,
        help_text=_('Shift')
    )
    operator = models.ForeignKey(
        'accounts.User',
        limit_choices_to={'role__name': 'operator'},
        related_name='ratings',
        on_delete=models.SET_NULL,
        null=True,
        help_text=_('Operator')
    )
    only_fans_model = models.ForeignKey(
        'only_fans_models.OnlyFansModel',
        related_name='ratings',
        on_delete=models.SET_NULL,
        null=True,
        help_text=_('Only fans model')
    )
    sales_amount = models.IntegerField(
        help_text=_('Sales amount'),
        default=0
    )
    sales_ratio = models.DecimalField(
        help_text=_('Sales ratio'),
        default=0,
        max_digits=5,
        decimal_places=1
    )
    chats_count = models.IntegerField(
        help_text=_('Chats ratio'),
        default=0
    )
    response_ratio = models.DecimalField(
        help_text=_('Response ratio'),
        default=0,
        max_digits=5,
        decimal_places=1
    )
    operator_ratio = models.DecimalField(
        help_text=_('Operator ratio'),
        default=0,
        max_digits=5,
        decimal_places=1
    )


class ResultStats(TimeStampedUUIDModel):
    shift = models.ForeignKey(
        'shifts.Shift',
        related_name='stats',
        on_delete=models.SET_NULL,
        null=True,
        help_text=_('Shift')
    )
    operator = models.ForeignKey(
        'accounts.User',
        limit_choices_to={'role__name': 'operator'},
        related_name='stats',
        on_delete=models.SET_NULL,
        null=True,
        help_text=_('Operator')
    )
    only_fans_model = models.ForeignKey(
        'only_fans_models.OnlyFansModel',
        related_name='stats',
        on_delete=models.SET_NULL,
        null=True,
        help_text=_('Only fans model')
    )
    revenue = models.IntegerField(
        help_text=_('Total sales revenue'),
        default=0
    )
    outgoing_messages = models.IntegerField(
        help_text=_('Number of outgoing messages'),
        default=0
    )
    revenue_tingz = models.IntegerField(
        help_text=_('Total sales revenue in tingz'),
        default=0
    )
    incoming_messages = models.IntegerField(
        help_text=_('Number of incoming messages'),
        default=0
    )
    messages_with_content = models.IntegerField(
        help_text=_('Number of messages with content'),
        default=0
    )
    messages_with_paid_content = models.IntegerField(
        help_text=_('Number of messages with paid content'),
        default=0
    )
    paid_messages = models.IntegerField(
        help_text=_('Number of paid messages'),
        default=0
    )
    chats_with_payments = models.IntegerField(
        help_text=_('Number of chats with payments'),
        default=0
    )
    outgoing_symbols = models.IntegerField(
        help_text=_('Number of outgoing symbols'),
        default=0
    )
    total_chats = models.IntegerField(
        help_text=_('Total number of chats'),
        default=0
    )
    active_chats = models.IntegerField(
        help_text=_('Number of active chats'),
        default=0
    )
    replied_chats = models.IntegerField(
        help_text=_('Number of replied chats'),
        default=0
    )
    not_replied_chats = models.IntegerField(
        help_text=_('Number of not replied chats'),
        default=0
    )
    chats_with_paid_content = models.IntegerField(
        help_text=_('Number of chats with paid content'),
        default=0
    )
    chats_tingz = models.IntegerField(
        help_text=_('Number of chats in tingz'),
        default=0
    )
    time_to_respond_by_message = models.DurationField(
        help_text=_('Average speed answer'),
        default=datetime.timedelta(0)
    )
    time_to_respond_by_chat = models.DurationField(
        help_text=_('Average speed answer by chat'),
        default=datetime.timedelta(0)
    )
    afk_summ = models.DurationField(
        help_text=_('Total time of afk'),
        default=datetime.timedelta(0)
    )
    max_afk_duration = models.DurationField(
        help_text=_('Max afk duration'),
        default=datetime.timedelta(0)
    )
    median_time_to_respond_by_message = models.DurationField(
        help_text=_('Median speed answer time'),
        default=datetime.timedelta(0)
    )
    median_time_to_respond_by_chat = models.DurationField(
        help_text=_('Median speed answer time by chat'),
        default=datetime.timedelta(0)
    )
    payments_count = models.IntegerField(
        help_text=_('Number of payments'),
        default=0
    )
    new_fans_count = models.IntegerField(
        help_text=_('Number of new fans who made purchases during the shift'),
        default=0
    )
    old_fans_count = models.IntegerField(
        help_text=_('Number of old fans who made purchases during the shift'),
        default=0
    )
    new_fans_sales = models.IntegerField(
        help_text=_('Total sales of new fans during the shift'),
        default=0
    )
    old_fans_sales = models.IntegerField(
        help_text=_('Total sales of old fans during the shift'),
        default=0
    )

    @property
    def chats_active_to_replied(self) -> float:
        if self.replied_chats:
            return round((self.active_chats / self.replied_chats) * 100, 2)
        return 0

    @property
    def chats_active_to_with_payments(self) -> float:
        if self.active_chats:
            return round((self.chats_with_payments / self.active_chats) * 100, 2)
        return 0

    @property
    def revenue_per_fan(self) -> float:
        if self.chats_with_payments:
            return round((self.revenue + self.revenue_tingz) / self.chats_with_payments, 2)
        return 0

    @property
    def average_bill(self):
        if self.payments_count:
            return round((self.revenue + self.revenue_tingz) / self.payments_count, 2)
        return 0

    @property
    def average_payments(self):
        if self.chats_with_payments:
            return round(self.payments_count / self.chats_with_payments, 2)
        return 0


class OperatorProfit(TimeStampedUUIDModel):
    shift = models.ForeignKey(
        'shifts.Shift',
        related_name='operators_profits',
        on_delete=models.SET_NULL,
        null=True,
        help_text=_('Shift')
    )
    shift_date = models.DateField(
        help_text=_('Shift date')
    )
    operator = models.ForeignKey(
        'accounts.User',
        limit_choices_to={'role__name': 'operator'},
        related_name='operator_profits',
        on_delete=models.SET_NULL,
        null=True,
        help_text=_('Operator')
    )
    operator_name = models.CharField(
        max_length=255,
        help_text=_('Operator full name')
    )
    only_fans_model = models.ForeignKey(
        'only_fans_models.OnlyFansModel',
        related_name='operators_profits',
        on_delete=models.SET_NULL,
        null=True,
        help_text=_('Only fans model')
    )
    only_fans_model_name = models.CharField(
        max_length=255,
        help_text=_('Only fans model full name')
    )
    team_lead = models.ForeignKey(
        'accounts.User',
        limit_choices_to={'role__name': 'team_lead'},
        related_name='team_lead_operators_profits',
        on_delete=models.SET_NULL,
        null=True,
        help_text=_('Team lead')
    )
    team_lead_name = models.CharField(
        max_length=255,
        help_text=_('Team lead full name')
    )
    model_category = models.ForeignKey(
        'only_fans_models.ModelCategory',
        related_name='operators_profits',
        on_delete=models.SET_NULL,
        null=True,
        help_text=_('Model category')
    )
    model_category_name = models.CharField(
        max_length=255,
        help_text=_('Model category name')
    )
    of_amount = models.IntegerField(
        help_text=_('Only fans amount'),
    )
    of_profit = models.IntegerField(
        help_text=_('Only fans profit'),
    )
    tingz_amount = models.IntegerField(
        help_text=_('Tingz amount'),
    )
    tingz_profit = models.IntegerField(
        help_text=_('Tingz profit'),
    )
    total_amount = models.IntegerField(
        help_text=_('Total amount'),
    )
    total_profit = models.IntegerField(
        help_text=_('Total profit'),
    )


# ----------------------------------Scheduling---------------------------------------
class ScheduleTeam(TimeStampedUUIDModel):
    """
    Schedule Team model
    """
    only_fans_model = models.ForeignKey(
        'only_fans_models.OnlyFansModel',
        related_name='schedule_teams',
        on_delete=models.RESTRICT,
        help_text=_('Reference to OnlyFansModel')
    )
    shift_index = models.ForeignKey(
        'shifts.ShiftIndex',
        related_name='schedule_teams',
        on_delete=models.RESTRICT,
        help_text=_('Reference to Shift index'),
        null=True,
    )
    shift_number = models.ForeignKey(
        'shifts.ShiftNumber',
        related_name='schedule_teams',
        on_delete=models.RESTRICT,
        null=True,
        blank=True,
        help_text=_('Reference to Shift number'),
    )
    operator = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        limit_choices_to={'role__name': 'operator'},
        related_name='operator_schedule_team',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text=_('Main Operator'),
    )
    replacement = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        limit_choices_to={'role__name': 'operator'},
        related_name='replacement_schedule_teams',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text=_('Operator for replacement'),
    )

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=['only_fans_model', 'shift_index'],
                name='unique_only_fans_model_shift_index'
            ),
        ]

    def __str__(self):
        return f'{self.only_fans_model.nickname}-{str(self.shift_number)}-{str(self.operator)}'

    def clean(self):
        if self.replacement and self.operator and self.replacement == self.operator:
            raise ValidationError(
                {'replacement': _('Replacement operator can not be the same as main operator')}
            )

        if getattr(self, 'shift_number', None) and self.shift_number.index != self.shift_index:
            raise ValidationError(
                {'shift_number': _("'Shift number's index must be the same as shift_index")}
            )

    def save(self, *args, **kwargs):
        self.full_clean()
        super().save(*args, **kwargs)


class ScheduleMonitoring(TimeStampedUUIDModel):
    """
    Schedule Monitoring model for monitoring shift number, operator and replacement of the shift
    """
    shift_result = models.OneToOneField(
        'shifts.ShiftResult',
        related_name='monitoring',
        on_delete=models.CASCADE,
        help_text=_('Reference to ShiftResult'),
    )
    shift_number = models.BooleanField(
        help_text=_('Shift number is valid'),
        null=True
    )
    operator = models.BooleanField(
        help_text=_('Operator is valid'),
        null=True
    )
    replacement = models.BooleanField(
        help_text=_('Replacement is valid'),
        null=True
    )

    def __str__(self):
        return f'{self.shift_result.shift}-{self.shift_result.only_fans_model}'


class DBSale(TimeStampedModel):
    """
    DB Sale model
    """
    class SaleSource(models.TextChoices):
        ONLYFANS = 'onlyfans', _('OnlyFans')
        TINGZ = 'tingz', _('Tingz')

    class SaleType(models.IntegerChoices):
        SUBSCRIPTION = 1, _('Subscription')
        TIP = 2, _('Tip')
        PAID_MESSAGE = 3, _('Paid message')

    trans_id = models.CharField(
        max_length=255,
        help_text=_('Transaction ID'),
        primary_key=True
    )
    trans_date = models.DateTimeField(
        help_text=_('Transaction date'),
    )
    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text=_('Amount')
    )
    fan_username = models.CharField(
        max_length=255,
        help_text=_('Fan username'),
        blank=True
    )
    fan_id = models.BigIntegerField(
        help_text=_('Fan ID (OF)'),
        blank=True,
        null=True
    )
    model_id = models.BigIntegerField(
        help_text=_('Model ID (OF)'),
        blank=True,
        null=True
    )
    model_username = models.CharField(
        max_length=255,
        help_text=_('Model username (Tingz)'),
        blank=True
    )
    sale_source = models.CharField(
        max_length=20,
        choices=SaleSource.choices,
        help_text=_('Sale source')
    )
    sale_type = models.IntegerField(
        choices=SaleType.choices,
        help_text=_('Sale type'),
        null=True
    )
    insert_date = models.DateTimeField(
        help_text=_('Insert date'),
        null=True
    )

    class Meta:
        ordering = ['-trans_date']
        indexes = [
            models.Index(fields=['trans_date', 'model_id', 'sale_source']),
            models.Index(fields=['trans_date', 'model_username', 'sale_source']),
        ]

    def __str__(self):
        return f'{self.trans_id}-{self.fan_username}'


class ShiftSale(TimeStampedUUIDModel):
    shift = models.ForeignKey(
        'shifts.Shift',
        related_name='sales',
        on_delete=models.SET_NULL,
        help_text=_('Reference to Shift'),
        null=True
    )
    db_sale = models.ForeignKey(
        'shifts.DBSale',
        related_name='shift_sales',
        on_delete=models.CASCADE,
        help_text=_('Reference to DBSale'),
    )
    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text=_('Amount')
    )

    def __str__(self):
        return f'{self.shift_id}-{self.amount}'


# ToDo implement this with swap feature
# class SwapSale(TimeStampedUUIDModel):
#     """
#     Swap Sale model
#     """
#     request_shift = models.ForeignKey(
#         'shifts.Shift',
#         related_name='swap_requests',
#         on_delete=models.CASCADE,
#         help_text=_('Reference to Shift swap from'),
#     )
#     target_shift = models.ForeignKey(
#         'shifts.Shift',
#         related_name='swap_targets',
#         on_delete=models.CASCADE,
#         help_text=_('Reference to Shift swap to'),
#     )
#     shift_sale = models.ForeignKey(
#         'shifts.ShiftSale',
#         related_name='swap_tables',
#         on_delete=models.CASCADE,
#         help_text=_('Reference to ShiftSale'),
#     )
#     request = models.ForeignKey(
#         ...
#     )
#


# ToDo implement this with split feature
# class SplitSale(TimeStampedUUIDModel):
#     """
#     Split Sale model
#     """
#     shift_sale = models.ForeignKey(
#         'shifts.ShiftSale',
#         related_name='split_sales',
#         on_delete=models.CASCADE,
#         help_text=_('Reference to ShiftSale'),
#     )
#     target_shift = models.ForeignKey(
#         'shifts.Shift',
#         related_name='split_targets',
#         on_delete=models.CASCADE,
#         help_text=_('Reference to Shift split to'),
#     )
#     amount = models.DecimalField(
#         max_digits=10,
#         decimal_places=2,
#         help_text=_('Amount')
#     )
#     request = models.ForeignKey(
#         ...
#     )


# -----------------------------------------------------------Alarm bot------------------------------------------------
class TeamLeadABConfig(TimeStampedUUIDModel):
    """
    Model for Team Lead Alarm Bot configuration which contains metrics
    """
    team_lead = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        limit_choices_to={'role__name': 'team_lead'},
        related_name='ab_config',
        on_delete=models.CASCADE,
        help_text=_('Reference to Team Lead'),
    )
    max_speed_answer_timedelta = models.DurationField(
        help_text=_('Max speed answer timedelta'),
    )
    min_replied_conversion = models.FloatField(
        help_text=_('Min replied conversion'),
    )
    min_payment_conversion = models.FloatField(
        help_text=_('Min payment conversion'),
    )
    min_revenue_per_fan = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        help_text=_('Min revenue per fan'),
    )
    shift_numbers_plan = models.CharField(
        max_length=63,
        help_text=_('Shift numbers plan'),
        blank=True,
        null=True,
    )

    def __str__(self):
        return f'{self.team_lead.full_name} - {self.team_lead.telegram_id}'

    def clean(self):
        if self.shift_numbers_plan:
            plans = self.shift_numbers_plan.split('-')

            plans_sum = 0
            for plan in plans:
                if not plan.isdigit():
                    raise ValidationError(
                        {'shift_numbers_plan': _('Shift numbers plan must contain only digits separated by "-"')}
                    )
                plans_sum += int(plan)

            if plans_sum != 100:
                raise ValidationError(
                    {'shift_numbers_plan': _('Shift numbers plan must sum up to 100')}
                )

            chats_count = self.chats.count()

            if chats_count and len(plans) != chats_count:
                raise ValidationError(
                    {'shift_numbers_plan': _('Shift numbers plan must contain the same number of plans as chats')}
                )

    def save(self, *args, **kwargs):
        self.full_clean()
        super().save(*args, **kwargs)


class TeamLeadABChat(TimeStampedUUIDModel):
    """
    Model for Team Lead Alarm Bot chat
    """
    team_lead_ab_config = models.ForeignKey(
        'shifts.TeamLeadABConfig',
        related_name='chats',
        on_delete=models.CASCADE,
        help_text=_('Reference to Team Lead AB config'),
    )
    forum_id = models.BigIntegerField(
        help_text=_('Telegram forum ID'),
    )
    shift_number_index = models.ForeignKey(
        'shifts.ShiftIndex',
        related_name='ab_chats',
        on_delete=models.CASCADE,
        help_text=_('Reference to Shift number index'),
    )
    thread_id = models.BigIntegerField(
        help_text=_('Telegram thread ID'),
    )

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=['forum_id', 'thread_id'], name='unique_thread_id'
            ),
            models.UniqueConstraint(
                fields=['team_lead_ab_config', 'shift_number_index'], name='unique_shift_number_index'
            ),
        ]

    def __str__(self):
        return f'{self.team_lead_ab_config.team_lead.full_name}: {self.forum_id} - {self.thread_id}'
