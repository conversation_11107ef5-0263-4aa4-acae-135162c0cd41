from django.urls import path
from rest_framework import routers

from shifts.views import (
    AlarmBotViewSet,
    CurrentTimeView,
    ScheduleTeamViewSet,
    ShiftIndexViewSet,
    ShiftViewSet,
)

router = routers.DefaultRouter()
router.register(r'shifts', ShiftViewSet, basename='shifts')
router.register(r'schedule-teams', ScheduleTeamViewSet, basename='schedule-teams')
router.register(r'shift-indexes', ShiftIndexViewSet, basename='shift-indexes')
router.register('alarm-bot', AlarmBotViewSet, basename='alarm-bot')

urlpatterns = router.urls

urlpatterns.append(
    path('current-time/', CurrentTimeView.as_view(), name='current-time')
)
