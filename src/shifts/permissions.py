from rest_framework import permissions

from shifts.validators import ShiftValidator


class CanDeleteShiftByTeamLeadOrSeniorOperator(permissions.BasePermission):
    """
    Custom permission to only allow team leads and senior operators to delete shifts.
    """
    def has_permission(self, request, view):
        user = request.user

        if user.is_authenticated and user.role.name in ('team_lead', 'senior_operator'):
            return True

        return False

    def has_object_permission(self, request, view, obj):
        """
        Check if the user is a team lead and status of shift is hold or overdue.
        """
        user = request.user

        if user.is_authenticated and user.role.name in ('team_lead', 'senior_operator'):
            ShiftValidator(obj).validate_delete_shift()

            return True

        return False
