from datetime import timedelta

from django.utils import timezone
from rest_framework.filters import BaseFilterBackend


class ShiftsHistoryFilter(BaseFilterBackend):
    """
    Custom filter for ShiftsHistory
    """
    @staticmethod
    def filter_by_date(request, queryset):
        date_after = request.query_params.get('date_after')
        date_before = request.query_params.get('date_before')

        if date_after:
            queryset = queryset.filter(shift_date__gte=date_after)

        if date_before:
            queryset = queryset.filter(shift_date__lte=date_before)

        if not date_after and not date_before:
            queryset = queryset.filter(shift_date__gte=timezone.now().date() - timedelta(days=30))

        return queryset

    @staticmethod
    def filter_by_only_fans_models(request, queryset):
        only_fans_models = request.query_params.get('only_fans_models')

        if only_fans_models:
            queryset = queryset.filter(only_fans_models__in=only_fans_models.split(','))

        return queryset

    @staticmethod
    def filter_by_shift_numbers(request, queryset):
        shift_numbers = request.query_params.get('shift_numbers')

        if shift_numbers:
            queryset = queryset.filter(shift_number_id__in=shift_numbers.split(','))

        return queryset

    @staticmethod
    def filter_by_operators(request, queryset):
        operators = request.query_params.get('operators')

        if operators:
            queryset = queryset.filter(operator_id__in=operators.split(','))

        return queryset

    @staticmethod
    def filter_by_team_leads(request, queryset):
        team_leads = request.query_params.get('team_leads')

        if team_leads:
            queryset = queryset.filter(team_lead_id__in=team_leads.split(','))

        return queryset

    def filter_queryset(self, request, queryset, view):
        if view.action not in ['history', 'history_total']:
            return queryset

        queryset = queryset.order_by('-shift_end', 'shift_number')

        if view.action == 'history':
            return queryset.filter(shift_date__gte=timezone.now().date() - timedelta(days=15))

        queryset = self.filter_by_date(request, queryset)
        queryset = self.filter_by_only_fans_models(request, queryset)
        queryset = self.filter_by_shift_numbers(request, queryset)
        queryset = self.filter_by_operators(request, queryset)
        queryset = self.filter_by_team_leads(request, queryset)

        return queryset
