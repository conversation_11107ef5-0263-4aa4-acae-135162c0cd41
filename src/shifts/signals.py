from django.db.models.signals import m2m_changed, post_save
from django.dispatch import receiver

from accounts.models import User, UserOperatorProxy
from only_fans_models.models import OnlyFansModel
from shifts.models import Shift
from shifts.services import ScheduleMonitoringService, ScheduleTeamService


@receiver(m2m_changed,  sender=Shift.only_fans_models.through)
@receiver([post_save], sender=Shift)
def write_shift_monitoring(sender, instance, *args, **kwargs):
    if (
            sender is Shift.only_fans_models.through
            and 'pre' in kwargs.get('action', '')
            or 'remove' in kwargs.get('action', '')
    ):
        return

    if sender is Shift and kwargs.get('created', False):
        return

    ScheduleMonitoringService().create_for_shift_result_list(list(instance.results.all()))


@receiver([post_save], sender=User)
@receiver([post_save], sender=UserOperatorProxy)
@receiver([post_save], sender=OnlyFansModel)
def clear_schedule_teams_operator(sender, instance, created, *args, **kwargs):
    """
    Clear the schedule teams operator and replacements when the parent is changed.
    """
    if not instance or created:
        return

    if isinstance(instance, (UserOperatorProxy, User)) and instance.role and instance.role.name == 'operator':
        original_parent = getattr(instance, '__original_parent',  None)
        new_parent = instance.parent

        if original_parent != new_parent:
            ScheduleTeamService().clear_operators_and_replacements_by_operator(instance)

    elif isinstance(instance, OnlyFansModel):
        original_team_lead = getattr(instance, '__original_team_lead', None)
        new_team_lead = instance.team_lead

        if original_team_lead != new_team_lead:
            ScheduleTeamService().clear_operators_and_replacements_by_model(instance)
