import datetime

from django.utils import timezone
from django.utils.timezone import make_aware
from django.utils.translation import gettext_lazy as _
from rest_framework import serializers

from accounts.validators import OperatorValidator
from base.validators import SerializerAttrsValidator
from only_fans_models.validators import OnlyFansModelValidator
from shifts.models import Shift


class ShiftValidator:
    """
    Shift Validator
    """
    def __init__(self, instance: Shift) -> None:
        self.instance = instance

    def _validate_shift_not_started(self) -> None:
        """
        Validate that shift has not started
        """
        if self.instance.shift_start_fact:
            raise serializers.ValidationError(
                _('Shift has already started')
            )

    def validate_shift_status(self, statuses: list[str]) -> None:
        """
        Validate that shift has valid status

        Args:
            statuses (list[str]): list of valid statuses
        """
        if self.instance.status not in statuses:
            raise serializers.ValidationError(
                {'status': _(f'Shift status must be in {statuses} not "{self.instance.status}" status')},
            )

    @staticmethod
    def validate_shift_creation_time(shift_end: datetime.datetime) -> None:
        """
        Validate shift creation time, that shift end time is greater than current time

        Args:
            shift_end (datetime.datetime): shift end time
        """
        if shift_end < timezone.now():
            raise serializers.ValidationError(
                _('Shift end time must be greater than current time')
            )

    def validate_delete_shift(self) -> None:
        """
        Validate shift can be deleted
        """
        self.validate_shift_status(['hold', 'overdue'])

    @classmethod
    def validate_create_shift(cls, attrs: dict) -> None:
        """
        Validate shift can be created
        """
        operator = attrs['operator']
        only_fans_models = attrs['only_fans_models']
        shift_start = attrs['shift_start']
        shift_end = attrs['shift_end']

        SerializerAttrsValidator(attrs).validate_len_of_objects('only_fans_models', 1, 3)
        cls.validate_shift_creation_time(shift_end)

        for model in only_fans_models:
            OnlyFansModelValidator(model).validate_model_is_not_busy_for_shift(shift_start, shift_end)

        OperatorValidator(operator).validate_operator_is_not_busy_for_shift(shift_start, shift_end)

    def _validate_current_time_is_valid_start_time_range(self, gap_time=1) -> None:
        """
        Validate that current time is in valid start time range
i
        Args:
            gap_time (int): gap time in hours
        """
        valid_start_time = self.instance.shift_start - datetime.timedelta(hours=gap_time)
        time_now = timezone.now()
        if not (
                valid_start_time
                < time_now
                < self.instance.shift_end
        ):
            raise serializers.ValidationError(
                _(f'Shift can be started in time range: '
                  f'{make_aware(valid_start_time)} - {make_aware(self.instance.shift_end)}\n'
                  f'Current time: {time_now}')
            )

    def validate_start_shift(self) -> None:
        """
        Validate shift can be started
        """
        self.validate_shift_status(['hold'])
        self._validate_current_time_is_valid_start_time_range()

        OperatorValidator(self.instance.operator).validate_operator_without_progress_shifts()

        for model in self.instance.only_fans_models.all():
            OnlyFansModelValidator(model).validate_model_without_more_than_one_progress_shifts()

    def validate_end_shift(self) -> None:
        """
        Validate shift can be ended
        """
        self.validate_shift_status(['progress'])

    def validate_update_shift(self, validated_data) -> None:
        """
        Validate shift can be updated

        Args:
            validated_data (dict): validated data
        """
        self.validate_shift_status(
            ['hold', 'progress', 'overdue']
        )
        only_fans_models = validated_data.get('only_fans_models', None)
        operator = validated_data.get('operator', None)
        shift_models = [
            result.only_fans_model for result in self.instance.results.filter(shift_end_fact__isnull=True)
        ]

        if only_fans_models:
            for model in only_fans_models:
                if model not in shift_models:
                    OnlyFansModelValidator(model).validate_model_is_not_busy_for_shift(
                        self.instance.shift_start,
                        self.instance.shift_end
                    )

        if operator and operator != self.instance.operator:
            OperatorValidator(operator).validate_operator_is_not_busy_for_shift(
                self.instance.shift_start,
                self.instance.shift_end
            )
