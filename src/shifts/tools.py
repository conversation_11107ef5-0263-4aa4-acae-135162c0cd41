import json
from typing import List
from uuid import UUID

from django.db.models import (
    Char<PERSON>ield,
    F,
    Value,
)
from django.db.models.functions import Concat

from accounts.models import User
from shifts.models import Shift


class SeniorOperatorParser:
    """
    A class to parse and retrieve information about senior operators participating in shifts.

    Attributes:
        shifts (List[Shift]): A list of Shift objects representing the shifts.
    """

    def __init__(self, shifts: List[Shift]):
        self.shifts = shifts

    def get_senior_operators_ids_dicts(self) -> List[dict]:
        """
        Retrieve a list of dictionaries containing information about senior operators participating
        in shifts based on the provided list of Shift objects.

        Returns:
            List[dict]: A list of dictionaries containing information about senior operators.
                        Each dictionary contains the following keys:
                        - 'senior_operator_id' (str): The ID of the senior operator.
                        - 'shift_id' (str): The ID of the shift in which the senior operator participates.
        """

        team_leads_ids = User.objects.filter(team_lead_shifts__in=self.shifts).values_list('id', flat=True)
        senior_operators = list(User.objects.filter(
            role__name='senior_operator',
            parent__id__in=team_leads_ids,
        ).annotate(
            senior_operator_id=F('id'),
            shift_id=Concat(Value(''), 'parent__team_lead_shifts__id', output_field=CharField())
        ).values(
            'senior_operator_id', 'shift_id'
        ))
        senior_operators = [
            {'senior_operator_id': str(item['senior_operator_id']), 'shift_id': str(item['shift_id'])}
            for item in senior_operators
        ]

        return senior_operators

    def get_senior_operators_ids_list(self) -> list[str]:
        """
        Retrieve a list of operators ids
        in shifts based on the provided list of Shift objects.
        """
        senior_dicts = self.get_senior_operators_ids_dicts()
        return [operator['senior_operator_id'] for operator in senior_dicts]


def uuid_to_str(obj: UUID) -> str:
    """
    Convert UUID object to str.

    Args:
        obj (UUID): UUID object.

    Returns:
        str: UUID as str.
    """
    return str(obj)


def uuid_converter(data: dict) -> dict:
    """
    Looking for UUID objects in data and convert them to str.

    Args:
        data (dict): Data.
    """
    json_string = json.dumps(data, default=uuid_to_str)

    return json.loads(json_string)
