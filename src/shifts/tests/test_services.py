import datetime
import warnings
from unittest import mock
from unittest.mock import Mock

from django.contrib.auth import get_user_model
from django.urls import reverse

from base.tests.mixins import BaseCRMTest
from shifts.models import (
    ScheduleTeam,
    Shift,
    ShiftIndex,
)
from shifts.services import ShiftService

User = get_user_model()


class TestShiftService(BaseCRMTest):
    def setUp(self):
        super().setUp()
        self.shift = Mock()
        self.service = ShiftService(self.shift)

    def test_get_shift_time_intervals(self):
        self.service.instance.shift_start = datetime.datetime(2023, 5, 17, 8, 0)
        self.service.instance.shift_end = datetime.datetime(2023, 5, 17, 14, 0)
        result = self.service._get_shift_time_intervals(shift=self.shift)

        expected = [
            (datetime.datetime(2023, 5, 17, 8, 0), datetime.datetime(2023, 5, 17, 9, 0)),
            (datetime.datetime(2023, 5, 17, 9, 0), datetime.datetime(2023, 5, 17, 10, 0)),
            (datetime.datetime(2023, 5, 17, 10, 0), datetime.datetime(2023, 5, 17, 11, 0)),
            (datetime.datetime(2023, 5, 17, 11, 0), datetime.datetime(2023, 5, 17, 12, 0)),
            (datetime.datetime(2023, 5, 17, 12, 0), datetime.datetime(2023, 5, 17, 13, 0)),
            (datetime.datetime(2023, 5, 17, 13, 0), datetime.datetime(2023, 5, 17, 14, 0)),
        ]

        self.assertEqual(result, expected)

    def test_update_json_info_service(self):
        self.patcher = mock.patch('django.utils.timezone.now')
        self.mock_now = self.patcher.start()
        self.mock_now.return_value = datetime.datetime.now().replace(
            hour=7,
            minute=0,
            second=0,
            day=17,
            month=5,
            year=2023,
        )
        warnings.filterwarnings("ignore")

        shift_data = {
            'shift_date': datetime.date(2023, 5, 17),
            'shift_number': self.create_shift_number(
                1, datetime.time(8, 0, 0), datetime.time(16, 0, 0)
            ).id,
            'only_fans_models': [self.create_only_fans_model(i).id for i in range(3)],
            'operator': self.user_operator.id,
        }

        response = self.client.post(
            reverse('shifts-list'),
            data=shift_data
        )
        shift = Shift.objects.get(id=response.data['id'])

        self.service = ShiftService(shift)
        self.mock_now.return_value = self.mock_now.return_value.replace(
            hour=8,
            minute=30,
            second=0,
        )
        self.service.start_shift()
        self.mock_now.return_value = self.mock_now.return_value.replace(
            hour=16,
            minute=0,
            second=0,
        )
        self.service.end_shift()
        shift.refresh_from_db()
        self.mock_now.return_value = self.mock_now.return_value.replace(
            hour=16,
            minute=30,
            second=0,
        )
        self.service.update_shift_json_info(shift, {}, {})
        self.assertEqual(shift.date_updated_stats, self.mock_now.return_value)
        self.assertEqual(shift.is_updated, False)

        self.mock_now.return_value = self.mock_now.return_value.replace(
            hour=19,
            minute=0,
            second=0,
        )
        self.service.update_shift_json_info(shift, {}, {})
        shift.refresh_from_db()
        self.assertEqual(shift.date_updated_stats, self.mock_now.return_value)
        self.assertEqual(shift.is_updated, True)


class TestScheduleTeamService(BaseCRMTest):
    def test_schedule_teams_clear_operator_after_team_lead_changed(self):
        only_fans_model = self.create_only_fans_model(10)
        shift_index, _ = ShiftIndex.objects.get_or_create(number=1)
        new_team_lead = User.objects.create(
            email='<EMAIL>',
            first_name='first',
            last_name='last',
            role=self.team_lead_role
        )
        new_operator = User.objects.create(
            email='<EMAIL>',
            first_name='first',
            last_name='last',
            role=self.operator_role
        )
        schedule_team = ScheduleTeam.objects.create(
            only_fans_model=only_fans_model,
            shift_index=shift_index,
            operator=self.user_operator,
            replacement=new_operator,
        )

        self.user_operator.parent = new_team_lead
        self.user_operator.save()
        schedule_team.refresh_from_db()
        self.assertIsNone(schedule_team.operator)
        self.assertIsNotNone(schedule_team.replacement)

        new_operator.parent = self.user_team_lead
        new_operator.save()
        schedule_team.refresh_from_db()
        self.assertIsNone(schedule_team.operator)
        self.assertIsNone(schedule_team.replacement)

        only_fans_model.team_lead = new_team_lead
        only_fans_model.save()
        new_operator.parent = new_team_lead
        new_operator.save()
        schedule_team.operator = new_operator
        schedule_team.replacement = self.user_operator
        schedule_team.save()
        schedule_team.refresh_from_db()
        self.assertEqual(schedule_team.operator, new_operator)
        self.assertEqual(schedule_team.replacement, self.user_operator)

        only_fans_model.team_lead = self.user_team_lead
        only_fans_model.save()
        schedule_team.refresh_from_db()
        self.assertIsNone(schedule_team.operator)
        self.assertIsNone(schedule_team.replacement)
