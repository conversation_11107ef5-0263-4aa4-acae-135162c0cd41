import datetime

from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError

from base.tests.mixins import BaseCRMTest
from shifts.models import ScheduleTeam, ShiftIndex

User = get_user_model()


class TestShiftNumberModel(BaseCRMTest):
    def test_can_not_create_shift_number_with_equal_time_start_and_time_end(self):
        with self.assertRaises(ValidationError):
            shift_number = self.create_shift_number(1, datetime.time(10, 0), datetime.time(10, 0))
            shift_number.full_clean()


class TestScheduleTeamModel(BaseCRMTest):
    def test_replacement_could_not_be_the_same_as_operator(self):
        new_operator = User.objects.create(
            email='<EMAIL>',
            first_name='first',
            last_name='last',
            role=self.operator_role,
            parent=self.user_team_lead
        )
        only_fans_model = self.create_only_fans_model(1)

        shift_index, _ = ShiftIndex.objects.get_or_create(
            number=1
        )

        with self.assertRaises(ValidationError):
            ScheduleTeam.objects.create(
                only_fans_model=only_fans_model,
                shift_index=shift_index,
                operator=new_operator,
                replacement=new_operator
            )

    def test_shift_number_index_the_same_as_schedule_team_shift_index(self):
        only_fans_model = self.create_only_fans_model(1)
        shift_index, _ = ShiftIndex.objects.get_or_create(
            number=1
        )
        schedule_team = ScheduleTeam.objects.create(
            only_fans_model=only_fans_model,
            shift_index=shift_index,
        )
        self.assertEqual(shift_index, schedule_team.shift_index)

        shift_number = self.create_shift_number(2,  datetime.time(10, 0), datetime.time(11, 0))
        with self.assertRaises(ValidationError):
            schedule_team.shift_number = shift_number
            schedule_team.save()

        shift_number = self.create_shift_number(1, datetime.time(10, 0), datetime.time(11, 0))
        schedule_team.shift_number = shift_number
        schedule_team.save()

        self.assertEqual(shift_number, schedule_team.shift_number)
