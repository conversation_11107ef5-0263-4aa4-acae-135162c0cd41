import datetime
import warnings
from datetime import date
from unittest import mock

from django.contrib.auth import get_user_model
from django.urls import reverse
from django.utils import timezone

from base.tests.mixins import BaseCRMTest
from shifts.models import (
    ScheduleMonitoring,
    ScheduleTeam,
    Shift,
    ShiftIndex,
    ShiftNumber,
    ShiftResult,
)


class ShiftApiTestCase(BaseCRMTest):
    """
    Test case for testing shifts API endpoints.
    """

    def setUp(self):
        self.patcher = mock.patch('django.utils.timezone.now')
        self.mock_now = self.patcher.start()
        self.mock_now.return_value = datetime.datetime.now().replace(
                hour=10,
                minute=0,
                second=0,
            )

        super().setUp()
        warnings.filterwarnings("ignore")

        self.valid_shift_data = {
            'shift_date': date.today(),
            'shift_number': self.create_shift_number(
                1, datetime.time(8, 0, 0), datetime.time(16, 0, 0)
            ).id,
            'only_fans_models': [self.create_only_fans_model(i).id for i in range(3)],
            'operator': self.user_operator.id,
        }

    def post_shift_with_valida_data(self):
        return self.client.post(
            reverse('shifts-list'),
            data=self.valid_shift_data
        )

    def test_shift_can_create_only_team_lead_and_senior_operator(self):
        response = self.post_shift_with_valida_data()
        self.assertEqual(response.status_code, 201)

    def test_shift_can_not_create_operator(self):
        self.client.force_authenticate(user=self.user_operator)
        response = self.post_shift_with_valida_data()

        self.assertEqual(response.status_code, 403)

    def test_shift_can_not_create_with_invalid_number_of_models(self):
        self.valid_shift_data['only_fans_models'] = [
            self.create_only_fans_model(i).id for i in range(4, 8)
        ]
        response = self.post_shift_with_valida_data()

        self.assertEqual(response.status_code, 400)

    def test_shift_can_delete_only_team_lead_and_senior_operator_with_correct_status(
        self,
    ):
        response = self.post_shift_with_valida_data()
        created_shift_id = response.json()['id']

        self.client.force_authenticate(user=self.user_operator)
        response = self.client.delete(reverse('shifts-detail', args=[created_shift_id]))
        self.assertEqual(response.status_code, 403)

        shift = Shift.objects.get(id=created_shift_id)
        shift.status = 'progress'
        shift.save()

        self.client.force_authenticate(user=self.user_team_lead)
        response = self.client.delete(reverse('shifts-detail', args=[created_shift_id]))
        self.assertEqual(response.status_code, 400)

        shift.status = 'end'
        shift.save()

        self.client.force_authenticate(user=self.user_team_lead)
        response = self.client.delete(reverse('shifts-detail', args=[created_shift_id]))
        self.assertEqual(response.status_code, 400)

        shift.status = 'overdue'
        shift.save()

        self.client.force_authenticate(user=self.user_team_lead)
        response = self.client.delete(reverse('shifts-detail', args=[created_shift_id]))
        self.assertEqual(response.status_code, 204)

    def test_can_not_create_shift_with_past_date(self):
        self.valid_shift_data['shift_date'] = date.today() - datetime.timedelta(days=1)
        response = self.post_shift_with_valida_data()

        self.assertEqual(response.status_code, 400)

    def test_shift_can_not_create_current_time_not_in_shift_number_time_range(self):
        self.mock_now.return_value = datetime.datetime.now().replace(
            hour=17, minute=0, second=0
        )

        response = self.post_shift_with_valida_data()

        self.assertEqual(response.status_code, 400)

    def test_can_not_create_with_busy_only_fans_models(self):
        success_response = self.client.post(
            reverse('shifts-list'), data=self.valid_shift_data
        )

        self.assertEqual(success_response.status_code, 201)

        new_operator = get_user_model().objects.create(
            email='<EMAIL>',
            first_name='new_operator',
            last_name='new_operator',
            role=self.operator_role,
            parent=self.user_team_lead,
        )

        self.valid_shift_data['operator'] = new_operator.id
        bad_response_the_same_shift_time = self.client.post(
            reverse('shifts-list'), data=self.valid_shift_data
        )

        self.assertEqual(bad_response_the_same_shift_time.status_code, 400)
        self.assertIn(
            'is busy in this shift time range',
            bad_response_the_same_shift_time.json()['only_fans_models'][0],
        )

        self.valid_shift_data['shift_number'] = self.create_shift_number(
            2, datetime.time(7, 0, 0), datetime.time(15, 0, 0)
        ).id
        bad_response_time_start_out_end_time_in = self.client.post(
            reverse('shifts-list'), data=self.valid_shift_data
        )
        self.assertEqual(bad_response_time_start_out_end_time_in.status_code, 400)
        self.assertIn(
            'is busy in this shift time range',
            bad_response_time_start_out_end_time_in.json()['only_fans_models'][0],
        )

        self.valid_shift_data['shift_number'] = self.create_shift_number(
            3, datetime.time(9, 0, 0), datetime.time(17, 0, 0)
        ).id
        bad_response_time_start_in_end_time_out = self.client.post(
            reverse('shifts-list'), data=self.valid_shift_data
        )
        self.assertEqual(bad_response_time_start_in_end_time_out.status_code, 400)
        self.assertIn(
            'is busy in this shift time range',
            bad_response_time_start_in_end_time_out.json()['only_fans_models'][0],
        )

    def test_can_not_create_with_busy_only_operator(self):
        success_response = self.client.post(
            reverse('shifts-list'), data=self.valid_shift_data
        )
        self.assertEqual(success_response.status_code, 201)

        new_models = [self.create_only_fans_model(i).id for i in range(4, 6)]
        self.valid_shift_data['only_fans_models'] = new_models

        bad_response_the_same_shift_time = self.client.post(
            reverse('shifts-list'), data=self.valid_shift_data
        )
        self.assertEqual(bad_response_the_same_shift_time.status_code, 400)
        self.assertIn(
            'is busy in this shift time range',
            bad_response_the_same_shift_time.json()['operator'][0],
        )

        self.valid_shift_data['shift_number'] = self.create_shift_number(
            2, datetime.time(7, 0, 0), datetime.time(15, 0, 0)
        ).id
        bad_response_time_start_out_end_time_in = self.client.post(
            reverse('shifts-list'), data=self.valid_shift_data
        )
        self.assertEqual(bad_response_time_start_out_end_time_in.status_code, 400)
        self.assertIn(
            'is busy in this shift time range',
            bad_response_time_start_out_end_time_in.json()['operator'][0],
        )

        self.valid_shift_data['shift_number'] = self.create_shift_number(
            3, datetime.time(9, 0, 0), datetime.time(17, 0, 0)
        ).id
        bad_response_time_start_in_end_time_out = self.client.post(
            reverse('shifts-list'), data=self.valid_shift_data
        )
        self.assertEqual(bad_response_time_start_in_end_time_out.status_code, 400)
        self.assertIn(
            'is busy in this shift time range',
            bad_response_time_start_in_end_time_out.json()['operator'][0],
        )

    def test_shift_can_started_by_operator_so_team_lead(self):
        shift_id = self.post_shift_with_valida_data().json()['id']

        for user in self.users:
            if user.role.name in ['operator', 'team_lead', 'senior_operator']:
                continue

            self.client.force_authenticate(user=user)
            response_shift_started = self.client.post(
                reverse('shifts-start-shift', kwargs={'pk': shift_id})
            )
            self.assertEqual(response_shift_started.status_code, 403)

        for user in [self.user_operator, self.user_team_lead, self.user_senior_operator]:
            self.client.force_authenticate(user=user)
            response_shift_started = self.client.post(
                reverse('shifts-start-shift', kwargs={'pk': shift_id})
            )
            self.assertNotEquals(response_shift_started.status_code, 403)

    def test_shift_can_be_ended_by_operator_team_lead_senior_operator(self):
        shift_id = self.post_shift_with_valida_data().json()['id']

        self.client.force_authenticate(user=self.user_superuser)
        response_shift_ended = self.client.post(
            reverse('shifts-end-shift', kwargs={'pk': shift_id})
        )
        self.assertEqual(response_shift_ended.status_code, 403)

        for user in [self.user_team_lead, self.user_senior_operator, self.user_operator]:
            self.client.force_authenticate(user=user)
            response_shift_ended = self.client.post(
                reverse('shifts-end-shift', kwargs={'pk': shift_id})
            )
            self.assertNotEquals(response_shift_ended.status_code, 403)

    def test_shift_can_not_started_if_current_time_not_in_time_shift_number_time_range_or_earlier_than_one_hour(self):
        shift_id = self.post_shift_with_valida_data().json()['id']
        self.client.force_authenticate(user=self.user_operator)

        self.mock_now.return_value = datetime.datetime.now().replace(
                hour=6,
                minute=0,
                second=0,
            )
        response_shift_started = self.client.post(
            reverse('shifts-start-shift', kwargs={'pk': shift_id})
        )
        self.assertEqual(response_shift_started.status_code, 400)
        self.assertIn(
            'Shift can be started in time range',
            response_shift_started.json()[0]
        )

        self.mock_now.return_value = datetime.datetime.now().replace(
                hour=17,
                minute=0,
                second=0,
            )
        response_shift_started = self.client.post(
            reverse('shifts-start-shift', kwargs={'pk': shift_id})
        )
        self.assertEqual(response_shift_started.status_code, 400)
        self.assertIn(
            'Shift can be started in time range',
            response_shift_started.json()[0]
        )

    def test_shift_can_not_be_started_if_status_not_hold(self):
        shift_id = self.post_shift_with_valida_data().json()['id']
        self.client.force_authenticate(user=self.user_operator)

        for status in ['progress', 'end']:
            shift = Shift.objects.get(id=shift_id)
            shift.status = status
            shift.save()
            response_shift_started = self.client.post(
                reverse('shifts-start-shift', kwargs={'pk': shift_id})
            )
            self.assertEqual(response_shift_started.status_code, 400)
            self.assertIn(
                'Shift status must be in [\'hold\']',
                response_shift_started.json()['status']
            )

    def test_shift_can_not_be_ended_if_status_not_progress(self):
        shift_id = self.post_shift_with_valida_data().json()['id']
        self.client.force_authenticate(user=self.user_operator)

        for status in ['hold', 'end']:
            shift = Shift.objects.get(id=shift_id)
            shift.status = status
            shift.save()
            response_shift_ended = self.client.post(
                reverse('shifts-end-shift', kwargs={'pk': shift_id})
            )
            self.assertEqual(response_shift_ended.status_code, 400)
            self.assertIn(
                'Shift status must be in [\'progress\']',
                response_shift_ended.json()['status']
            )

    def test_shift_can_not_be_created_if_operator_already_has_shift_in_progress(self):
        shift_id = self.post_shift_with_valida_data().json()['id']
        self.client.force_authenticate(user=self.user_operator)
        self.mock_now.return_value = datetime.datetime.now().replace(
                hour=7,
                minute=0,
                second=0,
            )
        self.client.post(
            reverse('shifts-start-shift', kwargs={'pk': shift_id})
        )
        self.client.force_authenticate(user=self.user_senior_operator)
        self.valid_shift_data['only_fans_models'] = [self.create_only_fans_model(i).id for i in range(4, 6)]
        self.valid_shift_data['shift_number'] = self.create_shift_number(
            2,
            datetime.time(7, 0, 0),
            datetime.time(7, 55, 0)
        ).id
        second_shift_id = self.post_shift_with_valida_data().json()['id']
        self.client.force_authenticate(user=self.user_operator)
        response_start_second_shift = self.client.post(
            reverse('shifts-start-shift', kwargs={'pk': second_shift_id})
        )
        self.assertEqual(response_start_second_shift.status_code, 400)
        self.assertIn(
            'already has shift in progress',
            response_start_second_shift.json()['operator']
        )

    def test_model_shift_result_fact_start_set_when_the_shift_started(self):
        shift_id = self.post_shift_with_valida_data().json()['id']
        self.client.force_authenticate(user=self.user_operator)
        self.client.post(
            reverse('shifts-start-shift', kwargs={'pk': shift_id})
        )
        only_fans_models_ids = self.valid_shift_data['only_fans_models']
        shift = Shift.objects.get(id=shift_id)

        for only_fans_models_id in only_fans_models_ids:
            result = shift.results.filter(only_fans_model_id=only_fans_models_id).first()
            self.assertEqual(result.shift_start_fact, timezone.now())

    def test_shift_only_fans_models_queue_is_correct_between_shifts(self):
        first_shift_id = self.post_shift_with_valida_data().json()['id']
        self.client.force_authenticate(user=self.user_operator)
        self.client.post(
            reverse('shifts-start-shift', kwargs={'pk': first_shift_id})
        )
        second_operator = get_user_model().objects.create(
            email='<EMAIL>',
            first_name='second',
            last_name='second',
            role=self.operator_role,
            parent=self.user_team_lead
        )
        only_fans_models_ids = self.valid_shift_data['only_fans_models']
        second_shift_data = {
            'shift_date': date.today(),
            'shift_number': self.create_shift_number(
                1, datetime.time(16, 1, 0), datetime.time(17, 0, 0)
            ).id,
            'only_fans_models': only_fans_models_ids,
            'operator': second_operator.id,
        }
        self.client.force_authenticate(user=self.user_team_lead)
        self.mock_now.return_value = datetime.datetime.now().replace(hour=16, minute=1, second=0)
        second_shift_id = self.client.post(reverse('shifts-list'), second_shift_data).json()['id']
        self.client.force_authenticate(user=second_operator)
        response_second_shift_start = self.client.post(
            reverse('shifts-start-shift', kwargs={'pk': second_shift_id})
        )
        self.assertEqual(response_second_shift_start.status_code, 200)

        for i in range(len(only_fans_models_ids)):
            self.assertIn(
                {"model": f"model_{i}",
                 "shift_number": "1 ShiftNumber 1",
                 "operator": "first last",
                 "team_lead": "first last"},
                response_second_shift_start.json()['message']
            )

        first_shift = Shift.objects.get(id=first_shift_id)
        second_shift = Shift.objects.get(id=second_shift_id)

        for only_fans_models_id in only_fans_models_ids:
            result = second_shift.results.filter(only_fans_model_id=only_fans_models_id).first()
            self.assertIsNone(result.shift_start_fact)

        self.client.post(
            reverse('shifts-end-shift', kwargs={'pk': first_shift_id})
        )

        for only_fans_models_id in only_fans_models_ids:
            second_shift_result = second_shift.results.filter(only_fans_model_id=only_fans_models_id).first()
            self.assertEqual(second_shift_result.shift_start_fact, timezone.now())
            self.assertIsNone(second_shift_result.shift_end_fact)

            second_shift_result = first_shift.results.filter(only_fans_model_id=only_fans_models_id).first()
            self.assertEqual(second_shift_result.shift_end_fact, timezone.now())

    def test_shifts_list_correct(self):
        first_shift_id = self.post_shift_with_valida_data().json()['id']
        self.valid_shift_data['shift_date'] = date.today() + datetime.timedelta(days=1)
        second_shift_id = self.post_shift_with_valida_data().json()['id']
        self.client.force_authenticate(user=self.user_operator)
        self.client.post(
            reverse('shifts-start-shift', kwargs={'pk': first_shift_id})
        )

        response = self.client.get(reverse('shifts-list'))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()), 2)
        self.assertEqual(response.json()[0]['id'], first_shift_id)
        self.assertEqual(response.json()[0]['status'], 'progress')
        self.assertIn('shift_number', response.json()[0])
        self.assertIn('operator', response.json()[0])
        self.assertIn('team_lead', response.json()[0])
        self.assertIn('shift_start', response.json()[0])
        self.assertIn('shift_end', response.json()[0])
        self.assertIn('id', response.json()[0])

        self.assertIn('shift_info', response.json()[0])

        self.assertIn('models', response.json()[0]['shift_info'])
        self.assertIn('model_id', response.json()[0]['shift_info']['models'][0])
        self.assertIn('name', response.json()[0]['shift_info']['models'][0])
        self.assertIn('id', response.json()[0]['shift_info']['models'][0])
        self.assertIn('total_sales_amount', response.json()[0]['shift_info']['models'][0])
        self.assertIn('count_unique_chats', response.json()[0]['shift_info']['models'][0])
        self.assertIn('count_mass_messages', response.json()[0]['shift_info']['models'][0])
        self.assertIn('count_outgoing_messages', response.json()[0]['shift_info']['models'][0])
        self.assertIn('average_speed_answer', response.json()[0]['shift_info']['models'][0])
        self.assertIn('average_speed_answer_by_chat', response.json()[0]['shift_info']['models'][0])
        self.assertIn('detail', response.json()[0]['shift_info']['models'][0])

        self.assertIn('time', response.json()[0]['shift_info']['models'][0]['detail'][0])
        self.assertIn('count_unique_chats', response.json()[0]['shift_info']['models'][0]['detail'][0])
        self.assertIn('count_mass_messages', response.json()[0]['shift_info']['models'][0]['detail'][0])
        self.assertIn('count_outgoing_messages', response.json()[0]['shift_info']['models'][0]['detail'][0])

        self.assertIn('total_statistic', response.json()[0]['shift_info'])
        self.assertIn('total_mass_messages', response.json()[0]['shift_info']['total_statistic'])
        self.assertIn('total_outgoing_messages', response.json()[0]['shift_info']['total_statistic'])
        self.assertIn('total_unique_chats', response.json()[0]['shift_info']['total_statistic'])
        self.assertIn('average_speed_answer', response.json()[0]['shift_info']['total_statistic'])
        self.assertIn('average_speed_answer_by_chat', response.json()[0]['shift_info']['total_statistic'])
        self.assertIn('last_operator_message_time', response.json()[0]['shift_info']['total_statistic'])

        self.assertEqual(response.json()[1]['id'], second_shift_id)
        self.assertEqual(response.json()[1]['id'], second_shift_id)
        self.assertEqual(response.json()[1]['status'], 'hold')

        self.assertNotEquals(response.json()[0], response.json()[1])

    def test_list_superuser(self):
        self.client.force_authenticate(user=self.user_operator)
        response = self.client.get(reverse('shifts-list'))
        self.assertEqual(response.status_code, 200)

    def test_list_no_login(self):
        self.client.logout()
        response = self.client.get(reverse('shifts-list'))
        self.assertEqual(response.status_code, 401)

    def test_shift_can_be_created_with_late_shift_number(self):
        self.valid_shift_data['shift_number'] = self.create_shift_number(
            3,
            datetime.time(20, 0),
            datetime.time(5, 0)
        ).id
        response = self.post_shift_with_valida_data()
        self.assertEqual(response.status_code, 201)

    def test_shift_can_be_started_earlier_than_shift_time_start_with_gmt_3(self):
        self.mock_now.return_value = datetime.datetime.now().replace(
                hour=11,
                minute=20,
                second=0,
            )
        self.valid_shift_data['shift_number'] = self.create_shift_number(
            2,
            datetime.time(12, 0),
            datetime.time(20, 0)
        ).id
        shift_id = self.post_shift_with_valida_data().json()['id']
        self.client.force_authenticate(user=self.user_operator)
        response = self.client.post(
            reverse('shifts-start-shift', kwargs={'pk': shift_id})
        )
        self.assertEqual(response.status_code, 200)

    def test_shift_can_not_be_updated(self):
        shift_id = self.post_shift_with_valida_data().json()['id']
        self.client.force_authenticate(user=self.user_operator)
        new_operator = get_user_model().objects.create(
            email='<EMAIL>',
            first_name='second',
            last_name='second',
            role=self.operator_role,
            parent=self.user_team_lead
        )

        data_for_update = {
            'operator': new_operator.id,
            'only_fans_models': [self.create_only_fans_model(i).id for i in range(4, 5)]
        }
        response = self.client.patch(
            reverse('shifts-detail', kwargs={'pk': shift_id}),
            data_for_update
        )
        self.assertEqual(response.status_code, 403)

        self.client.force_authenticate(user=self.user_team_lead)
        shift = Shift.objects.get(id=shift_id)
        shift.status = "end"
        shift.save()
        response = self.client.patch(
            reverse('shifts-detail', kwargs={'pk': shift_id}),
            data_for_update
        )
        self.assertEqual(response.status_code, 400)

        shift.status = "hold"
        shift.save()
        new_shift_data = {
            'shift_date': date.today(),
            'shift_number': self.create_shift_number(
                1, datetime.time(8, 1, 0), datetime.time(20, 0, 0)
            ).id,
            'only_fans_models': [self.create_only_fans_model(i).id for i in range(6, 7)],
            'operator': new_operator.id,
        }
        new_shift = self.client.post(
            reverse('shifts-list'),
            new_shift_data
        )
        self.client.force_authenticate(user=new_operator)
        self.client.post(
            reverse('shifts-start-shift', kwargs={'pk': new_shift.json()['id']})
        )
        self.client.force_authenticate(user=self.user_team_lead)
        response_busy_operator = self.client.patch(
            reverse('shifts-detail', kwargs={'pk': shift_id}),
            data_for_update
        )
        self.assertEqual(response_busy_operator.status_code, 400)

        response_put_shift = self.client.put(
            reverse('shifts-detail', kwargs={'pk': shift_id}),
            data_for_update
        )
        self.assertEqual(response_put_shift.status_code, 405)

    def test_shift_can_be_updated(self):
        shift_data = self.post_shift_with_valida_data().json()
        shift_id = shift_data['id']
        new_operator = get_user_model().objects.create(
            email='<EMAIL>',
            first_name='second',
            last_name='second',
            role=self.operator_role,
            parent=self.user_team_lead
        )
        response = self.client.patch(
            reverse('shifts-detail', kwargs={'pk': shift_id}),
            {'operator': new_operator.id}
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()['operator']['id'], str(new_operator.id))

        response = self.client.patch(
            reverse('shifts-detail', kwargs={'pk': shift_id}),
            {'only_fans_models': [self.create_only_fans_model(i).id for i in range(4, 5)]}
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()['shift_info']['models']), 1)

        self.client.force_authenticate(user=self.user_operator)
        start_response = self.client.post(
            reverse('shifts-start-shift', kwargs={'pk': shift_id})
        )
        self.assertEqual(start_response.status_code, 200)

        self.client.force_authenticate(user=self.user_team_lead)
        response = self.client.patch(
            reverse('shifts-detail', kwargs={'pk': shift_id}),
            {'operator': self.user_operator.id}
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()['operator']['id'], str(self.user_operator.id))
        self.assertNotEqual(response.json()['id'], shift_id)

    def test_shift_can_be_created_if_shift_start_of_one_shift_equal_shift_end_of_another(self):
        self.mock_now.return_value = datetime.datetime.now().replace(
            hour=6,
            minute=0,
            second=0,
        )
        self.valid_shift_data['shift_number'] = self.create_shift_number(
            1,
            datetime.time(10, 0),
            datetime.time(12, 0)
        ).id
        self.post_shift_with_valida_data()
        self.valid_shift_data['shift_number'] = self.create_shift_number(
            2,
            datetime.time(8, 0),
            datetime.time(10, 0)
        ).id
        response = self.post_shift_with_valida_data()
        self.assertEqual(response.status_code, 201)
        self.valid_shift_data['shift_number'] = self.create_shift_number(
            3,
            datetime.time(12, 0),
            datetime.time(14, 0)
        ).id
        response = self.post_shift_with_valida_data()
        self.assertEqual(response.status_code, 201)

    def test_ended_shift_in_shifts_history(self):
        shift_id = self.post_shift_with_valida_data().json()['id']
        self.client.force_authenticate(user=self.user_operator)
        self.client.post(
            reverse('shifts-start-shift', kwargs={'pk': shift_id})
        )
        self.client.post(
            reverse('shifts-end-shift', kwargs={'pk': shift_id})
        )
        response = self.client.get(reverse('shifts-history'))
        self.assertEqual(response.status_code, 200)
        self.assertIn("count", response.json())
        self.assertIn("next", response.json())
        self.assertIn("previous", response.json())
        self.assertIn("results", response.json())
        self.assertEqual(len(response.json()['results']), 1)
        self.assertEqual(response.json()['results'][0]['date'], str(date.today()))
        self.assertEqual(response.json()['results'][0]['shifts'][0]['id'], str(shift_id))

    def test_operator_names_stores_to_created_shift(self):
        shift_id = self.post_shift_with_valida_data().json()['id']
        shift_instance = Shift.objects.get(id=shift_id)
        self.assertEqual(shift_instance.operator_name, self.user_operator.full_name)

    def test_model_can_be_moved_from_active_shift_to_another_shift(self):
        first_shift_id = self.post_shift_with_valida_data().json()['id']
        self.client.force_authenticate(user=self.user_operator)
        self.client.post(
            reverse('shifts-start-shift', kwargs={'pk': first_shift_id})
        )
        only_fans_model, rest_models = self.valid_shift_data["only_fans_models"][0], self.valid_shift_data["only_fans_models"][1:]
        self.client.force_authenticate(user=self.user_team_lead)
        patch_response = self.client.patch(
            reverse('shifts-detail', kwargs={'pk': first_shift_id}),
            {'only_fans_models': rest_models}
        )
        second_shift_data = {
            'shift_date': date.today(),
            'shift_number': self.create_shift_number(
                1, datetime.time(16, 1, 0), datetime.time(17, 0, 0)
            ).id,
            'only_fans_models': [only_fans_model],
            'operator': self.user_operator.id,
        }

        post_response = self.client.post(reverse('shifts-list'), second_shift_data)

        self.assertEqual(patch_response.status_code, 200)
        self.assertEqual(post_response.status_code, 201)

    def test_model_can_be_removed_from_the_active_shift_and_reverted(self):
        shift_id = self.post_shift_with_valida_data().json()['id']
        shift = Shift.objects.get(id=shift_id)
        self.client.force_authenticate(user=self.user_operator)
        self.client.post(
            reverse('shifts-start-shift', kwargs={'pk': shift_id})
        )
        only_fans_model, rest_models = self.valid_shift_data["only_fans_models"][0], self.valid_shift_data["only_fans_models"][1:]

        self.client.force_authenticate(user=self.user_team_lead)
        patch_response = self.client.patch(
            reverse('shifts-detail', kwargs={'pk': shift_id}),
            {'only_fans_models': rest_models}
        )
        self.assertEqual(patch_response.status_code, 200)
        shift.refresh_from_db()
        self.assertEqual(shift.results.count(), 3)
        self.assertIsNotNone(shift.results.get(only_fans_model__id=only_fans_model).shift_end_fact)
        self.mock_now.return_value = self.mock_now.return_value.replace(
            hour=11,
            minute=1,
            second=0,
        )
        patch_response = self.client.patch(
            reverse('shifts-detail', kwargs={'pk': shift_id}),
            {'only_fans_models': rest_models + [only_fans_model]}
        )
        self.assertEqual(patch_response.status_code, 200)
        shift.refresh_from_db()
        self.assertEqual(shift.results.count(), 4)
        self.assertEqual(shift.results.filter(
            only_fans_model__id=only_fans_model,
            shift_end_fact__isnull=True
        ).count(), 1)
        self.assertEqual(shift.results.filter(
            only_fans_model__id=only_fans_model,
            shift_end_fact__isnull=False
        ).count(), 1)
        self.assertNotEqual(
            shift.results.filter(
                only_fans_model__id=only_fans_model,
                shift_end_fact__isnull=True
            ).first().shift_start_fact,
            shift.results.filter(
                only_fans_model__id=only_fans_model,
                shift_end_fact__isnull=False
            ).first().shift_start_fact
        )

    def test_shift_result_start_fact_sets_if_shift_ended_without_result_start_fact(self):
        shift_id = self.post_shift_with_valida_data().json()['id']

        self.client.force_authenticate(user=self.user_operator)
        self.client.post(
            reverse('shifts-start-shift', kwargs={'pk': shift_id})
        )

        new_operator = get_user_model().objects.create(
            email='<EMAIL>',
            first_name='first',
            last_name='last',
            role=self.operator_role,
            parent=self.user_team_lead
        )
        self.client.force_authenticate(user=self.user_team_lead)

        new_shift_id = self.client.post(
            reverse('shifts-list'),
            {
                'shift_date': date.today(),
                'shift_number': self.create_shift_number(
                    2, datetime.time(16, 0, 0), datetime.time(18, 0, 0)
                ).id,
                'only_fans_models': self.valid_shift_data['only_fans_models'],
                'operator': new_operator.id,
            }
        ).json()['id']
        new_shift = Shift.objects.get(id=new_shift_id)

        self.mock_now.return_value = datetime.datetime.now().replace(
            hour=16,
            minute=10,
            second=0,
        )
        self.client.force_authenticate(user=new_operator)
        self.client.post(
            reverse('shifts-start-shift', kwargs={'pk': new_shift.id})
        )
        new_shift.refresh_from_db()
        self.assertIsNone(new_shift.results.first().shift_start_fact)

        self.client.post(
            reverse('shifts-end-shift', kwargs={'pk': new_shift.id})
        )
        new_shift.refresh_from_db()
        self.assertIsNotNone(new_shift.results.first().shift_start_fact)
        self.assertEqual(new_shift.results.first().shift_start_fact, new_shift.results.first().shift_end_fact)

    def test_shift_could_be_created_if_the_date_in_the_past_but_time_in_shift_range(self):
        self.mock_now.return_value = datetime.datetime.now().replace(
            hour=00,
            minute=1,
            second=0,
            day=datetime.datetime.now().day
        )
        self.valid_shift_data['shift_date'] = self.mock_now.return_value.date() - datetime.timedelta(days=1)
        self.valid_shift_data['shift_number'] = self.create_shift_number(
            1,
            datetime.time(23, 0),
            datetime.time(1, 0)
        ).id
        response = self.post_shift_with_valida_data()
        self.assertEqual(response.status_code, 201)

    def test_shifts_history_total_permissions(self):
        for user in [self.user_senior_operator, self.user_team_lead, self.user_superuser]:
            self.client.force_authenticate(user=user)
            response = self.client.get(reverse('shifts-history-total'))
            self.assertEqual(response.status_code, 200)

        for user in [self.user_operator, self.user_marketer, self.user_smm, self.user_financier]:
            self.client.force_authenticate(user=user)
            response = self.client.get(reverse('shifts-history-total'))
            self.assertEqual(response.status_code, 403)

    def test_schedule_team_list_permissions(self):
        for user in [
            self.user_team_lead, self.user_superuser, self.user_senior_operator
        ]:
            self.client.force_authenticate(user=user)
            response = self.client.get(reverse('schedule-teams-list'))
            self.assertEqual(response.status_code, 200)

        for user in [
            self.user_operator, self.user_marketer, self.user_smm, self.user_financier,
        ]:
            self.client.force_authenticate(user=user)
            response = self.client.get(reverse('schedule-teams-list'))
            self.assertEqual(response.status_code, 403)

    def test_schedule_team_create(self):
        shift_number = self.create_shift_number(
            1,
            datetime.time(8, 0),
            datetime.time(10, 0)
        )
        shift_number.team_leads.add(self.user_team_lead)
        only_fans_model = self.create_only_fans_model(10)
        shift_index, _ = ShiftIndex.objects.get_or_create(1)

        response = self.client.post(
            reverse('schedule-teams-list'),
            {
                'only_fans_model': str(only_fans_model.id),
                'shift_index': str(shift_index.id)
            }
        )
        self.assertEqual(response.status_code, 201)

    def test_schedule_team_update(self):
        shift_number = self.create_shift_number(
            1,
            datetime.time(8, 0),
            datetime.time(10, 0)
        )
        shift_number.team_leads.add(self.user_team_lead)
        only_fans_model = self.create_only_fans_model(10)
        shift_index, _ = ShiftIndex.objects.get_or_create(1)
        schedule_team = ScheduleTeam.objects.create(
            only_fans_model=only_fans_model,
            shift_index=shift_index,
        )

        response = self.client.patch(
            reverse('schedule-teams-detail', kwargs={'pk': schedule_team.id}),
            {
                'shift_number':  str(shift_number.id),
                'shift_index': str(shift_index.id)
            }
        )
        self.assertEqual(response.status_code, 200)
        schedule_team.refresh_from_db()
        self.assertEqual(schedule_team.shift_number, shift_number)

        response = self.client.patch(
            reverse('schedule-teams-detail', kwargs={'pk': schedule_team.id}),
            {
                'operator':  str(self.user_operator.id)
            }
        )
        self.assertEqual(response.status_code, 200)
        schedule_team.refresh_from_db()
        self.assertEqual(schedule_team.operator, self.user_operator)

        new_operator = get_user_model().objects.create(
            email='<EMAIL>',
            first_name='first',
            last_name='last',
            role=self.operator_role,
            parent=self.user_team_lead
        )
        response = self.client.patch(
            reverse('schedule-teams-detail', kwargs={'pk': schedule_team.id}),
            {
                'replacement':  str(new_operator.id)
            }
        )
        self.assertEqual(response.status_code, 200)
        schedule_team.refresh_from_db()
        self.assertEqual(schedule_team.replacement, new_operator)

    def test_schedule_monitoring(self):
        shift_number = ShiftNumber.objects.get(id=self.valid_shift_data['shift_number'])
        shift_index = shift_number.index
        operator_id = self.valid_shift_data['operator']
        replacement_operator = get_user_model().objects.create(
            email='<EMAIL>',
            first_name='first',
            last_name='last',
            role=self.operator_role,
            parent=self.user_team_lead
        )
        valid_schedule_team = ScheduleTeam.objects.create(
            only_fans_model_id=self.valid_shift_data['only_fans_models'][0],
            shift_index=shift_index,
            shift_number=shift_number,
            operator_id=operator_id,
            replacement=replacement_operator
        )
        valid_replacement_schedule_team = ScheduleTeam.objects.create(
            only_fans_model_id=self.valid_shift_data['only_fans_models'][1],
            shift_index=shift_index,
            shift_number=shift_number,
            operator=replacement_operator,
            replacement_id=operator_id
        )
        new_shift_number = ShiftNumber.objects.create(
            index=shift_index,
            name='new shift number',
            time_start=datetime.time(8, 0),
            time_end=datetime.time(10, 0),
        )
        new_shift_number.team_leads.add(self.user_team_lead)
        new_operator = get_user_model().objects.create(
            email='<EMAIL>',
            first_name='first',
            last_name='last',
            role=self.operator_role,
            parent=self.user_team_lead
        )
        invalid_shift_number_schedule_team = ScheduleTeam.objects.create(
            only_fans_model_id=self.valid_shift_data['only_fans_models'][2],
            shift_index=shift_index,
            shift_number=new_shift_number,
            operator=new_operator,
            replacement_id=operator_id
        )
        response = self.post_shift_with_valida_data()
        shift_id = response.json()['id']
        self.client.post(
                reverse('shifts-start-shift', kwargs={'pk': shift_id})
            )
        shift_results = ShiftResult.objects.filter(shift_id=shift_id)

        for shift_result in shift_results:
            schedule_monitoring = ScheduleMonitoring.objects.get(shift_result=shift_result)
            if shift_result.only_fans_model == valid_schedule_team.only_fans_model:
                self.assertTrue(schedule_monitoring.shift_number)
                self.assertTrue(schedule_monitoring.operator)
                self.assertFalse(schedule_monitoring.replacement)
            elif shift_result.only_fans_model == valid_replacement_schedule_team.only_fans_model:
                self.assertTrue(schedule_monitoring.shift_number)
                self.assertFalse(schedule_monitoring.operator)
                self.assertTrue(schedule_monitoring.replacement)
            elif shift_result.only_fans_model == invalid_shift_number_schedule_team.only_fans_model:
                self.assertFalse(schedule_monitoring.shift_number)
                self.assertFalse(schedule_monitoring.operator)
                self.assertTrue(schedule_monitoring.replacement)
            else:
                self.assertIsNone(schedule_monitoring.shift_number)
                self.assertIsNone(schedule_monitoring.operator)
                self.assertIsNone(schedule_monitoring.replacement)

    def test_shift_stats_listed(self):
        shift_data = self.post_shift_with_valida_data().json()
        self.assertEqual(len(shift_data['stats']), 0)

        self.client.force_authenticate(user=self.user_operator)
        self.client.post(
            reverse('shifts-start-shift', kwargs={'pk': shift_data['id']})
        )

        response = self.client.get(reverse('shifts-list'))
        response_json = response.json()
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response_json), 1)
        self.assertEqual(response_json[0]['id'], shift_data['id'])
        self.assertNotEqual(len(response_json[0]['stats']), 0)

    def test_shifts_alarm_bot_permissions(self):
        for user in self.users:
            self.client.force_authenticate(user=user)
            response = self.client.get(reverse('shifts-alarm-bot'))
            self.assertEqual(response.status_code, 403)

        response = self.client.get(reverse('shifts-alarm-bot'), headers=self.api_key_headers)
        self.assertEqual(response.status_code, 200)

    def test_alarm_bot_team_leads_metrics_permissions(self):
        for user in self.users:
            self.client.force_authenticate(user=user)
            response = self.client.get(reverse('alarm-bot-team-leads-metrics'))
            self.assertEqual(response.status_code, 403)

        response = self.client.get(reverse('alarm-bot-team-leads-metrics'), headers=self.api_key_headers)
        self.assertEqual(response.status_code, 200)

    def test_alarm_bot_team_leads_chats_permissions(self):
        for user in self.users:
            self.client.force_authenticate(user=user)
            response = self.client.get(reverse('alarm-bot-team-leads-chats'))
            self.assertEqual(response.status_code, 403)

        response = self.client.get(reverse('alarm-bot-team-leads-chats'), headers=self.api_key_headers)
        self.assertEqual(response.status_code, 200)
