import copy
import datetime
from itertools import groupby

from django.contrib.auth.models import User
from django.core.paginator import Paginator
from django.db import transaction
from django.db.models import Q, QuerySet
from django.shortcuts import get_object_or_404
from django.utils import timezone
from rest_framework import serializers

from base.tools import chunks
from only_fans_db.services import (
    DetailTotalInfoService,
    OnlyFansDBDataCollectorWithDistance,
    ProfitService,
    RatingService,
    StatsService,
)
from only_fans_models.models import OnlyFansModel
from shifts.celery_helpers import celery_shift_stats_updater
from shifts.models import (
    DBSale,
    OperatorProfit,
    ResultStats,
    ScheduleMonitoring,
    ScheduleTeam,
    Shift,
    ShiftNumber,
    ShiftRating,
    ShiftResult,
    ShiftSale,
)
from shifts.tools import uuid_converter
from shifts.validators import ShiftValidator


class ShiftNumberService:
    """
    Shift Number service
    """

    MODEL = ShiftNumber

    def __init__(self, instance: MODEL) -> None:
        self.instance = instance

    def generate_start_end_time_datetime(
            self,
            shift_date: datetime.date
    ) -> tuple[datetime.datetime, datetime.datetime]:
        """
        Generate shift_start and shift_end from Shift Number start_time and ent_time and shift_date.
        If end time less than start time, then end time will be next day

        Args:
            shift_date (datetime.date): shift date
        """
        start_time = datetime.datetime.combine(shift_date, self.instance.time_start)

        end_time = datetime.datetime.combine(shift_date, self.instance.time_end)

        if start_time > end_time:
            end_time += datetime.timedelta(days=1)

        return start_time, end_time


class ShiftService:
    """
    Shift service
    """

    MODEL = Shift

    def __init__(self, instance: MODEL) -> None:
        self.instance = instance

    @classmethod
    def create(cls, validated_data: dict) -> MODEL:
        """
        Create a shift. Set the start and end time of the shift.
        """
        only_fans_models = validated_data.pop('only_fans_models')

        with transaction.atomic():
            validated_data['operator_name'] = validated_data['operator'].full_name
            shift = cls.MODEL(**validated_data)

            for model in only_fans_models:
                ShiftResultService.create({'shift': shift, 'only_fans_model': model})

            shift.json_info = cls.get_statistic(
                shift=shift,
                messages_data={},
                mass_messages_data={},
            )
            shift.save()
            shift.refresh_from_db()

            return shift

    def start_shift(self) -> dict:
        """
        Start shift
        """
        with transaction.atomic():
            self.instance = get_object_or_404(Shift.objects.select_for_update(nowait=True), pk=self.instance.pk)
            ShiftValidator(self.instance).validate_start_shift()
            current_time = timezone.now()

            busy_models = []

            for only_fans_model in self.instance.only_fans_models.all():
                shift_result_not_ended = only_fans_model.results.select_for_update(
                ).select_related('shift').exclude(shift=self.instance).filter(
                    shift_end_fact__isnull=True,
                    shift_start_fact__isnull=False,
                    shift__status='progress'
                ).first()

                if shift_result_not_ended:
                    busy_models.append(
                        {
                            'model': str(only_fans_model),
                            'shift_number': str(shift_result_not_ended.shift.shift_number),
                            'operator': str(shift_result_not_ended.shift.operator),
                            'team_lead': str(shift_result_not_ended.shift.team_lead),
                        }
                    )
                else:
                    result = self.instance.results.filter(
                        only_fans_model=only_fans_model
                    ).first()
                    result.shift_start_fact = current_time
                    result.save()

            self.instance.shift_start_fact = current_time
            self.instance.status = 'progress'
            self.instance.save()

            ResultStatsService.create_or_update_shift_results(
                shift=self.instance,
                messages_data={},
                mass_messages_data={},
                distance_data={}
            )

        return {'message': 'OK' if not busy_models else busy_models}

    def end_shift(self) -> dict:
        """
        End shift
        """
        with transaction.atomic():
            self.instance = get_object_or_404(Shift.objects.select_for_update(nowait=True), pk=self.instance.pk)
            ShiftValidator(self.instance).validate_end_shift()
            current_time = timezone.now()

            self.instance.shift_end_fact = current_time
            self.instance.status = 'end'

            for only_fans_model in self.instance.only_fans_models.all():
                only_fans_model_shift_result_without_start_fact = (
                    only_fans_model.results.select_for_update()
                    .exclude(shift=self.instance)
                    .filter(
                        shift_start_fact__isnull=True,
                        shift__status='progress'
                    ).first()
                )

                if only_fans_model_shift_result_without_start_fact:
                    only_fans_model_shift_result_without_start_fact.shift_start_fact = (
                        current_time
                    )
                    only_fans_model_shift_result_without_start_fact.save()

                result = self.instance.results.select_for_update().filter(
                    only_fans_model=only_fans_model,
                    shift_end_fact__isnull=True
                ).first()

                if result:
                    result.shift_end_fact = current_time

                    if not result.shift_start_fact:
                        result.shift_start_fact = current_time

                    result.save()

            self.instance.save()
            self.instance.refresh_from_db()

            # TEMPORARY OFF
            # celery_write_operator_profits(shift=self.instance)

            celery_shift_stats_updater(shift=self.instance)

        return {
            'message': 'OK',
            'duration': str(self.instance.duration).split('.')[0],
        }

    @staticmethod
    def __convert_datetime_to_string(datetime_value: datetime.datetime | tuple) -> str:
        """
        Convert datetime to string
        """
        if isinstance(datetime_value, datetime.timedelta):
            string_value = str(datetime_value)

            if datetime_value == datetime.timedelta():
                string_value = "-"

            if "." in string_value:
                string_value = string_value.split('.')[0]

            return string_value

        if isinstance(datetime_value, datetime.datetime):
            return datetime_value.strftime('%H:%M')

        if type(datetime_value) is tuple:
            return f'{datetime_value[0].strftime("%H:%M")} - {datetime_value[1].strftime("%H:%M")}'

    @staticmethod
    def _get_shift_time_intervals(shift: Shift) -> list[tuple[datetime, datetime]]:
        """
        Get shift time intervals for shift in hours
        """
        start_time = shift.shift_start.replace(
            minute=0, second=0, microsecond=0
        )
        end_time = shift.shift_end.replace(minute=0, second=0, microsecond=0)

        result = []
        current_time = start_time

        while current_time < end_time:
            next_time = current_time + datetime.timedelta(hours=1)
            result.append((current_time, next_time))
            current_time = next_time

        return result

    @classmethod
    def get_statistic(
            cls,
            shift: Shift,
            messages_data: dict,
            mass_messages_data: dict,
    ) -> dict:
        """
        Get shift statistic

        Args:
            shift: Shift
            messages_data: messages data
            mass_messages_data: mass messages data
        """
        shift_time_intervals = cls._get_shift_time_intervals(shift=shift)
        statistic_data = DetailTotalInfoService(
            shift=shift,
            time_intervals=shift_time_intervals,
            messages_data=messages_data,
            mass_messages_data=mass_messages_data,
        ).get_full_statistic()

        total_statistic = statistic_data.pop('total_statistic')
        total_statistic['average_speed_answer'] = cls.__convert_datetime_to_string(
            total_statistic['average_speed_answer']
        )
        total_statistic[
            'average_speed_answer_by_chat'
        ] = cls.__convert_datetime_to_string(
            total_statistic['average_speed_answer_by_chat']
        )
        total_statistic['last_operator_message_time'] = (
            cls.__convert_datetime_to_string(
                total_statistic['last_operator_message_time']
            )
            if total_statistic['last_operator_message_time']
            else '-'
        )

        for model_result in statistic_data['models']:
            for key in model_result.keys():
                if key in ['average_speed_answer', 'average_speed_answer_by_chat']:
                    model_result[key] = cls.__convert_datetime_to_string(
                        model_result[key]
                    )

        statistic_data['total_statistic'] = total_statistic
        statistic_data = uuid_converter(statistic_data)

        return statistic_data

    @staticmethod
    def get_min_shift_start_fact_from_shift_list(
            shift_list: list[Shift],
    ) -> datetime:
        """
        Get min shift start fact

        Args:
            shift_list (list[Shift]): list of shifts
        """
        min_shift_start_fact = None

        for shift in shift_list:
            if (
                    not min_shift_start_fact
                    or shift.shift_start_fact < min_shift_start_fact
            ):
                min_shift_start_fact = shift.shift_start_fact

        return min_shift_start_fact

    @staticmethod
    def get_max_shift_end_fact_from_shift_list(
            shift_list: list[Shift],
    ):
        """
        Get max shift end fact

        Args:
            shift_list (list[Shift]): list of shifts
        """
        max_shift_end_fact = None

        for shift in shift_list:
            shift_end_fact = shift.shift_end_fact

            if not shift_end_fact:
                continue

            if (
                    not max_shift_end_fact
                    or shift.shift_end_fact > max_shift_end_fact
            ):
                max_shift_end_fact = shift.shift_end_fact

        return max_shift_end_fact

    @staticmethod
    def get_models_from_shift_list(shift_list: list[Shift]) -> list:
        """
        Get models from shift list

        Args:
            shift_list (list[Shift]): list of shifts
        """
        models = set()

        for shift in shift_list:
            for model in shift.only_fans_models.all():
                models.add(model)

        return list(models)

    def get_users_ids(self, exclude_roles: list[str] = None) -> list[str]:
        """
        Get a list of user IDs associated with the shift instance,
        including team lead, operator, senior operators and superusers.

        Args:
            exclude_roles (list[str]): A list of roles to exclude from the list of user IDs.

        Returns:
            list[str]: A list of user IDs.
        """
        from accounts.models import User
        from shifts.tools import SeniorOperatorParser

        exclude_roles = exclude_roles if exclude_roles else []

        superusers_ids = []

        if 'superuser' not in exclude_roles:
            superusers_uuids = User.objects.filter(role__name='superuser').values_list('id', flat=True)
            superusers_ids = [str(user_uuid) for user_uuid in superusers_uuids]

        users_ids = []

        for attr in ['operator', 'team_lead']:
            if user := getattr(self.instance, attr, None):
                users_ids.append(str(user.pk))

        seniors_operators_ids = SeniorOperatorParser([self.instance, ]).get_senior_operators_ids_list()

        return list(set(users_ids + seniors_operators_ids + superusers_ids))

    @staticmethod
    def _get_validated_data_with_attrs_from_shift(
            shift: Shift,
            validated_data: dict[str, [str, list[str]]]
    ):
        """
        Get validated data with attrs from shift

        Args:
            shift (Shift): shift
            validated_data (dict[str, [str, list[str]]]): validated data
        """
        for attr in ['shift_date', 'shift_number', 'shift_start', 'shift_end', 'team_lead']:
            validated_data[attr] = getattr(shift, attr)

        return validated_data

    @staticmethod
    def _handle_only_fans_models_changed(
            shift: Shift,
            only_fans_models_from_serializer: list[OnlyFansModel],
            only_fans_models_from_shift: list[OnlyFansModel],
    ):
        """
        Handle only fans models changed in shift

        Args:
            shift (Shift): shift instance
            only_fans_models_from_serializer (list[OnlyFansModel]): only fans models from serializer
            only_fans_models_from_shift (list[OnlyFansModel]): only fans models from shift
        """
        for model in only_fans_models_from_serializer:
            if model not in only_fans_models_from_shift:
                data_for_create_shift_result = {
                    'shift': shift,
                    'only_fans_model': model
                }

                if not model.results.select_for_update().filter(
                    shift__status='progress',
                    shift_end_fact__isnull=True
                ).select_related('shift').exists():
                    data_for_create_shift_result['shift_start_fact'] = timezone.now()

                ShiftResultService.create(data_for_create_shift_result)

        for shift_model in only_fans_models_from_shift:
            if shift_model not in only_fans_models_from_serializer:
                shift_result = shift.results.select_for_update().filter(
                    only_fans_model=shift_model,
                    shift_end_fact__isnull=True
                ).first()

                if shift_result:
                    current_time = timezone.now()
                    if not shift_result.shift_start_fact:
                        shift_result.shift_start_fact = current_time
                    shift_result.shift_end_fact = current_time
                    shift_result.save()

    def _handle_operator_changed(
            self,
            shift: Shift,
            validated_data: dict[str, [str, list[str]]],
            operator: User
    ) -> Shift:
        """
        Handle operator changed in shift

        Args:
            shift (Shift): shift instance
            validated_data (dict[str, [str, list[str]]]): validated data
            operator (User): operator instance
        """
        if not validated_data.get('only_fans_models'):
            validated_data['only_fans_models'] = list(
                shift.only_fans_models.filter(
                    results__shift_end_fact__isnull=True
                )
            )

        shift_service = ShiftService(shift)
        shift_service.end_shift()
        ShiftEventer(shift=shift).send_event_ended()

        validated_data['operator'] = operator
        self._get_validated_data_with_attrs_from_shift(
            shift, validated_data
        )

        new_shift = ShiftService.create(validated_data)
        ShiftEventer(shift=new_shift).send_event_created()

        return new_shift

    def update_active_shift(
            self,
            validated_data: dict[str, [str, list[str]]]
    ) -> Shift:
        """
        Update shift with status 'progress' with provided data

        Args:
            validated_data (dict[str, [str, list[str]]]): validated data
        """
        ShiftValidator(self.instance).validate_shift_status(['progress'])

        only_fans_models = validated_data.get('only_fans_models')
        operator = validated_data.get('operator')

        with transaction.atomic():
            shift = Shift.objects.select_for_update().get(id=self.instance.id)
            shift_models_list = list(shift.only_fans_models.filter(results__shift_end_fact__isnull=True))

            if operator and operator != shift.operator:
                return self._handle_operator_changed(
                    shift,
                    validated_data,
                    operator
                )

            if only_fans_models:
                self._handle_only_fans_models_changed(
                    shift,
                    only_fans_models,
                    shift_models_list,
                )

        self.refresh_shifts_stats([shift], without_ratings=True)

        return shift

    @classmethod
    def __shifts_history_paginator_by_date(
            cls,
            shifts: QuerySet[Shift],
            page_size: int = 1
    ) -> Paginator:
        """
        Get paginator object for shifts history.

        Args:
            shifts (QuerySet[Shift]): List of shifts.
            page_size (int): number of dates per page.
        """
        grouped = {
            key: list(group)
            for key, group in
            groupby(shifts, lambda shift: shift.shift_date)
        }

        return Paginator(list(grouped.items()), page_size)

    @classmethod
    def get_paginated_history_data(
            cls,
            shifts: QuerySet,
            page_number: int,
            serializer: serializers,
    ) -> dict:
        """
        Get shifts history paginated data.

        Args:
            shifts (QuerySet[Shift]): list of shifts
            page_number (int): page number
            serializer (serializers): serializer
        """
        paginator = cls.__shifts_history_paginator_by_date(shifts)
        page = paginator.page(page_number)

        page_data = [
            (date, serializer(shifts, many=True).data)
            for date, shifts in page.object_list
        ]

        return {
            'count': paginator.count,
            'next': page.next_page_number() if page.has_next() else None,
            'previous': page.previous_page_number() if page.has_previous() else None,
            'results': [{"date": date, "shifts": shifts} for date, shifts in page_data]
        }

    @staticmethod
    def _check_is_last_update(shift: Shift, max_interval_updating: int = 180) -> bool:
        """
        Check last update

        Args:
            shift (Shift): shift instance
            max_interval_updating (int): max interval updating in minutes
        """
        return timezone.now() - shift.shift_end_fact >= datetime.timedelta(minutes=max_interval_updating - 60)

    @classmethod
    def update_shift_json_info(
            cls,
            shift: Shift,
            messages_data: dict,
            mass_messages_data: dict,
            max_interval_updating: int = 180,
            commit: bool = True
    ) -> Shift:
        """
        Update shift info

        Args:
            shift (Shift): shift instance
            messages_data (dict): messages data
            mass_messages_data (dict): mass messages data
            max_interval_updating (int): max interval updating in minutes
            commit (bool): commit changes
        """
        shift_info_data = cls.get_statistic(
            shift=shift,
            messages_data=messages_data,
            mass_messages_data=mass_messages_data,
        )

        with transaction.atomic():
            shift.json_info = shift_info_data

            if (
                shift.status == 'end'
                and shift.shift_end_fact
                and cls._check_is_last_update(shift=shift, max_interval_updating=max_interval_updating)
                and not shift.is_updated
            ):
                shift.is_updated = True
            shift.date_updated_stats = timezone.now()

            if commit:
                shift.save()

            return shift

    @classmethod
    def get_history_total_data(
            cls,
            shifts: QuerySet[Shift]
    ) -> list[dict[str, str | list[dict[str, dict | list[dict]]]]]:
        """
        Get history total data

        Args:
            shifts (QuerySet[Shift]): list of shifts
        """
        from shifts.serializers import ShiftListHistorySerializer

        grouped_data = {}
        shift_number_data = {}

        for shift in shifts:
            (
                grouped_data.setdefault(shift.shift_date.strftime('%Y-%m-%d'), {})
                .setdefault(shift.shift_number.id, [])
                .append(ShiftListHistorySerializer(shift).data)
            )
            if shift.shift_number.id not in shift_number_data:
                shift_number_data[shift.shift_number.id] = {
                    'id': shift.shift_number.id,
                    'name':  shift.shift_number.name,
                    'number':  shift.shift_number.index.number,
                }

        return [
            {
                'date': date,
                'shift_numbers': [
                    {
                        'shift_number_info': shift_number_data.get(shift_number_id),
                        'shifts': data
                    }
                    for shift_number_id, data in shift_numbers.items()
                ]
            }
            for date, shift_numbers in grouped_data.items()
        ]

    @classmethod
    def refresh_shifts_stats(
            cls,
            shifts: list[MODEL],
            use_temp_data: bool = False,
            without_ratings: bool = False
    ) -> None:
        """
        Update json_info for shift and replace its stats and shift ratings(if without ratings = False)

        Args:
            shifts: list[Shift]
            use_temp_data: bool,
            without_ratings: bool
        """
        for shifts_chunk in chunks(shifts, 10):
            min_shift_start_fact = cls.get_min_shift_start_fact_from_shift_list(shifts_chunk)
            max_shift_end_fact = cls.get_max_shift_end_fact_from_shift_list(shifts_chunk)
            models = cls.get_models_from_shift_list(shifts_chunk)
            data_collector = OnlyFansDBDataCollectorWithDistance(
                start_time=min_shift_start_fact,
                shifts_models=models,
                end_time=max_shift_end_fact,
                use_temp_data=use_temp_data
            )

            shifts_with_updated_json_info = []
            result_stats_for_update = []
            result_stats_for_create = []
            shift_ratings_for_create = []
            shift_ratings_for_update = []
            for shift in shifts_chunk:
                ShiftSalesService(shift).update_sales_data()

                shifts_with_updated_json_info.append(
                    cls.update_shift_json_info(
                        shift=shift,
                        messages_data=data_collector.messages_data,
                        mass_messages_data=data_collector.mass_messages_data,
                        commit=False
                    )
                )

                rs_create, rs_update = ResultStatsService.create_or_update_shift_results(
                    shift=shift,
                    messages_data=data_collector.messages_data,
                    mass_messages_data=data_collector.mass_messages_data,
                    distance_data=data_collector.distance_data,
                    commit=False
                )
                result_stats_for_create.extend(rs_create)
                result_stats_for_update.extend(rs_update)

                if not without_ratings:
                    sr_create, sr_update = ShiftRatingService.create_or_update_shift_ratings(
                        shift=shift,
                        messages_data=data_collector.messages_data,
                        mass_messages_data=data_collector.mass_messages_data,
                        commit=False
                    )
                    shift_ratings_for_create.extend(sr_create)
                    shift_ratings_for_update.extend(sr_update)

            with transaction.atomic():
                skip_keys = ['only_fans_model', 'shift', 'id', 'operator']

                Shift.objects.bulk_update(
                    shifts_with_updated_json_info,
                    ['json_info', 'date_updated_stats', 'is_updated'],
                    batch_size=50
                )

                ResultStats.objects.bulk_update(
                    result_stats_for_update,
                    [
                        field.name
                        for field in ResultStats._meta.get_fields(include_parents=False)
                        if field.name not in skip_keys
                    ],
                    batch_size=50
                )
                ResultStats.objects.bulk_create(
                    result_stats_for_create,
                    batch_size=50
                )

                ShiftRating.objects.bulk_update(
                    shift_ratings_for_update,
                    [
                        field.name
                        for field in ShiftRating._meta.get_fields(include_parents=False)
                        if field.name not in skip_keys
                    ],
                    batch_size=50
                )
                ShiftRating.objects.bulk_create(
                    shift_ratings_for_create,
                    batch_size=50
                )


class ShiftResultService:
    """
    Shift Result service
    """

    MODEL = ShiftResult

    def __init__(self, instance: MODEL) -> None:
        self.instance = instance

    @classmethod
    def create(cls, validated_data: dict) -> MODEL:
        """
        Create a shift result
        """
        return cls.MODEL.objects.create(**validated_data)


class ShiftEventer:
    """
    The ShiftEventer class is used for sending asynchronous events via Celery
    related to different stages of a shift's (Shift) lifecycle.

    Attributes:
        shift_id (int): The identifier of the shift (Shift) associated with the events.

    """

    def __init__(self, shift: Shift):
        """
        Initializes the ShiftEventer object with the specified shift object.

        Parameters:
            shift (Shift): The shift object associated with the events.
        """
        self.shift = shift
        self.shift_id = str(shift.id)

    def send_event_updated(self) -> None:
        """
       Asynchronously sends an event about the updation of the shift via Celery.
       """
        from shifts.tasks import send_socket_event_shift_updated
        send_socket_event_shift_updated.delay(shift_id=self.shift_id)

    def send_event_created(self) -> None:
        """
        Asynchronously sends an event about the creation of the shift via Celery.
        """
        from shifts.tasks import send_socket_event_shift_created

        send_socket_event_shift_created.delay(shift_id=self.shift_id)

    def send_event_started(self) -> None:
        """
        Asynchronously sends an event about the start of the shift via Celery.
        """
        from shifts.tasks import send_socket_event_shift_started

        send_socket_event_shift_started.delay(shift_id=self.shift_id)

    def send_event_ended(self) -> None:
        """
        Asynchronously sends an event about the end of the shift via Celery.
        """
        from shifts.tasks import send_socket_event_shift_ended

        send_socket_event_shift_ended.delay(shift_id=self.shift_id)

    def send_event_deleted(self) -> None:
        """
        Asynchronously sends an event about the deletion of the shift via Celery.
        """
        from shifts.tasks import send_socket_event_shift_deleted
        users_ids = ShiftService(instance=self.shift).get_users_ids()

        send_socket_event_shift_deleted.delay(shift_id=self.shift_id, users_ids=users_ids)

    def send_event_deleted_for_operator(self, operator_id: str) -> None:
        from shifts.tasks import send_socket_event_shift_deleted

        send_socket_event_shift_deleted.delay(shift_id=self.shift_id, users_ids=[operator_id, ])


class ShiftRatingService:
    """
    Shift Rating service
    """
    @classmethod
    def create_or_update_shift_ratings(
            cls,
            shift: Shift,
            messages_data: dict,
            mass_messages_data: dict,
            commit: bool = True
    ) -> tuple[list[ShiftRating], list[ShiftRating]]:
        """
        Create shift rating if not exist or update. If commit - save to db.
        Returns list for create and list for update

        Args:
            shift (Shift): shift instance
            messages_data (dict): messages data
            mass_messages_data (dict): mass messages data
            commit (bool): commit changes
        """
        skip_keys = ['only_fans_model', 'shift', 'id', 'operator']

        shift_rating_data = RatingService(
            shift=shift,
            messages_data=messages_data,
            mass_messages_data=mass_messages_data,
        ).get_rating_data()
        shift_ratings_dict = {
            shift_rating.only_fans_model: shift_rating for shift_rating in shift.ratings.all()
        }

        shift_ratings_for_update = []
        shift_ratings_for_create = []
        for data in shift_rating_data:
            if model_rating := shift_ratings_dict.get(data.get('only_fans_model')):
                for key, value in data.items():
                    if key in skip_keys:
                        continue
                    setattr(model_rating, key, value)

                shift_ratings_for_update.append(model_rating)
            else:
                shift_ratings_for_create.append(ShiftRating(**data))

        if commit:
            ShiftRating.objects.bulk_update(
                shift_ratings_for_update,
                [
                    field.name
                    for field in ShiftRating._meta.get_fields(include_parents=False)
                    if field.name not in skip_keys
                ],
                batch_size=10
            )
            ShiftRating.objects.bulk_create(
                shift_ratings_for_create,
                batch_size=10
            )

        return shift_ratings_for_create, shift_ratings_for_update


class ResultStatsService:
    """
    Result Stats service
    """
    @classmethod
    def create_or_update_shift_results(
            cls,
            shift: Shift,
            messages_data: dict,
            mass_messages_data: dict,
            distance_data: dict,
            commit: bool = True
    ) -> tuple[list[ResultStats], list[ResultStats]]:
        """
        Create shift result if not exist or update. If commit - save to db.
        Return two list created and updated ResultStats

        Args:
            shift (Shift): shift instance
            messages_data (dict): messages data
            mass_messages_data (dict): mass messages data
            distance_data (dict): distance data
            commit (bool): commit changes
        """
        skip_keys = ['only_fans_model', 'shift', 'id', 'operator']

        stats_service = StatsService(
            shift=shift,
            messages_data=messages_data,
            mass_messages_data=mass_messages_data,
            end_time=shift.shift_end_fact,
            distance_data=distance_data
        )
        shift_stats_dict = {
            result_stat.only_fans_model: result_stat for result_stat in shift.stats.all()
        }
        stats_data = stats_service.get_stats()
        result_stats_for_update = []
        result_stats_for_create = []

        for data in stats_data:
            if model_stat := shift_stats_dict.get(data.get('only_fans_model')):
                for key, value in data.items():
                    if key in skip_keys:
                        continue
                    setattr(model_stat, key, value)

                result_stats_for_update.append(model_stat)
            else:
                result_stats_for_create.append(ResultStats(**data))

        if commit:
            ResultStats.objects.bulk_update(
                result_stats_for_update,
                [
                    field.name
                    for field in ResultStats._meta.get_fields(include_parents=False)
                    if field.name not in skip_keys
                ],
                batch_size=10
            )
            ResultStats.objects.bulk_create(
                result_stats_for_create,
                batch_size=10
            )

        return result_stats_for_create, result_stats_for_update


class OperatorProfitService:
    """
    Operator Profit service
    """
    MODEL = OperatorProfit

    def __init__(self, instance: MODEL) -> None:
        self.instance = instance

    @classmethod
    def create(cls, validated_data: dict) -> MODEL:
        """
        Create a result stats
        """
        return cls.MODEL.objects.create(**validated_data)

    @classmethod
    def create_operator_profits_for_shift(cls, shift: Shift) -> list[MODEL]:
        """
        Create a result stats for shift
        """
        created_profits = []

        profit_service = ProfitService(
            shift=shift,
            messages_data={},
            mass_messages_data={},
            distance_data={},
            end_time=shift.shift_end_fact,
        )
        profit_data = profit_service.get_profits()

        for data in profit_data:
            operator_profit = OperatorProfitService.create(
                data
            )
            created_profits.append(operator_profit)

        return created_profits


class ScheduleTeamService:
    """
    Schedule Team service
    """
    MODEL = ScheduleTeam

    def clear_operators_and_replacements_by_operator(self, operator: User) -> None:
        """
        Clear operators and replacements for a given operator.

        Args:
            operator (User): The operator whose assignments need to be cleared.

        Raises:
            ValueError: If the operator is None.
        """

        self.MODEL.objects.filter(operator=operator).update(operator=None)
        self.MODEL.objects.filter(replacement=operator).update(replacement=None)

    def clear_operators_and_replacements_by_model(self, model: OnlyFansModel) -> None:
        """
        Clear operators and replacements for a given OnlyFans model.

        Args:
            model (OnlyFansModel): The OnlyFans model whose assignments need to be cleared.

        Raises:
            ValueError: If the model is None.
        """
        self.MODEL.objects.filter(only_fans_model=model).update(operator=None, replacement=None)


class ScheduleMonitoringService:
    """
    Schedule Monitoring service
    """
    MODEL = ScheduleMonitoring

    @staticmethod
    def check_shift_number_is_valid(shift_result: ShiftResult, schedule_team: ScheduleTeam) -> bool | None:
        """
        Check shift number is valid
        """
        if not schedule_team:
            return

        return shift_result.shift.shift_number == schedule_team.shift_number

    @staticmethod
    def check_operator_is_valid(shift_result: ShiftResult, schedule_team: ScheduleTeam | None) -> bool | None:
        """
        Check operator is valid
        """
        if not schedule_team:
            return

        return shift_result.shift.operator == schedule_team.operator

    @staticmethod
    def check_replacement_is_valid(shift_result: ShiftResult, schedule_team: ScheduleTeam | None) -> bool | None:
        """
        Check replacement operator is valid
        """
        if not schedule_team:
            return

        return shift_result.shift.operator == schedule_team.replacement

    @staticmethod
    def get_schedule_team_for_shift_result(shift_result: ShiftResult) -> ScheduleTeam | None:
        """
        Get schedule team for shift result
        """
        try:
            return ScheduleTeam.objects.get(
                only_fans_model=shift_result.only_fans_model,
                shift_index=shift_result.shift.shift_number.index
            )
        except ScheduleTeam.DoesNotExist:
            return

    def create_for_single_shift_result(self, shift_result: ShiftResult) -> MODEL:
        """
        Create a schedule monitoring for shift result
        """
        schedule_team = self.get_schedule_team_for_shift_result(shift_result)
        shift_number_is_valid = self.check_shift_number_is_valid(shift_result, schedule_team)
        operator_is_valid = self.check_operator_is_valid(shift_result, schedule_team)
        replacement_is_valid = self.check_replacement_is_valid(shift_result, schedule_team)

        schedule_monitoring, _ = self.MODEL.objects.update_or_create(
            shift_result=shift_result,
            defaults={
                "shift_number": shift_number_is_valid,
                "operator": operator_is_valid,
                "replacement": replacement_is_valid
            }
        )

        return schedule_monitoring

    def create_for_shift_result_list(self, shift_results: list[ShiftResult]) -> list[MODEL]:
        """
        Create a schedule monitoring for shift results
        """
        schedule_monitoring_list = []

        for shift_result in shift_results:
            schedule_monitoring_list.append(self.create_for_single_shift_result(shift_result))

        return schedule_monitoring_list


class ShiftSalesService:
    def __init__(
            self,
            shift: Shift,
    ) -> None:
        self.shift = shift
        self.start_time = self.shift.shift_start_fact if self.shift.shift_start_fact else self.shift.shift_start
        self.end_time = self.shift.shift_end_fact or self.shift.shift_end
        self.shift_results = list(self.shift.results.all())
        self.shift_models = list(set(self.shift.only_fans_models.all()))
        self.shift_models_ids = [model.model_id for model in self.shift_models]
        self.excluded_time_ranges_for_models = self._get_excluded_time_ranges_for_models()

    def update_sales_data(self) -> None:
        """
        Update sales data
        """
        self._update_tingz_sales_data()
        self._update_of_sales_data()

    def _group_data_by_model_ids_with_model_tingz_username(self, rows_data: list[dict]) -> dict[int, list]:
        """
        Group data by model ids with model username

        Args:
            rows_data(list[dict]): data from only_fans_db
        """
        model_username_to_id = {
            model.tingz_username.lower(): model.model_id for model in self.shift_models if model.tingz_username
        }

        result = {}

        for data in rows_data:
            try:
                model_username = data.pop('model_username')

                if not model_username:
                    continue

                model_username = model_username.lower()
                model_id = model_username_to_id.get(model_username)

                if model_id:
                    result.setdefault(model_id, []).append(data)

            except KeyError:
                pass

        return result

    def _update_tingz_sales_data(self):
        """
        Link new tingz shift sales based on new DBSales
        """
        models_tingz_usernames = [
            model.tingz_username.lower()
            for model in self.shift_models
            if model.tingz_username
        ]

        q_tingz_usernames = Q()
        for tingz_username in models_tingz_usernames:
            q_tingz_usernames |= Q(model_username__iexact=tingz_username)

        new_db_sales = DBSale.objects.filter(
            q_tingz_usernames,
            trans_date__range=(self.start_time, self.end_time),
            shift_sales__shift_id__isnull=True,
            sale_source=DBSale.SaleSource.TINGZ
        ).order_by(
            'trans_date'
        ).values(
            'trans_id',
            'model_username',
            'amount',
            'trans_date',
            'sale_type',
            'fan_username',
        )

        model_username_to_id = {
            model.tingz_username.lower().strip(): model.model_id
            for model in self.shift_models
            if model.tingz_username
        }

        grouped_data = {model_id: [] for model_id in self.shift_models_ids}

        for data in new_db_sales:
            try:
                model_username = data.get('model_username')

                if not model_username:
                    continue

                model_username = model_username.lower().strip()
                model_id = model_username_to_id.get(model_username)

                if model_id:
                    grouped_data.setdefault(model_id, []).append(data)
            except KeyError:
                pass

        cleaned_data = self.exclude_data_for_models_in_excluded_time_ranges(
            grouped_data,
            'trans_date'
        )

        link_shift_sales = [
            sale['trans_id']
            for sales in cleaned_data.values()
            for sale in sales
        ]
        ShiftSale.objects.filter(db_sale__trans_id__in=link_shift_sales).update(shift_id=self.shift.id)

    def _update_of_sales_data(self):
        """
        Link new of shift sales based on new DBSales
        """
        new_db_sales = DBSale.objects.filter(
            trans_date__range=(self.start_time, self.end_time),
            model_id__in=self.shift_models_ids,
            shift_sales__shift_id__isnull=True,
            sale_source=DBSale.SaleSource.ONLYFANS
        ).order_by(
            'trans_date'
        ).values(
            'trans_id',
            'model_id',
            'amount',
            'trans_date',
            'sale_type',
            'fan_id',
        )

        grouped_data = {model_id: [] for model_id in self.shift_models_ids}

        for data in new_db_sales:
            try:
                grouped_data[data.pop('model_id')].append(data)

            except KeyError:
                pass

        cleaned_data = self.exclude_data_for_models_in_excluded_time_ranges(
            grouped_data,
            'trans_date'
        )

        link_shift_sales = [
            sale['trans_id']
            for sales in cleaned_data.values()
            for sale in sales
        ]
        ShiftSale.objects.filter(
            db_sale__trans_id__in=link_shift_sales,
            shift_id__isnull=True
        ).update(shift_id=self.shift.id)

    def exclude_data_for_models_in_excluded_time_ranges(
            self,
            data: dict[int, list[dict]],
            time_detect_field: str,
    ) -> dict[int, list[dict]]:
        """
        Exclude data for models in excluded time ranges if they are exist

        Args:
            data(list[dict]): data
            time_detect_field(str): field for detect time to exclude
        """
        if not self.excluded_time_ranges_for_models:
            return data

        cleaned_result = copy.deepcopy(data)

        for model_id, excluded_time_ranges in self.excluded_time_ranges_for_models.items():
            cleaned_data = []
            model_data = data.get(model_id, [])

            for row in model_data:
                try:
                    data_time = row[time_detect_field]
                except KeyError:
                    continue

                exclude = False

                for start_time, end_time in excluded_time_ranges:
                    if end_time and start_time <= data_time <= end_time:
                        exclude = True
                        continue

                    if not end_time and data_time >= start_time:
                        exclude = True
                        continue

                if not exclude:
                    cleaned_data.append(row)

            cleaned_result[model_id] = cleaned_data

        return cleaned_result

    def _get_excluded_time_ranges_for_models(self) -> dict[int, list[list]]:
        """
        Get excluded times for models if number of models is not equal to number of shift results
        """
        excluded_time_ranges_for_models = {}

        if len(self.shift_models) == len(self.shift_results):
            return excluded_time_ranges_for_models

        for model in self.shift_models:
            model_results = self._get_model_shift_results(model)

            if len(model_results) <= 1:
                continue

            results_time_ranges = [
                (result.shift_start_fact, result.shift_end_fact)
                for result in model_results
            ]

            for start_fact, end_fact in results_time_ranges:
                if not excluded_time_ranges_for_models.get(model.model_id):
                    excluded_time_ranges_for_models[model.model_id] = [[end_fact, None]]
                else:
                    last_excluded_range = excluded_time_ranges_for_models[model.model_id][-1]

                    if (
                            last_excluded_range[1] is None
                            and start_fact is not None
                    ):
                        excluded_time_ranges_for_models[model.model_id][-1][1] = start_fact
                    if (
                            last_excluded_range[1] is not None
                            and end_fact is not None
                    ):
                        excluded_time_ranges_for_models[model.model_id].append([end_fact, None])

        return excluded_time_ranges_for_models

    def _get_model_shift_results(self, model: type[OnlyFansModel]) -> list[ShiftResult]:
        """
        Get model shift results

        Args:
            model(OnlyFansModel): model
        """
        shift_results = list(
            filter(
                lambda result: result.only_fans_model_id == model.id,
                self.shift_results
            )
        )

        if len(shift_results) > 1:
            shift_results = sorted(
                shift_results,
                key=lambda result: (result.shift_start_fact is None, result.shift_start_fact)
            )

        return shift_results
