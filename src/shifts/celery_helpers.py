from shifts.models import Shift


def celery_shift_stats_updater(shift: Shift) -> None:
    from shifts.tasks import update_shift_stats_by_shift_id

    for i in range(1, 3):
        update_shift_stats_by_shift_id.apply_async(
            kwargs={'shift_id': shift.id},
            countdown=i * 20 * 60
        )

    for i in range(1, 3):
        update_shift_stats_by_shift_id.apply_async(
            kwargs={'shift_id': shift.id},
            countdown=(60 + i * 60) * 60
        )

    for i in range(1, 3):
        update_shift_stats_by_shift_id.apply_async(
            kwargs={'shift_id': shift.id},
            countdown=24 * 60 * 60 * i
        )
