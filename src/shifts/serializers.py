import pytz
from django.contrib.auth import get_user_model
from django.db import transaction
from django.utils.timezone import make_aware
from drf_spectacular.utils import extend_schema_field
from rest_framework import serializers

from base.validators import SerializerAttrsValidator
from only_fans_models.models import OnlyFansModel
from only_fans_models.serializers import OnlyFansModelsSerializer
from shifts.models import (
    ResultStats,
    ScheduleTeam,
    Shift,
    ShiftIndex,
    ShiftNumber,
    TeamLeadABChat,
    TeamLeadABConfig,
)
from shifts.services import (
    ShiftEventer,
    ShiftNumberService,
    ShiftService,
)
from shifts.validators import ShiftValidator

User = get_user_model()


class ShiftSerializer(serializers.ModelSerializer):
    """
    Base serializer for shift.
    """
    class Meta:
        model = Shift
        fields = '__all__'


class ModelHourlyStatisticSerializer(serializers.Serializer):
    """
    Serializer class for model hourly statistic.
    """
    time = serializers.CharField(required=False, allow_null=True)
    total_sales_amount = serializers.DecimalField(
        required=False,
        allow_null=True,
        decimal_places=0,
        max_digits=10,
        coerce_to_string=False
    )
    total_tingz_sales_amount = serializers.DecimalField(
        required=False,
        allow_null=True,
        decimal_places=0,
        max_digits=10,
        coerce_to_string=False
    )
    count_unique_chats = serializers.IntegerField(required=False, allow_null=True)
    count_mass_messages = serializers.IntegerField(required=False, allow_null=True)
    count_outgoing_messages = serializers.IntegerField(required=False, allow_null=True)


class ModelStatisticSerializer(serializers.Serializer):
    """
    Serializer class for model statistic.
    """
    model_id = serializers.IntegerField(required=False, allow_null=True)
    id = serializers.UUIDField(required=False, allow_null=True)
    name = serializers.CharField(required=False, allow_null=True)
    spammer_key = serializers.CharField(required=False, allow_null=True)
    detail = ModelHourlyStatisticSerializer(many=True, required=False, allow_null=True)
    total_sales_amount = serializers.DecimalField(
        required=False,
        allow_null=True,
        decimal_places=0,
        max_digits=10,
        coerce_to_string=False
    )
    total_tingz_sales_amount = serializers.DecimalField(
        required=False,
        allow_null=True,
        decimal_places=0,
        max_digits=10,
        coerce_to_string=False
    )
    count_unique_chats = serializers.IntegerField(required=False, allow_null=True)
    count_mass_messages = serializers.IntegerField(required=False, allow_null=True)
    count_outgoing_messages = serializers.IntegerField(required=False, allow_null=True)
    average_speed_answer = serializers.CharField(required=False, allow_null=True)
    average_speed_answer_by_chat = serializers.CharField(required=False, allow_null=True)
    ended = serializers.BooleanField(required=False, allow_null=True)


class TotalStatisticSerializer(serializers.Serializer):
    """
    Serializer class for total statistic.
    """
    total_mass_messages = serializers.IntegerField(required=False, allow_null=True)
    total_outgoing_messages = serializers.IntegerField(required=False, allow_null=True)
    total_unique_chats = serializers.IntegerField(required=False, allow_null=True)
    average_speed_answer = serializers.CharField(required=False, allow_null=True)
    average_speed_answer_by_chat = serializers.CharField(required=False, allow_null=True)
    last_operator_message_time = serializers.CharField(required=False, allow_null=True)


class ShiftInfoSerializer(serializers.Serializer):
    """
    Serializer class for shift information.
    """
    models = ModelStatisticSerializer(many=True, required=False, allow_null=True)
    total_statistic = TotalStatisticSerializer(required=False, allow_null=True)


class ShiftIndexSerializer(serializers.ModelSerializer):
    """
    Serializer class for shift index.
    """
    class Meta:
        model = ShiftIndex
        fields = (
            'id',
            'number',
        )


class ShiftNumberSerializer(serializers.ModelSerializer):
    """
    Serializer class for shift number.
    """
    index = ShiftIndexSerializer(read_only=True)

    class Meta:
        model = ShiftNumber
        exclude = ('team_leads', 'created_at', 'updated_at')


class ShiftOperatorTeamLeadSerializer(serializers.ModelSerializer):
    """
    Serializer class for shift operator and team_lead.
    """
    class Meta:
        model = User
        fields = (
            'id',
            'full_name'
        )


class ResultStatsSerializer(serializers.ModelSerializer):
    """
    Serializer class for result stats.
    """
    only_fans_model = OnlyFansModelsSerializer()

    class Meta:
        model = ResultStats
        fields = (
            'replied_chats',
            'active_chats',
            'chats_active_to_replied',
            'chats_with_paid_content',
            'chats_with_payments',
            'chats_active_to_with_payments',
            'revenue_per_fan',
            'average_bill',
            'average_payments',
            'only_fans_model'
        )


class ShiftListSerializer(serializers.ModelSerializer):
    """
    Serializer class for shift list.
    """
    shift_number = ShiftNumberSerializer()
    shift_info = ShiftInfoSerializer(source='json_info', allow_null=True)
    operator = ShiftOperatorTeamLeadSerializer(allow_null=True)
    team_lead = ShiftOperatorTeamLeadSerializer()
    duration = serializers.SerializerMethodField()
    shift_start_utc = serializers.SerializerMethodField()
    shift_end_utc = serializers.SerializerMethodField()
    stats = serializers.SerializerMethodField()

    class Meta:
        model = Shift
        fields = (
            'id',
            'shift_date',
            'shift_number',
            'shift_start',
            'shift_end',
            'shift_start_utc',
            'shift_end_utc',
            'status',
            'duration',
            'shift_info',
            'operator',
            'team_lead',
            'is_updated',
            'date_updated_stats',
            'stats'
        )

    def get_duration(self, obj):
        duration = obj.duration
        formatted_duration = str(duration).split('.')[0]

        return formatted_duration

    def get_shift_start_utc(self, obj):
        aware_shift_start = make_aware(obj.shift_start)
        utc_time = aware_shift_start.astimezone(pytz.utc)

        return utc_time.strftime("%Y-%m-%dT%H:%M:%S")

    def get_shift_end_utc(self, obj):
        aware_shift_end = make_aware(obj.shift_end)
        utc_time = aware_shift_end.astimezone(pytz.utc)

        return utc_time.strftime("%Y-%m-%dT%H:%M:%S")

    @extend_schema_field(ResultStatsSerializer(many=True))
    def get_stats(self, obj: Shift):
        filtered_stats = []
        if obj.status == 'progress':
            for stat in obj.stats.all():
                if list(
                        filter(
                            lambda x: x.get('id') == str(stat.only_fans_model.id) and not x.get('ended'),
                            obj.json_info.get('models', [])
                        )
                ):
                    filtered_stats.append(stat)
        else:
            filtered_stats = obj.stats.all()

        return ResultStatsSerializer(filtered_stats, many=True).data


class NestedShiftNumberAlarmBotSerializer(serializers.ModelSerializer):
    """
    Nested ShiftNumber serializer for the ShiftAlarmBotSerializer
    """
    index = serializers.IntegerField(source='index.number', read_only=True)

    class Meta:
        model = ShiftNumber
        fields = (
            'id',
            'name',
            'index'
        )


class NestedModelStatisticAlarmBotSerializer(serializers.Serializer):
    """
    Nested Serializer class for model statistic for alarm bot
    """
    model_id = serializers.IntegerField(required=False, allow_null=True)
    id = serializers.UUIDField(required=False, allow_null=True)
    name = serializers.CharField(required=False, allow_null=True)
    spammer_key = serializers.CharField(required=False, allow_null=True)
    detail = ModelHourlyStatisticSerializer(many=True, required=False, allow_null=True)
    total_sales_amount = serializers.DecimalField(
        required=False,
        allow_null=True,
        decimal_places=0,
        max_digits=10,
        coerce_to_string=False
    )
    total_tingz_sales_amount = serializers.DecimalField(
        required=False,
        allow_null=True,
        decimal_places=0,
        max_digits=10,
        coerce_to_string=False
    )
    count_unique_chats = serializers.IntegerField(required=False, allow_null=True)
    count_mass_messages = serializers.IntegerField(required=False, allow_null=True)
    count_outgoing_messages = serializers.IntegerField(required=False, allow_null=True)
    average_speed_answer = serializers.CharField(required=False, allow_null=True)
    average_speed_answer_by_chat = serializers.CharField(required=False, allow_null=True)
    ended = serializers.BooleanField(required=False, allow_null=True)
    active_chats = serializers.IntegerField(required=False, allow_null=True)
    replied_conversion = serializers.FloatField(required=False, allow_null=True)
    chats_with_payments = serializers.IntegerField(required=False, allow_null=True)
    payment_conversion = serializers.FloatField(required=False, allow_null=True)
    revenue_per_fan = serializers.FloatField(required=False, allow_null=True)
    new_fans_count = serializers.IntegerField(required=False, allow_null=True)
    old_fans_count = serializers.IntegerField(required=False, allow_null=True)
    new_fans_sales = serializers.IntegerField(required=False, allow_null=True)
    old_fans_sales = serializers.IntegerField(required=False, allow_null=True)
    expected_revenue = serializers.IntegerField(required=False, allow_null=True)
    chats_with_paid_content = serializers.IntegerField(required=False, allow_null=True)
    paid_content_conversion = serializers.FloatField(required=False, allow_null=True)


class NestedShiftInfoAlarmBotSerializer(serializers.Serializer):
    """
    Nested Serializer class for shift info for alarm bot
    """
    models = NestedModelStatisticAlarmBotSerializer(many=True, required=False, allow_null=True)
    total_statistic = TotalStatisticSerializer(required=False, allow_null=True)


class ShiftAlarmBotSerializer(serializers.ModelSerializer):
    """
    Serializer class for alarm bot
    """

    shift_number = NestedShiftNumberAlarmBotSerializer()
    shift_info = NestedShiftInfoAlarmBotSerializer(source='json_info', allow_null=True)
    operator = serializers.CharField(source='operator.full_name', read_only=True, allow_null=True)
    team_lead = serializers.CharField(source='team_lead.full_name', read_only=True)
    team_lead_telegram_id = serializers.IntegerField(
        source='team_lead.telegram_id', read_only=True
    )

    class Meta:
        model = Shift
        fields = (
            'id',
            'shift_number',
            'shift_start_fact',
            'shift_info',
            'operator',
            'team_lead',
            'team_lead_telegram_id',
            'shift_start',
            'shift_end'
        )


class ShiftCelerySerializer(ShiftListSerializer):
    """
    A serializer class for converting Shift model instances into JSON representations
    with additional fields related to shift information and user IDs.

    This serializer extends the ShiftListSerializer and adds fields like shift_info,
    operator, team_lead, and users_ids for the convenience of the consumer
    """
    users_ids = serializers.SerializerMethodField()

    class Meta(ShiftListSerializer.Meta):
        fields = ShiftListSerializer.Meta.fields + ('users_ids',)

    def get_users_ids(self, obj: Shift):
        """
        Retrieve a list of user IDs associated with the given shift.

        Args:
            obj (Shift): The Shift model instance for which user IDs need to be retrieved.

        Returns:
            list: A list of unique user IDs, including the team lead, operator, and
                any additional senior operators associated with the shift.
        """
        users_ids = [str(obj.team_lead.pk), str(obj.operator.pk)]
        if self.context.get('superusers_ids'):
            users_ids += self.context.get('superusers_ids')

        for shift_data in self.context['senior_operators_ids']:
            if not shift_data.get('shift_id') or not shift_data.get('senior_operator_id'):
                continue
            if str(shift_data.get('shift_id')) == str(obj.pk):
                senior_operator_id = str(shift_data.get('senior_operator_id'))
                users_ids.append(senior_operator_id)

        return list(set(users_ids))


class ShiftCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating a shift
    """
    operator = serializers.PrimaryKeyRelatedField(
        required=True,
        queryset=User.objects.filter(role__name='operator')
    )
    shift_number = serializers.PrimaryKeyRelatedField(
        required=True,
        queryset=ShiftNumber.objects.all()
    )
    only_fans_models = serializers.PrimaryKeyRelatedField(
        required=True,
        queryset=OnlyFansModel.objects.all(),
        many=True
    )

    class Meta:
        model = Shift
        fields = (
            'id',
            'shift_date',
            'shift_number',
            'only_fans_models',
            'operator',
            'shift_start',
            'shift_end',
            'status',
            'team_lead',
        )
        read_only_fields = (
            'id',
            'shift_start',
            'shift_end',
            'status',
            'team_lead'
        )

    def validate(self, attrs):
        """
        Validate the shift
        """
        start_time, end_time = (
            ShiftNumberService(
                attrs['shift_number']
            ).generate_start_end_time_datetime(attrs['shift_date'])
        )
        attrs['shift_start'] = start_time
        attrs['shift_end'] = end_time

        ShiftValidator.validate_create_shift(attrs)

        return attrs

    def create(self, validated_data):
        """
        Create a shift. Set the start and end time of the shift.
        """
        return ShiftService.create(validated_data)


class ShiftUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for updating a shift
    """
    only_fans_models = serializers.PrimaryKeyRelatedField(
        queryset=OnlyFansModel.objects.all(),
        many=True
    )
    operator = serializers.PrimaryKeyRelatedField(
        queryset=User.objects.filter(role__name='operator')
    )

    class Meta:
        model = Shift
        fields = (
            'only_fans_models',
            'operator',
        )

    def validate(self, attrs):
        """
        Validate the updating data
        """
        attrs_validator = SerializerAttrsValidator(attrs)

        if "only_fans_models" in attrs:
            attrs_validator.validate_len_of_objects(
                "only_fans_models",
                1,
                3
            )

        return attrs

    def update(self, instance, validated_data):
        """
        Update a shift.
        """
        ShiftValidator(instance).validate_update_shift(validated_data)

        if instance.status != 'progress':
            with transaction.atomic():
                old_operator_id = instance.operator_id
                res = super().update(instance, validated_data)
                shift_eventer = ShiftEventer(shift=instance)
                new_operator_id = validated_data.get('operator').id if validated_data.get('operator') else old_operator_id
                if old_operator_id != new_operator_id:
                    if instance.status == 'hold':
                        instance.refresh_from_db()
                        instance.operator_name = instance.operator.full_name
                        instance.save()

                    shift_eventer.send_event_deleted_for_operator(operator_id=str(old_operator_id))
                    shift_eventer.send_event_created()
                if validated_data.get('only_fans_models') is not None:
                    instance.json_info = ShiftService.get_statistic(
                        shift=instance,
                        messages_data={},
                        mass_messages_data={},
                    )
                    instance.save()
                    shift_eventer.send_event_updated()

            return res

        return ShiftService(instance).update_active_shift(validated_data)


class ShiftStartEndResponseSerializer(serializers.Serializer):
    """
    Serializer class for shift start and end response
    """
    message = serializers.CharField()
    duration = serializers.CharField()


class NestedShiftNumberSerializer(serializers.ModelSerializer):
    """
    Nested ShiftNumber serializer
    """
    index = ShiftIndexSerializer(read_only=True)

    class Meta:
        model = ShiftNumber
        fields = ('id', 'name', 'index')


class ShiftListHistorySerializer(ShiftListSerializer):
    """
    Serializer class for shift list history.
    """
    shift_number = NestedShiftNumberSerializer()

    class Meta:
        model = Shift
        fields = (
            'id',
            'shift_date',
            'shift_number',
            'duration',
            'shift_info',
            'operator',
            'team_lead',
            'status',
            'stats'
        )


class ShiftDateSerializer(serializers.Serializer):
    """
    Serializer class for shift date for drf spectacular.
    """
    date = serializers.DateField()
    shifts = ShiftListSerializer(many=True)


class PaginatedShiftDateSerializer(serializers.Serializer):
    """
    Serializer class for paginated shift date for drf spectacular.
    """
    count = serializers.IntegerField()
    next = serializers.IntegerField(allow_null=True, required=False)
    previous = serializers.IntegerField(allow_null=True, required=False)
    results = ShiftDateSerializer(many=True)


# --------------------------------------------------Schedule Team--------------------------------------

class ScheduleTeamListSerializer(serializers.ModelSerializer):
    """
    Serializer class for schedule team list.
    """
    only_fans_model = OnlyFansModelsSerializer()
    shift_number = NestedShiftNumberSerializer()
    operator = ShiftOperatorTeamLeadSerializer()
    replacement = ShiftOperatorTeamLeadSerializer()
    shift_index = ShiftIndexSerializer()

    class Meta:
        model = ScheduleTeam
        fields = (
            'id',
            'only_fans_model',
            'shift_number',
            'operator',
            'replacement',
            'shift_index'
        )


class ScheduleTeamCreateSerializer(serializers.ModelSerializer):
    """
    Serializer class for schedule team list.
    """
    class Meta:
        model = ScheduleTeam
        fields = (
            'only_fans_model',
            'shift_number',
            'operator',
            'replacement',
            'shift_index'
        )


class ScheduleTeamUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer class for schedule team update.
    """
    operator = serializers.PrimaryKeyRelatedField(
        queryset=User.objects.filter(role__name='operator'), allow_null=True
    )
    replacement = serializers.PrimaryKeyRelatedField(
        queryset=User.objects.filter(role__name='operator'), allow_null=True
    )

    class Meta:
        model = ScheduleTeam
        fields = (
            'id',
            'operator',
            'shift_number',
            'replacement',
        )


# ------------------------------------------------------Alarm Bot--------------------------------------
class AlarmBotTeamLeadMetricsSerializer(serializers.ModelSerializer):
    """
    Serializer class for team lead metrics for alarm bot
    """
    team_lead_telegram_id = serializers.IntegerField(
        source='team_lead.telegram_id', read_only=True
    )

    class Meta:
        model = TeamLeadABConfig
        fields = (
            'team_lead_telegram_id',
            'max_speed_answer_timedelta',
            'min_replied_conversion',
            'min_payment_conversion',
            'min_revenue_per_fan',
            'shift_numbers_plan',
        )
        read_only_fields = fields


class AlarmBotTeamLeadChatsSerializer(serializers.ModelSerializer):
    """
    Serializer class for team lead chats for alarm bot
    """
    shift_number_index = serializers.IntegerField(source='shift_number_index.number', read_only=True)
    team_lead_telegram_id = serializers.IntegerField(
        source='team_lead_ab_config.team_lead.telegram_id', read_only=True
    )

    class Meta:
        model = TeamLeadABChat
        fields = (
            'team_lead_telegram_id',
            'forum_id',
            'thread_id',
            'shift_number_index',
        )
        read_only_fields = fields
