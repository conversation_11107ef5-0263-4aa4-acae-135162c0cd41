from drf_spectacular.types import OpenApiTypes
from drf_spectacular.utils import OpenApiExample, OpenApiParameter

SHIFT_HISTORY_TOTAL_SCHEMA = {
    "parameters": [
        OpenApiParameter(
            name='page',
            type=OpenApiTypes.INT,
            description='Page number',
            required=False,
        ),
        OpenApiParameter(
            name='date_after',
            type=OpenApiTypes.DATE,
            description='Filter by shift_date date after this date',
            required=False,
        ),
        OpenApiParameter(
            name='date_before',
            type=OpenApiTypes.DATE,
            description='Filter by shift_date date before this date',
            required=False,
        ),
        OpenApiParameter(
            name='only_fans_models',
            type=OpenApiTypes.UUID,
            description='Filter by OnlyFansModel ID',
            required=False,
            many=True,
        ),
        OpenApiParameter(
            name='team_leads',
            type=OpenApiTypes.UUID,
            description='Filter by TeamLead ID',
            required=False,
            many=True,
        ),
        OpenApiParameter(
            name='operators',
            type=OpenApiTypes.UUID,
            description='Filter by Operator ID',
            required=False,
            many=True,
        ),
        OpenApiParameter(
            name="shift_numbers",
            type=OpenApiTypes.UUID,
            description="Filter by ShiftNumber ID",
            required=False,
            many=True,
        )
    ],
    'examples': [
        OpenApiExample(
            'Example response',
            value={
                "next": 2,
                "previous": None,
                "count": 5849,
                "total_pages": 100,
                "results": [
                    {
                        "date": "2024-01-29",
                        "shift_numbers": [
                            {
                                "shift_number_info": {
                                    "id": "d52db429-0d02-41b4-95e3-2e20ee600f89",
                                    "name": "1 shift",
                                    "number": 1
                                },
                                "shifts": [
                                    {
                                        "id": "e16ca061-6fb8-411a-9f0f-1af5ab08db18",
                                        "shift_date": "2024-01-29",
                                        "shift_number": {
                                            "id": "d52db429-0d02-41b4-95e3-2e20ee600f89",
                                            "name": "1 shift",
                                            "number": 1
                                        },
                                        "duration": "7:10:25",
                                        "shift_info": {
                                            "models": [
                                                {
                                                    "id": "a1e68efd-a1e2-4d5f-9a13-b85cf568abce",
                                                    "name": "kristy.fit",
                                                    "ended": True,
                                                    "detail": [
                                                        {
                                                            "time": "05:00 - 06:00",
                                                            "count_unique_chats": 6,
                                                            "total_sales_amount": 36,
                                                            "count_mass_messages": 2,
                                                            "count_outgoing_messages": 21
                                                        },
                                                    ],
                                                    "model_id": 266818868,
                                                    "spammer_key": "8d73c66ed410e59f7e1a9be6602f1e51",
                                                    "count_unique_chats": 48,
                                                    "total_sales_amount": 80,
                                                    "count_mass_messages": 2,
                                                    "average_speed_answer": "0:03:00",
                                                    "count_outgoing_messages": 348,
                                                    "average_speed_answer_by_chat": "0:04:13"
                                                }
                                            ],
                                            "total_statistic": {
                                                "total_unique_chats": 48,
                                                "total_mass_messages": 2,
                                                "average_speed_answer": "0:03:00",
                                                "total_outgoing_messages": 348,
                                                "last_operator_message_time": "12:11",
                                                "average_speed_answer_by_chat": "0:04:13"
                                            }
                                        },
                                        "operator": {
                                            "id": "6edfaf4d-d67a-466d-97ef-e48657fdab9b",
                                            "full_name": "Валя Сидоренко"
                                        },
                                        "team_lead": {
                                            "id": "46fe53b8-6626-4b30-8af5-9611c7d4ad0a",
                                            "full_name": "Татьяна Назымок"
                                        }
                                    }
                                ]
                            }
                        ]
                    }
                ]
            },
            response_only=True,
        )
    ]
}
