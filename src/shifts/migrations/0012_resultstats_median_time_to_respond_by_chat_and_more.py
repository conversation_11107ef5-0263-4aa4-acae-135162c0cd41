# Generated by Django 4.2.2 on 2024-01-17 13:08

import datetime

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("shifts", "0011_operatorprofit"),
    ]

    operations = [
        migrations.AddField(
            model_name="resultstats",
            name="median_time_to_respond_by_chat",
            field=models.DurationField(
                default=datetime.timedelta(0),
                help_text="Median speed answer time by chat",
            ),
        ),
        migrations.AddField(
            model_name="resultstats",
            name="median_time_to_respond_by_message",
            field=models.DurationField(
                default=datetime.timedelta(0), help_text="Median speed answer time"
            ),
        ),
    ]
