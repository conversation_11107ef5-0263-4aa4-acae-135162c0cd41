# Generated by Django 4.2.2 on 2025-01-30 19:38

import base.models
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("shifts", "0018_resultstats_new_fans_count_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="DBSale",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "trans_id",
                    models.CharField(
                        help_text="Transaction ID",
                        max_length=255,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("trans_date", models.DateTimeField(help_text="Transaction date")),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2, help_text="Amount", max_digits=10
                    ),
                ),
                (
                    "fan_username",
                    models.<PERSON>r<PERSON><PERSON>(help_text="Fan username", max_length=255),
                ),
                ("model_id", models.BigIntegerField(help_text="Model ID")),
                (
                    "sale_source",
                    models.CharField(
                        choices=[("onlyfans", "OnlyFans"), ("tingz", "Tingz")],
                        help_text="Sale source",
                        max_length=20,
                    ),
                ),
                (
                    "sale_type",
                    models.IntegerField(
                        choices=[(1, "Subscription"), (2, "Tip"), (3, "Paid message")],
                        help_text="Sale type",
                        null=True,
                    ),
                ),
            ],
            options={
                "ordering": ["-trans_date"],
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.CreateModel(
            name="ShiftSale",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2, help_text="Amount", max_digits=10
                    ),
                ),
                (
                    "db_sale",
                    models.ForeignKey(
                        help_text="Reference to DBSale",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="shift_sales",
                        to="shifts.dbsale",
                    ),
                ),
                (
                    "shift",
                    models.ForeignKey(
                        help_text="Reference to Shift",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="sales",
                        to="shifts.shift",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.AddIndex(
            model_name="dbsale",
            index=models.Index(
                fields=["trans_date", "fan_username", "model_id"],
                name="shifts_dbsa_trans_d_9fc78c_idx",
            ),
        ),
    ]
