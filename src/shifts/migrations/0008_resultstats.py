# Generated by Django 4.2.2 on 2023-08-21 09:59

import datetime
import uuid

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models

import base.models


class Migration(migrations.Migration):

    dependencies = [
        ("shifts", "0007_shift_date_updated_stats_shift_is_updated"),
    ]

    operations = [
        migrations.CreateModel(
            name="ResultStats",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "revenue",
                    models.IntegerField(default=0, help_text="Total sales revenue"),
                ),
                (
                    "outgoing_messages",
                    models.IntegerField(
                        default=0, help_text="Number of outgoing messages"
                    ),
                ),
                (
                    "revenue_tingz",
                    models.IntegerField(
                        default=0, help_text="Total sales revenue in tingz"
                    ),
                ),
                (
                    "incoming_messages",
                    models.IntegerField(
                        default=0, help_text="Number of incoming messages"
                    ),
                ),
                (
                    "messages_with_content",
                    models.IntegerField(
                        default=0, help_text="Number of messages with content"
                    ),
                ),
                (
                    "messages_with_paid_content",
                    models.IntegerField(
                        default=0, help_text="Number of messages with paid content"
                    ),
                ),
                (
                    "paid_messages",
                    models.IntegerField(default=0, help_text="Number of paid messages"),
                ),
                (
                    "chats_with_payments",
                    models.IntegerField(
                        default=0, help_text="Number of chats with payments"
                    ),
                ),
                (
                    "outgoing_symbols",
                    models.IntegerField(
                        default=0, help_text="Number of outgoing symbols"
                    ),
                ),
                (
                    "total_chats",
                    models.IntegerField(default=0, help_text="Total number of chats"),
                ),
                (
                    "active_chats",
                    models.IntegerField(default=0, help_text="Number of active chats"),
                ),
                (
                    "replied_chats",
                    models.IntegerField(default=0, help_text="Number of replied chats"),
                ),
                (
                    "not_replied_chats",
                    models.IntegerField(
                        default=0, help_text="Number of not replied chats"
                    ),
                ),
                (
                    "chats_with_paid_content",
                    models.IntegerField(
                        default=0, help_text="Number of chats with paid content"
                    ),
                ),
                (
                    "chats_tingz",
                    models.IntegerField(
                        default=0, help_text="Number of chats in tingz"
                    ),
                ),
                (
                    "time_to_respond_by_message",
                    models.DurationField(
                        default=datetime.timedelta(0), help_text="Average speed answer"
                    ),
                ),
                (
                    "time_to_respond_by_chat",
                    models.DurationField(
                        default=datetime.timedelta(0),
                        help_text="Average speed answer by chat",
                    ),
                ),
                (
                    "shift_result",
                    models.OneToOneField(
                        help_text="Shift result",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="stat",
                        to="shifts.shiftresult",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
    ]
