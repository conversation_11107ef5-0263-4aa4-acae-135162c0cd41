# Generated by Django 4.2.2 on 2023-09-22 18:42

import datetime

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("shifts", "0009_remove_resultstats_shift_result_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="resultstats",
            name="afk_summ",
            field=models.DurationField(
                default=datetime.timedelta(0), help_text="Total time of afk"
            ),
        ),
        migrations.AddField(
            model_name="resultstats",
            name="max_afk_duration",
            field=models.DurationField(
                default=datetime.timedelta(0), help_text="Max afk duration"
            ),
        ),
    ]
