# Generated by Django 4.2.2 on 2024-05-30 15:42

import uuid

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models

import base.models


class Migration(migrations.Migration):

    dependencies = [
        ("shifts", "0016_resultstats_payments_count"),
    ]

    operations = [
        migrations.CreateModel(
            name="ScheduleMonitoring",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "shift_number",
                    models.BooleanField(help_text="Shift number is valid", null=True),
                ),
                (
                    "operator",
                    models.BooleanField(help_text="Operator is valid", null=True),
                ),
                (
                    "replacement",
                    models.<PERSON>oleanField(help_text="Replacement is valid", null=True),
                ),
                (
                    "shift_result",
                    models.OneToOneField(
                        help_text="Reference to ShiftResult",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="monitoring",
                        to="shifts.shiftresult",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
    ]
