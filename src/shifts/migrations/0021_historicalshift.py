# Generated by Django 4.2.2 on 2025-02-28 15:22

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import simple_history.models
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("shifts", "0020_remove_dbsale_shifts_dbsa_trans_d_9fc78c_idx_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="HistoricalShift",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(blank=True, editable=False, null=True),
                ),
                (
                    "id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, editable=False),
                ),
                ("shift_date", models.DateField(help_text="Shift date")),
                (
                    "status",
                    models.Char<PERSON>ield(
                        choices=[
                            ("hold", "Ожидает начала"),
                            ("end", "Закончена"),
                            ("overdue", "Опоздание"),
                            ("progress", "В работе"),
                        ],
                        default="hold",
                        help_text="Status from choices(hold, end, overdue, progress)",
                        max_length=100,
                    ),
                ),
                (
                    "shift_start",
                    models.DateTimeField(help_text="Shift number start time"),
                ),
                ("shift_end", models.DateTimeField(help_text="Shift number end time")),
                (
                    "shift_start_fact",
                    models.DateTimeField(
                        blank=True, help_text="Shift fact start time", null=True
                    ),
                ),
                (
                    "shift_end_fact",
                    models.DateTimeField(
                        blank=True, help_text="Shift fact end time", null=True
                    ),
                ),
                (
                    "shift_duration",
                    models.DurationField(
                        blank=True, help_text="Shift duration", null=True
                    ),
                ),
                (
                    "json_info",
                    models.JSONField(blank=True, help_text="Shift result", null=True),
                ),
                (
                    "operator_name",
                    models.CharField(
                        blank=True,
                        help_text="Operator full name",
                        max_length=255,
                        null=True,
                    ),
                ),
                (
                    "is_updated",
                    models.BooleanField(
                        default=False,
                        help_text="Information about shift has fully updated statistics",
                    ),
                ),
                (
                    "date_updated_stats",
                    models.DateTimeField(
                        blank=True,
                        help_text="Date when shift statistics was updated",
                        null=True,
                    ),
                ),
                ("history_id", models.AutoField(primary_key=True, serialize=False)),
                ("history_date", models.DateTimeField(db_index=True)),
                ("history_change_reason", models.CharField(max_length=100, null=True)),
                (
                    "history_type",
                    models.CharField(
                        choices=[("+", "Created"), ("~", "Changed"), ("-", "Deleted")],
                        max_length=1,
                    ),
                ),
                (
                    "history_user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "operator",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        help_text="Operator",
                        limit_choices_to={"role__name": "operator"},
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "shift_number",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        help_text="Reference to Shift number",
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="shifts.shiftnumber",
                    ),
                ),
                (
                    "team_lead",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        help_text="Team lead",
                        limit_choices_to={"role__name": "team_lead"},
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "historical shift",
                "verbose_name_plural": "historical shifts",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
    ]
