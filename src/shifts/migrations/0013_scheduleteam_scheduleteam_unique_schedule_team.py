# Generated by Django 4.2.2 on 2024-02-01 11:23

import uuid

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models

import base.models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("only_fans_models", "0010_modelpagetype_modelstatus_and_more"),
        ("shifts", "0012_resultstats_median_time_to_respond_by_chat_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="ScheduleTeam",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "only_fans_model",
                    models.ForeignKey(
                        help_text="Reference to OnlyFansModel",
                        on_delete=django.db.models.deletion.RESTRICT,
                        related_name="schedule_teams",
                        to="only_fans_models.onlyfansmodel",
                    ),
                ),
                (
                    "operator",
                    models.OneToOneField(
                        blank=True,
                        help_text="Main Operator",
                        limit_choices_to={"role__name": "operator"},
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="operator_schedule_team",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "replacement",
                    models.ForeignKey(
                        blank=True,
                        help_text="Operator for replacement",
                        limit_choices_to={"role__name": "operator"},
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="replacement_schedule_teams",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "shift_number",
                    models.ForeignKey(
                        blank=True,
                        help_text="Reference to Shift number",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="schedule_teams",
                        to="shifts.shiftnumber",
                    ),
                ),
            ],
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.AddConstraint(
            model_name="scheduleteam",
            constraint=models.UniqueConstraint(
                fields=("only_fans_model", "shift_number"), name="unique_schedule_team"
            ),
        ),
    ]
