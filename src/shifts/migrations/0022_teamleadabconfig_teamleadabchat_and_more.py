# Generated by Django 4.2.2 on 2025-04-01 13:51

import base.models
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("shifts", "0021_historicalshift"),
    ]

    operations = [
        migrations.CreateModel(
            name="TeamLeadABConfig",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "max_spead_answer_timedelta",
                    models.DurationField(help_text="Max speed answer timedelta"),
                ),
                (
                    "min_replied_conversion",
                    models.FloatField(help_text="Min replied conversion"),
                ),
                (
                    "min_payment_conversion",
                    models.FloatField(help_text="Min payment conversion"),
                ),
                (
                    "min_revenue_per_fan",
                    models.DecimalField(
                        decimal_places=2, help_text="Min revenue per fan", max_digits=5
                    ),
                ),
                (
                    "team_lead",
                    models.OneToOneField(
                        help_text="Reference to Team Lead",
                        limit_choices_to={"role__name": "team_lead"},
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="ab_config",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.CreateModel(
            name="TeamLeadABChat",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("forum_id", models.BigIntegerField(help_text="Telegram forum ID")),
                ("thread_id", models.BigIntegerField(help_text="Telegram thread ID")),
                (
                    "shift_number_index",
                    models.ForeignKey(
                        help_text="Reference to Shift number index",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="ab_chats",
                        to="shifts.shiftindex",
                    ),
                ),
                (
                    "team_lead_ab_config",
                    models.ForeignKey(
                        help_text="Reference to Team Lead AB config",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="chats",
                        to="shifts.teamleadabconfig",
                    ),
                ),
            ],
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.AddConstraint(
            model_name="teamleadabchat",
            constraint=models.UniqueConstraint(
                fields=("forum_id", "thread_id"), name="unique_thread_id"
            ),
        ),
        migrations.AddConstraint(
            model_name="teamleadabchat",
            constraint=models.UniqueConstraint(
                fields=("team_lead_ab_config", "shift_number_index"),
                name="unique_shift_number_index",
            ),
        ),
    ]
