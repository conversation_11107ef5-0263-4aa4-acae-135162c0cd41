# Generated by Django 4.2.2 on 2023-07-03 10:53

import uuid

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models

import base.models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("only_fans_models", "0001_initial"),
        ("shifts", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Shift",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("shift_date", models.DateField(help_text="Shift date")),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("hold", "Ожидает начала"),
                            ("end", "Закончена"),
                            ("overdue", "Опоздание"),
                            ("progress", "В работе"),
                        ],
                        default="hold",
                        help_text="Status from choices(hold, end, overdue, progress)",
                        max_length=100,
                    ),
                ),
                (
                    "shift_start",
                    models.DateTimeField(help_text="Shift number start time"),
                ),
                ("shift_end", models.DateTimeField(help_text="Shift number end time")),
                (
                    "shift_start_fact",
                    models.DateTimeField(
                        blank=True, help_text="Shift fact start time", null=True
                    ),
                ),
                (
                    "shift_end_fact",
                    models.DateTimeField(
                        blank=True, help_text="Shift fact end time", null=True
                    ),
                ),
                (
                    "shift_duration",
                    models.DurationField(
                        blank=True, help_text="Shift duration", null=True
                    ),
                ),
            ],
            options={
                "ordering": ["shift_start"],
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.CreateModel(
            name="ShiftResult",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "shift_start_fact",
                    models.DateTimeField(
                        blank=True, help_text="Shift Result fact start time", null=True
                    ),
                ),
                (
                    "shift_end_fact",
                    models.DateTimeField(
                        blank=True, help_text="Shift Result fact end time", null=True
                    ),
                ),
                (
                    "only_fans_model",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="results",
                        to="only_fans_models.onlyfansmodel",
                    ),
                ),
                (
                    "shift",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="results",
                        to="shifts.shift",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.AddField(
            model_name="shift",
            name="only_fans_models",
            field=models.ManyToManyField(
                help_text="Only fans models",
                related_name="shifts",
                through="shifts.ShiftResult",
                to="only_fans_models.onlyfansmodel",
            ),
        ),
        migrations.AddField(
            model_name="shift",
            name="operator",
            field=models.ForeignKey(
                help_text="Operator",
                limit_choices_to={"role__name": "operator"},
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="operator_shifts",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="shift",
            name="shift_number",
            field=models.ForeignKey(
                help_text="Reference to Shift number",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="shifts",
                to="shifts.shiftnumber",
            ),
        ),
        migrations.AddField(
            model_name="shift",
            name="team_lead",
            field=models.ForeignKey(
                help_text="Team lead",
                limit_choices_to={"role__name": "team_lead"},
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="team_lead_shifts",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
