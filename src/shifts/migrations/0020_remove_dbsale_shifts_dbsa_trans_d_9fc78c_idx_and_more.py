# Generated by Django 4.2.2 on 2025-02-07 11:39

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("shifts", "0019_dbsale_shiftsale_and_more"),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name="dbsale",
            name="shifts_dbsa_trans_d_9fc78c_idx",
        ),
        migrations.AddField(
            model_name="dbsale",
            name="fan_id",
            field=models.BigIntegerField(
                blank=True, help_text="Fan ID (OF)", null=True
            ),
        ),
        migrations.AddField(
            model_name="dbsale",
            name="insert_date",
            field=models.DateTimeField(help_text="Insert date", null=True),
        ),
        migrations.AddField(
            model_name="dbsale",
            name="model_username",
            field=models.CharField(
                blank=True, help_text="Model username (Tingz)", max_length=255
            ),
        ),
        migrations.Alter<PERSON><PERSON>(
            model_name="dbsale",
            name="fan_username",
            field=models.Char<PERSON>ield(
                blank=True, help_text="Fan username", max_length=255
            ),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name="dbsale",
            name="model_id",
            field=models.BigIntegerField(
                blank=True, help_text="Model ID (OF)", null=True
            ),
        ),
        migrations.AlterField(
            model_name="shiftsale",
            name="shift",
            field=models.ForeignKey(
                help_text="Reference to Shift",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="sales",
                to="shifts.shift",
            ),
        ),
        migrations.AddIndex(
            model_name="dbsale",
            index=models.Index(
                fields=["trans_date", "model_id", "sale_source"],
                name="shifts_dbsa_trans_d_971988_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="dbsale",
            index=models.Index(
                fields=["trans_date", "model_username", "sale_source"],
                name="shifts_dbsa_trans_d_d5a7a6_idx",
            ),
        ),
    ]
