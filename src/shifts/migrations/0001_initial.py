# Generated by Django 4.2.2 on 2023-06-29 13:45

import uuid

import django.utils.timezone
from django.conf import settings
from django.db import migrations, models

import base.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="ShiftNumber",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(help_text="Name", max_length=100)),
                ("number", models.IntegerField(help_text="Number")),
                ("time_start", models.TimeField(help_text="Start time")),
                ("time_end", models.TimeField(help_text="End time")),
                (
                    "team_leads",
                    models.ManyTo<PERSON>any<PERSON>ield(
                        blank=True,
                        help_text="Team leads",
                        limit_choices_to={"role__name": "team_lead"},
                        related_name="shift_numbers",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Team leads",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
    ]
