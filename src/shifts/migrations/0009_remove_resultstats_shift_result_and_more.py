# Generated by Django 4.2.2 on 2023-08-24 12:47

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("only_fans_models", "0006_onlyfansmodel_tingz_username"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("shifts", "0008_resultstats"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="resultstats",
            name="shift_result",
        ),
        migrations.AddField(
            model_name="resultstats",
            name="only_fans_model",
            field=models.ForeignKey(
                help_text="Only fans model",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="stats",
                to="only_fans_models.onlyfansmodel",
            ),
        ),
        migrations.AddField(
            model_name="resultstats",
            name="operator",
            field=models.ForeignKey(
                help_text="Operator",
                limit_choices_to={"role__name": "operator"},
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="stats",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="resultstats",
            name="shift",
            field=models.ForeignKey(
                help_text="Shift",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="stats",
                to="shifts.shift",
            ),
        ),
    ]
