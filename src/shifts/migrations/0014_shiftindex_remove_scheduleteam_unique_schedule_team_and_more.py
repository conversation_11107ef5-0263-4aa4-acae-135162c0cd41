# Generated by Django 4.2.2 on 2024-02-05 11:13

import uuid

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models

import base.models


class Migration(migrations.Migration):

    dependencies = [
        ("shifts", "0013_scheduleteam_scheduleteam_unique_schedule_team"),
    ]

    operations = [
        migrations.CreateModel(
            name="ShiftIndex",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("number", models.IntegerField(help_text="Number", unique=True)),
            ],
            options={
                "verbose_name": "Shift index",
                "verbose_name_plural": "Shift indexes",
                "ordering": ["number"],
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.RemoveConstraint(
            model_name="scheduleteam",
            name="unique_schedule_team",
        ),
        migrations.RemoveField(
            model_name="shiftnumber",
            name="number",
        ),
        migrations.AlterField(
            model_name="scheduleteam",
            name="shift_number",
            field=models.ForeignKey(
                blank=True,
                help_text="Reference to Shift number",
                null=True,
                on_delete=django.db.models.deletion.RESTRICT,
                related_name="schedule_teams",
                to="shifts.shiftnumber",
            ),
        ),
        migrations.AddConstraint(
            model_name="scheduleteam",
            constraint=models.UniqueConstraint(
                fields=("only_fans_model", "shift_number"),
                name="unique_only_fans_model_shift_number",
            ),
        ),
        migrations.AddField(
            model_name="scheduleteam",
            name="shift_index",
            field=models.ForeignKey(
                help_text="Reference to Shift index",
                null=True,
                on_delete=django.db.models.deletion.RESTRICT,
                related_name="schedule_teams",
                to="shifts.shiftindex",
            ),
        ),
        migrations.AddField(
            model_name="shiftnumber",
            name="index",
            field=models.ForeignKey(
                help_text="Reference to Shift index",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="shift_numbers",
                to="shifts.shiftindex",
            ),
        ),
    ]
