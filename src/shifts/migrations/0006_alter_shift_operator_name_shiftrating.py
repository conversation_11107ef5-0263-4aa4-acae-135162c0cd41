# Generated by Django 4.2.2 on 2023-08-07 12:12

import uuid

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models

import base.models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("only_fans_models", "0005_alter_modelcategory_name"),
        ("shifts", "0005_shift_operator_name"),
    ]

    operations = [
        migrations.AlterField(
            model_name="shift",
            name="operator_name",
            field=models.CharField(
                blank=True, help_text="Operator full name", max_length=255, null=True
            ),
        ),
        migrations.CreateModel(
            name="ShiftRating",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "sales_amount",
                    models.IntegerField(default=0, help_text="Sales amount"),
                ),
                (
                    "sales_ratio",
                    models.DecimalField(
                        decimal_places=1,
                        default=0,
                        help_text="Sales ratio",
                        max_digits=5,
                    ),
                ),
                (
                    "chats_count",
                    models.IntegerField(default=0, help_text="Chats ratio"),
                ),
                (
                    "response_ratio",
                    models.DecimalField(
                        decimal_places=1,
                        default=0,
                        help_text="Response ratio",
                        max_digits=5,
                    ),
                ),
                (
                    "operator_ratio",
                    models.DecimalField(
                        decimal_places=1,
                        default=0,
                        help_text="Operator ratio",
                        max_digits=5,
                    ),
                ),
                (
                    "only_fans_model",
                    models.ForeignKey(
                        help_text="Only fans model",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="ratings",
                        to="only_fans_models.onlyfansmodel",
                    ),
                ),
                (
                    "operator",
                    models.ForeignKey(
                        help_text="Operator",
                        limit_choices_to={"role__name": "operator"},
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="ratings",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "shift",
                    models.ForeignKey(
                        help_text="Shift",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="ratings",
                        to="shifts.shift",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
    ]
