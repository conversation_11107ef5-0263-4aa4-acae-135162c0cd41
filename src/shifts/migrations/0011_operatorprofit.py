# Generated by Django 4.2.2 on 2023-10-02 11:16

import uuid

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models

import base.models


class Migration(migrations.Migration):

    dependencies = [
        ("only_fans_models", "0008_balanceprofit_modelcategory_balance_profit"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("shifts", "0010_resultstats_afk_summ_resultstats_max_afk_duration"),
    ]

    operations = [
        migrations.CreateModel(
            name="OperatorProfit",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("shift_date", models.DateField(help_text="Shift date")),
                (
                    "operator_name",
                    models.Char<PERSON>ield(help_text="Operator full name", max_length=255),
                ),
                (
                    "only_fans_model_name",
                    models.CharField(
                        help_text="Only fans model full name", max_length=255
                    ),
                ),
                (
                    "team_lead_name",
                    models.CharField(help_text="Team lead full name", max_length=255),
                ),
                (
                    "model_category_name",
                    models.CharField(help_text="Model category name", max_length=255),
                ),
                ("of_amount", models.IntegerField(help_text="Only fans amount")),
                ("of_profit", models.IntegerField(help_text="Only fans profit")),
                ("tingz_amount", models.IntegerField(help_text="Tingz amount")),
                ("tingz_profit", models.IntegerField(help_text="Tingz profit")),
                ("total_amount", models.IntegerField(help_text="Total amount")),
                ("total_profit", models.IntegerField(help_text="Total profit")),
                (
                    "model_category",
                    models.ForeignKey(
                        help_text="Model category",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="operators_profits",
                        to="only_fans_models.modelcategory",
                    ),
                ),
                (
                    "only_fans_model",
                    models.ForeignKey(
                        help_text="Only fans model",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="operators_profits",
                        to="only_fans_models.onlyfansmodel",
                    ),
                ),
                (
                    "operator",
                    models.ForeignKey(
                        help_text="Operator",
                        limit_choices_to={"role__name": "operator"},
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="operator_profits",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "shift",
                    models.ForeignKey(
                        help_text="Shift",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="operators_profits",
                        to="shifts.shift",
                    ),
                ),
                (
                    "team_lead",
                    models.ForeignKey(
                        help_text="Team lead",
                        limit_choices_to={"role__name": "team_lead"},
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="team_lead_operators_profits",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
    ]
