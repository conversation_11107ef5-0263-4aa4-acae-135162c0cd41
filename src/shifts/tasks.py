import json
import logging
import time
from datetime import datetime, timedelta

import httpx
from django.contrib.auth import get_user_model
from django.core.serializers.json import DjangoJSONEncoder
from django.db import transaction
from django.db.models import Q
from django.utils import timezone

from base.tools import TimeManager
from core.celery import BaseTaskWithRetry
from core.celery import app as celery_app
from core.celery import celery_task_locker_decorator
from only_fans_db.models import Sales, TingzSales
from only_fans_models.models import OnlyFansModel
from shifts.models import (
    DBSale,
    ResultStats,
    Shift,
    ShiftNumber,
    ShiftResult,
    ShiftSale,
)
from shifts.serializers import ShiftCelerySerializer
from shifts.services import (
    OperatorProfitService,
    ShiftNumberService,
    ShiftService,
)
from shifts.tools import SeniorOperatorParser

User = get_user_model()

logging.basicConfig(
    level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)


def send_event(event_type: str, data: list | dict) -> dict | None:
    """
    Send an event to the specified URL.

    Parameters:
    - event_type: A string that specifies the type of event to send.
    - data: Either a list or a dictionary, containing the data to send along with the event.

    Returns:
    - A dictionary that represents the response from the server, if the request is successful. Otherwise, None.
    """
    logger = logging.getLogger("send_event")

    url = f"http://fast_api:5000/shifts/{event_type}/"
    headers = {"Content-Type": "application/json"}

    logger.info("Inside send event func, before setting client")
    with httpx.Client() as client:
        logger.info(f"Sending {event_type} event to {url}")

        json_data = json.dumps(data, cls=DjangoJSONEncoder)
        response = client.post(url, headers=headers, data=json_data)

        logger.info(f"Response status code: {response.status_code}")

        if response.status_code == 200:
            return response.json()


def get_data_for_socket_event(shift_id: str, exclude_roles: list[str] = None) -> dict:
    """
    Retrieves data for a socket event related to a specific shift.

    Args:
        shift_id (str): The unique identifier of the shift.
        exclude_roles (list[str]): A list of roles to exclude from the event data.

    Returns:
        dict: A dictionary containing shift-related data for the socket event.
              The dictionary includes the following keys:
              - 'id': The ID of the shift.
              - 'users_ids': A list of user IDs associated with the shift.
              - 'data': Serialized data of the shift using ShiftCelerySerializer.
                        The serializer context includes additional information
                        about senior operators from SeniorOperatorParser.
    """
    shift_instance = Shift.objects.get(id=shift_id)
    shift_service = ShiftService(instance=shift_instance)
    context = {
        'senior_operators_ids': SeniorOperatorParser(
            shifts=[
                shift_instance,
            ]
        ).get_senior_operators_ids_dicts()
    }
    serializer = ShiftCelerySerializer(shift_instance, context=context)
    return {
        'id': shift_id,
        'users_ids': shift_service.get_users_ids(
            exclude_roles=exclude_roles if exclude_roles else None
        ),
        'data': serializer.data,
    }


def get_data_for_updated_socket_event(shifts_list: list[Shift]) -> dict:
    superusers_uuids = User.objects.filter(role__name='superuser').values_list(
        'id', flat=True
    )
    superusers_ids = [str(user_uuid) for user_uuid in superusers_uuids]
    context = {
        'senior_operators_ids': SeniorOperatorParser(
            shifts=shifts_list
        ).get_senior_operators_ids_dicts(),
        'superusers_ids': superusers_ids,
    }
    serializer = ShiftCelerySerializer(shifts_list, many=True, context=context)

    return {'data': serializer.data}


@celery_app.task(bind=BaseTaskWithRetry)
def send_socket_event_shift_created(self, shift_id: str):
    data = get_data_for_socket_event(shift_id=shift_id, exclude_roles=['superuser'])
    send_event(event_type='created', data=data)
    return f'{shift_id} created'


@celery_app.task(bind=BaseTaskWithRetry)
def send_socket_event_shift_deleted(self, shift_id: str, users_ids: list):
    data = {'id': shift_id, 'users_ids': users_ids, 'data': {}}
    send_event(event_type='deleted', data=data)
    return f'{shift_id} deleted'


@celery_app.task(bind=BaseTaskWithRetry)
def send_socket_event_shift_started(self, shift_id: str):
    data = get_data_for_socket_event(shift_id=shift_id)
    send_event(event_type='started', data=data)
    return f'{shift_id} started'


@celery_app.task(bind=BaseTaskWithRetry)
def send_socket_event_shift_ended(self, shift_id: str):
    data = get_data_for_socket_event(shift_id=shift_id)
    send_event(event_type='ended', data=data)
    return f'{shift_id} ended'


@celery_app.task(bind=BaseTaskWithRetry)
@celery_task_locker_decorator(
    key='send_socket_event_progress_shifts_updated_lock', timeout=300
)
def send_socket_event_progress_shifts_updated(self):
    from shifts.models import Shift

    logger = logging.getLogger("send_socket_event_progress_shifts_updated")

    logger.info("Updating shifts list info")
    start_before_sending_events = time.perf_counter()
    shifts_list = list(
        Shift.objects.filter(status='progress')
        .select_related('shift_number__index', 'operator', 'team_lead')
        .prefetch_related('stats__only_fans_model', 'only_fans_models', 'results')
    )
    data = get_data_for_updated_socket_event(shifts_list=shifts_list)
    time_before_sending_events = time.perf_counter() - start_before_sending_events

    start_sending_events = time.perf_counter()
    logger.info("Before send_event")
    response = send_event(event_type='updated', data=data)
    time_for_sending_event = time.perf_counter() - start_sending_events

    if response:
        return (
            f'{datetime.now()} sent event updated\n'
            f'Timestamps: ['
            f'before_sending_ws_events: {time_before_sending_events}, '
            f'for_sending_ws_events: {time_for_sending_event}'
            f']'
        )

    return f'{datetime.now()} sent event updated FAILED'


@celery_app.task(bind=BaseTaskWithRetry)
def send_socket_event_shift_updated(self, shift_id):
    shifts_list = list(
        Shift.objects.filter(id=shift_id)
        .select_related('shift_number__index', 'operator', 'team_lead')
        .prefetch_related('stats__only_fans_model', 'only_fans_models', 'results')
    )
    data = get_data_for_updated_socket_event(shifts_list=shifts_list)
    response = send_event(event_type='updated', data=data)

    if response:
        return f'{shift_id} sent event updated'

    return f'{shift_id} sent event updated FAILED'


@celery_app.task(bind=BaseTaskWithRetry)
def check_shifts_without_result_stats(self):
    start_date = timezone.now().date() - timedelta(days=10)
    end_date = timezone.now().date() - timedelta(days=1)
    shifts = list(
        Shift.objects.filter(status='end', shift_date__range=(start_date, end_date))
        .prefetch_related('only_fans_models', 'stats__only_fans_model', 'ratings', 'results', 'sales__db_sale')
        .select_related('operator')
    )
    result_stats_shifts = list(
        ResultStats.objects.filter(shift__in=shifts).values_list('shift_id', flat=True)
    )

    shifts_without_result_stats = [
        shift for shift in shifts if shift.id not in result_stats_shifts
    ]

    if not shifts_without_result_stats:
        return 'No shifts without result stats'

    ShiftService.refresh_shifts_stats(shifts_without_result_stats)

    return f'Number of shifts which was without result_stats: {len(shifts)}'


@celery_app.task(bind=BaseTaskWithRetry)
def write_operator_profits(self, shift_id: str):
    shift = Shift.objects.get(id=shift_id)

    if not shift:
        return f'Shift with id {shift_id} not found'

    operator_profits = OperatorProfitService.create_operator_profits_for_shift(
        shift=shift
    )

    if operator_profits:
        return f'Operator profits created for shift {shift_id}'


@celery_app.task(bind=BaseTaskWithRetry)
def update_day_before_yesterday_shifts_data(self):
    """
    Task for updating stats data day before yesterday shifts
    """
    shift_date = timezone.now().date() - timedelta(days=2)
    shifts = list(
        Shift.objects.filter(status='end', shift_date=shift_date)
        .prefetch_related('only_fans_models', 'stats', 'ratings', 'results')
        .select_related('operator')
    )

    if not shifts:
        return 'No shifts to update'

    ShiftService.refresh_shifts_stats(shifts=shifts)

    return f'Updated {len(shifts)} shifts'


@celery_app.task(bind=BaseTaskWithRetry)
def create_externals_shifts(self):
    team_lead_email = '<EMAIL>'
    operator_email = '<EMAIL>'

    team_lead = User.objects.get(email=team_lead_email, role__name='team_lead')
    operator = User.objects.get(email=operator_email, role__name='operator')
    shift_date = timezone.now().date() - timedelta(days=1)
    shift_numbers = ShiftNumber.objects.filter(team_leads=team_lead)
    only_fans_models = OnlyFansModel.objects.filter(team_lead=team_lead)

    with transaction.atomic():
        shifts = []
        for shift_number in shift_numbers:
            start_time, end_time = ShiftNumberService(
                shift_number
            ).generate_start_end_time_datetime(shift_date=shift_date)
            shift_data = {
                'shift_date': shift_date,
                'status': 'end',
                'shift_number': shift_number,
                'shift_start': start_time,
                'shift_start_fact': start_time,
                'shift_end': end_time,
                'shift_end_fact': end_time,
                'operator': operator,
                'team_lead': team_lead,
                'operator_name': operator.full_name,
            }

            for only_fans_model in only_fans_models:
                shift = Shift.objects.filter(
                    **shift_data, results__only_fans_model=only_fans_model
                ).first()

                if not shift:
                    shift = Shift.objects.create(**shift_data)
                    ShiftResult.objects.create(
                        shift=shift,
                        only_fans_model=only_fans_model,
                        shift_start_fact=shift.shift_start_fact,
                        shift_end_fact=shift.shift_end_fact,
                    )

                shifts.append(shift)

        ShiftService.refresh_shifts_stats(shifts=shifts)

        return f'Created {len(shifts)} shifts'


@celery_app.task(bind=BaseTaskWithRetry)
def update_shift_stats_by_shift_id(self, shift_id: str):
    shift = Shift.objects.get(id=shift_id)

    if not shift:
        return f'Shift with id {shift_id} not found'

    ShiftService.refresh_shifts_stats([shift])

    return f'Shift {shift_id} stats updated'


@celery_app.task(bind=BaseTaskWithRetry)
@celery_task_locker_decorator(
    key='update_shift_stats_for_progress_shifts_lock', timeout=300
)
def update_shift_stats_for_progress_shifts(self):
    shifts = list(
        Shift.objects.filter(status='progress')
        .select_related('shift_number__index', 'operator', 'team_lead')
        .prefetch_related('stats__only_fans_model', 'only_fans_models', 'results', 'sales__db_sale')
    )

    if not shifts:
        return 'Active shifts not found'

    ShiftService.refresh_shifts_stats(shifts, use_temp_data=True, without_ratings=True)
    send_socket_event_progress_shifts_updated.delay()

    return f'Updated {len(shifts)} shifts'


@celery_app.task(bind=BaseTaskWithRetry)
@celery_task_locker_decorator(key='db_sales_synchronization_lock', timeout=120)
def db_sales_synchronization(self):
    """
    Task for synchronization shifts from MVP
    """
    sales_time_delta = timedelta(days=10)

    try:
        last_db_sale = DBSale.objects.filter(sale_source=DBSale.SaleSource.ONLYFANS).latest('insert_date')
    except DBSale.DoesNotExist:
        last_db_sale = None

    current_time = timezone.now()

    start_date_of_sales = TimeManager.time_to_utc(
        last_db_sale.insert_date if last_db_sale else current_time - sales_time_delta
    )
    new_of_sales = Sales.objects.filter(
        insert_date__gt=start_date_of_sales,
        fan_id__isnull=False,
        model_id__isnull=False
    ).select_related('fan')

    if new_of_sales:
        with transaction.atomic():
            created_of_sales = DBSale.objects.bulk_create(
                [
                    DBSale(
                        trans_id=sale.trans_id,
                        trans_date=TimeManager.time_from_utc(sale.trans_date),
                        amount=sale.amount,
                        fan_id=sale.fan_id,
                        model_id=sale.model_id,
                        sale_source=DBSale.SaleSource.ONLYFANS,
                        sale_type=sale.type,
                        insert_date=TimeManager.time_from_utc(sale.insert_date),
                    )
                    for sale in new_of_sales
                ],
                ignore_conflicts=True,
                batch_size=1000,
            )

            ShiftSale.objects.bulk_create(
                [
                    ShiftSale(
                        db_sale=created_of_sale,
                        amount=created_of_sale.amount,
                    )
                    for created_of_sale in created_of_sales
                ],
                ignore_conflicts=True,
                batch_size=1000,
            )

    start_date_tingz_sales = current_time - sales_time_delta
    existing_tingz_sales_ids = list(
        DBSale.objects.filter(
            sale_source=DBSale.SaleSource.TINGZ,
            trans_date__gte=start_date_tingz_sales
        ).values_list('trans_id', flat=True)
    )

    new_tingz_sales = TingzSales.objects.filter(
        ~Q(trans_id__in=existing_tingz_sales_ids),
        created_at__gte=TimeManager.time_to_utc(start_date_tingz_sales),
        fan_username__isnull=False,
        model_username__isnull=False,
    )

    if new_tingz_sales:
        with transaction.atomic():
            created_tingz_sales = DBSale.objects.bulk_create(
                [
                    DBSale(
                        trans_id=tingz_sale.trans_id,
                        trans_date=TimeManager.time_from_utc(tingz_sale.created_at),
                        amount=tingz_sale.amount,
                        fan_username=tingz_sale.fan_username,
                        sale_source=DBSale.SaleSource.TINGZ,
                        sale_type=None,
                        insert_date=None,
                        model_username=tingz_sale.model_username,
                    )
                    for tingz_sale in new_tingz_sales
                ],
                ignore_conflicts=True,
                batch_size=1000,
            )

            ShiftSale.objects.bulk_create(
                [
                    ShiftSale(
                        db_sale=created_tingz_sale,
                        amount=created_tingz_sale.amount,
                    )
                    for created_tingz_sale in created_tingz_sales
                ],
                ignore_conflicts=True,
                batch_size=1000,
            )

    return (f'New OF sales: {len(new_of_sales)}; '
            f'New TINGZ sales: {len(new_tingz_sales)}')
