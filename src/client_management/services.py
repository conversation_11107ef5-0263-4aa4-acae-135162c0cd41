import calendar
import datetime
from datetime import timedelta

from dateutil.relativedelta import relativedelta
from django.contrib.auth import get_user_model
from django.db import transaction
from django.utils import timezone
from rest_framework.utils import model_meta

from client_management.models import (
    Calendar<PERSON>vent,
    Calendar<PERSON>ventOccurrence,
    CalendarEventRepeatType,
    CalendarEventType,
    ModelContact,
    ModelInfo,
    ModelPlatform,
)
from only_fans_models.models import OnlyFansModel, SubProfile

User = get_user_model()


class ModelInfoService:
    MODEL = ModelInfo

    def create(self, validated_data: dict) -> MODEL:
        contacts = validated_data.pop('contacts', [])
        platforms = validated_data.pop('platforms', [])
        sub_profiles = validated_data.pop('sub_profiles', [])

        info = model_meta.get_field_info(self.MODEL)
        many_to_many = {}

        for field_name, relation_info in info.relations.items():
            if relation_info.to_many and (field_name in validated_data):
                many_to_many[field_name] = validated_data.pop(field_name)

        with transaction.atomic():
            model_info = self.MODEL.objects.create(**validated_data)

            if many_to_many:
                for field_name, value in many_to_many.items():
                    field = getattr(model_info, field_name)
                    field.set(value)

            ModelContact.objects.bulk_create(
                [ModelContact(model_info=model_info, **contact) for contact in contacts]
            )
            ModelPlatform.objects.bulk_create(
                [
                    ModelPlatform(model_info=model_info, **platform)
                    for platform in platforms
                ]
            )
            self._create_subprofiles(sub_profiles=sub_profiles, model_info=model_info)

        return model_info

    def _create_subprofiles(
            self,
            sub_profiles: list[OnlyFansModel],
            model_info: ModelInfo
    ) -> list[SubProfile]:
        model_info.only_fans_model.sub_profiles.all().delete()

        subprofiles_to_create = []
        for sub_profile in sub_profiles:
            subprofiles_to_create.append(
                SubProfile(
                    only_fans_model=model_info.only_fans_model,
                    sub_profile=sub_profile,
                )
            )
            sub_profile.client_manager = None
            sub_profile.save()

        return SubProfile.objects.bulk_create(subprofiles_to_create, batch_size=10)

    def _update_dob(
            self,
            dob: datetime.date,
            model_info: ModelInfo
    ) -> None:
        calendar_service = CalendarService()

        if model_info.dob is not None:
            calendar_service.delete_birthday_events(model_info=model_info)

        current_year_dob = dob.replace(year=timezone.now().year)
        event_data = {
            "only_fans_model": model_info.only_fans_model,
            "title": "Birthday",
            "start_date": current_year_dob,
            "end_date": current_year_dob,
            "event_type": CalendarEventType.objects.get(
                name="Birthday"
            ),
            "repeat_type": CalendarEventRepeatType.objects.get(
                name="every year"
            ),
        }
        calendar_service.create_event(
            validated_data=event_data
        )

    def update(self, instance: MODEL, validated_data: dict) -> MODEL:
        with transaction.atomic():
            if 'contacts' in validated_data:
                contacts = validated_data.pop('contacts')
                instance.contacts.all().delete()
                ModelContact.objects.bulk_create(
                    [
                        ModelContact(model_info=instance, **contact)
                        for contact in contacts
                    ]
                )
            if 'platforms' in validated_data:
                platforms = validated_data.pop('platforms')
                instance.platforms.all().delete()
                ModelPlatform.objects.bulk_create(
                    [
                        ModelPlatform(model_info=instance, **platform)
                        for platform in platforms
                    ]
                )
            if 'sub_profiles' in validated_data:
                sub_profiles = validated_data.pop('sub_profiles')

                if sorted(sub_profiles, key=lambda x: x.model_id) != sorted(
                    [obj.sub_profile for obj in instance.only_fans_model.sub_profiles.select_related('sub_profile')],
                    key=lambda x: x.model_id
                ):
                    self._create_subprofiles(sub_profiles=sub_profiles, model_info=instance)

            if 'dob' in validated_data:
                if (dob := validated_data['dob']) != instance.dob:
                    self._update_dob(dob=dob, model_info=instance)

            info = model_meta.get_field_info(instance)

            m2m_fields = []
            for attr, value in validated_data.items():
                if attr in info.relations and info.relations[attr].to_many:
                    m2m_fields.append((attr, value))
                else:
                    setattr(instance, attr, value)

            instance.save()

            for attr, value in m2m_fields:
                field = getattr(instance, attr)
                field.set(value)

            return instance


# -----------------------------------------------Calendar----------------------------------------------
class CalendarService:
    """
    Service for creating and updating calendar events.
    """
    NUMBER_OF_YEARS_FOR_RECURRENCE = 10

    def _create_weekly_event_occurrences(
        self, event: CalendarEvent
    ) -> list[CalendarEventOccurrence]:
        occurrences = []
        current_date = event.start_date
        last_date = event.start_date + relativedelta(years=10)

        while current_date <= last_date:
            occurrences.append(
                CalendarEventOccurrence(
                    calendar_event=event,
                    start_date=current_date,
                    end_date=(
                        current_date
                        + timedelta(days=(event.end_date - event.start_date).days)
                    ),
                )
            )
            current_date += timedelta(weeks=1)

        return CalendarEventOccurrence.objects.bulk_create(occurrences, batch_size=500)

    def _create_monthly_event_occurrences(
        self, event: CalendarEvent
    ) -> list[CalendarEventOccurrence]:
        occurrences = []
        current_date = event.start_date
        last_date = event.start_date + relativedelta(years=10)

        while current_date <= last_date:
            occurrences.append(
                CalendarEventOccurrence(
                    calendar_event=event,
                    start_date=current_date,
                    end_date=(
                        current_date
                        + timedelta(days=(event.end_date - event.start_date).days)
                    ),
                )
            )
            month_days = calendar.monthrange(current_date.year, current_date.month)[1]
            current_date += timedelta(days=month_days)

        return CalendarEventOccurrence.objects.bulk_create(occurrences, batch_size=500)

    def _create_yearly_event_occurrences(
            self, event: CalendarEvent
    ) -> list[CalendarEventOccurrence]:
        occurrences = []
        current_date = event.start_date
        last_date = event.start_date + relativedelta(years=10)

        while current_date <= last_date:
            end_date = current_date + timedelta(days=(event.end_date - event.start_date).days)
            occurrences.append(
                CalendarEventOccurrence(
                    calendar_event=event,
                    start_date=current_date,
                    end_date=end_date,
                )
            )
            current_date = current_date.replace(year=current_date.year + 1)

        return CalendarEventOccurrence.objects.bulk_create(occurrences, batch_size=500)

    def create_event_occurrences(
        self, event: CalendarEvent
    ) -> list[CalendarEventOccurrence]:
        if not event.repeat_type:
            return [
                CalendarEventOccurrence.objects.create(
                    calendar_event=event,
                    start_date=event.start_date,
                    end_date=event.end_date,
                )
            ]
        elif event.repeat_type.name == 'every week':
            return self._create_weekly_event_occurrences(event)
        elif event.repeat_type.name == 'every month':
            return self._create_monthly_event_occurrences(event)
        elif event.repeat_type.name == 'every year':
            return self._create_yearly_event_occurrences(event)

        raise ValueError(f'Unknown repeat type: {event.repeat_type.name}')

    def create_event(self, validated_data: dict) -> CalendarEventOccurrence:
        with transaction.atomic():
            only_fans_model = validated_data.pop('only_fans_model')
            event = CalendarEvent.objects.create(**validated_data, model_info=only_fans_model.model_info)
            event_occurrences = self.create_event_occurrences(event)

            return event_occurrences[0]

    def update_event(self, event_occurrence: CalendarEventOccurrence, validated_data: dict) -> CalendarEventOccurrence:
        with transaction.atomic():
            event = event_occurrence.calendar_event
            deleted_occurrences = False

            for attr in ['start_date', 'end_date', 'repeat_type']:
                if attr in validated_data and getattr(event, attr) != validated_data[attr]:
                    event.calendar_event_occurrences.all().delete()
                    deleted_occurrences = True
                    break

            for attr, value in validated_data.items():
                setattr(event, attr, value)

            event.save()

            if deleted_occurrences is True:
                new_occurrences = self.create_event_occurrences(event)

                return new_occurrences[0]

            return event_occurrence

    def delete_event(self, event_occurrence: CalendarEventOccurrence) -> None:
        event_occurrence.calendar_event.delete()

    def delete_birthday_events(self, model_info: ModelInfo):
        model_birthday_events = CalendarEvent.objects.filter(
            model_info=model_info,
            event_type__name='Birthday',
        )
        model_birthday_events.delete()
