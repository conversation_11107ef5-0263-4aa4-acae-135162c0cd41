import json

from django.contrib.auth import get_user_model
from django.urls import reverse
from django.utils import timezone
from rest_framework import status

from base.tests.mixins import BaseCRMTest
from client_management.models import (
    CalendarEventOccurrence,
    CalendarEventRepeatType,
    CalendarEventType,
    Country,
    Language,
    LiveFeed,
    ModelInfo,
    Nationality,
    Social,
)
from client_management.services import CalendarService
from only_fans_models.models import SubProfile

User = get_user_model()


class TestClientManagementViews(BaseCRMTest):
    def setUp(self):
        super().setUp()
        self.client.force_authenticate(user=self.user_hocm)

    def test_permissions(self):
        for user in self.users:
            self.client.force_authenticate(user=user)

            if user.role in [self.hocm_role, self.client_manager_role]:
                response = self.client.get(reverse('models-info-list'))
                self.assertEqual(response.status_code, 200)
                continue

            response = self.client.get(reverse('models-info-list'))
            self.assertEqual(response.status_code, 403)

    def test_model_info_create_update(self):
        only_fans_model = self.create_only_fans_model(1)
        response = self.client.patch(
            reverse('models-info-detail', args=[only_fans_model.model_info.id]),
            data=json.dumps(
                {
                    'avatar': self.get_temporary_image(),
                    'nationality': str(Nationality.objects.create(nationality_name='test_nationality').id),
                    'model_name': 'test_name',
                    'dob': '1995-05-05',
                    'country': str(Country.objects.create(country_name='test_country').id),
                    'languages': [
                        str(Language.objects.create(language_name='test_language_1').id),
                        str(Language.objects.create(language_name='test_language_2').id)
                    ],
                    'model_conditions':  'test_conditions',
                    'another_job': 'another_job',
                    'yes_list': 'yes_list',
                    'stop_list': 'stop_list',
                    'like': 'like',
                    'dont_like': 'dont like',
                    'hobby': 'hobby',
                    'dreams': 'dreams',
                    'goals': 'goals',
                    'other': 'other',
                    'contacts': [
                        {
                            'contact_url': 'https://url.com',
                            'social': str(Social.objects.create(name='test_contact_social').id)
                        }
                    ],
                    'platforms': [
                        {
                            'platform_url': 'https://url.com',
                            'social': str(Social.objects.create(name='test_platform_social').id)
                        }
                    ],
                    'sub_profiles': [str(self.create_only_fans_model(2).id)],
                },
            ),
            content_type='application/json',
        )
        self.assertEqual(response.status_code, 200)
        model_info = ModelInfo.objects.get(id=response.data['id'])
        self.assertEqual(model_info.model_name, 'test_name')
        self.assertEqual(model_info.model_conditions, 'test_conditions')
        self.assertEqual(model_info.another_job, 'another_job')
        self.assertEqual(model_info.yes_list, 'yes_list')
        self.assertEqual(model_info.stop_list, 'stop_list')
        self.assertEqual(model_info.like, 'like')
        self.assertEqual(model_info.dont_like, 'dont like')
        self.assertEqual(model_info.hobby, 'hobby')
        self.assertEqual(model_info.dreams, 'dreams')
        self.assertEqual(model_info.goals, 'goals')
        self.assertEqual(model_info.other, 'other')
        self.assertEqual(model_info.contacts.count(), 1)
        self.assertEqual(model_info.platforms.count(), 1)
        self.assertEqual(model_info.only_fans_model.sub_profiles.count(), 1)
        self.assertEqual(model_info.nationality.nationality_name, 'test_nationality')
        self.assertEqual(model_info.languages.count(), 2)
        self.assertIsNotNone(model_info.avatar)
        self.assertIsNotNone(model_info.dob)
        self.assertIsNotNone(model_info.zodiac_sign)

        new_social = Social.objects.create(name='new_social')
        new_model = self.create_only_fans_model(3)
        new_model.client_manager = self.user_client_manager
        new_model.save()

        response = self.client.patch(
            reverse('models-info-detail', args=[model_info.id]),
            data=json.dumps(
                {
                    'contacts': [
                        {
                            'contact_url': 'https://new-url.com',
                            'social': str(new_social.id)
                        }
                    ],
                    'platforms': [
                        {
                            'platform_url': 'https://new-url.com',
                            'social': str(new_social.id)
                        }
                    ],
                    'sub_profiles': [str(new_model.id)],
                },
            ),
            content_type='application/json'
        )
        self.assertEqual(response.status_code, 200)
        model_info.refresh_from_db()
        self.assertEqual(model_info.contacts.count(), 1)
        self.assertEqual(model_info.model_contacts.first(), new_social)
        self.assertEqual(model_info.platforms.count(), 1)
        self.assertEqual(model_info.model_platforms.first(), new_social)
        self.assertEqual(model_info.only_fans_model.sub_profiles.count(), 1)
        self.assertEqual(model_info.only_fans_model.sub_profiles.first().sub_profile, new_model)
        self.assertIsNone(model_info.only_fans_model.sub_profiles.first().sub_profile.client_manager)

        # check dob event created
        current_year = timezone.now().year
        birthday_event = model_info.calendar_events.get(event_type__name='Birthday')
        self.assertEqual(birthday_event.start_date, model_info.dob.replace(year=current_year))
        self.assertEqual(birthday_event.end_date, model_info.dob.replace(year=current_year))
        birthday_event_occurrences = birthday_event.calendar_event_occurrences.order_by('start_date')
        self.assertIsNotNone(birthday_event_occurrences)
        self.assertEqual(birthday_event_occurrences[0].start_date, model_info.dob.replace(year=current_year))

    def test_unique_sub_profile_validation(self):
        only_fans_model_one = self.create_only_fans_model(1)
        only_fans_model_two = self.create_only_fans_model(2)
        only_fans_model_for_sub_profile = self.create_only_fans_model(3)

        sub_profile = SubProfile.objects.create(
            sub_profile=only_fans_model_for_sub_profile,
            only_fans_model=only_fans_model_one
        )
        response = self.client.patch(
            reverse('models-info-detail', args=[str(only_fans_model_two.model_info.id)]),
            data=json.dumps(
                {
                    'sub_profiles': [str(only_fans_model_for_sub_profile.id)],
                },
            ),
            content_type='application/json'
        )
        self.assertEqual(response.status_code, 400)
        sub_profile.delete()
        response = self.client.patch(
            reverse('models-info-detail', args=[str(only_fans_model_two.model_info.id)]),
            data=json.dumps(
                {
                    'sub_profiles': [str(only_fans_model_for_sub_profile.id)],
                },
            ),
            content_type='application/json'
        )
        self.assertEqual(response.status_code, 200)


class TestCalendarViews(BaseCRMTest):
    def setUp(self):
        super().setUp()
        self.client.force_authenticate(user=self.user_hocm)

    def test_events_list_permission(self):
        for user in self.users:
            self.client.force_authenticate(user=user)

            if user.role in [self.hocm_role, self.client_manager_role]:
                response = self.client.get(
                    reverse('calendar-list'),
                    data={
                        'date_after': '2024-01-01',
                        'date_before': '2024-12-01'
                    }
                )
                self.assertEqual(response.status_code, 200)
                continue

            response = self.client.get(
                reverse('calendar-list'),
                data={
                    'date_after': '2024-01-01',
                    'date_before': '2024-12-01'
                }
            )
            self.assertEqual(response.status_code, 403)

    def test_event_create_update(self):
        only_fans_model = self.create_only_fans_model(1)
        event_type, _ = CalendarEventType.objects.get_or_create(name='test event type')
        repeat_type, _ = CalendarEventRepeatType.objects.get_or_create(name='every month')
        create_data = {
            'only_fans_model': str(only_fans_model.id),
            'title': 'Event title',
            'event_type': str(event_type.id),
            'repeat_type': str(repeat_type.id),
            'start_date': timezone.now().strftime('%Y-%m-%d'),
            'end_date': timezone.now().strftime('%Y-%m-%d'),
        }
        response = self.client.post(
            reverse('calendar-list'),
            data=create_data,
        )
        self.assertEqual(response.status_code, 201)

        model_info_occurrence = CalendarEventOccurrence.objects.get(id=response.json()['id'])
        last_event_occurrence = CalendarEventOccurrence.objects.filter(
            calendar_event=model_info_occurrence.calendar_event
        ).order_by('-start_date').first()
        self.assertEqual(
            last_event_occurrence.start_date.year,
            timezone.now().date().year + CalendarService.NUMBER_OF_YEARS_FOR_RECURRENCE
        )

        response = self.client.patch(
            reverse('calendar-detail', args=[model_info_occurrence.id]),
            data={
                'start_date': timezone.now().replace(year=timezone.now().year + 1).strftime('%Y-%m-%d'),
                'end_date': timezone.now().replace(year=timezone.now().year + 1).strftime('%Y-%m-%d'),
            }
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        last_event_occurrence = CalendarEventOccurrence.objects.filter(
            calendar_event=model_info_occurrence.calendar_event
        ).order_by('-start_date').first()
        self.assertEqual(
            last_event_occurrence.start_date.year,
            timezone.now().replace(
                year=timezone.now().year + 1 + CalendarService.NUMBER_OF_YEARS_FOR_RECURRENCE
            ).year
        )

        # check date validation
        response = self.client.patch(
            reverse('calendar-detail', args=[last_event_occurrence.id]),
            data={
                'start_date': timezone.now().replace(year=timezone.now().year + 2).strftime('%Y-%m-%d'),
                'end_date': timezone.now().replace(year=timezone.now().year).strftime('%Y-%m-%d'),
            }
        )
        self.assertEqual(response.status_code, 400)


class TestLiveFeedViews(BaseCRMTest):
    def setUp(self):
        super().setUp()
        self.client.force_authenticate(user=self.user_client_manager)
        self.new_client_manager = User.objects.create(
            email='<EMAIL>',
            first_name='first_name',
            last_name='last_name',
            role=self.client_manager_role,
        )

    def test_permissions(self):
        for user in self.users:
            self.client.force_authenticate(user=user)

            if user.role in [self.hocm_role, self.client_manager_role]:
                response = self.client.get(reverse('live-feeds-list'))
                self.assertEqual(response.status_code, 200)
                continue

            response = self.client.get(reverse('live-feeds-list'))
            self.assertEqual(response.status_code, 403)

    def test_create(self):
        only_fans_model = self.create_only_fans_model(1)
        response = self.client.post(
            reverse('live-feeds-list'),
            data={
                'models_infos': [str(only_fans_model.model_info.id)],
                'text': 'live feed text'
            }
        )
        self.assertEqual(response.status_code, 201)
        live_feed = LiveFeed.objects.get(id=response.json()['id'])
        self.assertIn(only_fans_model.model_info, live_feed.models_infos.all())
        self.assertEqual(live_feed.text, 'live feed text')
        self.assertEqual(live_feed.user,  self.user_client_manager)
        self.assertIsNotNone(live_feed.created_at)

    def test_update(self):
        only_fans_model = self.create_only_fans_model(1)
        live_feed = LiveFeed.objects.create(
            text='live feed text',
            user=self.user_client_manager
        )
        live_feed.models_infos.add(only_fans_model.model_info)
        response = self.client.patch(
            reverse('live-feeds-detail', args=[str(live_feed.id)]),
            data={
                'text': 'new live feed text'
            }
        )
        self.assertEqual(response.status_code, 200)
        live_feed.refresh_from_db()
        self.assertEqual(live_feed.text, 'new live feed text')
        self.assertEqual(live_feed.user, self.user_client_manager)

        self.client.force_authenticate(user=self.new_client_manager)
        response = self.client.patch(
            reverse('live-feeds-detail', args=[str(live_feed.id)]),
            data={
                'text': 'new live feed text'
            }
        )
        self.assertEqual(response.status_code, 404)

    def test_delete(self):
        live_feed = LiveFeed.objects.create(
            text='live feed text',
            user=self.user_client_manager
        )
        self.client.force_authenticate(user=self.new_client_manager)
        response = self.client.delete(reverse('live-feeds-detail', args=[str(live_feed.id)]))
        self.assertEqual(response.status_code, 404)

        self.client.force_authenticate(user=self.user_hocm)
        response = self.client.delete(reverse('live-feeds-detail', args=[str(live_feed.id)]))
        self.assertEqual(response.status_code, 204)
        self.assertFalse(LiveFeed.objects.filter(id=live_feed.id).exists())
