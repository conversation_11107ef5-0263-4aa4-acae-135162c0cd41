import base64
import os

from drf_extra_fields.fields import Base64I<PERSON><PERSON>ield
from drf_spectacular.types import OpenApiTypes
from drf_spectacular.utils import extend_schema_field
from rest_framework import serializers
from rest_framework.relations import Primary<PERSON>eyRelatedField

from accounts.serializers import PureUserSerializer
from client_management.models import (
    CalendarEventOccurrence,
    CalendarEventRepeatType,
    CalendarEventType,
    Country,
    File,
    Language,
    LiveFeed,
    ModelContact,
    ModelInfo,
    ModelPlatform,
    Nationality,
    Social,
)
from client_management.services import CalendarService, ModelInfoService
from only_fans_models.models import OnlyFansModel, SubProfile
from only_fans_models.serializers import ModelCategorySerializer


class NationalitySerializer(serializers.ModelSerializer):
    """
    Serializer for Nationality model
    """
    class Meta:
        model = Nationality
        fields = ('id', 'nationality_name')


class SocialSerializer(serializers.ModelSerializer):
    """
    Serializer for Social model
    """
    class Meta:
        model = Social
        fields = ('id', 'name', 'icon')


class CountrySerializer(serializers.ModelSerializer):
    """
    Serializer for Country model
    """

    class Meta:
        model = Country
        fields = ('id', 'country_name')


class LanguageSerializer(serializers.ModelSerializer):
    """
    Serializer for Language model
    """

    class Meta:
        model = Language
        fields = ('id', 'language_name')


class NestedOnlyFansModelSerializer(serializers.ModelSerializer):
    """
    Serializer for OnlyFansModel model
    """
    client_manager = PureUserSerializer(read_only=True)
    category = ModelCategorySerializer(read_only=True)

    class Meta:
        model = OnlyFansModel
        fields = (
            'id',
            'nickname',
            'category',
            'client_manager'
        )


class NestedSubProfileSerializer(serializers.ModelSerializer):
    """
    Serializer for SubProfile model
    """
    id = serializers.UUIDField(source='sub_profile.id', read_only=True)
    nickname = serializers.CharField(source='sub_profile.nickname', read_only=True)

    class Meta:
        model = SubProfile
        fields = (
            'id',
            'nickname'
        )


class NestedOnlyFansModelSerializerExtended(NestedOnlyFansModelSerializer):
    """
    Serializer for OnlyFansModel model
    """
    team_lead = PureUserSerializer(read_only=True)
    marketer = PureUserSerializer(read_only=True)

    class Meta(NestedOnlyFansModelSerializer.Meta):
        fields = NestedOnlyFansModelSerializer.Meta.fields + ('team_lead', 'marketer')


class ModelInfoListSerializer(serializers.ModelSerializer):
    """
    Serializer for ModelInfo model
    """
    only_fans_model = NestedOnlyFansModelSerializer(read_only=True)
    nationality = NationalitySerializer()

    class Meta:
        model = ModelInfo
        fields = (
            'id',
            'avatar',
            'nationality',
            'updated_at',
            'only_fans_model'
        )


class ModelContactSerializer(serializers.ModelSerializer):
    """
    Serializer for ModelContact model
    """
    social = SocialSerializer(read_only=True)

    class Meta:
        model = ModelContact
        fields = ['contact_url', 'social']


class ModelContactCreateSerializer(ModelContactSerializer):
    """
    Serializer for create ModelContact model
    """
    social = serializers.PrimaryKeyRelatedField(
        queryset=Social.objects.all(),
        write_only=True
    )


class ModelPlatformSerializer(serializers.ModelSerializer):
    """
    Serializer for ModelPlatform model
    """
    social = SocialSerializer()

    class Meta:
        model = ModelPlatform
        fields = ['platform_url', 'social']


class ModelPlatformCreateSerializer(ModelPlatformSerializer):
    """
    Serializer for create ModelPlatform model
    """
    social = serializers.PrimaryKeyRelatedField(
        queryset=Social.objects.all(),
        write_only=True
    )


class ModelInfoDetailSerializer(ModelInfoListSerializer):
    """
    Serializer for detail ModelInfo model
    """
    only_fans_model = NestedOnlyFansModelSerializerExtended()
    country = CountrySerializer()
    languages = LanguageSerializer(many=True)
    contacts = ModelContactSerializer(many=True)
    platforms = ModelPlatformSerializer(many=True)
    sub_profiles = NestedSubProfileSerializer(source='only_fans_model.sub_profiles', many=True)

    class Meta(ModelInfoListSerializer.Meta):
        model = ModelInfo
        fields = ModelInfoListSerializer.Meta.fields + (
            'model_name',
            'age',
            'dob',
            'country',
            'languages',
            'zodiac_sign',
            'model_conditions',
            'another_job',
            'yes_list',
            'stop_list',
            'like',
            'dont_like',
            'hobby',
            'dreams',
            'goals',
            'other',
            'contacts',
            'platforms',
            'sub_profiles'
        )


class ModelInfoCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for create ModelInfo model
    """
    avatar = Base64ImageField(required=False)
    contacts = ModelContactCreateSerializer(many=True, required=False)
    platforms = ModelPlatformCreateSerializer(many=True,  required=False)
    sub_profiles = PrimaryKeyRelatedField(many=True, required=False, queryset=OnlyFansModel.objects.all())

    class Meta(ModelInfoListSerializer.Meta):
        model = ModelInfo
        fields = (
            'id',
            'avatar',
            'nationality',
            'only_fans_model',
            'model_name',
            'dob',
            'country',
            'languages',
            'zodiac_sign',
            'model_conditions',
            'another_job',
            'yes_list',
            'stop_list',
            'like',
            'dont_like',
            'hobby',
            'dreams',
            'goals',
            'other',
            'contacts',
            'platforms',
            'sub_profiles'
        )

    def validate(self, attrs):
        sub_profiles = attrs.get('sub_profiles', [])
        only_fans_model = attrs.get('only_fans_model') or self.instance.only_fans_model

        not_unique_sub_profiles = SubProfile.objects.filter(
            sub_profile__in=sub_profiles,
        ).exclude(only_fans_model=only_fans_model)

        if only_fans_model in sub_profiles:
            raise serializers.ValidationError(
                {'sub_profiles': 'SubProfile cannot be assigned to itself'}
            )

        if not_unique_sub_profiles:
            raise serializers.ValidationError({
                    'sub_profiles': [
                        f'SubProfile {sub_profile.sub_profile.nickname} '
                        f'is already assigned to {sub_profile.only_fans_model.nickname}'
                        for sub_profile in not_unique_sub_profiles
                    ]
                }
            )

        return attrs

    def create(self, validated_data):
        return ModelInfoService().create(validated_data)


class ModelInfoUpdateSerializer(ModelInfoCreateSerializer):
    """
    Serializer for update ModelInfo model
    """

    def update(self, instance, validated_data):
        return ModelInfoService().update(instance, validated_data)


# --------------------------------------------Calendar----------------------------------------------
class CalendarEventTypeSerializer(serializers.ModelSerializer):
    """
    Serializer for CalendarEventType model
    """
    class Meta:
        model = CalendarEventType
        fields = ('id', 'name')


class CalendarEventRepeatTypeSerializer(serializers.ModelSerializer):
    """
    Serializer for CalendarEventRepeatType model
    """
    class Meta:
        model = CalendarEventRepeatType
        fields = ('id', 'name')


class NestedPureOnlyFansModelSerializer(serializers.ModelSerializer):
    """
    Serializer for OnlyFansModel model
    """
    class Meta:
        model = OnlyFansModel
        fields = ('id', 'nickname')


class NestedModelInfoSerializer(serializers.ModelSerializer):
    """
    Serializer for ModelInfo model
    """
    only_fans_model = NestedPureOnlyFansModelSerializer()

    class Meta:
        model = ModelInfo
        fields = ('id', 'model_name', 'only_fans_model', 'avatar')


class CalendarEventOccurrenceListSerializer(serializers.ModelSerializer):
    """
    Serializer for CalendarEventOccurrence model
    """
    model_info = NestedModelInfoSerializer(source='calendar_event.model_info')
    title = serializers.CharField(source='calendar_event.title')
    event_type = CalendarEventTypeSerializer(source='calendar_event.event_type')
    repeat_type = CalendarEventRepeatTypeSerializer(source='calendar_event.repeat_type')
    description = serializers.CharField(source='calendar_event.description')

    class Meta:
        model = CalendarEventOccurrence
        fields = (
            'id',
            'start_date',
            'end_date',
            'event_type',
            'repeat_type',
            'title',
            'description',
            'model_info'
        )


class CalendarEventOccurrenceCreateSerializer(serializers.Serializer):
    """
    Serializer for create CalendarEventOccurrence model
    """
    only_fans_model = serializers.PrimaryKeyRelatedField(
        queryset=OnlyFansModel.objects.all(),
    )
    title = serializers.CharField()
    event_type = serializers.PrimaryKeyRelatedField(
        queryset=CalendarEventType.objects.all(),
    )
    repeat_type = serializers.PrimaryKeyRelatedField(
        queryset=CalendarEventRepeatType.objects.all(),
        allow_null=True,
        required=False
    )
    description = serializers.CharField(required=False)
    start_date = serializers.DateField()
    end_date = serializers.DateField()

    def validate(self, attrs):
        if attrs['start_date'] > attrs['end_date']:
            raise serializers.ValidationError({'start_date': 'Start date must be less than end date'})

        return attrs

    def create(self, validated_data):
        return CalendarService().create_event(validated_data)


class CalendarEventOccurrenceUpdateSerializer(serializers.Serializer):
    """
    Serializer for update CalendarEventOccurrence model
    """
    title = serializers.CharField()
    event_type = serializers.PrimaryKeyRelatedField(
        queryset=CalendarEventType.objects.all(),
    )
    repeat_type = serializers.PrimaryKeyRelatedField(
        queryset=CalendarEventRepeatType.objects.all(),
    )
    description = serializers.CharField(required=False)
    start_date = serializers.DateField()
    end_date = serializers.DateField()

    def validate(self, attrs):
        if attrs['start_date'] > attrs['end_date']:
            raise serializers.ValidationError({'start_date': 'Start date must be less than end date'})

        return attrs

    def update(self, instance, validated_data):
        return CalendarService().update_event(instance, validated_data)


# ----------------------------------------------------File------------------------------------------------------
class FileListSerializer(serializers.ModelSerializer):
    """
    Serializer for File model
    """
    file = serializers.SerializerMethodField(read_only=True)
    user = PureUserSerializer(read_only=True)
    upload_date = serializers.DateTimeField(source='created_at', read_only=True, format='%Y-%m-%d')

    class Meta:
        model = File
        fields = ('id', 'file', 'file_name', 'model_info', 'user', 'upload_date', 'file_extension')

    @extend_schema_field(OpenApiTypes.STR)
    def get_file(self, obj):
        return base64.urlsafe_b64encode(obj.file.url.encode()).decode()


class FileCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for File model
    """
    class Meta:
        model = File
        fields = ('id', 'file', 'file_name', 'model_info', 'user')

    def validate_file(self, value):
        _, extension = os.path.splitext(value.name)

        if extension not in ['.pdf', '.docx', '.jpeg', '.jpg', '.png']:
            raise serializers.ValidationError(f'File extension {extension} is not allowed')

        if value.size > 15 * 1024 * 1024:
            raise serializers.ValidationError(f'File size must be less than {15 / (1024 * 1024)} MB')

        return value


# -----------------------------------------------Live Feed--------------------------------------------
class LiveFeedCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating LiveFeed model
    """
    class Meta:
        model = LiveFeed
        fields = ('models_infos', 'text')


class LiveFeedUpdateSerializer(LiveFeedCreateSerializer):
    """
    Serializer for updating LiveFeed model
    """
    pass


class LiveFeedListSerializer(serializers.ModelSerializer):
    """
    Serializer for listing LiveFeed model
    """
    user = PureUserSerializer()
    models_infos = NestedModelInfoSerializer(many=True)

    class Meta:
        model = LiveFeed
        fields = ('id', 'created_at', 'text', 'user', 'created_at', 'models_infos')
