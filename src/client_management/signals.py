from django.db.models.signals import post_save
from django.dispatch import receiver

from client_management.models import ModelInfo
from only_fans_models.models import OnlyFansModel


@receiver([post_save], sender=OnlyFansModel)
def create_model_info_for_model(sender, instance, created, *args, **kwargs):
    """
    Create a ModelInfo for the new OnlyFansModel model.
    """
    if not getattr(instance, 'model_info', None):
        ModelInfo.objects.create(only_fans_model=instance)
