from django.contrib.auth import get_user_model
from django.db.models import Q
from django_filters import rest_framework as filters
from rest_framework.filters import OrderingFilter

from base.filters import ListCharFilter
from client_management.models import (
    CalendarEventOccurrence,
    File,
    ModelInfo,
)
from only_fans_models.models import OnlyFansModel

User = get_user_model()


class ModelInfoFilter(filters.FilterSet):
    client_managers = ListCharFilter(field_name='only_fans_model__client_manager', lookup_expr='in')
    categories = ListCharFilter(field_name='only_fans_model__category', lookup_expr='in')

    class Meta:
        model = ModelInfo
        fields = ['client_managers', 'nationality']

    def filter_queryset(self, queryset):
        queryset = queryset.filter(only_fans_model__parent_profile__isnull=True)

        if self.request.user.role.name == 'client_manager':
            queryset = queryset.filter(only_fans_model__client_manager=self.request.user)

        return super().filter_queryset(queryset)


class ModelInfoOrderingFilter(OrderingFilter):
    """
    Custom ordering filter for ModelInfo model
    """
    def get_ordering(self, request, queryset, view):
        """
        Ordering is set by a comma delimited ?ordering=... query parameter.

        The `ordering` query parameter can be overridden by setting
        the `ordering_param` value on the OrderingFilter or by
        specifying an `ORDERING_PARAM` value in the API settings.
        """
        params = request.query_params.get(self.ordering_param)
        if params:
            fields = [
                param.strip()
                .replace('nickname', 'only_fans_model__nickname')

                for param in params.split(',')
            ]
            ordering = self.remove_invalid_fields(queryset, fields, view, request)

            if ordering:
                return ordering

        # No ordering was included, or all the ordering fields were invalid
        return self.get_default_ordering(view)


# -------------------------------Calendar---------------------------------
class CalendarEventOccurrenceFilter(filters.FilterSet):
    """
    Calendar event occurrence filter.
    Filters events to include those where the event overlaps with the specified date range.
    """
    date_after = filters.DateFilter(field_name='end_date', lookup_expr='gte', required=True)
    date_before = filters.DateFilter(field_name='start_date', lookup_expr='lte', required=True)
    model_info = filters.ModelChoiceFilter(
        field_name='calendar_event__model_info',
        queryset=ModelInfo.objects.all()
    )
    event_type = ListCharFilter(field_name='calendar_event__event_type')
    only_fans_model = filters.ModelChoiceFilter(
        field_name='calendar_event__model_info__only_fans_model',
        queryset=OnlyFansModel.objects.all()
    )

    class Meta:
        model = CalendarEventOccurrence
        fields = ['date_after', 'date_before', 'model_info', 'only_fans_model']

    def filter_queryset(self, queryset):
        if self.request.user.role.name == 'client_manager':
            queryset = queryset.filter(calendar_event__model_info__only_fans_model__client_manager=self.request.user)

        return super().filter_queryset(queryset)


# -------------------------------File---------------------------------
class FileFilter(filters.FilterSet):
    """
    File filter
    """
    model_info = filters.ModelChoiceFilter(
        queryset=ModelInfo.objects.all(),
        required=True
    )

    class Meta:
        model = File
        fields = ['model_info']

    def filter_queryset(self, queryset):
        if self.request.user.role.name == 'client_manager':
            queryset = queryset.filter(model_info__only_fans_model__client_manager=self.request.user)

        return super().filter_queryset(queryset)


# ------------------------------------------------Live Feed---------------------------------
class LiveFeedFilter(filters.FilterSet):
    """
    Live feed filter.
    """
    created_at = filters.DateFromToRangeFilter()
    users = filters.ModelMultipleChoiceFilter(
        queryset=User.objects.all(),
        field_name='user'
    )
    models_infos = filters.ModelMultipleChoiceFilter(
        queryset=ModelInfo.objects.all(),
        field_name='models_infos'
    )

    class Meta:
        fields = ['created_at', 'users', 'models_infos']

    def filter_queryset(self, queryset):
        if self.request.user.role.name == 'client_manager':
            queryset = queryset.filter(
                Q(models_infos__only_fans_model__client_manager=self.request.user)
                | Q(user=self.request.user)
            )

        return super().filter_queryset(queryset)
