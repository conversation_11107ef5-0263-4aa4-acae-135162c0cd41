import os
import uuid

from django.utils.text import slugify


def social_icon_file_path(instance, file_name):
    _, extension = os.path.splitext(file_name)

    filename = f"{slugify(instance.name)}-{uuid.uuid4()}{extension}"

    return os.path.join("client_management/icons/", filename)


def avatar_file_path(instance, file_name):
    _, file_name = os.path.split(file_name)
    _, extension = os.path.splitext(file_name)

    if file_name.startswith(slugify(instance.only_fans_model.username_of)):
        new_filename = file_name
    else:
        new_filename = f"{slugify(instance.only_fans_model.username_of)}-{uuid.uuid4()}{extension}"

    return os.path.join("client_management/avatars/", new_filename)


def model_info_file_path(instance, file_name):
    _, extension = os.path.splitext(file_name)
    filename = (
        f"{slugify(instance.model_info.only_fans_model.username_of)}-"
        f"{slugify(instance.file_name) or uuid.uuid4()}{extension}"
    )

    return os.path.join("client_management", "files", filename)
