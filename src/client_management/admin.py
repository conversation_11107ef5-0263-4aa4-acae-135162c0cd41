from django.contrib import admin

from client_management.models import (
    CalendarEvent,
    CalendarEventOccurrence,
    CalendarEventRepeatType,
    CalendarEventType,
    Country,
    File,
    Language,
    LiveFeed,
    ModelContact,
    ModelInfo,
    ModelPlatform,
    Nationality,
    Social,
)


@admin.register(Social)
class SocialAdmin(admin.ModelAdmin):
    """
    Admin class for managing Socials in the admin panel.
    """
    search_fields = ('name',)


@admin.register(Nationality)
class NationalityAdmin(admin.ModelAdmin):
    """
    Admin class for managing Nationalities in the admin panel.
    """
    search_fields = ('nationality_name',)


@admin.register(Country)
class CountryAdmin(admin.ModelAdmin):
    """
    Admin class for managing Countries in the admin panel.
    """
    search_fields = ('country_name',)


@admin.register(Language)
class LanguageAdmin(admin.ModelAdmin):
    """
    Admin class for managing Languages in the admin panel.
    """
    search_fields = ('language_name',)


@admin.register(LiveFeed)
class LiveFeedAdmin(admin.ModelAdmin):
    """
    Admin class for managing LiveFeeds in the admin panel.
    """
    search_fields = ('text',)


class ModelContactInline(admin.TabularInline):
    """
    Inline class for managing ModelContacts in the admin panel.
    """
    model = ModelContact
    extra = 1


class ModelPlatformInline(admin.TabularInline):
    """
    Inline class for managing ModelPlatforms in the admin panel.
    """
    model = ModelPlatform
    extra = 1


class FileInline(admin.TabularInline):
    """
    Inline class for managing Files in the admin panel.
    """
    fields = ('file_name', 'file', 'user')
    readonly_fields = ('user',)
    model = File
    extra = 1


class LiveFeedInline(admin.TabularInline):
    """
    Inline class for managing LiveFeeds in the admin panel.
    """
    model = ModelInfo.live_feeds.through
    readonly_fields = ('get_livefeed_text',)
    extra = 0

    def get_livefeed_text(self, instance):
        return instance.livefeed.text if instance.livefeed else None

    get_livefeed_text.short_description = 'Text'


@admin.register(ModelInfo)
class ModelInfoAdmin(admin.ModelAdmin):
    """
    Admin class for managing ModelInfos in the admin panel.
    """
    search_fields = ('model_name',)
    list_display = ('model_name', 'only_fans_model', 'dob', 'country')
    readonly_fields = ('zodiac_sign',)
    inlines = [ModelContactInline, ModelPlatformInline, FileInline, LiveFeedInline]

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('country', 'only_fans_model')

    def save_formset(self, request, form, formset, change):
        """
        Override the save_formset method to automatically set the user field for File instances.
        """
        instances = formset.save(commit=False)

        for instance in instances:
            if isinstance(instance, File) and hasattr(instance, 'user') and not instance.user:
                instance.user = request.user
            instance.save()

        formset.save_m2m()


# -----------------------------------------Calendar-----------------------------------------
@admin.register(CalendarEventType)
class CalendarEventTypeAdmin(admin.ModelAdmin):
    pass


@admin.register(CalendarEventRepeatType)
class CalendarEventRepeatTypeAdmin(admin.ModelAdmin):
    pass


class CalendarEventOccurrenceInline(admin.TabularInline):
    model = CalendarEventOccurrence
    extra = 0

    def has_add_permission(self, request, obj):
        return False


@admin.register(CalendarEvent)
class CalendarEventAdmin(admin.ModelAdmin):
    """
    Admin class for managing CalendarEvents in the admin panel.
    """
    inlines = [CalendarEventOccurrenceInline]
