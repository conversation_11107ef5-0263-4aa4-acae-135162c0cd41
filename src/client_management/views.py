from django.shortcuts import get_object_or_404
from django_filters.rest_framework import DjangoFilterBackend
from drf_spectacular.utils import extend_schema
from rest_framework import (
    mixins,
    status,
    viewsets,
)
from rest_framework.filters import SearchFilter
from rest_framework.parsers import MultiPartParser
from rest_framework.response import Response

from base.mixins import BaseViewMethodsMixin, ListSerializerResponseMixin
from base.pagination import FifteenPerPagePagination
from base.permissions import IsClientManagerOrHOCM, IsOwnerOrReadOnly
from client_management.filters import (
    CalendarEventOccurrenceFilter,
    FileFilter,
    LiveFeedFilter,
    ModelInfoFilter,
    ModelInfoOrderingFilter,
)
from client_management.models import (
    CalendarEventOccurrence,
    CalendarEventRepeatType,
    CalendarEventType,
    Country,
    File,
    Language,
    LiveFeed,
    ModelInfo,
    Nationality,
    Social,
)
from client_management.serializers import (
    CalendarEventOccurrenceCreateSerializer,
    CalendarEventOccurrenceListSerializer,
    CalendarEventOccurrenceUpdateSerializer,
    CalendarEventRepeatTypeSerializer,
    CalendarEventTypeSerializer,
    CountrySerializer,
    FileCreateSerializer,
    FileListSerializer,
    LanguageSerializer,
    LiveFeedCreateSerializer,
    LiveFeedListSerializer,
    LiveFeedUpdateSerializer,
    ModelInfoCreateSerializer,
    ModelInfoDetailSerializer,
    ModelInfoListSerializer,
    ModelInfoUpdateSerializer,
    NationalitySerializer,
    SocialSerializer,
)
from client_management.services import CalendarService


class NationalityViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint that allows nationalities to be viewed.
    """
    queryset = Nationality.objects.all()
    serializer_class = NationalitySerializer
    permission_classes = [IsClientManagerOrHOCM]


class SocialViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint that allows socials to be viewed.
    """
    queryset = Social.objects.all()
    serializer_class = SocialSerializer
    permission_classes = [IsClientManagerOrHOCM]


class CountryViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint that allows countries to be viewed.
    """
    queryset = Country.objects.all()
    serializer_class = CountrySerializer
    permission_classes = [IsClientManagerOrHOCM]


class LanguageViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint that allows languages to be viewed.
    """
    queryset = Language.objects.all()
    serializer_class = LanguageSerializer
    permission_classes = [IsClientManagerOrHOCM]


class ModelInfoViewSet(BaseViewMethodsMixin, ListSerializerResponseMixin, viewsets.ModelViewSet):
    """
    API endpoint that allows models info to be viewed.
    """
    # post and delete is excluded, model_info creates with only_fans_model
    http_method_names = ['get', 'put', 'patch']
    queryset = ModelInfo.objects.select_related(
        'nationality',
        'only_fans_model__client_manager',
        'only_fans_model__category',

    ).prefetch_related(
        'contacts__social',
        'platforms__social',
        'only_fans_model__sub_profiles',
    )
    serializer_class = ModelInfoListSerializer
    permission_classes = [IsClientManagerOrHOCM]
    action_serializers = {
        'list': ModelInfoListSerializer,
        'retrieve': ModelInfoDetailSerializer,
        'create': ModelInfoCreateSerializer,
        'update': ModelInfoUpdateSerializer,
        'partial_update': ModelInfoUpdateSerializer,
    }
    LIST_SERIALIZER_CLASS = ModelInfoDetailSerializer
    filter_backends = [SearchFilter, ModelInfoOrderingFilter, DjangoFilterBackend]
    filterset_class = ModelInfoFilter
    search_fields = [
        'nationality__nationality_name',
        'updated_at',
        'only_fans_model__nickname',
        'only_fans_model__category__name',
        'only_fans_model__client_manager__first_name',
        'only_fans_model__client_manager__last_name'
    ]
    ordering_fields = [
        'only_fans_model__nickname',
        'updated_at'
    ]

    @extend_schema(
        responses={
            201: ModelInfoDetailSerializer,
        },
    )
    def create(self, request, *args, **kwargs):
        """
        POST a model info.
        """
        return super().create(request, *args, **kwargs)

    @extend_schema(
        responses={
            200: ModelInfoDetailSerializer,
        },
    )
    def update(self, request, *args, **kwargs):
        """
        PUT a model info.
        """
        return super().update(request, *args, **kwargs)

    @extend_schema(
        responses={
            200: ModelInfoDetailSerializer,
        },
    )
    def partial_update(self, request, *args, **kwargs):
        """
        PATCH a model info.
        """
        return super().partial_update(request, *args, **kwargs)


# ------------------------------------------Calendar--------------------------------------
@extend_schema(tags=['client-management - Calendar'])
class CalendarEventOccurrenceViewSet(BaseViewMethodsMixin, ListSerializerResponseMixin, viewsets.ModelViewSet):
    """
    API endpoint that allows manage calendar.
    """
    permission_classes = [IsClientManagerOrHOCM]
    queryset = CalendarEventOccurrence.objects.select_related(
        'calendar_event',
        'calendar_event__event_type',
        'calendar_event__repeat_type',
        'calendar_event__model_info__only_fans_model'
    )
    serializer_class = CalendarEventOccurrenceListSerializer
    action_serializers = {
        'list': CalendarEventOccurrenceListSerializer,
        'create': CalendarEventOccurrenceCreateSerializer,
        'update': CalendarEventOccurrenceUpdateSerializer,
        'partial_update': CalendarEventOccurrenceUpdateSerializer
    }
    filterset_class = CalendarEventOccurrenceFilter

    def get_object(self):
        return get_object_or_404(CalendarEventOccurrence, pk=self.kwargs.get('pk'))

    @extend_schema(
        responses={
            201: CalendarEventOccurrenceListSerializer,
        },
    )
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)

    @extend_schema(
        responses={
            200: CalendarEventOccurrenceListSerializer,
        },
    )
    def update(self, request, *args, **kwargs):
        return super().update(request, *args, **kwargs)

    @extend_schema(
        responses={
            200: CalendarEventOccurrenceListSerializer,
        },
    )
    def partial_update(self, request, *args, **kwargs):
        return super().partial_update(request, *args, **kwargs)

    def destroy(self, request, *args, **kwargs):
        CalendarService().delete_event(self.get_object())

        return Response(status=status.HTTP_204_NO_CONTENT)


@extend_schema(tags=['client-management - Calendar'])
class CalendarEventTypeViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint that allows to read Calendar Event Types.
    """
    queryset = CalendarEventType.objects.all()
    permission_classes = [IsClientManagerOrHOCM]
    serializer_class = CalendarEventTypeSerializer


@extend_schema(tags=['client-management - Calendar'])
class CalendarEventRepeatTypeViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint that allows to read Calendar Repeat Types.
    """
    queryset = CalendarEventRepeatType.objects.all()
    permission_classes = [IsClientManagerOrHOCM]
    serializer_class = CalendarEventRepeatTypeSerializer


# -----------------------------------------------Files----------------------------------------------------
@extend_schema(tags=['client-management - Files'])
class FileViewSet(
    ListSerializerResponseMixin,
    BaseViewMethodsMixin,
    mixins.CreateModelMixin,
    mixins.DestroyModelMixin,
    mixins.ListModelMixin,
    viewsets.GenericViewSet
):
    """
    API endpoint that allows to manage model info files.
    """
    permission_classes = [IsClientManagerOrHOCM]
    queryset = File.objects.select_related('model_info__only_fans_model__client_manager', 'user')
    parser_classes = (MultiPartParser,)
    action_filtersets = {
        'list': FileFilter
    }
    action_serializers = {
        'list': FileListSerializer,
        'create': FileCreateSerializer
    }

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)


# --------------------------------------------------Live Feed---------------------------------------------
@extend_schema(tags=['client-management - Live Feed'])
class LiveFeedViewSet(
    ListSerializerResponseMixin,
    BaseViewMethodsMixin,
    viewsets.ModelViewSet
):
    """
    API endpoint that allows to manage live feed.
    """
    permission_classes = [IsClientManagerOrHOCM & IsOwnerOrReadOnly]
    filterset_class = LiveFeedFilter
    pagination_class = FifteenPerPagePagination
    queryset = LiveFeed.objects.select_related(
        'user'
    ).prefetch_related(
        'models_infos__only_fans_model__client_manager'
    )
    action_serializers = {
        'create': LiveFeedCreateSerializer,
        'update': LiveFeedUpdateSerializer,
        'partial_update': LiveFeedUpdateSerializer,
        'list': LiveFeedListSerializer,
        'retrieve': LiveFeedListSerializer
    }

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)
