# Generated by Django 4.2.2 on 2024-09-11 19:01

import base.models
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("client_management", "0005_file"),
    ]

    operations = [
        migrations.CreateModel(
            name="LiveFeed",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("text", models.TextField(help_text="Text")),
                (
                    "models_infos",
                    models.ManyToManyField(
                        help_text="ModelInfo",
                        related_name="live_feeds",
                        to="client_management.modelinfo",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        help_text="Client manager",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="live_feeds",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
    ]
