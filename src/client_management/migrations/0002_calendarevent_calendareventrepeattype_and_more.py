# Generated by Django 4.2.2 on 2024-05-09 17:03

import uuid

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models

import base.models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("client_management", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="CalendarEvent",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("title", models.CharField(help_text="Title", max_length=255)),
                ("start_date", models.DateField(help_text="Start date")),
                ("end_date", models.DateField(help_text="End date")),
                (
                    "description",
                    models.TextField(blank=True, help_text="Description", null=True),
                ),
                (
                    "creator",
                    models.ForeignKey(
                        blank=True,
                        help_text="Creator",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="calendar_events",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["start_date"],
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.CreateModel(
            name="CalendarEventRepeatType",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "name",
                    models.CharField(help_text="Name", max_length=255, unique=True),
                ),
            ],
            options={
                "ordering": ["name"],
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.CreateModel(
            name="CalendarEventType",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "name",
                    models.CharField(help_text="Name", max_length=255, unique=True),
                ),
            ],
            options={
                "ordering": ["name"],
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.CreateModel(
            name="CalendarEventOccurrence",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("start_date", models.DateField(help_text="Start date")),
                (
                    "end_date",
                    models.DateField(blank=True, help_text="End date", null=True),
                ),
                (
                    "calendar_event",
                    models.ForeignKey(
                        help_text="Calendar event",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="calendar_event_occurrences",
                        to="client_management.calendarevent",
                    ),
                ),
            ],
            options={
                "ordering": ["start_date"],
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.AddField(
            model_name="calendarevent",
            name="event_type",
            field=models.ForeignKey(
                help_text="Calendar event type",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="calendar_events",
                to="client_management.calendareventtype",
            ),
        ),
        migrations.AddField(
            model_name="calendarevent",
            name="model_info",
            field=models.ForeignKey(
                help_text="ModelInfo",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="calendar_events",
                to="client_management.modelinfo",
            ),
        ),
        migrations.AddField(
            model_name="calendarevent",
            name="repeat_type",
            field=models.ForeignKey(
                blank=True,
                help_text="Calendar event repeat type",
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="calendar_events",
                to="client_management.calendareventrepeattype",
            ),
        ),
    ]
