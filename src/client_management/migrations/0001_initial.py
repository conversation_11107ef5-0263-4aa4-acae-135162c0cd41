# Generated by Django 4.2.2 on 2024-04-18 12:04

import uuid

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models

import base.models
import client_management.utils


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("only_fans_models", "0012_subprofile"),
    ]

    operations = [
        migrations.CreateModel(
            name="Country",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "country_name",
                    models.CharField(
                        help_text="Country name", max_length=255, unique=True
                    ),
                ),
            ],
            options={
                "ordering": ["country_name"],
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.CreateModel(
            name="Language",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "language_name",
                    models.CharField(
                        help_text="Language name", max_length=255, unique=True
                    ),
                ),
            ],
            options={
                "ordering": ["language_name"],
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.CreateModel(
            name="ModelContact",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("contact_url", models.URLField(help_text="Url")),
            ],
            options={
                "abstract": False,
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.CreateModel(
            name="ModelInfo",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "avatar",
                    models.ImageField(
                        blank=True,
                        help_text="Avatar",
                        null=True,
                        upload_to=client_management.utils.avatar_file_path,
                    ),
                ),
                (
                    "model_name",
                    models.CharField(
                        blank=True, help_text="Model name", max_length=255, null=True
                    ),
                ),
                (
                    "dob",
                    models.DateField(blank=True, help_text="Date of birth", null=True),
                ),
                (
                    "zodiac_sign",
                    models.CharField(
                        blank=True,
                        editable=False,
                        help_text="Zodiac sign",
                        max_length=35,
                        null=True,
                    ),
                ),
                (
                    "model_conditions",
                    models.TextField(
                        blank=True, help_text="Model conditions", null=True
                    ),
                ),
                (
                    "another_job",
                    models.TextField(blank=True, help_text="Another job", null=True),
                ),
                (
                    "yes_list",
                    models.TextField(blank=True, help_text="Yes list", null=True),
                ),
                (
                    "stop_list",
                    models.TextField(blank=True, help_text="stop list", null=True),
                ),
                ("like", models.TextField(blank=True, help_text="Like", null=True)),
                (
                    "dont_like",
                    models.TextField(blank=True, help_text="Don't like", null=True),
                ),
                ("hobby", models.TextField(blank=True, help_text="Hobby", null=True)),
                ("dreams", models.TextField(blank=True, help_text="Dreams", null=True)),
                ("goals", models.TextField(blank=True, help_text="Goals", null=True)),
                ("other", models.TextField(blank=True, help_text="Other", null=True)),
                (
                    "country",
                    models.ForeignKey(
                        blank=True,
                        help_text="Country",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="models_info",
                        to="client_management.country",
                    ),
                ),
                (
                    "languages",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Languages",
                        related_name="models_info",
                        to="client_management.language",
                    ),
                ),
            ],
            options={
                "ordering": ["-updated_at"],
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.CreateModel(
            name="Nationality",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "nationality_name",
                    models.CharField(
                        help_text="Nationality name", max_length=255, unique=True
                    ),
                ),
            ],
            options={
                "ordering": ["nationality_name"],
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.CreateModel(
            name="Social",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "name",
                    models.CharField(help_text="Name", max_length=255, unique=True),
                ),
                (
                    "icon",
                    models.ImageField(
                        help_text="Icon",
                        upload_to=client_management.utils.social_icon_file_path,
                    ),
                ),
            ],
            options={
                "ordering": ["name"],
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.CreateModel(
            name="ModelPlatform",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("platform_url", models.URLField(help_text="Url")),
                (
                    "model_info",
                    models.ForeignKey(
                        help_text="ModelInfo",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="platforms",
                        to="client_management.modelinfo",
                    ),
                ),
                (
                    "social",
                    models.ForeignKey(
                        help_text="Social",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="platform_socials",
                        to="client_management.social",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.AddField(
            model_name="modelinfo",
            name="model_contacts",
            field=models.ManyToManyField(
                blank=True,
                help_text="Model contacts",
                related_name="contact_models_info",
                through="client_management.ModelContact",
                to="client_management.social",
            ),
        ),
        migrations.AddField(
            model_name="modelinfo",
            name="model_platforms",
            field=models.ManyToManyField(
                blank=True,
                help_text="Model platforms",
                related_name="platform_models_info",
                through="client_management.ModelPlatform",
                to="client_management.social",
            ),
        ),
        migrations.AddField(
            model_name="modelinfo",
            name="nationality",
            field=models.ForeignKey(
                blank=True,
                help_text="Nationality",
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="models_info",
                to="client_management.nationality",
            ),
        ),
        migrations.AddField(
            model_name="modelinfo",
            name="only_fans_model",
            field=models.OneToOneField(
                help_text="OnlyFansModel",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="model_info",
                to="only_fans_models.onlyfansmodel",
            ),
        ),
        migrations.AddField(
            model_name="modelcontact",
            name="model_info",
            field=models.ForeignKey(
                help_text="ModelInfo",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="contacts",
                to="client_management.modelinfo",
            ),
        ),
        migrations.AddField(
            model_name="modelcontact",
            name="social",
            field=models.ForeignKey(
                help_text="Social",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="contact_socials",
                to="client_management.social",
            ),
        ),
    ]
