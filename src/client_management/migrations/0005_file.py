# Generated by Django 4.2.2 on 2024-07-15 19:25

import uuid

import django.db.models.deletion
import django.utils.timezone
import storages.backends.dropbox
from django.conf import settings
from django.db import migrations, models

import base.models
import client_management.utils


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("client_management", "0004_generate_model_infos_for_only_fans_models"),
    ]

    operations = [
        migrations.CreateModel(
            name="File",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "file_name",
                    models.CharField(help_text="Name", max_length=255, unique=True),
                ),
                (
                    "file",
                    models.FileField(
                        help_text="File",
                        storage=storages.backends.dropbox.DropboxStorage(
                            app_key="6van77yzqprp1x8",
                            app_secret="gp48h0zr61z968a",
                            oauth2_access_token="sl.B46Q_D_v8lkPVyf4pntOv5STihibX-bsVDrEDF8dwKF8io4sh6Nv_Y-TW039aIl_BQXoUw-TBH19y1z5yijNjifLztoBl4WDCT1OoSik2M9mPUKGIj-J-4ImGgF3TG4Cjq48Nrbn_j5h",
                            oauth2_refresh_token="7C3ljd0vx9YAAAAAAAAAASoD5MUwD0mHXqQzmhG6yx86aviAw3ttMjDQWASewoUz",
                        ),
                        upload_to=client_management.utils.model_info_file_path,
                    ),
                ),
                (
                    "file_extension",
                    models.CharField(
                        editable=False, help_text="File extension", max_length=55
                    ),
                ),
                (
                    "model_info",
                    models.ForeignKey(
                        help_text="ModelInfo",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="files",
                        to="client_management.modelinfo",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        editable=False,
                        help_text="User",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="files",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
    ]
