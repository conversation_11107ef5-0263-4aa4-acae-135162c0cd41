# Generated by Django 4.2.2 on 2024-05-13 12:37

from django.db import migrations


def create_calendar_event_types_and_repeat_types(apps, schema_editor):
    CalendarEventType = apps.get_model("client_management", "CalendarEventType")
    CalendarEventRepeatType = apps.get_model("client_management", "CalendarEventRepeatType")

    repeat_types = [
        CalendarEventRepeatType(name='every year'),
        CalendarEventRepeatType(name='every month'),
        CalendarEventRepeatType(name='every week'),
    ]
    CalendarEventRepeatType.objects.bulk_create(repeat_types, ignore_conflicts=True)

    event_types = [
        CalendarEventType(name='Birthday'),
        CalendarEventType(name='Sick'),
        CalendarEventType(name='Vacation'),
        CalendarEventType(name='Other'),
    ]
    CalendarEventType.objects.bulk_create(event_types, ignore_conflicts=True)


def reverse_create(apps, schema_editor):
    CalendarEventType = apps.get_model("client_management", "CalendarEventType")
    CalendarEventRepeatType = apps.get_model("client_management", "CalendarEventRepeatType")

    CalendarEventType.objects.all().delete()
    CalendarEventRepeatType.objects.all().delete()


class Migration(migrations.Migration):

    dependencies = [
        ("client_management", "0002_calendarevent_calendareventrepeattype_and_more"),
    ]

    operations = [
        migrations.RunPython(create_calendar_event_types_and_repeat_types, reverse_create)
    ]
