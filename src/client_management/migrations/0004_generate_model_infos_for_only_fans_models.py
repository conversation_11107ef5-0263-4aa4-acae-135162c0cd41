# Generated by Django 4.2.2 on 2024-05-14 16:00

from django.db import migrations


def create_model_infos(apps, schema_editor):
    OnlyFansModel = apps.get_model("only_fans_models", "OnlyFansModel")
    ModelInfo = apps.get_model("client_management", "ModelInfo")

    model_without_info = OnlyFansModel.objects.filter(model_info__isnull=True)
    ModelInfo.objects.bulk_create(
        [
            ModelInfo(only_fans_model=model) for model in model_without_info
        ],
        ignore_conflicts=True,
        batch_size=100
    )


def reverse_create_model_infos(apps, schema_editor):
    pass


class Migration(migrations.Migration):

    dependencies = [
        ("client_management", "0003_add_repeat_types_and_event_types_objects"),
    ]

    operations = [
        migrations.RunPython(create_model_infos, reverse_create_model_infos)
    ]
