from rest_framework import routers

from client_management.views import (
    CalendarEventOccurrenceViewSet,
    CalendarEventRepeatTypeViewSet,
    CalendarEventTypeViewSet,
    CountryViewSet,
    FileViewSet,
    LanguageViewSet,
    LiveFeedViewSet,
    ModelInfoViewSet,
    NationalityViewSet,
    SocialViewSet,
)

router = routers.DefaultRouter()

router.register('nationalities',  NationalityViewSet, basename='nationalities')
router.register('socials',  SocialViewSet,  basename='socials')
router.register('countries', CountryViewSet,  basename='countries')
router.register('languages', LanguageViewSet,  basename='languages')
router.register('models-info', ModelInfoViewSet,  basename='models-info')
router.register('calendar/event-types', CalendarEventTypeViewSet, basename='calendar-event-types')
router.register('calendar/event-repeat-types', CalendarEventRepeatTypeViewSet, basename='calendar-event-repeat-types')
router.register('calendar', CalendarEventOccurrenceViewSet, basename='calendar')
router.register('files', FileViewSet, basename='files')
router.register('live-feeds', LiveFeedViewSet, basename='live-feeds')


urlpatterns = router.urls
