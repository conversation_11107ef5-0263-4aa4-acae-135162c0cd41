import os

from django.conf import settings
from django.core.files.storage import storages
from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from base.models import TimeStampedUUIDModel
from base.tools import compress_image, get_zodiac_sign_by_date
from client_management.utils import (
    avatar_file_path,
    model_info_file_path,
    social_icon_file_path,
)


class Social(TimeStampedUUIDModel):
    """
    Social model
    """
    name = models.CharField(
        max_length=255,
        help_text=_("Name"),
        unique=True
    )
    icon = models.ImageField(
        upload_to=social_icon_file_path,
        help_text=_("Icon")
    )

    class Meta:
        ordering = ["name"]

    def __str__(self):
        return self.name


class Nationality(TimeStampedUUIDModel):
    """
    Nationality model
    """
    nationality_name = models.CharField(
        max_length=255,
        help_text=_("Nationality name"),
        unique=True
    )

    class Meta:
        ordering = ["nationality_name"]

    def __str__(self):
        return self.nationality_name


class Country(TimeStampedUUIDModel):
    """
    Country model
    """
    country_name = models.CharField(
        max_length=255,
        help_text=_("Country name"),
        unique=True
    )

    class Meta:
        ordering = ["country_name"]

    def __str__(self):
        return self.country_name


class Language(TimeStampedUUIDModel):
    """
    Language model
    """
    language_name = models.CharField(
        max_length=255,
        help_text=_("Language name"),
        unique=True
    )

    class Meta:
        ordering = ["language_name"]

    def __str__(self):
        return self.language_name


class ModelContact(TimeStampedUUIDModel):
    model_info = models.ForeignKey(
        'client_management.ModelInfo',
        help_text=_("ModelInfo"),
        on_delete=models.CASCADE,
        related_name='contacts'
    )
    social = models.ForeignKey(
        Social,
        help_text=_("Social"),
        on_delete=models.CASCADE,
        related_name='contact_socials'
    )
    contact_url = models.URLField(
        help_text=_("Url")
    )


class ModelPlatform(TimeStampedUUIDModel):
    model_info = models.ForeignKey(
        'client_management.ModelInfo',
        help_text=_("ModelInfo"),
        on_delete=models.CASCADE,
        related_name='platforms'
    )
    social = models.ForeignKey(
        Social,
        help_text=_("Social"),
        on_delete=models.CASCADE,
        related_name='platform_socials'
    )
    platform_url = models.URLField(
        help_text=_("Url")
    )


class ModelInfo(TimeStampedUUIDModel):
    only_fans_model = models.OneToOneField(
        'only_fans_models.OnlyFansModel',
        help_text=_("OnlyFansModel"),
        on_delete=models.CASCADE,
        related_name='model_info'
    )
    avatar = models.ImageField(
        storage=storages['overwrite_file_system_storage'],
        upload_to=avatar_file_path,
        help_text=_("Avatar"),
        blank=True,
        null=True
    )
    model_name = models.CharField(
        max_length=255,
        help_text=_("Model name"),
        blank=True,
        null=True
    )
    dob = models.DateField(
        help_text=_("Date of birth"),
        blank=True,
        null=True
    )
    nationality = models.ForeignKey(
        Nationality,
        help_text=_("Nationality"),
        on_delete=models.CASCADE,
        related_name='models_info',
        blank=True,
        null=True
    )
    country = models.ForeignKey(
        Country,
        help_text=_("Country"),
        on_delete=models.CASCADE,
        related_name='models_info',
        blank=True,
        null=True
    )
    languages = models.ManyToManyField(
        Language,
        help_text=_("Languages"),
        related_name='models_info',
        blank=True
    )
    zodiac_sign = models.CharField(
        max_length=35,
        help_text=_("Zodiac sign"),
        editable=False,
        blank=True,
        null=True

    )
    model_conditions = models.TextField(
        help_text=_("Model conditions"),
        blank=True,
        null=True
    )
    another_job = models.TextField(
        help_text=_("Another job"),
        blank=True,
        null=True
    )
    yes_list = models.TextField(
        help_text=_("Yes list"),
        blank=True,
        null=True
    )
    stop_list = models.TextField(
        help_text=_("stop list"),
        blank=True,
        null=True
    )
    like = models.TextField(
        help_text=_("Like"),
        blank=True,
        null=True
    )
    dont_like = models.TextField(
        help_text=_("Don't like"),
        blank=True,
        null=True
    )
    hobby = models.TextField(
        help_text=_("Hobby"),
        blank=True,
        null=True
    )
    dreams = models.TextField(
        help_text=_("Dreams"),
        blank=True,
        null=True
    )
    goals = models.TextField(
        help_text=_("Goals"),
        blank=True,
        null=True
    )
    other = models.TextField(
        help_text=_("Other"),
        blank=True,
        null=True
    )
    model_contacts = models.ManyToManyField(
        Social,
        help_text=_("Model contacts"),
        related_name='contact_models_info',
        blank=True,
        through='client_management.ModelContact'
    )
    model_platforms = models.ManyToManyField(
        Social,
        help_text=_("Model platforms"),
        related_name='platform_models_info',
        blank=True,
        through='client_management.ModelPlatform'
    )

    class Meta:
        ordering = ["-updated_at"]

    def __str__(self):
        return self.model_name or self.only_fans_model.nickname

    @property
    def age(self):
        today = timezone.now().date()

        return today.year - self.dob.year - ((today.month, today.day) < (self.dob.month, self.dob.day))

    def save(self, *args, **kwargs):
        if self.dob:
            self.zodiac_sign = get_zodiac_sign_by_date(self.dob)

        if self.avatar:
            self.avatar = compress_image(self.avatar)

        return super().save(*args, **kwargs)


# ------------------------------------------ Calendar ------------------------------------------

class CalendarEventType(TimeStampedUUIDModel):
    """
    Calendar event type
    """
    name = models.CharField(
        max_length=255,
        help_text=_("Name"),
        unique=True
    )

    class Meta:
        ordering = ["name"]

    def __str__(self):
        return self.name


class CalendarEventRepeatType(TimeStampedUUIDModel):
    """
    Calendar event repeat type
    """
    name = models.CharField(
        max_length=255,
        help_text=_("Name"),
        unique=True
    )

    class Meta:
        ordering = ["name"]

    def __str__(self):
        return self.name


class CalendarEvent(TimeStampedUUIDModel):
    """
    Calendar event
    """
    model_info = models.ForeignKey(
        ModelInfo,
        help_text=_("ModelInfo"),
        on_delete=models.CASCADE,
        related_name='calendar_events'
    )
    title = models.CharField(
        max_length=255,
        help_text=_("Title")
    )
    event_type = models.ForeignKey(
        CalendarEventType,
        help_text=_("Calendar event type"),
        on_delete=models.CASCADE,
        related_name='calendar_events'
    )
    start_date = models.DateField(
        help_text=_("Start date")
    )
    end_date = models.DateField(
        help_text=_("End date")
    )
    repeat_type = models.ForeignKey(
        CalendarEventRepeatType,
        help_text=_("Calendar event repeat type"),
        on_delete=models.CASCADE,
        related_name='calendar_events',
        blank=True,
        null=True
    )
    description = models.TextField(
        help_text=_("Description"),
        blank=True,
        null=True
    )
    creator = models.ForeignKey(
        'accounts.User',
        help_text=_("Creator"),
        on_delete=models.CASCADE,
        related_name='calendar_events',
        blank=True,
        null=True
    )

    class Meta:
        ordering = ["start_date"]

    def __str__(self):
        return self.title


class CalendarEventOccurrence(TimeStampedUUIDModel):
    """
    Calendar event occurrence
    """
    calendar_event = models.ForeignKey(
        CalendarEvent,
        help_text=_("Calendar event"),
        on_delete=models.CASCADE,
        related_name='calendar_event_occurrences'
    )
    start_date = models.DateField(
        help_text=_("Start date")
    )
    end_date = models.DateField(
        help_text=_("End date"),
        blank=True,
        null=True
    )

    class Meta:
        ordering = ["start_date"]

    def __str__(self):
        return f"{self.start_date} - {self.end_date}"


# ----------------------------------------------------Files-----------------------------------------------------
class File(TimeStampedUUIDModel):
    """
    File for model profile
    """
    file_name = models.CharField(
        max_length=255,
        help_text=_("Name"),
        unique=True
    )
    file = models.FileField(
        help_text=_("File"),
        storage=storages["dropbox"],
        upload_to=model_info_file_path
    )
    model_info = models.ForeignKey(
        ModelInfo,
        help_text=_("ModelInfo"),
        on_delete=models.CASCADE,
        related_name='files',
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        help_text=_("User"),
        on_delete=models.SET_NULL,
        related_name='files',
        editable=False,
        null=True
    )
    file_extension = models.CharField(
        max_length=55,
        help_text=_("File extension"),
        editable=False
    )

    class Meta:
        ordering = ["-created_at"]

    def save(self, *args, **kwargs):
        _, file_extension = os.path.splitext(self.file.name)
        self.file_extension = file_extension.replace('.', '')

        return super().save(*args, **kwargs)

    def __str__(self):
        return self.file_name


# -------------------------------------------------------------------Feed---------------------------------------
class LiveFeed(TimeStampedUUIDModel):
    """
    Live feed
    """
    models_infos = models.ManyToManyField(
        ModelInfo,
        help_text=_("ModelInfo"),
        related_name='live_feeds'
    )
    user = models.ForeignKey(
        'accounts.User',
        help_text=_("Client manager"),
        on_delete=models.CASCADE,
        related_name='live_feeds'
    )
    text = models.TextField(
        help_text=_("Text")
    )

    class Meta:
        ordering = ["-created_at"]

    def __str__(self):
        return self.text[:50]
