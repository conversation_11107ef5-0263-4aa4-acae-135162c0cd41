from dateutil.relativedelta import relativedelta
from rest_framework import serializers
from rest_framework.validators import UniqueTogetherValidator

from only_fans_models.models import OnlyFansModel
from only_fans_models_plan.models import (
    ExpectedResult,
    MarketingBudgetExpectedResult,
    MarketingBudgetPeriod,
    Period,
)
from only_fans_models_plan.services import (
    ExpectedResultService,
    MarketingBudgetExpectedResultService,
    MarketingBudgetPeriodService,
    PeriodService,
)


class ExpectedResultOnlyFansModel(serializers.ModelSerializer):
    """
    Serializer class for the ExpectedResultOnlyFansModel.

    This serializer is used to serialize and deserialize data for the ExpectedResultOnlyFansModel model.
    It is used in conjunction with the OnlyFansModel model.
    """
    class Meta:
        model = OnlyFansModel
        fields = ('id', 'nickname', 'model_id')


class NestedExpectedResultSerializer(serializers.ModelSerializer):
    """
    NestedExpectedResultSerializer class

    Serializer for ExpectedResult model with nested fields.
    Converts the ExpectedResult model data to a Python dict when serializing and vice versa when deserializing.

    Attributes:
        only_fans_model (ExpectedResultOnlyFansModel): An instance of the ExpectedResultOnlyFansModel class.
        fact_subscribers (serializers.IntegerField): Serializer field for the fact_result.fact_subscribers field of the ExpectedResult model.
        fact_revenue (serializers.IntegerField): Serializer field for the fact_result.fact_revenue field of the ExpectedResult model.

    Meta:
        model (ExpectedResult): The ExpectedResult model associated with this serializer.
        fields (tuple): The fields to include in the serialized output.
    """
    only_fans_model = ExpectedResultOnlyFansModel()
    fact_subscribers = serializers.IntegerField(source='fact_result.fact_subscribers', read_only=True)
    fact_revenue = serializers.IntegerField(source='fact_result.fact_revenue', read_only=True)

    class Meta:
        model = ExpectedResult
        fields = (
            'id',
            'expected_subscribers',
            'expected_subscribers_gg',
            'expected_subscribers_oftv',
            'expected_revenue',
            'only_fans_model',
            'fact_subscribers',
            'fact_revenue'
        )


class PeriodDetailSerializer(serializers.ModelSerializer):
    """
    Serializer class for Period model.

    This serializer is used to serialize and deserialize Period objects for API representation.

    Attributes:
        expected_results (NestedExpectedResultSerializer): Serializes and deserializes the expected_results nested relationship of the Period model.
        previous_period (MethodField): Retrieves the previous period based on the start date and end date of the current period.

    Meta:
        model (Period): The Period model that this serializer is associated with.
        fields (tuple): The fields to include in the serialized representation of the Period model.

    Methods:
        get_previous_period(obj): Retrieves the previous period based on the start date and end date of the current period.

    """
    expected_results = NestedExpectedResultSerializer(many=True)
    previous_period = serializers.SerializerMethodField()

    class Meta:
        model = Period
        fields = (
            'id',
            'start_date',
            'end_date',
            'expected_results',
            'previous_period',
            'name'
        )

    def get_previous_period(self, obj):
        start_date = obj.start_date - relativedelta(months=1)
        end_date = obj.end_date - relativedelta(months=1)

        previous_period = Period.objects.filter(
            start_date__month=start_date.month,
            end_date__month=end_date.month
        ).first()

        if previous_period:
            return str(previous_period.id)


class ExpectedResultForPeriodSerializer(serializers.ModelSerializer):
    """
    Serializer class for ExpectedResult model related to a specific Period.

    This serializer is used to serialize and deserialize ExpectedResult objects related to a specific Period.
    It defines the fields that can be included in the serialized representation of an ExpectedResult object
    and provides validation for those fields.
    """
    class Meta:
        model = ExpectedResult
        fields = (
            'expected_subscribers',
            'expected_subscribers_gg',
            'expected_subscribers_oftv',
            'expected_revenue',
            'only_fans_model'
        )

    def validate(self, attrs):
        expected_subscribers = attrs.get('expected_subscribers', 0)
        expected_subscribers_gg = attrs.get('expected_subscribers_gg', 0)
        expected_subscribers_oftv = attrs.get('expected_subscribers_oftv', 0)

        if expected_subscribers_gg + expected_subscribers_oftv > expected_subscribers:
            raise serializers.ValidationError(
                'Sum of gg and oftv subscribers should be equal or less than expected subscribers'
            )

        return attrs


class PeriodCreateUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating and updating Period instances.

    Fields:
    - start_date (Date): The start date of the period.
    - end_date (Date): The end date of the period.
    - expected_results (list[ExpectedResultForPeriodSerializer], optional): List of expected results for the period.

    Validators:
    - UniqueTogetherValidator: Validates that start_date and end_date combination is unique in the database.

    Methods:
    - validate(attrs): Custom validation method for validating start_date and end_date.
    - create(validated_data): Method for creating a new Period instance.
    - update(instance, validated_data): Method for updating an existing Period instance.
    """
    expected_results = ExpectedResultForPeriodSerializer(many=True,  required=False)

    class Meta:
        model = Period
        fields = ('start_date', 'end_date', 'expected_results', 'name')
        validators = [
            UniqueTogetherValidator(
                queryset=Period.objects.all(),
                fields=['start_date', 'end_date'],
                message='This month already exist'
            )
        ]

    def validate(self, attrs):
        start_date = attrs.get('start_date')
        end_date = attrs.get('end_date')

        if start_date and end_date and start_date > end_date:
            raise serializers.ValidationError('Start date must be less than end date')

        return attrs

    def create(self, validated_data):
        return PeriodService.create(validated_data)

    def update(self, instance, validated_data):
        return PeriodService(instance).update(validated_data)


class PeriodListSerializer(serializers.ModelSerializer):
    """
    Period List Serializer

    Serializer class for serializing and deserializing Period objects.
    """
    class Meta:
        model = Period
        fields = ('id', 'start_date', 'end_date', 'name')


class ExpectedResultSerializer(serializers.ModelSerializer):
    """
    The `ExpectedResultSerializer` class is a serializer that converts
    the `ExpectedResult` model instances to Python native datatypes, and vice versa.
    It is used for validating and serializing/deserializing data passed to/from the API endpoints.
    """
    class Meta:
        model = ExpectedResult
        fields = (
            'id',
            'expected_subscribers',
            'expected_subscribers_gg',
            'expected_subscribers_oftv',
            'expected_revenue',
            'only_fans_model',
            'period',
        )


class ExpectedResultCreateUpdateSerializer(ExpectedResultSerializer):
    """
    The `ExpectedResultCreateUpdateSerializer` class is a serializer that is used for creating and updating `ExpectedResult` objects.

    Attributes:
        parent (class): The parent class from which this class inherits.

    Methods:
        create(validated_data):
            This method is used for creating a new `ExpectedResult` object.

            Returns:
                ExpectedResult: The created `ExpectedResult` object.
    """
    def validate(self, attrs):
        expected_subscribers = attrs.get(
            'expected_subscribers',
            self.instance.expected_subscribers if self.instance else 0
        )
        expected_subscribers_gg = attrs.get(
            'expected_subscribers_gg',
            self.instance.expected_subscribers_gg if self.instance else 0
        )
        expected_subscribers_oftv = attrs.get(
            'expected_subscribers_oftv',
            self.instance.expected_subscribers_oftv if self.instance else 0
        )

        if expected_subscribers_gg + expected_subscribers_oftv > expected_subscribers:
            raise serializers.ValidationError(
                'Sum of gg and oftv subscribers should be equal or less than expected subscribers'
            )

        return attrs

    def create(self, validated_data):
        return ExpectedResultService.create(validated_data)


# ------------------------------------------Marketing Budget----------------------------------------------
class MarketingBudgetExpectedResultOnlyFansModel(serializers.ModelSerializer):
    """
    Serializer class for the MarketingExpectedResultOnlyFansModel.
    """
    marketer_full_name = serializers.CharField(
        source='marketer.full_name',
        read_only=True,
        allow_null=True
    )

    class Meta:
        model = OnlyFansModel
        fields = ('id', 'nickname', 'model_id', 'marketer_full_name')


class NestedMarketingBudgetExpectedResultSerializer(serializers.ModelSerializer):
    """
    NestedMarketingBudgetExpectedResultSerializer class
    """
    only_fans_model = MarketingBudgetExpectedResultOnlyFansModel()
    cost_fact = serializers.SerializerMethodField()

    class Meta:
        model = MarketingBudgetExpectedResult
        fields = (
            'id',
            'cost_plan',
            'only_fans_model',
            'promo_type',
            'cost_fact'
        )

    def get_cost_fact(self, obj):
        date_filter = self.context['request'].query_params.get('date_filter')

        if not hasattr(obj, 'marketing_budget_fact_result') or obj.marketing_budget_fact_result is None:
            return 0

        if date_filter == 'date_counter':
            return obj.marketing_budget_fact_result.cost_fact_date_counter or 0

        return obj.marketing_budget_fact_result.cost_fact or 0


class MarketingBudgetPeriodDetailSerializer(serializers.ModelSerializer):
    """
    Serializer class for MarketingBudgetPeriod model.
    """
    marketing_budget_expected_results = NestedMarketingBudgetExpectedResultSerializer(many=True)
    previous_period = serializers.SerializerMethodField()

    class Meta:
        model = MarketingBudgetPeriod
        fields = (
            'id',
            'start_date',
            'end_date',
            'marketing_budget_expected_results',
            'previous_period',
            'name'
        )

    def get_previous_period(self, obj):
        start_date = obj.start_date - relativedelta(months=1)
        end_date = obj.end_date - relativedelta(months=1)

        previous_period = MarketingBudgetPeriod.objects.filter(
            start_date__month=start_date.month,
            end_date__month=end_date.month
        ).first()

        if previous_period:
            return str(previous_period.id)


class MarketingBudgetExpectedResultForPeriodSerializer(serializers.ModelSerializer):
    """
    Serializer class for MarketingBudgetExpectedResult model related to a specific MarketingBudgetPeriod.
    """
    class Meta:
        model = MarketingBudgetExpectedResult
        fields = (
            'cost_plan',
            'only_fans_model',
            'promo_type',
        )


class MarketingBudgetPeriodCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating and updating MarketingBudgetPeriod instances.
    """
    marketing_budget_expected_results = MarketingBudgetExpectedResultForPeriodSerializer(many=True,  required=False)

    class Meta:
        model = MarketingBudgetPeriod
        fields = ('start_date', 'end_date', 'marketing_budget_expected_results', 'name')
        validators = [
            UniqueTogetherValidator(
                queryset=MarketingBudgetPeriod.objects.all(),
                fields=['start_date', 'end_date'],
                message='This month already exist'
            )
        ]

    def validate(self, attrs):
        start_date = attrs.get('start_date')
        end_date = attrs.get('end_date')

        if start_date and end_date and start_date > end_date:
            raise serializers.ValidationError('Start date must be less than end date')

        return attrs

    def create(self, validated_data):
        return MarketingBudgetPeriodService.create(validated_data)


class MarketingBudgetPeriodUpdateSerializer(MarketingBudgetPeriodCreateSerializer):
    """
    Serializer for updating MarketingBudgetPeriod instances.
    """
    marketing_budget_expected_results = MarketingBudgetExpectedResultForPeriodSerializer(many=True, required=False)
    promo_type = serializers.ChoiceField(MarketingBudgetExpectedResult.PromoType.choices, required=True)

    class Meta:
        model = MarketingBudgetPeriod
        fields = ('start_date', 'end_date', 'marketing_budget_expected_results', 'name', 'promo_type')
        validators = [
            UniqueTogetherValidator(
                queryset=MarketingBudgetPeriod.objects.all(),
                fields=['start_date', 'end_date'],
                message='This month already exist'
            )
        ]

    def update(self, instance, validated_data):
        return MarketingBudgetPeriodService(instance).update(validated_data)


class MarketingBudgetPeriodListSerializer(serializers.ModelSerializer):
    """
    MarketingBudgetPeriodListSerializer

    Serializer class for serializing and deserializing MarketingBudgetPeriod objects.
    """
    class Meta:
        model = MarketingBudgetPeriod
        fields = ('id', 'start_date', 'end_date', 'name')


class MarketingBudgetExpectedResultSerializer(serializers.ModelSerializer):
    """
    Serializer class for MarketingBudgetExpectedResult model.
    """
    marketer_full_name = serializers.CharField(
        source='only_fans_model.marketer.full_name',
        read_only=True,
        allow_null=True
    )

    class Meta:
        model = MarketingBudgetExpectedResult
        fields = (
            'id',
            'only_fans_model',
            'marketing_budget_period',
            'cost_plan',
            'promo_type',
            'marketer_full_name'
        )


class MarketingBudgetExpectedResultCreateUpdateSerializer(MarketingBudgetExpectedResultSerializer):
    def create(self, validated_data):
        return MarketingBudgetExpectedResultService.create(validated_data)


class CopyDataSerializer(serializers.Serializer):
    """
    Serializer for table data string representation
    """
    copy_data = serializers.CharField(max_length=100000)


class CopyMarketingBudgetPeriodDetailData(CopyDataSerializer):
    """
    Serializer for MarketingBudgetPeriodDetail string representation
    """
    pass
