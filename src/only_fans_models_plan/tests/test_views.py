import json

from django.urls import reverse
from rest_framework import status

from ads.models import Ads
from base.tests.mixins import BaseCRMTest
from only_fans_models_plan.models import (
    ExpectedResult,
    FactResult,
    MarketingBudgetExpectedResult,
    MarketingBudgetFactResult,
    MarketingBudgetPeriod,
    Period,
)


class TestOFModelsPlanViews(BaseCRMTest):
    def setUp(self):
        super().setUp()
        self.client.force_authenticate(user=self.user_superuser)

        self.only_fans_model = self.create_only_fans_model(1)

        self.valid_period_data = {
            'start_date': '2023-12-01',
            'end_date': '2023-12-31',
            'expected_results': [{
                "expected_subscribers": 1111,
                "expected_revenue": 2222,
                "only_fans_model": str(self.only_fans_model.id),
            }]
        }
        self.valid_marketing_budget_period_data = {
            'start_date': '2023-12-01',
            'end_date': '2023-12-31',
            'marketing_budget_expected_results': [{
                "cost_plan": 3333,
                "only_fans_model": str(self.only_fans_model.id),
            }]
        }

    def test_read_permissions(self):
        for user in [self.user_superuser, self.user_hom, self.user_marketer]:
            self.client.force_authenticate(user=user)
            response = self.client.get(reverse('periods-list'))
            self.assertEqual(response.status_code, 200)

        for user in [self.user_team_lead, self.user_operator, self.user_senior_operator]:
            self.client.force_authenticate(user=user)
            response = self.client.get(reverse('periods-list'))
            self.assertEqual(response.status_code, 403)

    def test_write_permissions(self):
        data = {
            'start_date': '2023-12-01',
            'end_date': '2023-12-31',
        }

        for user in [
            self.user_hom, self.user_marketer, self.user_operator, self.user_team_lead, self.user_senior_operator
        ]:
            self.client.force_authenticate(user=user)
            response = self.client.post(reverse('periods-list'),  data=data)
            self.assertEqual(response.status_code, 403)

        self.client.force_authenticate(user=self.user_superuser)
        response = self.client.post(reverse('periods-list'),  data=data)
        self.assertEqual(response.status_code, 201)

    def test_create_period(self):
        data = self.valid_period_data
        response = self.client.post(
            reverse('periods-list'),  data=json.dumps(data), content_type='application/json'
        )
        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.data['start_date'], self.valid_period_data['start_date'])
        self.assertEqual(response.data['end_date'], self.valid_period_data['end_date'])
        self.assertEqual(
            response.data['expected_results'][0]['expected_subscribers'],
            self.valid_period_data['expected_results'][0]['expected_subscribers']
        )
        self.assertEqual(
            response.data['expected_results'][0]['expected_revenue'],
            self.valid_period_data['expected_results'][0]['expected_revenue']
        )
        self.assertEqual(
            response.data['expected_results'][0]['only_fans_model']['id'],
            self.valid_period_data['expected_results'][0]['only_fans_model']
        )
        self.assertIn('fact_subscribers', response.data['expected_results'][0])
        self.assertIn('fact_revenue', response.data['expected_results'][0])

        expected_result = ExpectedResult.objects.get(id=response.data['expected_results'][0]["id"])
        self.assertTrue(FactResult.objects.filter(expected_result=expected_result).exists())

    def test_models_plan_period_cache(self):
        cache_key = f'models-plan-period_{self.user_superuser.id}_'
        self.assertIsNone(self.cache_service.get_cache_data(cache_key))
        response = self.client.get(reverse('periods-list'))
        self.assertIsNotNone(self.cache_service.get_cache_data(cache_key))
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data = self.valid_period_data
        response = self.client.post(
            reverse('periods-list'), data=json.dumps(data), content_type='application/json'
        )
        period_id = response.data['id']
        retreive_cache_key = f'models-plan-period_{self.user_superuser.id}_{period_id}_'
        self.assertIsNone(self.cache_service.get_cache_data(retreive_cache_key))
        response = self.client.get(reverse('periods-detail', kwargs={'pk': period_id}))
        self.assertEqual(response.status_code, 200)
        self.assertIsNotNone(self.cache_service.get_cache_data(retreive_cache_key))

    def test_invalidate_cache_models_plan_period(self):
        cache_key = f'models-plan-period_{self.user_superuser.id}_'
        self.assertIsNone(self.cache_service.get_cache_data(cache_key))
        self.client.get(reverse('periods-list'))
        self.assertIsNotNone(self.cache_service.get_cache_data(cache_key))
        period = Period.objects.create(
            start_date='2023-12-01',
            end_date='2023-12-31',
        )
        self.assertIsNone(self.cache_service.get_cache_data(cache_key))

        retreive_cache_key = f'models-plan-period_{self.user_superuser.id}_{period.id}_'
        self.client.get(reverse('periods-detail',  kwargs={'pk': period.id}))
        self.assertIsNotNone(self.cache_service.get_cache_data(retreive_cache_key))

        self.client.get(reverse('periods-list'))
        self.assertIsNotNone(self.cache_service.get_cache_data(cache_key))

        only_fans_model = self.create_only_fans_model(2)
        expected_result = ExpectedResult.objects.create(
            expected_subscribers=1111,
            expected_revenue=2222,
            only_fans_model=only_fans_model,
            period=period
        )
        self.assertIsNone(self.cache_service.get_cache_data(retreive_cache_key))
        self.assertIsNone(self.cache_service.get_cache_data(cache_key))

        self.client.get(reverse('periods-list'))
        self.assertIsNotNone(self.cache_service.get_cache_data(cache_key))
        self.client.get(reverse('periods-detail', kwargs={'pk': period.id}))
        self.assertIsNotNone(self.cache_service.get_cache_data(retreive_cache_key))
        expected_result.delete()
        self.assertIsNone(self.cache_service.get_cache_data(retreive_cache_key))
        self.assertIsNone(self.cache_service.get_cache_data(cache_key))

        self.client.get(reverse('periods-list'))
        self.assertIsNotNone(self.cache_service.get_cache_data(cache_key))
        period.delete()
        self.assertIsNone(self.cache_service.get_cache_data(cache_key))

    def test_read_permissions_marketing_budget(self):
        for user in self.users:
            self.client.force_authenticate(user=user)
            response = self.client.get(reverse('marketing-budget-periods-list'))

            if user.role.name in ['hom', 'marketer', 'superuser', 'financier', 'hof']:
                self.assertEqual(response.status_code, 200)
            else:
                self.assertEqual(response.status_code, 403)

    def test_write_permissions_marketing_budget(self):
        data = {
            'start_date': '2023-12-01',
            'end_date': '2023-12-31',
        }

        for user in self.users:
            self.client.force_authenticate(user=user)
            response = self.client.post(reverse('marketing-budget-periods-list'), data=data)

            if user.role.name in ['hom']:
                self.assertEqual(response.status_code, 201)
            else:
                self.assertEqual(response.status_code, 403)

    def test_create_marketing_budget_period(self):
        data = self.valid_marketing_budget_period_data
        self.client.force_authenticate(user=self.user_hom)
        response = self.client.post(
            reverse('marketing-budget-periods-list'),  data=json.dumps(data), content_type='application/json'
        )
        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.data['start_date'], data['start_date'])
        self.assertEqual(response.data['end_date'], data['end_date'])
        self.assertEqual(
            response.data['marketing_budget_expected_results'][0]['cost_plan'],
            data['marketing_budget_expected_results'][0]['cost_plan']
        )
        self.assertEqual(
            response.data['marketing_budget_expected_results'][0]['only_fans_model']['id'],
            data['marketing_budget_expected_results'][0]['only_fans_model']
        )
        self.assertIn('cost_plan', response.data['marketing_budget_expected_results'][0])

        expected_result = MarketingBudgetExpectedResult.objects.get(
            id=response.data['marketing_budget_expected_results'][0]["id"]
        )
        self.assertTrue(
            MarketingBudgetFactResult.objects.filter(marketing_budget_expected_result=expected_result).exists()
        )

    def test_models_plan_budget_marketing_period_cache(self):
        self.client.force_authenticate(user=self.user_hom)
        cache_key = f'marketing-budget-period_{self.user_hom.id}_'
        self.assertIsNone(self.cache_service.get_cache_data(cache_key))
        response = self.client.get(reverse('marketing-budget-periods-list'))
        self.assertIsNotNone(self.cache_service.get_cache_data(cache_key))
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data = self.valid_marketing_budget_period_data
        response = self.client.post(
            reverse('marketing-budget-periods-list'), data=json.dumps(data), content_type='application/json'
        )
        period_id = response.data['id']
        retrieve_cache_key = f'marketing-budget-period_{self.user_hom.id}_{period_id}_'
        self.assertIsNone(self.cache_service.get_cache_data(retrieve_cache_key))
        response = self.client.get(reverse('marketing-budget-periods-detail', kwargs={'pk': period_id}))
        self.assertEqual(response.status_code, 200)
        self.assertIsNotNone(self.cache_service.get_cache_data(retrieve_cache_key))

    def test_invalidate_cache_models_plan_period_budget_marketing_period(self):
        self.client.force_authenticate(user=self.user_hom)
        cache_key = f'marketing-budget-period_{self.user_hom.id}_'
        self.assertIsNone(self.cache_service.get_cache_data(cache_key))
        self.client.get(reverse('marketing-budget-periods-list'))
        self.assertIsNotNone(self.cache_service.get_cache_data(cache_key))
        period = MarketingBudgetPeriod.objects.create(
            start_date='2023-12-01',
            end_date='2023-12-31',
        )
        self.assertIsNone(self.cache_service.get_cache_data(cache_key))

        retreive_cache_key = f'marketing-budget-period_{self.user_hom.id}_{period.id}_'
        self.client.get(reverse('marketing-budget-periods-detail',  kwargs={'pk': period.id}))
        self.assertIsNotNone(self.cache_service.get_cache_data(retreive_cache_key))

        self.client.get(reverse('marketing-budget-periods-list'))
        self.assertIsNotNone(self.cache_service.get_cache_data(cache_key))

        only_fans_model = self.create_only_fans_model(2)
        expected_result = MarketingBudgetExpectedResult.objects.create(
            only_fans_model=only_fans_model,
            marketing_budget_period=period,
            cost_plan=3333
        )
        self.assertIsNone(self.cache_service.get_cache_data(retreive_cache_key))
        self.assertIsNone(self.cache_service.get_cache_data(cache_key))

        self.client.get(reverse('marketing-budget-periods-list'))
        self.assertIsNotNone(self.cache_service.get_cache_data(cache_key))
        self.client.get(reverse('marketing-budget-periods-detail', kwargs={'pk': period.id}))
        self.assertIsNotNone(self.cache_service.get_cache_data(retreive_cache_key))
        expected_result.delete()
        self.assertIsNone(self.cache_service.get_cache_data(retreive_cache_key))
        self.assertIsNone(self.cache_service.get_cache_data(cache_key))

        self.client.get(reverse('marketing-budget-periods-list'))
        self.assertIsNotNone(self.cache_service.get_cache_data(cache_key))
        period.delete()
        self.assertIsNone(self.cache_service.get_cache_data(cache_key))

    def test_ads_budget_fact_results_updated_if_model_date_changed(self):
        december_period = MarketingBudgetPeriod.objects.create(
            start_date='2023-12-01',
            end_date='2023-12-31',
        )
        december_expected_result = MarketingBudgetExpectedResult.objects.create(
            only_fans_model=self.only_fans_model,
            marketing_budget_period=december_period,
            cost_plan=1000
        )
        december_fact_result = MarketingBudgetFactResult.objects.create(
            marketing_budget_expected_result=december_expected_result,
            cost_fact=0
        )
        ads = Ads.objects.create(
            only_fans_model=self.only_fans_model,
            date='2023-12-01',
            cost=100
        )

        december_fact_result.refresh_from_db()
        self.assertEqual(december_fact_result.cost_fact, 100)

        january_period = MarketingBudgetPeriod.objects.create(
            start_date='2024-01-01',
            end_date='2024-01-31',
        )
        january_expected_result = MarketingBudgetExpectedResult.objects.create(
            only_fans_model=self.only_fans_model,
            marketing_budget_period=january_period,
            cost_plan=1000
        )
        january_fact_result = MarketingBudgetFactResult.objects.create(
            marketing_budget_expected_result=january_expected_result,
            cost_fact=0
        )

        ads.date = '2024-01-01'
        ads.save()

        january_fact_result.refresh_from_db()
        december_fact_result.refresh_from_db()

        self.assertEqual(january_fact_result.cost_fact, 100)
        self.assertEqual(december_fact_result.cost_fact, 0)

    def test_model_plans_period_expected_subscribers_not_less_than_gg_and_oftv(self):
        data = json.dumps({
                'start_date': '2023-12-01',
                'end_date': '2023-12-31',
                'expected_results': [{
                    "expected_subscribers": 100,
                    "expected_revenue": 2222,
                    "only_fans_model": str(self.only_fans_model.id),
                    "expected_subscribers_oftv":  100,
                    "expected_subscribers_gg": 100
                }]
            })
        response = self.client.post(
            reverse('periods-list'), data=data, content_type='application/json'
        )
        self.assertEqual(response.status_code, 400)

        data = json.dumps({
            'start_date': '2023-12-01',
            'end_date': '2023-12-31',
            'expected_results': [{
                "expected_subscribers": 100,
                "expected_revenue": 2222,
                "only_fans_model": str(self.only_fans_model.id),
                "expected_subscribers_oftv": 50,
                "expected_subscribers_gg": 50
            }]
        })
        response = self.client.post(
            reverse('periods-list'), data=data, content_type='application/json'
        )
        self.assertEqual(response.status_code, 201)

    def test_marketing_budget_retrieve_promo_type_filter(self):
        data = {
            'start_date': '2023-12-01',
            'end_date': '2023-12-31',
            'marketing_budget_expected_results': [
                {
                    "cost_plan": 3333,
                    "only_fans_model": str(self.only_fans_model.id),
                    "promo_type": MarketingBudgetExpectedResult.PromoType.ALL
                },
                {
                    "cost_plan": 3333,
                    "only_fans_model": str(self.only_fans_model.id),
                    "promo_type": MarketingBudgetExpectedResult.PromoType.FRIENDS_FOR_MODEL
                },
                {
                    "cost_plan": 3333,
                    "only_fans_model": str(self.only_fans_model.id),
                    "promo_type": MarketingBudgetExpectedResult.PromoType.NEW_DONOR
                }
            ]
        }
        self.client.force_authenticate(user=self.user_hom)
        response = self.client.post(
            reverse('marketing-budget-periods-list'), data=json.dumps(data), content_type='application/json'
        )
        self.assertEqual(response.status_code, 201)

        response = self.client.get(
            reverse('marketing-budget-periods-detail', kwargs={'pk': response.data['id']})
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data['marketing_budget_expected_results']), 1)
        self.assertEqual(
            response.data['marketing_budget_expected_results'][0]['promo_type'],
            MarketingBudgetExpectedResult.PromoType.ALL
        )

        response = self.client.get(
            reverse('marketing-budget-periods-detail', kwargs={'pk': response.data['id']}),
            data={'promo_type': 'friends for model'}
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data['marketing_budget_expected_results']), 1)
        self.assertEqual(
            response.data['marketing_budget_expected_results'][0]['promo_type'],
            MarketingBudgetExpectedResult.PromoType.FRIENDS_FOR_MODEL
        )

        response = self.client.get(
            reverse('marketing-budget-periods-detail', kwargs={'pk': response.data['id']}),
            data={'promo_type': 'new donor'}
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data['marketing_budget_expected_results']), 1)
        self.assertEqual(
            response.data['marketing_budget_expected_results'][0]['promo_type'],
            MarketingBudgetExpectedResult.PromoType.NEW_DONOR
        )
