from collections import defaultdict
from dataclasses import dataclass

from django.utils import timezone

from only_fans_models_plan.models import MarketingBudgetExpectedResult


@dataclass
class ModelOverlimitDTO:
    model_name: str
    value: int
    type: MarketingBudgetExpectedResult.PromoType


@dataclass
class MarketingBudgetAlertDTO:
    period_name: str
    overlimits: list[ModelOverlimitDTO]

    @property
    def tg_message(self) -> str:
        grouped_by_type = defaultdict(list)

        for overlimit in self.overlimits:
            grouped_by_type[overlimit.type].append(overlimit)

        message = (f'{timezone.now().strftime("%d.%m.%Y")}\n'
                   f'<b>{self.period_name}</b>\n\n')

        for pt in MarketingBudgetExpectedResult.PromoType:
            overlimits = grouped_by_type.get(pt)

            if not overlimits:
                continue

            message += f'<b>{pt.capitalize()}</b>\n'

            for item in overlimits:
                message += f'{item.model_name}: {item.value}%\n'

            message += '\n'

        return message
