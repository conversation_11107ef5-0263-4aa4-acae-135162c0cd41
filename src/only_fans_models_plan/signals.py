from django.contrib.auth import get_user_model
from django.db.models.signals import post_delete, post_save
from django.dispatch import receiver

from accounts.models import UserMarketerProxy
from ads.models import Ads
from base.crm_cache.service import RedisCacheService
from only_fans_models.models import OnlyFansModel
from only_fans_models_plan.models import (
    ExpectedResult,
    FactResult,
    MarketingBudgetExpectedResult,
    MarketingBudgetPeriod,
    Period,
)
from only_fans_models_plan.services import MarketingBudgetService
from only_fans_models_plan.tasks import update_specific_plan_fact_results

User = get_user_model()


@receiver([post_save, post_delete], sender=Period)
@receiver([post_save, post_delete], sender=ExpectedResult)
@receiver([post_save, post_delete], sender=OnlyFansModel)
def invalidate_cache_models_plan_period(*args, **kwargs):
    """

    This method, `invalidate_cache_models_plan_period`, is a signal receiver function that is triggered whenever there
    is a `post_save` or `post_delete` event on the `Period` or `ExpectedResult` models.

    Parameters:
    - *args: Variable-length argument list. Used to receive positional arguments passed to the signal receiver function.
    - **kwargs: Arbitrary keyword arguments. Used to receive keyword arguments passed to the signal receiver function.

    Return type:
    - None
    """
    cache_service = RedisCacheService()
    cache_service.delete_keys_with_prefix('models-plan-period')


@receiver([post_save], sender=OnlyFansModel)
def update_fact_results_for_model(instance:  OnlyFansModel, created: bool, **kwargs):
    """
    Update Fact Results for Model

    This method is a signal receiver that is triggered after an instance of the OnlyFansModel model is saved.
    It updates the fact results associated with the model by invoking a specific task for each fact result.
    """
    if not created:
        fact_results_ids = [
            str(fact_result.id)
            for fact_result in FactResult.objects.filter(expected_result__only_fans_model=instance)
        ]

        if not fact_results_ids:
            return

        update_specific_plan_fact_results.delay(fact_results_ids)


# ------------------------------------------Marketing Budget----------------------------------------------
@receiver([post_save, post_delete], sender=Ads)
def update_ads_budget_fact_results(sender, instance, *args, **kwargs):
    if isinstance(instance, Ads):
        number_of_updated_records = MarketingBudgetService.update_budget_fact_results(instance)

        if number_of_updated_records > 0:
            cache_service = RedisCacheService()
            cache_service.delete_keys_with_prefix('marketing-budget-period')


@receiver([post_save, post_delete], sender=MarketingBudgetPeriod)
@receiver([post_save, post_delete], sender=MarketingBudgetExpectedResult)
@receiver([post_save, post_delete], sender=OnlyFansModel)
@receiver([post_save, post_delete], sender=User)
@receiver([post_save, post_delete], sender=UserMarketerProxy)
def invalidate_cache_marketing_budget_period(sender, instance, *args, **kwargs):
    if sender == User and hasattr(instance, 'role') and getattr(instance.role, 'name', None) == 'marketer':
        return

    cache_service = RedisCacheService()
    cache_service.delete_keys_with_prefix('marketing-budget-period')
