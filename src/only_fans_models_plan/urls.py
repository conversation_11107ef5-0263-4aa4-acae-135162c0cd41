from rest_framework import routers

from only_fans_models_plan.views import (
    ExpectedResultViewSet,
    MarketingBudgetExpectedResultViewSet,
    MarketingBudgetPeriodViewSet,
    PeriodViewSet,
)

router = routers.DefaultRouter()
router.register(
    'periods',
    PeriodViewSet,
    basename='periods'
)
router.register(
    'expected-results',
    ExpectedResultViewSet,
    basename='expected-results'
)
router.register(
    'marketing-budget-periods',
    MarketingBudgetPeriodViewSet,
    basename='marketing-budget-periods'
)
router.register(
    'marketing-budget-expected-results',
    MarketingBudgetExpectedResultViewSet,
    basename='marketing-budget-expected-results'
)

urlpatterns = router.urls
