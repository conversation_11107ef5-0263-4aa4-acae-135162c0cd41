from datetime import datetime

from django.conf import settings
from django.utils import timezone

from base.services import TelegramBotService
from core.celery import BaseTaskWithRetry
from core.celery import app as celery_app
from only_fans_db.services import OnlyFansDBSalesSubscribersCollector
from only_fans_models_plan.models import FactResult, MarketingBudgetAlertUser
from only_fans_models_plan.services import FactResultService, MarketingBudgetService


@celery_app.task(bind=BaseTaskWithRetry)
def update_plan_fact_results(self, only_subscribers: bool = False) -> str:
    current_date = timezone.now().date()
    results_list = list(
        FactResult.objects.filter(
            expected_result__period__start_date__lte=current_date,
            expected_result__period__end_date__gte=current_date
        ).select_related('expected_result__only_fans_model')
    )

    if not results_list:
        return 'No results to update'

    models = list(
        set(
            result.expected_result.only_fans_model
            for result in results_list
        )
    )
    start_time = datetime.combine(current_date.replace(day=1), datetime.min.time())
    data_collector = OnlyFansDBSalesSubscribersCollector(
        start_time=start_time,
        shifts_models=models
    )

    if only_subscribers:
        number_of_updated_records = FactResultService.update_list_of_results(
            results_list,
            subscribers_data=data_collector.subscribers_data
        )
    else:
        number_of_updated_records = FactResultService.update_list_of_results(
            results_list,
            sales_data=data_collector.sales_data,
            tingz_sales_data=data_collector.tingz_sales_data,
            subscribers_data=data_collector.subscribers_data
        )

    return f'Updated {number_of_updated_records} of results'


@celery_app.task(bind=BaseTaskWithRetry)
def update_specific_plan_fact_results(self, fact_results_ids: str) -> str:
    results_list = list(
        FactResult.objects.filter(
            id__in=fact_results_ids
        ).select_related('expected_result__only_fans_model',  'expected_result__period')
    )

    if not results_list:
        return "No results to update"
    min_fact_result_date = min(
        fact_result.expected_result.period.start_date
        for fact_result in results_list
    )
    start_time = datetime.combine(min_fact_result_date, datetime.min.time())

    models = list(
        set(
            fact_result.expected_result.only_fans_model
            for fact_result in results_list
        )
    )
    data_collector = OnlyFansDBSalesSubscribersCollector(
        start_time=start_time,
        shifts_models=models
    )

    number_of_updated_records = FactResultService.update_list_of_results(
        results_list,
        sales_data=data_collector.sales_data,
        tingz_sales_data=data_collector.tingz_sales_data,
        subscribers_data=data_collector.subscribers_data
    )

    return f'Updated {number_of_updated_records} of results'


@celery_app.task(bind=BaseTaskWithRetry)
def send_marketing_budget_alerts(self) -> str:
    dtos = MarketingBudgetService.get_alerts()

    if not dtos:
        return 'No alerts to send'

    telegram_ids = MarketingBudgetAlertUser.objects.values_list('telegram_id', flat=True)

    if not telegram_ids:
        return 'No users to send alerts to'

    bot_service = TelegramBotService(bot_token=settings.TRACKER_BOT_TOKEN)

    for telegram_id in telegram_ids:
        for dto in dtos:
            bot_service.send_message(
                chat_id=telegram_id,
                text=dto.tg_message,
                parse_mode='HTML'
            )
