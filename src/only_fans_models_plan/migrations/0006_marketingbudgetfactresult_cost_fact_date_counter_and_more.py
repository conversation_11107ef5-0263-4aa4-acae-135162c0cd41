# Generated by Django 4.2.2 on 2024-12-18 14:21

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "only_fans_models_plan",
            "0005_remove_marketingbudgetexpectedresult_unique_marketing_budget_expected_result_and_more",
        ),
    ]

    operations = [
        migrations.AddField(
            model_name="marketingbudgetfactresult",
            name="cost_fact_date_counter",
            field=models.PositiveIntegerField(
                blank=True,
                default=0,
                help_text="Budget cost fact based on date_counter",
            ),
        ),
        migrations.AlterField(
            model_name="marketingbudgetfactresult",
            name="cost_fact",
            field=models.PositiveIntegerField(
                default=0, help_text="Budget cost fact based on date"
            ),
        ),
    ]
