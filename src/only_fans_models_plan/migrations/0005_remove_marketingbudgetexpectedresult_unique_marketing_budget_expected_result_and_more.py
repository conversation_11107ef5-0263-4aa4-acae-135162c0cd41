# Generated by Django 4.2.2 on 2024-05-02 13:41

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "only_fans_models_plan",
            "0004_expectedresult_expected_subscribers_gg_and_more",
        ),
    ]

    operations = [
        migrations.RemoveConstraint(
            model_name="marketingbudgetexpectedresult",
            name="unique_marketing_budget_expected_result",
        ),
        migrations.AddField(
            model_name="marketingbudgetexpectedresult",
            name="promo_type",
            field=models.CharField(
                choices=[("all", "All"), ("friends for model", "Friends for model")],
                default="all",
                help_text="Promo type",
                max_length=30,
            ),
        ),
        migrations.AddConstraint(
            model_name="marketingbudgetexpectedresult",
            constraint=models.UniqueConstraint(
                fields=("marketing_budget_period", "only_fans_model", "promo_type"),
                name="unique_marketing_budget_expected_result",
            ),
        ),
    ]
