# Generated by Django 4.2.2 on 2023-11-23 09:46

import uuid

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models

import base.models


class Migration(migrations.Migration):

    dependencies = [
        ("only_fans_models", "0009_onlyfansmodel_index_number"),
        ("only_fans_models_plan", "0002_period_name"),
    ]

    operations = [
        migrations.CreateModel(
            name="MarketingBudgetExpectedResult",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "cost_plan",
                    models.PositiveIntegerField(
                        default=0, help_text="Budget cost plan"
                    ),
                ),
            ],
            options={
                "db_table": "marketing_budget_expected_result",
                "ordering": ["only_fans_model"],
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.CreateModel(
            name="MarketingBudgetFactResult",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "cost_fact",
                    models.PositiveIntegerField(
                        default=0, help_text="Budget cost fact"
                    ),
                ),
            ],
            options={
                "db_table": "marketing_budget_fact_result",
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.CreateModel(
            name="MarketingBudgetPeriod",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("start_date", models.DateField(help_text="Start date of the period")),
                ("end_date", models.DateField(help_text="End date of the period")),
                (
                    "name",
                    models.CharField(
                        blank=True,
                        help_text="Name of the period",
                        max_length=255,
                        null=True,
                    ),
                ),
            ],
            options={
                "db_table": "marketing_budget_period",
                "ordering": ["-start_date"],
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.AddConstraint(
            model_name="marketingbudgetperiod",
            constraint=models.UniqueConstraint(
                fields=("start_date", "end_date"), name="marketing_budget_unique_period"
            ),
        ),
        migrations.AddField(
            model_name="marketingbudgetfactresult",
            name="marketing_budget_expected_result",
            field=models.OneToOneField(
                help_text="Budget fact result for budget expected result",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="marketing_budget_fact_result",
                to="only_fans_models_plan.marketingbudgetexpectedresult",
            ),
        ),
        migrations.AddField(
            model_name="marketingbudgetexpectedresult",
            name="marketing_budget_period",
            field=models.ForeignKey(
                help_text="Budget period of budget expected result",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="marketing_budget_expected_results",
                to="only_fans_models_plan.marketingbudgetperiod",
            ),
        ),
        migrations.AddField(
            model_name="marketingbudgetexpectedresult",
            name="only_fans_model",
            field=models.ForeignKey(
                help_text="OnlyFansModel",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="marketing_budget_expected_results",
                to="only_fans_models.onlyfansmodel",
            ),
        ),
        migrations.AddConstraint(
            model_name="marketingbudgetexpectedresult",
            constraint=models.UniqueConstraint(
                fields=("marketing_budget_period", "only_fans_model"),
                name="unique_marketing_budget_expected_result",
            ),
        ),
    ]
