# Generated by Django 4.2.2 on 2023-10-17 09:41

import uuid

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models

import base.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("only_fans_models", "0008_balanceprofit_modelcategory_balance_profit"),
    ]

    operations = [
        migrations.CreateModel(
            name="ExpectedResult",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "model_id",
                    models.IntegerField(blank=True, help_text="OF model id", null=True),
                ),
                (
                    "expected_subscribers",
                    models.PositiveIntegerField(
                        default=0, help_text="Expected subscribers number"
                    ),
                ),
                (
                    "expected_revenue",
                    models.PositiveIntegerField(
                        default=0, help_text="Expected revenue"
                    ),
                ),
            ],
            options={
                "ordering": ["only_fans_model"],
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.CreateModel(
            name="FactResult",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "fact_subscribers",
                    models.PositiveIntegerField(
                        default=0, help_text="Fact subscribers number"
                    ),
                ),
                (
                    "fact_revenue",
                    models.PositiveIntegerField(default=0, help_text="Fact revenue"),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.CreateModel(
            name="Period",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("start_date", models.DateField(help_text="Start date of the period")),
                ("end_date", models.DateField(help_text="End date of the period")),
            ],
            options={
                "ordering": ["-start_date"],
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.AddConstraint(
            model_name="period",
            constraint=models.UniqueConstraint(
                fields=("start_date", "end_date"), name="unique_period"
            ),
        ),
        migrations.AddField(
            model_name="factresult",
            name="expected_result",
            field=models.OneToOneField(
                help_text="Expected result",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="fact_result",
                to="only_fans_models_plan.expectedresult",
            ),
        ),
        migrations.AddField(
            model_name="expectedresult",
            name="only_fans_model",
            field=models.ForeignKey(
                help_text="OnlyFansModel",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="expected_results",
                to="only_fans_models.onlyfansmodel",
            ),
        ),
        migrations.AddField(
            model_name="expectedresult",
            name="period",
            field=models.ForeignKey(
                help_text="Period of expected result",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="expected_results",
                to="only_fans_models_plan.period",
            ),
        ),
        migrations.AddConstraint(
            model_name="expectedresult",
            constraint=models.UniqueConstraint(
                fields=("period", "only_fans_model"), name="unique_expected_result"
            ),
        ),
    ]
