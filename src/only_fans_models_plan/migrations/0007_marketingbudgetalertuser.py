# Generated by Django 4.2.2 on 2025-01-21 17:47

import base.models
from django.db import migrations, models
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    dependencies = [
        (
            "only_fans_models_plan",
            "0006_marketingbudgetfactresult_cost_fact_date_counter_and_more",
        ),
    ]

    operations = [
        migrations.CreateModel(
            name="MarketingBudgetAlertUser",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("telegram_id", models.PositiveIntegerField(unique=True)),
                ("username", models.CharField(max_length=60)),
            ],
            options={
                "db_table": "marketing_budget_alerts",
                "ordering": ["username"],
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
    ]
