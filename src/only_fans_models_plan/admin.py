from django.contrib import admin
from nested_inline.admin import NestedModelAdmin, NestedStackedInline

from only_fans_models_plan.models import (
    ExpectedResult,
    FactResult,
    MarketingBudgetAlertUser,
    MarketingBudgetExpectedResult,
    MarketingBudgetFactResult,
    MarketingBudgetPeriod,
    Period,
)


class FactResultInline(NestedStackedInline):
    model = FactResult
    extra = 1
    fk_name = 'expected_result'


class ExpectedResultInline(NestedStackedInline):
    model = ExpectedResult
    extra = 1
    fk_name = 'period'
    inlines = [FactResultInline]


@admin.register(Period)
class PeriodAdmin(NestedModelAdmin):
    list_display = ['start_date', 'end_date', 'name']
    ordering = ['start_date', 'end_date']
    inlines = [
        ExpectedResultInline,
    ]


# ------------------------------------------Marketing Budget----------------------------------------------
class MarketingBudgetFactResultInline(NestedStackedInline):
    """
    Inline class for BudgetFactResult model
    """
    model = MarketingBudgetFactResult
    extra = 1
    fk_name = 'marketing_budget_expected_result'


class MarketingBudgetExpectedResultInline(NestedStackedInline):
    """
    Inline class for BudgetExpectedResult model
    """
    model = MarketingBudgetExpectedResult
    extra = 1
    fk_name = 'marketing_budget_period'
    inlines = [MarketingBudgetFactResultInline]


@admin.register(MarketingBudgetPeriod)
class BudgetPeriodAdmin(NestedModelAdmin):
    """
    Admin class for managing BudgetPeriod in the admin panel.
    """
    list_display = ['start_date', 'end_date', 'name']
    ordering = ['start_date', 'end_date']
    inlines = [
        MarketingBudgetExpectedResultInline,
    ]


@admin.register(MarketingBudgetAlertUser)
class MarketingBudgetAlertUserAdmin(admin.ModelAdmin):
    list_display = ['telegram_id', 'username']
