from django.db import models
from django.utils.translation import gettext_lazy as _

from base.models import TimeStampedUUIDModel


class Period(TimeStampedUUIDModel):
    """
    Model which defines plans periods
    """
    start_date = models.DateField(
        help_text=_('Start date of the period')
    )
    end_date = models.DateField(
        help_text=_('End date of the period')
    )
    name = models.CharField(
        max_length=255,
        help_text=_('Name of the period'),
        blank=True,
        null=True
    )

    class Meta:
        ordering = ['-start_date']
        constraints = [
            models.UniqueConstraint(fields=['start_date', 'end_date'], name='unique_period')
        ]

    def __str__(self):

        if self.name:
            return f'{self.name}'

        return f'{self.start_date} - {self.end_date}'


class ExpectedResult(TimeStampedUUIDModel):
    """
    Model which defines expected results of plans
    """
    period = models.ForeignKey(
        'only_fans_models_plan.Period',
        on_delete=models.CASCADE,
        related_name='expected_results',
        help_text=_('Period of expected result')
    )
    only_fans_model = models.ForeignKey(
        'only_fans_models.OnlyFansModel',
        on_delete=models.SET_NULL,
        null=True,
        related_name='expected_results',
        help_text=_('OnlyFansModel')
    )
    model_id = models.BigIntegerField(
        null=True,
        blank=True,
        help_text=_('OF model id')
    )
    expected_subscribers = models.PositiveIntegerField(
        default=0,
        help_text=_('Expected subscribers number')
    )
    expected_revenue = models.PositiveIntegerField(
        default=0,
        help_text=_('Expected revenue')
    )
    expected_subscribers_gg = models.PositiveIntegerField(
        default=0,
        help_text=_('Expected gg subscribers number')
    )
    expected_subscribers_oftv = models.PositiveIntegerField(
        default=0,
        help_text=_('Expected oftv subscribers number')
    )

    class Meta:
        ordering = ['only_fans_model']
        constraints = [
            models.UniqueConstraint(fields=['period', 'only_fans_model'], name='unique_expected_result')
        ]

    def __str__(self):
        return f'{self.model_id}: {self.expected_subscribers} - {self.expected_revenue}'

    def save(self, *args, **kwargs):
        """
        Redefine save for storing model_id
        """
        if not self.model_id:
            self.model_id = self.only_fans_model.model_id

        return super().save(*args, **kwargs)


class FactResult(TimeStampedUUIDModel):
    """
    Model which defines fact results of plans
    """
    expected_result = models.OneToOneField(
        'only_fans_models_plan.ExpectedResult',
        on_delete=models.CASCADE,
        related_name='fact_result',
        help_text=_('Expected result')
    )
    fact_subscribers = models.PositiveIntegerField(
        default=0,
        help_text=_('Fact subscribers number')
    )
    fact_revenue = models.PositiveIntegerField(
        default=0,
        help_text=_('Fact revenue')
    )

    def __str__(self):
        return f'{self.fact_subscribers} - {self.fact_revenue}'


# ------------------------------------------Marketing Budget----------------------------------------------
class MarketingBudgetPeriod(TimeStampedUUIDModel):
    """
    MarketingBudgetPeriod model
    """
    start_date = models.DateField(
        help_text=_('Start date of the period')
    )
    end_date = models.DateField(
        help_text=_('End date of the period')
    )
    name = models.CharField(
        max_length=255,
        help_text=_('Name of the period'),
        blank=True,
        null=True
    )

    class Meta:
        ordering = ['-start_date']
        constraints = [
            models.UniqueConstraint(fields=['start_date', 'end_date'], name='marketing_budget_unique_period')
        ]
        db_table = 'marketing_budget_period'

    def __str__(self):

        if self.name:
            return f'{self.name}'

        return f'{self.start_date} - {self.end_date}'


class MarketingBudgetExpectedResult(TimeStampedUUIDModel):
    """
    Model which defines budget expected results for marketing plans
    """
    class PromoType(models.TextChoices):
        """
        Marketing budget expected result type
        """
        ALL = 'all', _('All')
        FRIENDS_FOR_MODEL = 'friends for model', _('Friends for model')
        NEW_DONOR = 'new donor', _('New donor')
        STORIES_FOR_MODELS = 'stories for models', _('Stories for models')

    marketing_budget_period = models.ForeignKey(
        'only_fans_models_plan.MarketingBudgetPeriod',
        on_delete=models.CASCADE,
        related_name='marketing_budget_expected_results',
        help_text=_('Budget period of budget expected result')
    )
    only_fans_model = models.ForeignKey(
        'only_fans_models.OnlyFansModel',
        on_delete=models.SET_NULL,
        null=True,
        related_name='marketing_budget_expected_results',
        help_text=_('OnlyFansModel')
    )
    cost_plan = models.PositiveIntegerField(
        default=0,
        help_text=_('Budget cost plan')
    )
    promo_type = models.CharField(
        max_length=30,
        choices=PromoType.choices,
        default=PromoType.ALL,
        help_text=_('Promo type')
    )

    class Meta:
        ordering = ['only_fans_model']
        constraints = [
            models.UniqueConstraint(
                fields=['marketing_budget_period', 'only_fans_model', 'promo_type'],
                name='unique_marketing_budget_expected_result'
            )
        ]
        db_table = 'marketing_budget_expected_result'

    def __str__(self):
        return f'{self.only_fans_model} - {self.cost_plan}'


class MarketingBudgetFactResult(TimeStampedUUIDModel):
    """
    Model which defines budget fact results for marketing plans
    """
    marketing_budget_expected_result = models.OneToOneField(
        'only_fans_models_plan.MarketingBudgetExpectedResult',
        on_delete=models.CASCADE,
        related_name='marketing_budget_fact_result',
        help_text=_('Budget fact result for budget expected result'),
        unique=True
    )
    cost_fact = models.PositiveIntegerField(
        default=0,
        help_text=_('Budget cost fact based on date')
    )
    cost_fact_date_counter = models.PositiveIntegerField(
        default=0,
        help_text=_('Budget cost fact based on date_counter'),
        blank=True,
    )

    class Meta:
        db_table = 'marketing_budget_fact_result'

    def __str__(self):
        return f'{self.cost_fact}'


class MarketingBudgetAlertUser(TimeStampedUUIDModel):
    telegram_id = models.PositiveIntegerField(unique=True)
    username = models.CharField(max_length=60)

    class Meta:
        db_table = 'marketing_budget_alerts'
        ordering = ['username']

    def __str__(self):
        return f'{self.telegram_id} {self.username}'
