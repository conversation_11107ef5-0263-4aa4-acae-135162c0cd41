from datetime import datetime
from enum import Enum

from django.db import transaction
from django.db.models import (
    F,
    Q,
    Sum,
)
from django.db.models.functions import Coalesce
from django.utils import timezone

from ads.models import Ads
from base.crm_cache.service import RedisCacheService
from only_fans_db.services import OnlyFansDBSalesSubscribersCollector
from only_fans_models_plan.dtos import MarketingBudgetAlertDTO, ModelOverlimitDTO
from only_fans_models_plan.models import (
    ExpectedResult,
    FactResult,
    MarketingBudgetExpectedResult,
    MarketingBudgetFactResult,
    MarketingBudgetPeriod,
    Period,
)


class FactResultService:
    """
    Ads service
    """

    MODEL = FactResult

    def __init__(self, instance: MODEL) -> None:
        self.instance = instance

    @staticmethod
    def calculate_fact_revenue(
        model_id: int,
        start_time: datetime,
        end_time: datetime,
        sales_data: dict[int, list[dict]] = None,
        tingz_sales_data: dict[int, list[dict]] = None,
    ) -> int:
        """
        Calculate fact revenue.

        Args:
            model_id: int,
            start_time: datetime,
            end_time: datetime,
            sales_data: list[dict]
            tingz_sales_data: list[dict]
        """
        sum_of_sales = (
            sum(
                sale.get('amount', 0)
                for sale in sales_data.get(model_id, [])
                if sale.get('trans_date')
                and start_time <= sale.get('trans_date') <= end_time
            )
            if sales_data
            else 0
        )

        sum_of_tingz_sales = (
            sum(
                tingz_sales.get('amount', 0)
                for tingz_sales in tingz_sales_data.get(model_id, [])
                if tingz_sales.get('created_at')
                and start_time <= tingz_sales.get('created_at') <= end_time
            )
            if tingz_sales_data
            else 0
        )

        return round(sum_of_sales + sum_of_tingz_sales)

    @staticmethod
    def calculate_fact_subscribers(
        model_id: int,
        start_time: datetime,
        end_time: datetime,
        subscribers_data: dict[int, list[dict]] = None,
    ) -> int:
        """
        Calculate fact subscribers.

        Args:
            model_id: int,
            start_time: datetime,
            end_time: datetime,
            subscribers_data: list[dict]
        """

        return (
            sum(
                data.get('all_subs', 0)
                for data in subscribers_data.get(model_id, [])
                if data.get('insert_date')
                and start_time <= data.get('insert_date') <= end_time
            )
            if subscribers_data
            else 0
        )

    @classmethod
    def update_list_of_results(
        cls,
        results_list: list[MODEL],
        sales_data: dict[int, list[dict]] = None,
        tingz_sales_data: dict[int, list[dict]] = None,
        subscribers_data: dict[int, list[dict]] = None,
    ) -> int:
        """
        Update list of fact results.

        Args:
            results_list: list[FactResult]
            sales_data: list[dict] = None,
            tingz_sales_data: list[dict] = None,
            subscribers_data: list[dict] = None

        Returns:
            number of updated records
        """
        update_list = []

        for result in results_list:
            updated = False
            model_id = result.expected_result.only_fans_model.model_id
            start_time = datetime.combine(
                result.expected_result.period.start_date, datetime.min.time()
            )
            end_time = datetime.combine(
                result.expected_result.period.end_date, datetime.max.time()
            )

            fact_revenue = cls.calculate_fact_revenue(
                sales_data=sales_data,
                tingz_sales_data=tingz_sales_data,
                model_id=model_id,
                start_time=start_time,
                end_time=end_time,
            )

            if result.fact_revenue != fact_revenue:
                updated = True
                result.fact_revenue = fact_revenue

            fact_subscribers = cls.calculate_fact_subscribers(
                subscribers_data=subscribers_data,
                model_id=model_id,
                start_time=start_time,
                end_time=end_time,
            )

            if result.fact_subscribers != fact_subscribers:
                updated = True
                result.fact_subscribers = fact_subscribers

            if updated:
                update_list.append(result)

        if update_list:
            cache_service = RedisCacheService()
            cache_service.delete_keys_with_prefix('models-plan-period')

            return cls.MODEL.objects.bulk_update(
                update_list, fields=["fact_subscribers", "fact_revenue"]
            )

        return 0


class PeriodService:
    """
    Period service
    """

    MODEL = Period

    def __init__(self, instance: MODEL) -> None:
        self.instance = instance

    @classmethod
    def create(cls, data: dict) -> MODEL:
        """
        Create period the new period

        Args:
            data: serializer validated data
        """
        fact_results = []

        with transaction.atomic():

            expected_results = data.pop('expected_results', None)
            period = cls.MODEL.objects.create(**data)

            if expected_results:
                for expected_result in expected_results:
                    expected_result['period'] = period
                    new_expected_result = ExpectedResult.objects.create(
                        **expected_result
                    )
                    fact_results.append(
                        FactResult.objects.create(expected_result=new_expected_result)
                    )

            if fact_results:
                models = list(
                    set(
                        fact_result.expected_result.only_fans_model
                        for fact_result in fact_results
                    )
                )
                start_time = datetime.combine(data['start_date'], datetime.min.time())
                end_time = datetime.combine(data['end_date'], datetime.max.time())

                data_collector = OnlyFansDBSalesSubscribersCollector(
                    start_time=start_time, end_time=end_time, shifts_models=models
                )
                FactResultService.update_list_of_results(
                    results_list=fact_results,
                    sales_data=data_collector.sales_data,
                    tingz_sales_data=data_collector.tingz_sales_data,
                    subscribers_data=data_collector.subscribers_data,
                )

            return period

    def update_expected_result(
        self, expected_result_data: dict, expected_result_instance: ExpectedResult
    ) -> None:
        """
        Update single expected result
        """
        is_updated = False

        for key, value in expected_result_data.items():

            if getattr(expected_result_instance, key) != value:
                setattr(expected_result_instance, key, value)
                is_updated = True

        if is_updated:
            expected_result_instance.save()

    def update(self, data: dict) -> MODEL:
        """
        Update period

        Args:
            data: serializer validated data
        """
        with transaction.atomic():
            updated = False

            expected_results = data.pop('expected_results', [])

            for key, value in data.items():
                if getattr(self.instance, key) != value:
                    setattr(self.instance, key, value)
                    updated = True

            if updated:
                self.instance.save()

            if expected_results:
                instance_expected_results = list(self.instance.expected_results.all())

                for expected_result_data in expected_results:
                    appropriate_instance_expected_result = list(
                        filter(
                            (
                                lambda instance_expected_result: expected_result_data.get(
                                    'only_fans_model'
                                )
                                == instance_expected_result.only_fans_model
                            ),
                            instance_expected_results,
                        )
                    )

                    if appropriate_instance_expected_result:
                        appropriate_instance_expected_result = (
                            appropriate_instance_expected_result[0]
                        )
                        instance_expected_results.remove(
                            appropriate_instance_expected_result
                        )
                        self.update_expected_result(
                            expected_result_data=expected_result_data,
                            expected_result_instance=appropriate_instance_expected_result,
                        )
                    else:
                        expected_result_data['period'] = self.instance
                        new_expected_result = ExpectedResult.objects.create(
                            **expected_result_data
                        )
                        FactResult.objects.create(expected_result=new_expected_result)

                if instance_expected_results:
                    self.instance.expected_results.filter(
                        id__in=[result.id for result in instance_expected_results]
                    ).delete()

                self.instance.refresh_from_db()
                start_time = datetime.combine(
                    self.instance.start_date, datetime.min.time()
                )
                end_time = datetime.combine(self.instance.end_date, datetime.max.time())
                refreshed_expected_results = list(
                    self.instance.expected_results.select_related('only_fans_model')
                )
                models = [
                    expected_result.only_fans_model
                    for expected_result in refreshed_expected_results
                ]
                fact_results = [
                    result.fact_result for result in refreshed_expected_results
                ]

                data_collector = OnlyFansDBSalesSubscribersCollector(
                    start_time=start_time, end_time=end_time, shifts_models=models
                )
                FactResultService.update_list_of_results(
                    results_list=fact_results,
                    sales_data=data_collector.sales_data,
                    tingz_sales_data=data_collector.tingz_sales_data,
                    subscribers_data=data_collector.subscribers_data,
                )

                if instance_expected_results:
                    self.instance.expected_results.filter(
                        id__in=[result.id for result in instance_expected_results]
                    ).delete()

            return self.instance


class ExpectedResultService:
    """
    Expected result service
    """

    MODEL = ExpectedResult

    def __init__(self, instance: MODEL) -> None:
        self.instance = instance

    @classmethod
    def create(cls, data: dict) -> MODEL:
        """
        Create expected result with fact result and collecting statistics
        """
        with transaction.atomic():
            created_expected_result = cls.MODEL.objects.create(**data)
            created_fact_result = FactResult.objects.create(
                expected_result=created_expected_result
            )

            period = data["period"]
            model = data["only_fans_model"]

            start_time = datetime.combine(period.start_date, datetime.min.time())
            end_time = datetime.combine(period.end_date, datetime.max.time())

            data_collector = OnlyFansDBSalesSubscribersCollector(
                start_time=start_time, end_time=end_time, shifts_models=[model]
            )
            FactResultService.update_list_of_results(
                results_list=[created_fact_result],
                sales_data=data_collector.sales_data,
                tingz_sales_data=data_collector.tingz_sales_data,
                subscribers_data=data_collector.subscribers_data,
            )

            return created_expected_result


# ------------------------------------------Marketing Budget----------------------------------------------
class MarketingBudgetService:
    """
    Service class for budget related operations
    """

    class DateFilterValue(str, Enum):
        """
        Enum for date filter values
        """

        DATE = "date"
        DATE_COUNTER = "date_counter"

    @classmethod
    def calculate_cost_for_budget_fact_result(
        cls,
        budget_fact_result: MarketingBudgetFactResult,
        date_filter_value: DateFilterValue = DateFilterValue.DATE,
    ) -> int:
        """
        Calculate the cost for a given BudgetFactResult instance.

        Args:
            budget_fact_result (BudgetFactResult): The BudgetFactResult instance to calculate the cost for.
            date_filter_value (DateFilterValue, optional): The date filter value to use. Defaults to DateFilterValue.DATE.

        Returns:
            float: The calculated result_cost.
        """
        date_filter = {
            f"{date_filter_value}__gte": budget_fact_result.marketing_budget_expected_result.marketing_budget_period.start_date,
            f"{date_filter_value}__lte": budget_fact_result.marketing_budget_expected_result.marketing_budget_period.end_date,
        }

        queryset = Ads.objects.filter(
            only_fans_model=budget_fact_result.marketing_budget_expected_result.only_fans_model,
            department=Ads.DepartmentChoices.MARKETING,
            **date_filter,
        )

        expected_result_promo_type = budget_fact_result.marketing_budget_expected_result.promo_type

        if expected_result_promo_type == MarketingBudgetExpectedResult.PromoType.NEW_DONOR:
            queryset = queryset.filter(promo__new_donor_budget=True)
        elif expected_result_promo_type == MarketingBudgetExpectedResult.PromoType.FRIENDS_FOR_MODEL:
            queryset = queryset.filter(
                Q(promo__name__iexact=expected_result_promo_type) | Q(promo__name__iexact='new donor friends')
            )
        elif expected_result_promo_type == MarketingBudgetExpectedResult.PromoType.STORIES_FOR_MODELS:
            queryset = queryset.filter(
                Q(promo__name__iexact=expected_result_promo_type) | Q(promo__name__iexact='new donor stories')
            )
        elif expected_result_promo_type != MarketingBudgetExpectedResult.PromoType.ALL:
            queryset = queryset.filter(promo__name__iexact=expected_result_promo_type)

        total_cost = queryset.aggregate(
            total=Sum(Coalesce('phantom_cost', 'cost_result'))
        )['total']

        return round(total_cost) if total_cost else 0

    @classmethod
    def update_budget_fact_results(cls, ads: Ads) -> int:
        """
        Update budget fact results with the given Ads instance.

        Args:
            ads (Ads): The Ads instance to update.

        Returns:
            int: The number of rows affected by the update.
        """
        data_to_update = []

        if not all([ads.cost_result, ads.only_fans_model, ads.date]):

            return len(data_to_update)

        previous_date = getattr(ads, '__original_date', None) or ads.date
        previous_only_fans_model = (
            getattr(ads, '__original_only_fans_model', None) or ads.only_fans_model
        )

        budget_fact_results = list(
            MarketingBudgetFactResult.objects.filter(
                Q(
                    marketing_budget_expected_result__marketing_budget_period__start_date__lte=ads.date,
                    marketing_budget_expected_result__marketing_budget_period__end_date__gte=ads.date,
                    marketing_budget_expected_result__only_fans_model=ads.only_fans_model,
                )
                | Q(
                    marketing_budget_expected_result__marketing_budget_period__start_date__lte=previous_date,
                    marketing_budget_expected_result__marketing_budget_period__end_date__gte=previous_date,
                    marketing_budget_expected_result__only_fans_model=previous_only_fans_model,
                )
            )
        )

        if not budget_fact_results:

            return len(data_to_update)

        for budget_fact_result in budget_fact_results:
            budget_fact_result.cost_fact = cls.calculate_cost_for_budget_fact_result(
                budget_fact_result, cls.DateFilterValue.DATE
            )
            budget_fact_result.cost_fact_date_counter = (
                cls.calculate_cost_for_budget_fact_result(
                    budget_fact_result, cls.DateFilterValue.DATE_COUNTER
                )
            )
            data_to_update.append(budget_fact_result)

        return MarketingBudgetFactResult.objects.bulk_update(
            data_to_update, fields=['cost_fact', 'cost_fact_date_counter']
        )

    @staticmethod
    def get_alerts() -> list[MarketingBudgetAlertDTO]:
        """
        Retrieve marketing budget alerts for periods where models exceed their planned costs.
        Returns a list of MarketingBudgetAlertDTO containing overbudget information.
        """
        current_date = timezone.now().date().replace(day=1)

        periods = MarketingBudgetPeriod.objects.filter(
            start_date__gte=current_date
        ).order_by('start_date')

        alert_dtos = []
        for period in periods:
            overlimit_results = (
                period.marketing_budget_expected_results.filter(
                    marketing_budget_fact_result__cost_fact__gt=F('cost_plan')
                )
                .select_related('only_fans_model', 'marketing_budget_fact_result')
                .order_by('only_fans_model__nickname')
            )

            if overlimit_results:
                alert_dtos.append(
                    MarketingBudgetAlertDTO(
                        period_name=period.name,
                        overlimits=[
                            ModelOverlimitDTO(
                                model_name=result.only_fans_model.nickname,
                                value=round(
                                    result.marketing_budget_fact_result.cost_fact
                                    * 100
                                    / result.cost_plan
                                ),
                                type=result.promo_type,
                            )
                            for result in overlimit_results
                            if round(
                                result.marketing_budget_fact_result.cost_fact
                                * 100
                                / result.cost_plan
                            )
                            >= 110
                        ],
                    )
                )

        return alert_dtos


class MarketingBudgetExpectedResultService(MarketingBudgetService):
    """
    Service class for budget expected result related operations
    """

    @classmethod
    def create(cls, data: dict) -> MarketingBudgetExpectedResult:
        """
        Create BudgetExpectedResult

        Args:
            data: serializer validated data
        """
        with transaction.atomic():
            new_expected_result = MarketingBudgetExpectedResult.objects.create(**data)
            new_fact_result = MarketingBudgetFactResult(
                marketing_budget_expected_result=new_expected_result
            )
            new_fact_result.cost_fact = cls.calculate_cost_for_budget_fact_result(
                new_fact_result, cls.DateFilterValue.DATE
            )
            new_fact_result.cost_fact_date_counter = (
                cls.calculate_cost_for_budget_fact_result(
                    new_fact_result, cls.DateFilterValue.DATE_COUNTER
                )
            )
            new_fact_result.save()

        return new_expected_result


class MarketingBudgetPeriodService(MarketingBudgetService):
    """
    Service class for budget period related operations
    """

    def __init__(self, instance: MarketingBudgetPeriod) -> None:
        self.instance = instance

    @classmethod
    def create(cls, data: dict) -> MarketingBudgetPeriod:
        """
        Create BudgetPeriod

        Args:
            data: serializer validated data
        """
        with transaction.atomic():

            marketing_budget_expected_results = data.pop(
                'marketing_budget_expected_results', None
            )
            marketing_budget_period = MarketingBudgetPeriod.objects.create(**data)

            if not marketing_budget_expected_results:
                return marketing_budget_period

            for expected_result_data in marketing_budget_expected_results:
                expected_result_data[
                    'marketing_budget_period'
                ] = marketing_budget_period
                MarketingBudgetExpectedResultService.create(expected_result_data)

            return marketing_budget_period

    def update_budget_expected_result(
        self,
        budget_expected_result_data: dict,
        budget_expected_result_instance: MarketingBudgetExpectedResult,
    ) -> None:
        """
        Update single BudgetExpectedResult
        """
        is_updated = False

        for key, value in budget_expected_result_data.items():

            if getattr(budget_expected_result_instance, key) != value:
                setattr(budget_expected_result_instance, key, value)
                is_updated = True

        if is_updated:
            budget_expected_result_instance.save()

    def update(self, data: dict) -> MarketingBudgetPeriod:
        """
        Update BudgetPeriod

        Args:
            data: serializer validated data
        """
        with transaction.atomic():
            updated = False

            budget_expected_results = data.pop('marketing_budget_expected_results', [])
            promo_type = data.pop(
                'promo_type', MarketingBudgetExpectedResult.PromoType.ALL
            )

            for key, value in data.items():
                if getattr(self.instance, key) != value:
                    setattr(self.instance, key, value)
                    updated = True

            if updated:
                self.instance.save()

            if not budget_expected_results:
                return self.instance

            instance_budget_expected_results = list(
                self.instance.marketing_budget_expected_results.filter(
                    promo_type=promo_type
                )
            )

            for budget_expected_result_data in budget_expected_results:
                appropriate_instance_budget_expected_result = list(
                    filter(
                        (
                            lambda instance_expected_result: budget_expected_result_data.get(
                                'only_fans_model'
                            )
                            == instance_expected_result.only_fans_model
                        ),
                        instance_budget_expected_results,
                    )
                )

                if appropriate_instance_budget_expected_result:
                    appropriate_instance_budget_expected_result = (
                        appropriate_instance_budget_expected_result[0]
                    )
                    instance_budget_expected_results.remove(
                        appropriate_instance_budget_expected_result
                    )
                    self.update_budget_expected_result(
                        budget_expected_result_data=budget_expected_result_data,
                        budget_expected_result_instance=appropriate_instance_budget_expected_result,
                    )

                else:
                    budget_expected_result_data[
                        'marketing_budget_period'
                    ] = self.instance
                    MarketingBudgetExpectedResultService.create(
                        budget_expected_result_data
                    )

            if instance_budget_expected_results:
                self.instance.marketing_budget_expected_results.filter(
                    id__in=[result.id for result in instance_budget_expected_results]
                ).delete()

            return self.instance

    def get_budget_period_detail_string(self, date_filter: str) -> str:
        """
        Get string representation of marketing budget period detail page

        Args:
            date_filter (str): date_filter value "date" or "date_counter"
        """
        headers = ['Marketer', 'Model', 'Plan', 'Fact', 'Percent']
        string = '\t'.join(headers) + '\n'

        for expected_result in self.instance.marketing_budget_expected_results.all():
            expected_result: MarketingBudgetExpectedResult
            marketer = expected_result.only_fans_model.marketer
            cost_fact = (
                expected_result.marketing_budget_fact_result.cost_fact_date_counter
                if date_filter == 'date_counter'
                else expected_result.marketing_budget_fact_result.cost_fact
            )

            string += (
                '\t'.join(
                    [
                        marketer.full_name if marketer else '',
                        expected_result.only_fans_model.nickname,
                        str(expected_result.cost_plan),
                        str(cost_fact),
                        str(round(cost_fact / expected_result.cost_plan * 100)),
                    ]
                )
                + '\n'
            )

        return string
