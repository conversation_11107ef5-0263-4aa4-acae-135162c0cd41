from enum import Enum
from typing import Any


class EventTypeEnum(Enum):
    """
    Enum for event types.
    """
    CREATED = 'created'
    UPDATED = 'updated'
    DELETED = 'deleted'
    STARTED = 'started'
    ENDED = 'ended'


class Event:
    """
    Helper class for creating event data.
    """

    def __init__(self, event_type: EventTypeEnum, data: dict[str, Any]):
        self.event_type = event_type
        self.data = data

    def get_event_data(self) -> dict:
        """
        Get the event data as a dictionary.

        Returns:
            dict: The event data.
        """
        event_data = {
            'event_type': self.event_type.value,
            'data': self.data
        }
        return event_data

    def get_users_ids(self) -> list:
        """
        Get the users_ids from the event data.

        Returns:
            str: The shift ID.
        """
        return self.data.get('users_ids')


class ShiftEvent(Event):
    def get_event_data(self) -> dict:
        """
        Get the event data as a dictionary.

        Returns:
            dict: The event data.
        """
        event_data = {
            'event_type': self.event_type.value,
            'shift_data': self.data
        }
        return event_data

    def get_shift_id(self) -> str:
        """
        Get the shift ID from the event data.

        Returns:
            str: The shift ID.
        """
        return self.data.get('id')


class ShiftEventIterator:
    """
    Iterator class for generating event creators for shifts.
    """

    def __init__(self, event_type: EventTypeEnum, list_data: list, ):
        self.list_data = list_data
        self.event_type = event_type

    def __iter__(self):
        """
        Iterate over the list data and yield event creators.

        Yields:
            EventCreator: An instance of EventCreator with each data item.
        """
        for data in self.list_data:
            yield ShiftEvent(self.event_type, data)
