from typing import Dict, List

from starlette.websockets import WebSocket, WebSocketState


class ConnectionManager:
    """
    Manages WebSocket connections and facilitates communication with connected clients.
    """

    def __init__(self):
        """
        Initializes a new instance of the ConnectionManager class.
        """
        self.active_connections: Dict[str, list[WebSocket]] = {}

    async def connect(self, websocket: WebSocket, user_id: str) -> None:
        """
        Accepts a WebSocket connection and adds it to the active connections for the specified user.

        Args:
            websocket (WebSocket): The WebSocket object representing the connection.
            user_id (str): The ID of the user associated with the connection.

        Returns:
            None
        """
        await websocket.accept()

        list_user_connections = self.active_connections.get(user_id)
        if not list_user_connections:
            self.active_connections[user_id] = [websocket, ]
        else:
            list_user_connections.append(websocket)
            self.active_connections[user_id] = list_user_connections

    def disconnect(self, websocket: WebSocket) -> None:
        """
        Disconnects the specified WebSocket connection and removes it from the active connections.

        Args:
            websocket (WebSocket): The WebSocket object representing the connection.

        Returns:
            None
        """
        user_id = websocket.cookies.get('user')
        list_user_connections = self.active_connections.get(user_id)
        try:
            list_user_connections.remove(websocket)
            self.active_connections[user_id] = list_user_connections
        except ValueError as e:
            print(e)

    async def send_event_data_for_single_user(self, connection, event_data):
        try:
            await connection.send_json(event_data)
        except RuntimeError:
            self.disconnect(connection)

    async def send_events(
            self,
            event_data,
            users_ids: List[str],
            socket_name: str | None = None
    ) -> None:
        """
        Sends the specified event data to the connected users with the given user IDs.

        Args:
            event_data: The data to be sent as an event.
            users_ids (List[str]): The list of user IDs to send the event to.
            socket_name (str | None): The name of the socket to send the event to.

        Returns:
            None
        """
        for user_id in users_ids:
            list_user_connections = self.active_connections.get(user_id)

            if not list_user_connections:
                continue

            for websocket in list_user_connections:
                if socket_name:
                    if socket_route := websocket.scope.get("route"):
                        if socket_name != socket_route.name:
                            continue

                if websocket.application_state == WebSocketState.DISCONNECTED:
                    continue

                await self.send_event_data_for_single_user(websocket, event_data)
