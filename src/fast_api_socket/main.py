import asyncio
import logging
import os
import time

from fastapi import FastAP<PERSON>, WebSocket
from fastapi.responses import HTMLResponse
from starlette.middleware.cors import CORSMiddleware
from starlette.websockets import WebSocketDisconnect

from base.crm_cache.service import RedisCacheService
from fast_api_socket.connection_service import ConnectionManager
from fast_api_socket.schemas import (
    PayoutReview,
    RequestCashflow,
    RequestPayment,
    ShiftEventData,
    ShiftUpdatedData,
)
from fast_api_socket.services import (
    Event,
    EventTypeEnum,
    ShiftEvent,
    ShiftEventIterator,
)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

app = FastAPI()
origins = os.environ.get('CORS_ALLOWED_ORIGINS').split(',') if os.environ.get('CORS_ALLOWED_ORIGINS') else []
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

connection_manager = ConnectionManager()


@app.get("/events_test/")
async def get():
    template_path = os.path.join(os.path.dirname(__file__), 'templates', 'test_template.html')
    with open(template_path, 'r') as file:
        html_content = file.read()

    return HTMLResponse(html_content)


async def handle_websocket(websocket: WebSocket):
    user_id = websocket.cookies.get('user')
    origin = websocket.headers.get('Origin')

    if not user_id:
        logging.warning("Missing user cookie in WebSocket connection.")
        return await websocket.close(code=4001)

    if not origin or origin not in origins:
        logging.warning(f"Invalid or missing origin: {origin}")
        return await websocket.close(code=4003)

    await connection_manager.connect(
        websocket=websocket,
        user_id=user_id
    )

    try:
        while True:
            await websocket.receive_text()
    except WebSocketDisconnect:
        connection_manager.disconnect(websocket)
    except Exception as e:
        logging.error(f"WebSocket connection error: {e}")
        connection_manager.disconnect(websocket)


@app.post("/shifts/created/")
async def event_created(data: ShiftEventData):
    """
    Handles an HTTP POST request for created shift.
    Sends an event on a socket

    Args:
        data (ShiftEventData): The ShiftEventData object containing the deleted shift data.

    Returns:
        dict: A dictionary indicating the success of the operation.
    """
    event_data = ShiftEvent(event_type=EventTypeEnum.CREATED, data=data.data).get_event_data()
    await connection_manager.send_events(
        event_data=event_data,
        users_ids=data.users_ids,
        socket_name='shifts_events'
    )

    return {'message': 'created data send'}


@app.post("/shifts/started/")
async def event_started(data: ShiftEventData):
    """
    Handles an HTTP POST request for started shift.
    Sends an event on a socket

    Args:
        data (ShiftEventData): The ShiftEventData object containing the deleted shift data.

    Returns:
        dict: A dictionary indicating the success of the operation.
    """
    event_data = ShiftEvent(event_type=EventTypeEnum.STARTED, data=data.data).get_event_data()
    event_data.update(shift_id=data.id)
    await connection_manager.send_events(
        event_data=event_data,
        users_ids=data.users_ids,
        socket_name='shifts_events'
    )

    return {'message': 'started data send'}


@app.post("/shifts/ended/")
async def event_ended(data: ShiftEventData):
    """
    Handles an HTTP POST request for ended shift.
    Sends an event on a socket

    Args:
        data (ShiftEventData): The ShiftEventData object containing the deleted shift data.

    Returns:
        dict: A dictionary indicating the success of the operation.
    """
    event_data = ShiftEvent(event_type=EventTypeEnum.ENDED, data=data.data).get_event_data()
    event_data.update(shift_id=data.id)
    await connection_manager.send_events(
        event_data=event_data,
        users_ids=data.users_ids,
        socket_name='shifts_events'
    )

    return {'message': 'moved data send'}


@app.post("/shifts/deleted/")
async def event_deleted(data: ShiftEventData):
    """
    Handles an HTTP POST request for deleted shift.
    Sends an event on a socket

    Args:
        data (ShiftEventData): The ShiftEventData object containing the deleted shift data.

    Returns:
        dict: A dictionary indicating the success of the operation.
    """
    event_data = ShiftEvent(event_type=EventTypeEnum.DELETED, data=data.data).get_event_data()
    event_data.update(shift_id=data.id)
    await connection_manager.send_events(
        event_data=event_data,
        users_ids=data.users_ids,
        socket_name='shifts_events'
    )

    return {'message': 'deleted data send'}


@app.post("/shifts/updated/")
async def event_updated(data: ShiftUpdatedData):
    """
    Handles the HTTP POST request for updated shift data.
    Sends an event on a socket.

    Args:
        data (ShiftUpdatedData): The ShiftUpdatedData object containing the updated shift data.

    Returns:
        dict: A dictionary indicating the success of the operation.
    """
    logger = logging.getLogger("shifts_updated_endpoint")
    logger.info("Processing shifts_updated_endpoint...")

    start_setting_iterator = time.perf_counter()
    event_iterator = ShiftEventIterator(event_type=EventTypeEnum.UPDATED, list_data=data.data)
    logger.info(
        f'Time spent for setting iterator: {time.perf_counter() - start_setting_iterator}'
    )

    start_setting_cache = time.perf_counter()
    cache_service = RedisCacheService(db_number=7, cache_ttl=60)
    logger.info(
        f'Time spent for setting cache: {time.perf_counter() - start_setting_cache}'
    )

    start_generating_task = time.perf_counter()
    tasks = []
    for event in event_iterator:
        shift_id = event.get_shift_id()
        event_data = event.get_event_data()
        cached_data = cache_service.get_cache_data(key=shift_id)

        if cached_data == event_data:
            continue

        cache_service.set_cache_data(key=shift_id, data=event_data)

        tasks.append(
            connection_manager.send_events(
                event_data=event_data,
                users_ids=event.get_users_ids(),
                socket_name='shifts_events'
            )
        )
    logger.info(
        f'Time spent for generating tasks: {time.perf_counter() - start_generating_task}'
    )

    start_sending_task = time.perf_counter()
    await asyncio.gather(*tasks)
    logger.info(
        f'Time spent for sending tasks: {time.perf_counter() - start_sending_task}'
    )

    return {
        'message': 'updated data sent',
    }


@app.websocket("/ws/shifts_events/")
async def shifts_events(websocket: WebSocket):
    """
    WebSocket endpoint that handles incoming WebSocket connections for a specific user for shifts.

    Args:
        websocket (WebSocket): The WebSocket object representing the connection.

    Returns:
        None
    """
    await handle_websocket(websocket)


@app.websocket("/ws/requests-payments/")
async def request_payments_socket(websocket: WebSocket):
    """
    WebSocket endpoint that handles incoming WebSocket connections for a specific user for requests payments.

    Args:
        websocket (WebSocket): The WebSocket object representing the connection.

    Returns:
        None
    """
    await handle_websocket(websocket)


@app.post("/requests-payments/created/")
async def request_payment_created(request_payment: RequestPayment):
    """
    Handles an HTTP POST request for created request payment.
    Sends an event on a socket

    Args:
        request_payment (RequestPayment): The RequestPayment object containing the created request payment data.

    Returns:
        dict: A dictionary indicating the success of the operation.
    """
    event_data = Event(event_type=EventTypeEnum.CREATED, data=request_payment.data).get_event_data()
    await connection_manager.send_events(
        event_data=event_data,
        users_ids=request_payment.users_ids,
        socket_name='request_payments_socket'
    )

    return {'message': 'request payment created sent'}


@app.post("/requests-payments/updated/")
async def request_payment_updated(request_payment: RequestPayment):
    """
    Handles an HTTP POST request for updated request payment.
    Sends an event on a socket

    Args:
        request_payment (RequestPayment): The RequestPayment object containing the updated request payment data.

    Returns:
        dict: A dictionary indicating the success of the operation.
    """
    event_data = Event(event_type=EventTypeEnum.UPDATED, data=request_payment.data).get_event_data()
    await connection_manager.send_events(
        event_data=event_data,
        users_ids=request_payment.users_ids,
        socket_name='request_payments_socket'
    )

    return {'message': 'request payment updated sent'}


@app.websocket("/ws/payout-reviews/")
async def payout_reviews_socket(websocket: WebSocket):
    """
    WebSocket endpoint that handles incoming WebSocket connections for a specific user for payout review.

    Args:
        websocket (WebSocket): The WebSocket object representing the connection.

    Returns:
        None
    """
    await handle_websocket(websocket)


@app.post("/payout-reviews/created/")
async def payout_review_created(payout_review: PayoutReview):
    """
    Handles an HTTP POST request for created payout review.
    Sends an event on a socket

    Args:
        payout_review (PayoutReview): The RequestPayment object containing the created payout review data.

    Returns:
        dict: A dictionary indicating the success of the operation.
    """
    event_data = Event(event_type=EventTypeEnum.CREATED, data=payout_review.data).get_event_data()
    await connection_manager.send_events(
        event_data=event_data,
        users_ids=payout_review.users_ids,
        socket_name='payout_reviews_socket'
    )

    return {'message': 'payout review created sent'}


@app.post("/payout-reviews/updated/")
async def payout_review_updated(payout_review: PayoutReview):
    """
    Handles an HTTP POST request for updated payout review.
    Sends an event on a socket

    Args:
        payout_review (PayoutReview): The RequestPayment object containing the updated payout review data.

    Returns:
        dict: A dictionary indicating the success of the operation.
    """
    event_data = Event(event_type=EventTypeEnum.UPDATED, data=payout_review.data).get_event_data()
    await connection_manager.send_events(
        event_data=event_data,
        users_ids=payout_review.users_ids,
        socket_name='payout_reviews_socket'
    )

    return {'message': 'payout review updated sent'}


@app.post("/payout-reviews/deleted/")
async def payout_review_deleted(payout_review: PayoutReview):
    """
    Handles an HTTP POST request for deleted payout review.
    Sends an event on a socket

    Args:
        payout_review (PayoutReview): The PayoutReview object containing the deleted payout review data.

    Returns:
        dict: A dictionary indicating the success of the operation.
    """
    event_data = Event(event_type=EventTypeEnum.DELETED, data=payout_review.data).get_event_data()
    await connection_manager.send_events(
        event_data=event_data,
        users_ids=payout_review.users_ids,
        socket_name='payout_reviews_socket'
    )

    return {'message': 'payout review deleted send'}


@app.websocket("/ws/request-cashflows/")
async def request_cashflows_socket(websocket: WebSocket):
    """
    WebSocket endpoint that handles incoming WebSocket connections for a specific user for request cashflows.

    Args:
        websocket (WebSocket): The WebSocket object representing the connection.

    Returns:
        None
    """
    await handle_websocket(websocket)


@app.post("/request-cashflows/created/")
async def request_cashflow_created(request_cashflow: RequestCashflow):
    """
    Handles an HTTP POST request for created request cashflow.
    Sends an event on a socket

    Args:
        request_cashflow (RequestCashflow): The RequestCashflow object containing the created payout review data.

    Returns:
        dict: A dictionary indicating the success of the operation.
    """
    event_data = Event(event_type=EventTypeEnum.CREATED, data=request_cashflow.data).get_event_data()
    await connection_manager.send_events(
        event_data=event_data,
        users_ids=request_cashflow.users_ids,
        socket_name='request_cashflows_socket'
    )

    return {'message': 'request cashflow created sent'}


@app.post("/request-cashflows/updated/")
async def request_cashflow_updated(request_cashflow: RequestCashflow):
    """
    Handles an HTTP POST request for updated payout review.
    Sends an event on a socket

    Args:
        request_cashflow (RequestCashflow): The RequestCashflow object containing the updated request cashflow data.

    Returns:
        dict: A dictionary indicating the success of the operation.
    """
    event_data = Event(event_type=EventTypeEnum.UPDATED, data=request_cashflow.data).get_event_data()
    await connection_manager.send_events(
        event_data=event_data,
        users_ids=request_cashflow.users_ids,
        socket_name='request_cashflows_socket'
    )

    return {'message': 'request cashflow updated sent'}


@app.post("/request-cashflows/deleted/")
async def request_cashflow_deleted(request_cashflow: RequestCashflow):
    """
    Handles an HTTP POST request for deleted request cashflow.
    Sends an event on a socket

    Args:
        request_cashflow (RequestCashflow): The RequestCashflow object containing the deleted payout review data.

    Returns:
        dict: A dictionary indicating the success of the operation.
    """
    event_data = Event(event_type=EventTypeEnum.DELETED, data=request_cashflow.data).get_event_data()
    await connection_manager.send_events(
        event_data=event_data,
        users_ids=request_cashflow.users_ids,
        socket_name='request_cashflows_socket'
    )

    return {'message': 'request cashflow deleted send'}
