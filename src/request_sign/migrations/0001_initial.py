# Generated by Django 4.2.2 on 2025-07-23 20:07

import base.models
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("only_fans_models", "0013_historicalonlyfansmodel"),
    ]

    operations = [
        migrations.CreateModel(
            name="Hashtag",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                ("name", models.CharField(max_length=63, unique=True)),
            ],
            options={
                "db_table": "request_sign_hashtags",
                "ordering": ["name"],
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.CreateModel(
            name="RequestSign",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                ("description", models.TextField(help_text="Description")),
                (
                    "comment",
                    models.TextField(blank=True, help_text="Comment", null=True),
                ),
                (
                    "tg_username",
                    models.CharField(help_text="Author tg username", max_length=128),
                ),
                ("tg_id", models.BigIntegerField(help_text="Author tg id")),
                (
                    "hashtag",
                    models.ForeignKey(
                        help_text="Hashtag",
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="request_signs",
                        to="request_sign.hashtag",
                    ),
                ),
                (
                    "only_fans_model",
                    models.ForeignKey(
                        help_text="OnlyFansModel",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="request_signs",
                        to="only_fans_models.onlyfansmodel",
                    ),
                ),
            ],
            options={
                "db_table": "request_signs",
                "ordering": ["-created_at"],
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.CreateModel(
            name="SignResult",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "tg_username",
                    models.CharField(help_text="Executor tg username", max_length=128),
                ),
                ("tg_id", models.BigIntegerField(help_text="Executor tg id")),
                (
                    "request_sign",
                    models.OneToOneField(
                        help_text="Request sign",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="sign_result",
                        to="request_sign.requestsign",
                    ),
                ),
            ],
            options={
                "db_table": "request_sign_results",
                "ordering": ["-created_at"],
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
    ]
