from rest_framework import serializers

from only_fans_models.models import OnlyFansModel
from request_sign.models import (
    Hashtag,
    RequestSign,
    SignResult,
)


class HashtagSerializer(serializers.ModelSerializer):
    """
    Serializer for Hashtag model
    """

    class Meta:
        model = Hashtag
        fields = ('name',)
        read_only_fields = fields


class RequestSignSerializer(serializers.ModelSerializer):
    """
    Serializer for RequestSign model
    """
    hashtag = serializers.SlugRelatedField(
        slug_field='name',
        queryset=Hashtag.objects.all(),
    )
    only_fans_model = serializers.SlugRelatedField(
        slug_field='username_of',
        queryset=OnlyFansModel.objects.all(),
    )

    class Meta:
        model = RequestSign
        fields = (
            'id',
            'hashtag',
            'only_fans_model',
            'description',
            'comment',
            'tg_username',
            'tg_id',
        )


class SignResultSerializer(serializers.ModelSerializer):
    """
    Serializer for SignResult
    """
    class Meta:
        model = SignResult
        fields = (
            'id',
            'tg_username',
            'tg_id',
        )
