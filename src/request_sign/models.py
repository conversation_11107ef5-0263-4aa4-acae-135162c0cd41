from django.db import models
from django.utils.translation import gettext_lazy as _

from base.models import TimeStampedModel


class Hashtag(TimeStampedModel):
    name = models.CharField(max_length=63, unique=True)

    class Meta:
        ordering = ['name']
        db_table = 'request_sign_hashtags'

    def __str__(self):
        return self.name


class RequestSign(TimeStampedModel):
    hashtag = models.ForeignKey(
        Hashtag,
        on_delete=models.PROTECT,
        help_text=_('Hashtag'),
        related_name='request_signs'
    )
    only_fans_model = models.ForeignKey(
        'only_fans_models.OnlyFansModel',
        on_delete=models.CASCADE,
        help_text=_('OnlyFansModel'),
        related_name='request_signs'
    )
    description = models.TextField(
        help_text=_('Description'),
    )
    comment = models.TextField(
        help_text=_('Comment'),
        blank=True,
        null=True
    )
    tg_username = models.Char<PERSON>ield(
        max_length=128,
        help_text=_('Author tg username'),
    )
    tg_id = models.BigIntegerField(
        help_text=_('Author tg id'),
    )

    class Meta:
        ordering = ['-created_at']
        db_table = 'request_signs'

    def __str__(self):
        return f'{self.created_at} - {self.hashtag.name} - {self.only_fans_model.username_of}'


class SignResult(TimeStampedModel):
    request_sign = models.OneToOneField(
        RequestSign,
        on_delete=models.CASCADE,
        help_text=_('Request sign'),
        related_name='sign_result'
    )
    tg_username = models.CharField(
        max_length=128,
        help_text=_('Executor tg username'),
    )
    tg_id = models.BigIntegerField(
        help_text=_('Executor tg id'),
    )

    class Meta:
        ordering = ['-created_at']
        db_table = 'request_sign_results'

    def __str__(self):
        return f'{self.created_at} - {self.request_sign.hashtag.name} - {self.request_sign.only_fans_model.username_of}'
