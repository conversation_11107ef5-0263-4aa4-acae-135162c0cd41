from django.contrib import admin

from base.mixins import ReadOnlyAdminMixin
from request_sign.models import (
    Hashtag,
    RequestSign,
    SignResult,
)


@admin.register(Hashtag)
class HashtagAdmin(admin.ModelAdmin):
    pass


class SignResultInline(admin.TabularInline):
    model = SignResult
    extra = 0
    fields = ('tg_username', 'tg_id', 'created_at')
    readonly_fields = ('created_at',)


@admin.register(RequestSign)
class RequestSignAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = ('id', 'hashtag', 'created_at', 'only_fans_model', 'tg_username')
    list_filter = ('hashtag',)
    search_fields = ('hashtag__name', 'only_fans_model__username_of', 'tg_username')
    list_select_related = ('hashtag', 'only_fans_model')
    inlines = (SignResultInline,)
