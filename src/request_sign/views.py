from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.mixins import (
    CreateModelMixin,
    ListModelMixin,
    RetrieveModelMixin,
)
from rest_framework.response import Response
from rest_framework_api_key.permissions import HasAP<PERSON><PERSON>ey

from request_sign.models import (
    Hashtag,
    RequestSign,
    SignResult,
)
from request_sign.serializers import (
    HashtagSerializer,
    RequestSignSerializer,
    SignResultSerializer,
)


class HashtagViewSet(ListModelMixin, viewsets.GenericViewSet):
    permission_classes = (<PERSON><PERSON><PERSON><PERSON><PERSON>,)
    serializer_class = HashtagSerializer
    queryset = Hashtag.objects.all()


class RequestSignViewSet(CreateModelMixin, RetrieveModelMixin, viewsets.GenericViewSet):
    permission_classes = (HasAPIKey,)
    serializer_class = RequestSignSerializer
    queryset = RequestSign.objects.all()

    @action(detail=True, methods=['post'], url_path='result', serializer_class=SignResultSerializer)
    def result(self, request, *args, **kwargs):
        request_sign = self.get_object()
        SignResult.objects.create(request_sign=request_sign, **request.data)

        return Response(status=status.HTTP_201_CREATED)
