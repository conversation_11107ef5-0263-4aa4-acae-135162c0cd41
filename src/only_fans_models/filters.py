from django.contrib.auth import get_user_model
from django_filters import rest_framework as filters

from only_fans_models.models import ModelStatus, OnlyFansModel

User = get_user_model()


class OnlyFansModelFilter(filters.FilterSet):
    """
    Filter for OnlyFansModel
    """
    business_unit = filters.ModelChoiceFilter(
        field_name='team_lead__parent',
        queryset=User.objects.filter(role__name='business_unit')
    )
    status = filters.ModelChoiceFilter(
        field_name='status',
        queryset=ModelStatus.objects.all()
    )
    username_of = filters.CharFilter(
        field_name='username_of',
        lookup_expr='icontains'
    )

    class Meta:
        model = OnlyFansModel
        fields = ['business_unit', 'status', 'username_of']
