from rest_framework import routers

from only_fans_models.views import (
    ModelCategoryViewSet,
    ModelPageTypeViewSet,
    ModelStatusViewSet,
    OnlyFansModelsViewSet,
)

router = routers.DefaultRouter()

router.register('statuses', ModelStatusViewSet, basename='model_statuses')
router.register('page-types', ModelPageTypeViewSet, basename='model_page_types')
router.register('genres', ModelCategoryViewSet, basename='model_genres')
router.register('', OnlyFansModelsViewSet, basename='only_fans_models')

urlpatterns = router.urls
