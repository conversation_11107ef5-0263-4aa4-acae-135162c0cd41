import datetime

from django.db.models import Q

from only_fans_models.models import OnlyFansModel


class OnlyFansModelService:
    """
    Service class for OnlyFansModel
    """
    MODEL = OnlyFansModel

    def __init__(self, instance: MODEL):
        self.instance = instance

    @classmethod
    def get_models_on_shifts_by_time_range(
            cls,
            start_time: datetime,
            end_time: datetime
    ) -> list[MODEL]:
        """
        Get models on sessions by time range

        Args:
            start_time: start time
            end_time: end time
        """
        return list(
            cls.MODEL.objects.filter(
                Q(results__shift_start_fact__range=(start_time, end_time)) |
                Q(results__shift_end_fact__range=(start_time, end_time))
            )
            .prefetch_related('results')
            .distinct()
        )
