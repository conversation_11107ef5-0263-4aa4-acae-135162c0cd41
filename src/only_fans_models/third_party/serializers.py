from rest_framework import serializers

from accounts.serializers import TeamLeadNicknameSerializer
from only_fans_models.models import OnlyFansModel


class OnlyFansListBlankIdSerializer(serializers.ModelSerializer):
    """
    Serializer for the list OnlyFansModels model.
    """

    team_lead = TeamLeadNicknameSerializer(read_only=True)
    model_id = serializers.SerializerMethodField()

    class Meta:
        model = OnlyFansModel
        fields = ['model_id', 'team_lead']

    def get_model_id(self, instance):
        return instance.model_id


class OnlyFansListCreateOrUpdateSerializer(serializers.Serializer):
    """
    Serializer for the create or update OnlyFansModels model.
    """
    model_id = serializers.IntegerField(required=True)
    username_of = serializers.CharField(required=True)

    class Meta:
        model = OnlyFansModel
        fields = ['model_id', 'username_of']
