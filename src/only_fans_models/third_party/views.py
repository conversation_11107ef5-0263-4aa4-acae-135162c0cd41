from django.db.models import Prefetch
from rest_framework import status
from rest_framework.mixins import CreateModelMixin, ListModelMixin
from rest_framework.response import Response
from rest_framework.viewsets import GenericViewSet

from accounts.models import User
from base.authentications import DateKeyAuthentication
from base.mixins import BaseViewMethodsMixin
from base.permissions import IsDateTokenAuthenticated
from only_fans_models.models import OnlyFansModel
from only_fans_models.third_party.serializers import OnlyFansListBlankIdSerializer, OnlyFansListCreateOrUpdateSerializer
from only_fans_models.third_party.services import OnlyFansThirdPartyService


class OnlyFansModelsViewSet(BaseViewMethodsMixin, CreateModelMixin, ListModelMixin, GenericViewSet):
    """
    API endpoints that allows only fans models to be list and create or update.
    """
    queryset = OnlyFansModel.objects.all()
    serializer_class = OnlyFansListBlankIdSerializer
    authentication_classes = [DateKeyAuthentication, ]
    permission_classes = [IsDateTokenAuthenticated, ]

    action_serializers = {
        'create': OnlyFansListCreateOrUpdateSerializer,
    }

    def get_queryset(self):
        queryset = super().get_queryset()
        queryset = queryset.select_related('team_lead')
        prefetch = Prefetch('team_lead__childrens', queryset=User.objects.filter(role__name='senior_operator'))
        queryset = queryset.prefetch_related(prefetch)
        return queryset

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        model_id = serializer.validated_data['model_id']
        only_fans_third_party_service = OnlyFansThirdPartyService(model_id)
        instance = only_fans_third_party_service.update_or_create(serializer.validated_data)
        return Response(serializer.to_representation(instance), status=status.HTTP_200_OK)
