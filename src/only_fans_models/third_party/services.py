from only_fans_models.models import OnlyFansModel


class OnlyFansThirdPartyService:
    """
    OnlyFans Model Third Party service
    """
    MODEL = OnlyFansModel

    def __init__(self, model_id: int) -> None:
        self.model_id = model_id

    def update_or_create(self, validate_data: dict) -> MODEL:
        """
        Update or create an instance of the model.

        Args:
            validate_data (dict): Validated data for updating or creating the instance.

        Returns:
            MODEL: The updated or created instance of the model.
        """
        instance, created = self.MODEL.objects.get_or_create(
            model_id=self.model_id,
            defaults=validate_data
        )
        if not created:
            for attr, value in validate_data.items():
                setattr(instance, attr, value)
            instance.save()
        return instance
