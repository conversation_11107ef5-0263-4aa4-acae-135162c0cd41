from django.db.models.signals import (
    post_delete,
    post_save,
    pre_save,
)
from django.dispatch import receiver

from base.crm_cache.service import RedisCacheService
from only_fans_models.models import OnlyFansModel, SubProfile


@receiver([post_save, post_delete], sender=OnlyFansModel)
@receiver([post_save, post_delete], sender=SubProfile)
def invalidate_cache_only_fans_models(sender, instance, *args, **kwargs):
    """
    Invalidate the cache for OnlyFansModel instances.
    """
    cache_service = RedisCacheService()
    prefixes = [
        'only-fans-models-list',
    ]

    for prefix in prefixes:
        cache_service.delete_keys_with_prefix(prefix)


@receiver([pre_save], sender=OnlyFansModel)
def cache_previous_only_fans_model(instance, *args, **kwargs):
    """
    Caches the previous only_fans_model object.

    Parameters:
    - instance (OnlyFansModel): The instance object.
    - *args: Additional arguments.
    - **kwargs: Additional keyword arguments.
    """
    original_team_lead = None

    try:
        original_model = OnlyFansModel.objects.get(pk=instance.pk)
        original_team_lead = original_model.team_lead
    except OnlyFansModel.DoesNotExist:
        pass

    instance.__original_team_lead = original_team_lead
