import hashlib
from datetime import datetime

import pytz
from django.db import models
from django.utils.translation import gettext_lazy as _
from simple_history.models import HistoricalRecords

from base.models import TimeStampedUUIDModel


class ModelCategory(TimeStampedUUIDModel):
    """
    OnlyFansModel Category model
    """
    name = models.CharField(
        max_length=120,
        help_text=_('Category name'),
        unique=True
    )
    balance_profit = models.ForeignKey(
        'only_fans_models.BalanceProfit',
        help_text=_('Balance profit'),
        related_name='model_categories',
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )

    class Meta:
        ordering = ('name',)

    def __str__(self):
        return self.name


class ModelStatus(TimeStampedUUIDModel):
    """
    OnlyFansModel Status model
    """
    name = models.CharField(
        max_length=120,
        help_text=_('OnlyFansModel status name'),
        unique=True
    )

    class Meta:
        ordering = ('name',)

    def __str__(self):
        return self.name


class ModelPageType(TimeStampedUUIDModel):
    """
    OnlyFansModel Page Type model
    """
    name = models.CharField(
        max_length=120,
        help_text=_('OnlyFansModel page type name'),
        unique=True
    )

    class Meta:
        ordering = ('name',)

    def __str__(self):
        return self.name


class OnlyFansModel(TimeStampedUUIDModel):
    """
    OnlyFansModel model
    """
    model_id = models.BigIntegerField(
        help_text=_('Model_id'),
        unique=True,
    )
    nickname = models.CharField(
        help_text=_('Nickname'),
        unique=True,
        max_length=128,
        blank=True,
    )
    username_of = models.CharField(
        help_text=_('Username Only Fans'),
        unique=True,
        max_length=128
    )
    category = models.ForeignKey(
        'only_fans_models.ModelCategory',
        help_text=_('Category'),
        related_name='only_fans_models',
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )
    team_lead = models.ForeignKey(
        'accounts.User',
        help_text=_('Team Lead'),
        related_name='only_fans_models',
        limit_choices_to={'role__name': 'team_lead'},
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )
    tingz_username = models.CharField(
        help_text=_('Tingz username'),
        max_length=128,
        null=True,
        blank=True,
        unique=True
    )
    marketer = models.ForeignKey(
        'accounts.User',
        help_text=_('Marketer'),
        related_name='marketer_models',
        limit_choices_to={'role__name': 'marketer'},
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )
    index_number = models.IntegerField(
        help_text=_('Index number for ordering in finance interface'),
        blank=True,
        null=True,
        unique=True
    )
    start_date = models.DateField(
        help_text=_('Start date'),
        blank=True,
        null=True,
    )
    status = models.ForeignKey(
        'only_fans_models.ModelStatus',
        help_text=_('Status'),
        related_name='only_fans_models',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )
    page_type = models.ForeignKey(
        'only_fans_models.ModelPageType',
        help_text=_('Page type'),
        related_name='only_fans_models',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )
    client_manager = models.ForeignKey(
        'accounts.User',
        help_text=_('ClientManager'),
        related_name='client_manager_models',
        limit_choices_to={'role__name': 'client_manager'},
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )
    history = HistoricalRecords()

    class Meta:
        ordering = ('username_of',)

    @property
    def spammer_key(self) -> str:
        """
        Generate password for model_id
        :return: hashed password for third service
        """
        kiev_tz = pytz.timezone('Europe/Kiev')
        current_date = datetime.now(kiev_tz).date().isoformat()
        data = str(self.model_id) + current_date
        md5_hash = hashlib.md5()
        md5_hash.update(data.encode())
        return md5_hash.hexdigest()

    def __str__(self):
        return self.username_of

    def save(self,  *args, **kwargs):
        if self.index_number is None and self._state.adding is True:
            self.index_number = 1
            max_index_number = (
                    OnlyFansModel.objects.aggregate(models.Max('index_number'))['index_number__max']
                    or 0
            )
            self.index_number = max_index_number + 1

        if not self.nickname or self.nickname != self.username_of.capitalize():
            self.nickname = self.username_of.capitalize()

        super().save(*args, **kwargs)


class BalanceProfit(TimeStampedUUIDModel):
    """
    Balance profit model
    """
    percentage_of_profit = models.DecimalField(
        help_text=_('OnlyFans profit'),
        max_digits=5,
        decimal_places=2,
        default=0
    )
    percentage_tingz_profit = models.DecimalField(
        help_text=_('Tingz profit'),
        max_digits=5,
        decimal_places=2,
        default=0
    )

    def __str__(self):
        return f'of_profit: {self.percentage_of_profit}, tingz_profit: {self.percentage_tingz_profit}'


class SubProfile(TimeStampedUUIDModel):
    """
    SubProfile model
    """
    only_fans_model = models.ForeignKey(
        'only_fans_models.OnlyFansModel',
        help_text=_('OnlyFansModel'),
        related_name='sub_profiles',
        on_delete=models.CASCADE
    )
    sub_profile = models.OneToOneField(
        'only_fans_models.OnlyFansModel',
        help_text=_('OnlyFansModel subprofile'),
        related_name='parent_profile',
        on_delete=models.CASCADE,
    )
