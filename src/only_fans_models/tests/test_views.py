from django.urls import reverse

from base.tests.mixins import BaseCRMTest
from only_fans_models.models import (
    ModelCategory,
    ModelPageType,
    ModelStatus,
    OnlyFansModel,
)


class AdsAPITestCase(BaseCRMTest):

    def test_that_is_owner_field_listed_in_list(self):
        """
        Check that the is_owner field is listed in the list.
        """
        self.client.force_authenticate(user=self.user_marketer)
        OnlyFansModel.objects.create(
            nickname='test',
            marketer=self.user_marketer,
            model_id=1244213345
        )
        response = self.client.get(reverse('only_fans_models-list'))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data[0]['is_owner'], True)

    def test_cache_only_fans_models_list(self):
        """
        Check that the is_owner field is listed in the list.
        """
        self.client.force_authenticate(user=self.user_superuser)

        cache_key = f'only-fans-models-list_{self.user_superuser.id}_'
        self.assertIsNone(self.cache_service.get_cache_data(cache_key))
        response = self.client.get(reverse('only_fans_models-list'))
        self.assertEqual(response.status_code, 200)
        self.assertIsNotNone(self.cache_service.get_cache_data(cache_key))

    def test_model_index_generates_only_when_creating(self):
        model_without_index = OnlyFansModel.objects.create(
            username_of='test_of_model_without_index',
            nickname='test_of_model_without_index',
            model_id=11234545,
        )
        self.assertIsNotNone(model_without_index.index_number)

        model_with_index = OnlyFansModel.objects.create(
            username_of='test_of_model_with_index_number',
            model_id=1123454343455,
            nickname='test_of_model_with_index_number',
            index_number=1111
        )
        self.assertEqual(model_with_index.index_number, 1111)

        model_with_index.index_number = None
        model_with_index.save()

        self.assertIsNone(model_with_index.index_number)

    def test_update_only_fans_model(self):
        self.client.force_authenticate(self.user_superuser)

        only_fans_model = self.create_only_fans_model(1)

        model_category = ModelCategory.objects.create(name='test_category')
        response = self.client.patch(
            reverse('only_fans_models-detail', args=(str(only_fans_model.id),)), data={'genre': str(model_category.id)}
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['genre']['id'], str(model_category.id))

        model_status = ModelStatus.objects.create(name='test_status')
        response = self.client.patch(
            reverse('only_fans_models-detail', args=(str(only_fans_model.id),)), data={'status': str(model_status.id)}
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['status']['id'], str(model_status.id))

        model_page_type = ModelPageType.objects.create(name='test_page_type')
        response = self.client.patch(
            reverse('only_fans_models-detail', args=(str(only_fans_model.id),)), data={'page_type': str(model_page_type.id)}
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['page_type']['id'], str(model_page_type.id))

        team_lead = self.user_team_lead
        response = self.client.patch(
            reverse('only_fans_models-detail', args=(str(only_fans_model.id),)), data={'team_lead': str(team_lead.id)}
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['team_lead']['id'], str(team_lead.id))

        marketer = self.user_marketer
        response = self.client.patch(
            reverse('only_fans_models-detail', args=(str(only_fans_model.id),)), data={'marketer': str(marketer.id)}
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['marketer']['full_name'], str(marketer.full_name))

        start_date = '2021-01-01'
        response = self.client.patch(
            reverse('only_fans_models-detail', args=(str(only_fans_model.id),)), data={'start_date': start_date}
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['start_date'], start_date)

        tingz_username = 'tingz'
        response = self.client.patch(
            reverse('only_fans_models-detail', args=(str(only_fans_model.id),)), data={'tingz_username': tingz_username}
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['tingz_username'], tingz_username)

        self.client.force_authenticate(self.user_team_lead)
        response = self.client.patch(
            reverse('only_fans_models-detail', args=(str(only_fans_model.id),)), data={'marketer': ''}
        )
        self.assertEqual(response.status_code, 403)

        self.client.force_authenticate(self.user_superuser)
        response = self.client.patch(
            reverse('only_fans_models-detail', args=(str(only_fans_model.id),)), data={'marketer': ''}
        )
        self.assertEqual(response.status_code, 200)
        self.assertIsNone(response.data['marketer'])

    def test_list_model_status(self):
        model_status = ModelStatus.objects.create(name='test_status')
        response = self.client.get(reverse('model_statuses-list'))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data[0]['id'], str(model_status.id))

    def test_list_model_page_types(self):
        model_status = ModelPageType.objects.create(name='test_page_type')
        response = self.client.get(reverse('model_page_types-list'))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data[0]['id'], str(model_status.id))

    def test_list_model_genres(self):
        model_genre = ModelCategory.objects.create(name='test_genre')
        response = self.client.get(reverse('model_genres-list'))
        self.assertEqual(response.status_code, 200)

        genres_id = [genre['id'] for genre in response.data]
        self.assertIn(str(model_genre.id), genres_id)

    def test_model_nickname_generates_from_username_of(self):
        model = OnlyFansModel.objects.create(
            username_of='new_model',
            model_id=1123454343455,
        )
        self.assertEqual(model.nickname, 'New_model')

        model.username_of = 'new_model_username'
        model.save()
        self.assertEqual(model.nickname, 'New_model_username')
