
import warnings

from django.urls import reverse

from base.services import DateTokenService
from base.tests.mixins import BaseCRMTest
from only_fans_models.models import OnlyFansModel


class OnlyFansThirdApiTestCase(BaseCRMTest):
    """
    Test case for testing Only Fans Third API endpoints.
    """

    def setUp(self):
        super().setUp()
        self.date_token_key = DateTokenService.get_token_key()
        self.model_data = {'model_id': 555, 'username_of': 'lucy'}
        warnings.filterwarnings("ignore")

    def test_list_models_un_login_with_valid_date_token(self):
        self.client.logout()
        headers = {'TOKEN_MODELS': self.date_token_key}
        response = self.client.get(reverse('third_party_models-list'), headers=headers)
        self.assertEqual(response.status_code, 200)

    def test_list_models_un_login_with_no_valid_date_token(self):
        self.client.logout()
        headers = {'TOKEN_MODELS': f'{self.date_token_key}22'}
        response = self.client.get(reverse('third_party_models-list'), headers=headers)
        self.assertEqual(response.status_code, 401)

    def test_list_models_login_with_no_valid_date_token(self):
        headers = {'TOKEN_MODELS': f'{self.date_token_key}22'}
        response = self.client.get(reverse('third_party_models-list'), headers=headers)
        self.assertEqual(response.status_code, 403)

    def test_create_new_model_auth(self):
        headers = {'TOKEN_MODELS': self.date_token_key}
        response = self.client.get(reverse('third_party_models-list'), headers=headers, data=self.model_data)
        self.assertEqual(response.status_code, 403)
        self.client.logout()
        headers = {'TOKEN_MODELS': f'{self.date_token_key}22'}
        response = self.client.get(reverse('third_party_models-list'), headers=headers, data=self.model_data)
        self.assertEqual(response.status_code, 401)

    def test_create_new_model(self):
        self.client.logout()
        self.create_only_fans_model(123)
        only_fans_models_count = OnlyFansModel.objects.count()
        self.assertEqual(1, only_fans_models_count)
        headers = {'TOKEN_MODELS': self.date_token_key}
        response = self.client.post(reverse('third_party_models-list'), headers=headers, data=self.model_data)
        self.assertEqual(response.status_code, 200)
        only_fans_models_count = OnlyFansModel.objects.count()
        self.assertEqual(2, only_fans_models_count)

    def test_update_new_model(self):
        self.client.logout()
        self.create_only_fans_model(123)
        self.assertEqual(1, OnlyFansModel.objects.count())
        headers = {'TOKEN_MODELS': self.date_token_key}
        data = {'model_id': 123, 'username_of': 'liza'}
        response = self.client.post(reverse('third_party_models-list'), headers=headers, data=data)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(1, OnlyFansModel.objects.count())
        model = OnlyFansModel.objects.filter(model_id=data.get('model_id')).first()
        model.refresh_from_db()
        self.assertEqual(model.username_of, data.get('username_of'))
