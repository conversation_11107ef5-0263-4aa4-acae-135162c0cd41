from django.contrib import admin
from simple_history.admin import SimpleHistoryAdmin

from only_fans_models.models import (
    BalanceProfit,
    ModelCategory,
    ModelPageType,
    ModelStatus,
    OnlyFansModel,
    SubProfile,
)


@admin.register(ModelCategory)
class ModelCategoryAdmin(admin.ModelAdmin):
    """
    Admin class for managing ModelCategory model.
    """
    pass


class SubProfileInline(admin.TabularInline):
    model = SubProfile
    can_delete = True
    extra = 1
    fk_name = 'only_fans_model'


@admin.register(OnlyFansModel)
class OnlyFansModelAdmin(SimpleHistoryAdmin, admin.ModelAdmin):
    """
    Admin class for managing OnlyFansModel model.
    """
    list_display = ('username_of', 'model_id', 'category', 'team_lead', 'start_date', 'status', 'page_type')
    readonly_fields = ('spammer_key', 'nickname')
    search_fields = ('model_id', 'nickname', 'username_of', 'tingz_username')
    list_filter = ('category__name', 'team_lead', 'status__name', 'page_type__name')
    fieldsets = (
        (None,
         {'fields': (
             'model_id',
             'username_of',
             'category',
             'team_lead',
             'tingz_username',
             'status',
             'page_type',
             'start_date',
         )}
         ),
        ('Extra data',
         {
             'fields': ('index_number', 'marketer', 'spammer_key', 'nickname', 'client_manager'),
             'classes': ('collapse',),
         }
         ),
    )

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        return queryset.select_related('team_lead', 'category', 'status', 'page_type')

    inlines = [SubProfileInline]


class OnlyFansModelInline(admin.TabularInline):
    """
    Inline admin class for displaying OnlyFansModel instances in the admin panel.
    """
    model = OnlyFansModel
    fk_name = 'team_lead'
    fields = ('nickname', 'model_id', 'username_of')
    extra = 3
    max_num = 0
    can_delete = False
    show_change_link = True

    def has_add_permission(self, request, obj):
        """
        Determines whether the 'Add' action is permitted for OnlyFansModel instances.

        Args:
        - request: The current request object.
        - obj: The parent object (TeamLead instance).

        Returns:
        - bool: True if 'Add' action is permitted, False otherwise.
        """
        return False

    def has_change_permission(self, request, obj=None):
        """
        Determines whether the 'Change' action is permitted for OnlyFansModel instances.

        Args:
        - request: The current request object.
        - obj: The parent object (TeamLead instance).

        Returns:
        - bool: True if 'Change' action is permitted, False otherwise.
        """
        return False


@admin.register(BalanceProfit)
class BalanceProfitAdmin(admin.ModelAdmin):
    pass


@admin.register(ModelStatus)
class ModelStatusAdmin(admin.ModelAdmin):
    pass


@admin.register(ModelPageType)
class ModelPageTypeAdmin(admin.ModelAdmin):
    pass
