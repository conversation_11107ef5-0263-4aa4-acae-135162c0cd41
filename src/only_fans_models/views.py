from drf_spectacular.utils import extend_schema
from rest_framework.viewsets import ModelViewSet, ReadOnlyModelViewSet
from rest_framework_api_key.permissions import HasAP<PERSON><PERSON>ey

from base.crm_cache.decorators import cache_response
from base.mixins import BaseViewMethodsMixin, ListSerializerResponseMixin
from base.permissions import (
    IsClientManagerOrHOCM,
    IsNotOperator,
    IsSpectator,
    IsTeamLeadOrSeniorOperatorOrSuperUser,
    WriteOnlyForSuperUserOrTeamLeadOrSeniorOperatorRole,
)
from only_fans_models.filters import OnlyFansModelFilter
from only_fans_models.models import (
    ModelCategory,
    ModelPageType,
    ModelStatus,
    OnlyFansModel,
)
from only_fans_models.serializers import (
    ModelCategorySerializer,
    ModelPageTypeSerializer,
    ModelStatusSerializer,
    OnlyFansModelListSerializer,
    OnlyFansModelsSerializer,
    OnlyFansModelUpdateSerializer,
)


@extend_schema(tags=['only-fnas-models - Only Fans Models'])
class OnlyFansModelsViewSet(BaseViewMethodsMixin, ListSerializerResponseMixin, ModelViewSet):
    """
    API endpoints for OnlyFansModels.
    """
    serializer_class = OnlyFansModelsSerializer
    http_method_names = ['get', 'post', 'put', 'patch']
    permission_classes = [
        (IsNotOperator & WriteOnlyForSuperUserOrTeamLeadOrSeniorOperatorRole) | IsSpectator | HasAPIKey
    ]
    action_serializers = {
        'list': OnlyFansModelListSerializer,
        'update':  OnlyFansModelUpdateSerializer,
        'partial_update': OnlyFansModelUpdateSerializer,
    }
    action_filtersets = {
        'list': OnlyFansModelFilter,
    }

    @cache_response('only-fans-models-list')
    def list(self, request, *args, **kwargs):
        """
        This list endpoint returns all OnlyFans Models.
        For users with 'marketer' role, an extra boolean field 'is_owner'
        is included to indicate if the user is the owner of each model.
        """
        return super().list(request, *args, **kwargs)

    def get_queryset(self):
        queryset = OnlyFansModel.objects.select_related(
            'team_lead',
            'status',
            'marketer',
            'category',
            'page_type'
        )

        if hasattr(self.request.user, 'role'):
            if self.request.user.role.name == 'team_lead':
                queryset = queryset.filter(team_lead=self.request.user)

            elif self.request.user.role.name == 'senior_operator':
                queryset = queryset.filter(team_lead=self.request.user.parent)

            elif self.request.user.role.name in ['hof', 'financier']:
                queryset = queryset.filter(index_number__isnull=False)

            elif self.request.user.role.name == 'smm':
                queryset = queryset.filter(ads__isnull=False)
                queryset = queryset.filter(ads__ads_tracking__isnull=False).distinct()

            elif self.request.user.role.name in ['client_manager', 'hocm']:
                queryset = queryset.filter(parent_profile__isnull=True)

        return queryset


@extend_schema(tags=['only-fnas-models - Model Statuses'])
class ModelStatusViewSet(BaseViewMethodsMixin, ReadOnlyModelViewSet):
    """
    API endpoints for OnlyFansModel Status
    """
    queryset = ModelStatus.objects.all()
    serializer_class = ModelStatusSerializer
    permission_classes = [IsTeamLeadOrSeniorOperatorOrSuperUser]


@extend_schema(tags=['only-fnas-models - Model Genres'])
class ModelCategoryViewSet(BaseViewMethodsMixin, ReadOnlyModelViewSet):
    """
    API endpoints for OnlyFansModel Category
    """
    queryset = ModelCategory.objects.all()
    serializer_class = ModelCategorySerializer
    permission_classes = [IsTeamLeadOrSeniorOperatorOrSuperUser | IsClientManagerOrHOCM]


@extend_schema(tags=['only-fnas-models - Model Page Types'])
class ModelPageTypeViewSet(BaseViewMethodsMixin, ReadOnlyModelViewSet):
    """
    API endpoints for OnlyFansModel Page Type
    """
    queryset = ModelPageType.objects.all()
    serializer_class = ModelPageTypeSerializer
    permission_classes = [IsTeamLeadOrSeniorOperatorOrSuperUser]
