import datetime

from django.contrib.auth import get_user_model
from django.db.models import Q
from django.utils.translation import gettext_lazy as _
from rest_framework import serializers
from rest_framework.exceptions import PermissionDenied

from only_fans_models.models import OnlyFansModel

User = get_user_model()


class OnlyFansModelValidator:
    """
    OnlyFansModel validator
    """
    def __init__(self, instance: OnlyFansModel) -> None:
        self.instance = instance

    def validate_model_is_not_busy_for_shift(
            self,
            start_time: datetime.datetime,
            end_time: datetime.datetime
    ) -> None:
        """
        Validate that model is not busy in shift time range

        Args:
            start_time (datetime.datetime): shift start time
            end_time (datetime.datetime): shift end time
        """
        if self.instance.results.filter(
            (Q(shift__shift_start__gte=start_time) & Q(shift__shift_start__lt=end_time)) |
            (Q(shift__shift_end__gt=start_time) & Q(shift__shift_end__lte=end_time)),
            ~Q(shift__status='end'),
            Q(shift_end_fact__isnull=True)
        ).select_related('shift').exists():
            raise serializers.ValidationError({
                'only_fans_models':
                _(f'OnlyFansModel {self.instance} is busy in this shift time range {start_time} - {end_time}')
            })

    def validate_model_without_more_than_one_progress_shifts(self) -> None:
        """
        Validate that model does not have more than one progress shifts
        """
        if self.instance.results.filter(shift__status='progress', shift_end_fact__isnull=True).count() > 1:
            raise serializers.ValidationError({
                'only_fans_models':
                _(f'OnlyFansModel {self.instance} already has more than one shift in progress')
            })

    def validate_update_model(self, data: dict, user: User):
        if 'marketer' not in data or user.role.name == 'superuser':
            return

        marketer = data['marketer']

        if not marketer and self.instance.marketer:
            raise PermissionDenied(
                {'marketer': _("You don't have permission to change marketer for model")},
            )

        elif (
                (self.instance.marketer and marketer != str(self.instance.marketer.id))
                or not self.instance.marketer
        ):

            raise PermissionDenied(
                {'marketer': _("You don't have permission to change marketer for model")},
            )
