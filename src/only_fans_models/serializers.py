from django.contrib.auth import get_user_model
from drf_spectacular.types import OpenApiTypes
from drf_spectacular.utils import extend_schema_field
from rest_framework import serializers
from rest_framework.relations import PrimaryKeyRelatedField

from only_fans_models.models import (
    ModelCategory,
    ModelPageType,
    ModelStatus,
    OnlyFansModel,
)
from only_fans_models.validators import OnlyFansModelValidator


class OnlyFansModelsSerializer(serializers.ModelSerializer):
    """
    Serializer for the OnlyFansModels model.
    """
    class Meta:
        model = OnlyFansModel
        fields = ['id', 'nickname']


class ModelStatusSerializer(serializers.ModelSerializer):
    class Meta:
        model = ModelStatus
        fields = ['id', 'name']


class ModelCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = ModelCategory
        fields = ['id', 'name']


class ModelPageTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = ModelPageType
        fields = ['id', 'name']


class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = get_user_model()
        fields = ['id', 'full_name']


class OnlyFansModelListSerializer(serializers.ModelSerializer):
    """
    Serializer for the list OnlyFansModels model.
    """
    is_owner = serializers.SerializerMethodField()
    marketer = UserSerializer(read_only=True, allow_null=True)
    status = ModelStatusSerializer(read_only=True,  allow_null=True)
    genre = ModelCategorySerializer(source='category', read_only=True, allow_null=True)
    page_type = ModelStatusSerializer(read_only=True, allow_null=True)
    team_lead = UserSerializer(read_only=True, allow_null=True)

    class Meta:
        model = OnlyFansModel
        fields = (
            'id',
            'nickname',
            'is_owner',
            'index_number',
            'marketer',
            'username_of',
            'start_date',
            'status',
            'genre',
            'page_type',
            'team_lead',
            'tingz_username',
            'model_id'
        )

    @extend_schema_field(OpenApiTypes.BOOL)
    def get_is_owner(self, obj):
        """
        Check if the user's role name 'marketer' and the model's marketer is the same.
        """
        request = self.context.get('request')

        if request and hasattr(request, "user"):

            return obj.marketer == request.user

        return False


class OnlyFansModelUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for the OnlyFansModels model.
    """
    genre = PrimaryKeyRelatedField(
        queryset=ModelCategory.objects.all(), source='category', required=False,  allow_null=True
    )
    team_lead = PrimaryKeyRelatedField(
        queryset=get_user_model().objects.filter(role__name='team_lead'), required=False, allow_null=True
    )
    marketer = PrimaryKeyRelatedField(
        queryset=get_user_model().objects.filter(role__name='marketer'), required=False, allow_null=True
    )

    class Meta:
        model = OnlyFansModel
        fields = ['genre', 'start_date', 'status', 'page_type', 'team_lead', 'tingz_username', 'marketer']

    def validate(self, data):
        request = self.context.get('request')

        if request and hasattr(request, "user"):
            OnlyFansModelValidator(self.instance).validate_update_model(data, request.user)

        return data

    def to_internal_value(self, data):
        if data.get('tingz_username') == '':
            data['tingz_username'] = None

        return super().to_internal_value(data)
