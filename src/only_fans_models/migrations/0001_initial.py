# Generated by Django 4.2.2 on 2023-06-18 14:13

import uuid

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models

import base.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ModelCategory',
            fields=[
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, editable=False)),
                ('modified_at', models.DateTimeField(auto_now=True, null=True)),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(help_text='Category name', unique=True)),
            ],
            options={
                'ordering': ('name',),
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.CreateModel(
            name='ModelTier',
            fields=[
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, editable=False)),
                ('modified_at', models.DateTimeField(auto_now=True, null=True)),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('level', models.BigIntegerField(help_text='Level', unique=True)),
            ],
            options={
                'ordering': ('level',),
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.CreateModel(
            name='OnlyFansModel',
            fields=[
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, editable=False)),
                ('modified_at', models.DateTimeField(auto_now=True, null=True)),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('blank_id', models.BigIntegerField(help_text='Blank_id', unique=True)),
                ('nickname', models.CharField(help_text='Nickname', max_length=128, unique=True)),
                ('username_of', models.CharField(help_text='Username Only Fans', max_length=128, unique=True)),
                ('headers', models.JSONField(blank=True, help_text='Headers for parsing data', null=True)),
                ('proxy', models.TextField(blank=True, help_text='Proxies for parsing data', null=True)),
                ('category', models.ForeignKey(help_text='Category', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='only_fans_models', to='only_fans_models.modelcategory')),
                ('team_lead', models.ForeignKey(blank=True, help_text='Team Lead', limit_choices_to={'role__name': 'team_lead'}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='only_fans_models', to=settings.AUTH_USER_MODEL)),
                ('tier', models.ForeignKey(help_text='Tier', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='only_fans_models', to='only_fans_models.modeltier')),
            ],
            options={
                'ordering': ('username_of',),
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
    ]
