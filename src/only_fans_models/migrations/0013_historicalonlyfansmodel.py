# Generated by Django 4.2.2 on 2025-05-20 13:18

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import simple_history.models
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("only_fans_models", "0012_subprofile"),
    ]

    operations = [
        migrations.CreateModel(
            name="HistoricalOnlyFansModel",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(blank=True, editable=False, null=True),
                ),
                (
                    "id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, editable=False),
                ),
                (
                    "model_id",
                    models.BigIntegerField(db_index=True, help_text="Model_id"),
                ),
                (
                    "nickname",
                    models.Char<PERSON><PERSON>(
                        blank=True, db_index=True, help_text="Nickname", max_length=128
                    ),
                ),
                (
                    "username_of",
                    models.CharField(
                        db_index=True, help_text="Username Only Fans", max_length=128
                    ),
                ),
                (
                    "tingz_username",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        help_text="Tingz username",
                        max_length=128,
                        null=True,
                    ),
                ),
                (
                    "index_number",
                    models.IntegerField(
                        blank=True,
                        db_index=True,
                        help_text="Index number for ordering in finance interface",
                        null=True,
                    ),
                ),
                (
                    "start_date",
                    models.DateField(blank=True, help_text="Start date", null=True),
                ),
                ("history_id", models.AutoField(primary_key=True, serialize=False)),
                ("history_date", models.DateTimeField(db_index=True)),
                ("history_change_reason", models.CharField(max_length=100, null=True)),
                (
                    "history_type",
                    models.CharField(
                        choices=[("+", "Created"), ("~", "Changed"), ("-", "Deleted")],
                        max_length=1,
                    ),
                ),
                (
                    "category",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        help_text="Category",
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="only_fans_models.modelcategory",
                    ),
                ),
                (
                    "client_manager",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        help_text="ClientManager",
                        limit_choices_to={"role__name": "client_manager"},
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "history_user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "marketer",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        help_text="Marketer",
                        limit_choices_to={"role__name": "marketer"},
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "page_type",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        help_text="Page type",
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="only_fans_models.modelpagetype",
                    ),
                ),
                (
                    "status",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        help_text="Status",
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="only_fans_models.modelstatus",
                    ),
                ),
                (
                    "team_lead",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        help_text="Team Lead",
                        limit_choices_to={"role__name": "team_lead"},
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "historical only fans model",
                "verbose_name_plural": "historical only fans models",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
    ]
