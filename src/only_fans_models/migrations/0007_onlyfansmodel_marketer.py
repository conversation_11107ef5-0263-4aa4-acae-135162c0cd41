# Generated by Django 4.2.2 on 2023-09-08 12:16

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("only_fans_models", "0006_onlyfansmodel_tingz_username"),
    ]

    operations = [
        migrations.AddField(
            model_name="onlyfansmodel",
            name="marketer",
            field=models.ForeignKey(
                blank=True,
                help_text="Marketer",
                limit_choices_to={"role__name": "marketer"},
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="marketer_models",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
