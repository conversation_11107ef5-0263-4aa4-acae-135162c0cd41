# Generated by Django 4.2.2 on 2024-04-04 16:19

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("only_fans_models", "0010_modelpagetype_modelstatus_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="onlyfansmodel",
            name="client_manager",
            field=models.ForeignKey(
                blank=True,
                help_text="ClientManager",
                limit_choices_to={"role__name": "client_manager"},
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="client_manager_models",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
