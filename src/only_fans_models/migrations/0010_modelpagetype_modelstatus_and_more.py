# Generated by Django 4.2.2 on 2024-01-24 16:56

import uuid

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models

import base.models


class Migration(migrations.Migration):

    dependencies = [
        ("only_fans_models", "0009_onlyfansmodel_index_number"),
    ]

    operations = [
        migrations.CreateModel(
            name="ModelPageType",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="OnlyFansModel page type name",
                        max_length=120,
                        unique=True,
                    ),
                ),
            ],
            options={
                "ordering": ("name",),
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.CreateModel(
            name="ModelStatus",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="OnlyFansModel status name",
                        max_length=120,
                        unique=True,
                    ),
                ),
            ],
            options={
                "ordering": ("name",),
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.RemoveField(
            model_name="onlyfansmodel",
            name="headers",
        ),
        migrations.RemoveField(
            model_name="onlyfansmodel",
            name="proxy",
        ),
        migrations.RemoveField(
            model_name="onlyfansmodel",
            name="tier",
        ),
        migrations.AddField(
            model_name="onlyfansmodel",
            name="start_date",
            field=models.DateField(blank=True, help_text="Start date", null=True),
        ),
        migrations.AlterField(
            model_name="onlyfansmodel",
            name="category",
            field=models.ForeignKey(
                blank=True,
                help_text="Category",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="only_fans_models",
                to="only_fans_models.modelcategory",
            ),
        ),
        migrations.AlterField(
            model_name="onlyfansmodel",
            name="nickname",
            field=models.CharField(
                blank=True, help_text="Nickname", max_length=128, unique=True
            ),
        ),
        migrations.DeleteModel(
            name="ModelTier",
        ),
        migrations.AddField(
            model_name="onlyfansmodel",
            name="page_type",
            field=models.ForeignKey(
                blank=True,
                help_text="Page type",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="only_fans_models",
                to="only_fans_models.modelpagetype",
            ),
        ),
        migrations.AddField(
            model_name="onlyfansmodel",
            name="status",
            field=models.ForeignKey(
                blank=True,
                help_text="Status",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="only_fans_models",
                to="only_fans_models.modelstatus",
            ),
        ),
    ]
