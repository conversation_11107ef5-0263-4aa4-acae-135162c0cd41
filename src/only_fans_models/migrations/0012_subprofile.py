# Generated by Django 4.2.2 on 2024-04-16 16:30

import uuid

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models

import base.models


class Migration(migrations.Migration):

    dependencies = [
        ("only_fans_models", "0011_onlyfansmodel_client_manager"),
    ]

    operations = [
        migrations.CreateModel(
            name="SubProfile",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "only_fans_model",
                    models.ForeignKey(
                        help_text="OnlyFansModel",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="sub_profiles",
                        to="only_fans_models.onlyfansmodel",
                    ),
                ),
                (
                    "sub_profile",
                    models.OneToOneField(
                        help_text="OnlyFansModel subprofile",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="parent_profile",
                        to="only_fans_models.onlyfansmodel",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
    ]
