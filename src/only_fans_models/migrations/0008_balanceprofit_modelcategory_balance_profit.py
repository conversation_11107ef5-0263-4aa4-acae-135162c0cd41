# Generated by Django 4.2.2 on 2023-09-29 19:37

import uuid

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models

import base.models


class Migration(migrations.Migration):

    dependencies = [
        ("only_fans_models", "0007_onlyfansmodel_marketer"),
    ]

    operations = [
        migrations.CreateModel(
            name="BalanceProfit",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "percentage_of_profit",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="OnlyFans profit",
                        max_digits=5,
                    ),
                ),
                (
                    "percentage_tingz_profit",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Tingz profit",
                        max_digits=5,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.AddField(
            model_name="modelcategory",
            name="balance_profit",
            field=models.ForeignKey(
                blank=True,
                help_text="Balance profit",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="model_categories",
                to="only_fans_models.balanceprofit",
            ),
        ),
    ]
