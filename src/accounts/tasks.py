from urllib.parse import urljoin

import httpx
from celery.utils.log import get_task_logger
from django.conf import settings
from django.contrib.auth import get_user_model

from core.celery import BaseTaskWithRetry
from core.celery import app as celery_app

User = get_user_model()
MVP_URL = settings.MVP_URL
MVP_API_KEY = settings.MVP_API_KEY

logger = get_task_logger(__name__)


@celery_app.task(bind=BaseTaskWithRetry)
def create_telegram_operator(self, user_id: str):
    operator = User.objects.get(id=user_id)

    data = {
        "first_name": operator.first_name,
        "last_name": operator.last_name,
        "telegram_id": operator.telegram_id,
        "id": str(operator.id),
        "parent_id": str(operator.parent_id),
    }

    url = urljoin(MVP_URL, "account/api/operator/")
    headers = {"X-Api-Key": MVP_API_KEY}

    with httpx.Client() as client:
        response = client.post(url, headers=headers, json=data)

        logger.info("RESPONSE", response.text)

        if response.status_code == 201:
            return f"Operator {operator.id} created in MVP"

        return f"RESPONSE STATUS: {response.status_code} - {response.text}"


@celery_app.task(bind=BaseTaskWithRetry)
def update_telegram_operator(self, user_id: str):
    operator = User.objects.get(id=user_id)

    data = {
        "first_name": operator.first_name,
        "last_name": operator.last_name,
        "telegram_id": operator.telegram_id,
        "parent_id": str(operator.parent_id)
    }

    url = urljoin(MVP_URL, f"account/api/operator/{operator.id}/")
    headers = {"X-Api-Key": MVP_API_KEY}

    with httpx.Client() as client:
        response = client.patch(url, headers=headers, json=data)

        if response.status_code == 200:
            return f"Operator {operator.id} updated in MVP"

        if response.status_code == 201:
            return f"Operator {operator.id} created in MVP"

        return f"RESPONSE STATUS: {response.status_code} - {response.text}"


@celery_app.task(bind=BaseTaskWithRetry)
def delete_telegram_operator(self, user_id: str):
    url = urljoin(MVP_URL, f"account/api/operator/{user_id}/")
    headers = {"X-Api-Key": MVP_API_KEY}

    with httpx.Client() as client:
        response = client.delete(url, headers=headers)

        if response.status_code == 204:
            return f"Operator {user_id} deleted in MVP"

        return f"RESPONSE STATUS: {response.status_code} - {response.text}"
