from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from drf_spectacular.utils import OpenApiParameter, extend_schema
from rest_framework import (
    mixins,
    status,
    viewsets,
)
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework_api_key.permissions import HasAPIKey

from accounts.serializers import (
    ClientManagerCreateSerializer,
    ClientManagerListSerializer,
    ClientManagerUpdateSerializer,
    MarketerCreateSerializer,
    MarketerListSerializer,
    MarketerUpdateSerializer,
    OperatorCreateSerializer,
    OperatorListSerializer,
    OperatorUpdateSerializer,
    PureUserSerializer,
    TeamLeadSerializer,
    TelegramUserSerializer,
    UserSerializer,
)
from base.crm_cache.decorators import cache_response
from base.mixins import BaseViewMethodsMixin, ListSerializerResponseMixin
from base.permissions import (
    IsBusinessDevOrHOBD,
    IsClientManagerOrHOCM,
    IsHOC<PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>r<PERSON><PERSON><PERSON>r<PERSON><PERSON>or<PERSON>ark<PERSON>,
    IsTeamLeadOrSeniorOperatorOr<PERSON>uperUser,
    WriteOnlyForHOMRole,
)

User = get_user_model()


class UsersViewSet(BaseViewMethodsMixin,
                   mixins.RetrieveModelMixin,
                   viewsets.GenericViewSet):
    """
    A view set that provides retrieve and list actions.
    """

    serializer_class = UserSerializer
    pagination_class = None

    action_serializers = {
        'me': UserSerializer,
        'my_group': TeamLeadSerializer,
        'team_leads': PureUserSerializer,
        'telegram_users': TelegramUserSerializer
    }

    action_permissions = {
        'my_group': [IsTeamLeadOrSeniorOperatorOrSuperUser | IsSpectator],
        'team_leads': [IsSuperUser | IsSpectator],
        'telegram_users': [HasAPIKey]
    }

    def get_object(self):
        return self.request.user

    def get_queryset(self):
        """
        Method returns queryset with User objects
        """
        queryset = User.objects.all()

        if self.action == 'my_group':

            if self.request.user.external:

                return queryset.filter(
                    role__name='team_lead',
                    external=True
                ).prefetch_related(
                    'childrens',
                    'only_fans_models',
                    'shift_numbers__index'
                )
            else:

                return queryset.filter(
                    role__name='team_lead',
                    external=False
                ).prefetch_related(
                    'childrens',
                    'only_fans_models',
                    'shift_numbers__index'
                )

        if self.action == 'team_leads':

            return queryset.filter(
                role__name='team_lead'
            )

        return queryset

    @action(detail=False, methods=['get'])
    def me(self, request, *args, **kwargs):
        """
        Endpoint shows self account information
        """
        return self.retrieve(self, request, *args, **kwargs)

    @cache_response(prefix='my_group')
    @action(detail=False, methods=['get'], url_path='my-group')
    def my_group(self, request, *args, **kwargs):
        """
        Retrieve the list of 'my_group' and cache the response.

        This endpoint returns the list of 'my_group', which are the operators and models
         associated with the current user who is a team lead or senior operator.
        The response is cached using the 'my_group' prefix and the user's ID as part of the cache key.

        Args:
            request (rest_framework.request.Request): The request object.
            *args: Variable length argument list.
            **kwargs: Arbitrary keyword arguments.

        Returns:
            rest_framework.response.Response: The serialized list of 'my_group'.
        """

        serializer = self.get_serializer(self.get_queryset(), many=True)
        return Response(serializer.data)

    @cache_response(prefix='team-leads')
    @action(detail=False, methods=['get'], url_path='team-leads')
    def team_leads(self, request, *args, **kwargs):
        """
        Retrieve the list of 'team_leads' and cache the response.

        This endpoint returns the list of 'team_leads', which are the operators and models
         associated with the current user who is super user.
        The response is cached using the 'team_leads' prefix and the user's ID as part of the cache key.

        Args:
            request (rest_framework.request.Request): The request object.
            *args: Variable length argument list.
            **kwargs: Arbitrary keyword arguments.

        Returns:
            rest_framework.response.Response: The serialized list of 'team_leads'.
        """

        serializer = self.get_serializer(self.get_queryset(), many=True)
        return Response(serializer.data)

    @extend_schema(
        parameters=[
            OpenApiParameter(
                name="tg_id",
                description="Telegram user ID (lookup on telegram_id field)",
                required=True,
                type=int,
            ),
            OpenApiParameter(
                name="role",
                description="Filter by role",
                required=False,
                type=str,
            ),
        ],
        summary="Retrieve a Telegram user by telegram ID",
    )
    @action(
        detail=False,
        methods=['get'],
        url_path='telegram',
    )
    def telegram_users(self, request):
        """
        Endpoint for getting telegram user by tg_id
        """
        tg_id = request.query_params.get('tg_id')

        if not tg_id:
            raise ValidationError('tg_id is required')

        queryset = User.objects.filter(telegram_id=tg_id, is_active=True)

        if role := request.query_params.get('role'):
            queryset = queryset.filter(role__name=role)

        user = queryset.first()

        if not user:
            return Response(status=status.HTTP_404_NOT_FOUND)

        serializer = self.get_serializer(user)

        return Response(serializer.data)


class OperatorViewSet(BaseViewMethodsMixin, ListSerializerResponseMixin, viewsets.ModelViewSet):
    """
    A view set that provides CRUD actions for operators.
    """
    queryset = User.objects.filter(role__name='operator').select_related('parent__role', 'role')
    serializer_class = OperatorListSerializer
    http_method_names = ['get', 'post', 'delete', 'patch']

    action_serializers = {
        'list': OperatorListSerializer,
        'create': OperatorCreateSerializer,
        'update': OperatorUpdateSerializer,
        'partial_update': OperatorUpdateSerializer,
    }

    permission_classes = [IsTeamLeadOrSeniorOperatorOrSuperUser,]
    action_permissions = {
        'list': [IsTeamLeadOrSeniorOperatorOrSuperUser | IsSpectator],
        'create': [IsTeamLeadOrSeniorOperatorOrSuperUser, ],
        'update': [IsTeamLeadOrSeniorOperatorOrSuperUser, ],
        'partial_update': [IsTeamLeadOrSeniorOperatorOrSuperUser, ],
        'destroy': [IsTeamLeadOrSeniorOperatorOrSuperUser, ],
    }

    def get_queryset(self):
        """
        Method returns queryset for list action.
        """
        queryset = super().get_queryset()

        if self.action == 'list':
            if self.request.user.role.name == 'team_lead':
                queryset = queryset.filter(parent=self.request.user)
            elif self.request.user.role.name == 'senior_operator':
                queryset = queryset.filter(parent=self.request.user.parent)

        return queryset

    @extend_schema(
        request=OperatorCreateSerializer,
        responses=OperatorListSerializer,
    )
    def create(self, request, *args, **kwargs):
        """
        Define create method to get correct response data structure.
        """
        return super().create(request, *args, **kwargs)

    @extend_schema(
        responses=OperatorListSerializer,
    )
    def update(self, request, *args, **kwargs):
        """
        Define update method to get correct response data structure.
        """
        return super().update(request, *args, **kwargs)

    @extend_schema(
        request=OperatorUpdateSerializer,
        responses=OperatorListSerializer,
    )
    def partial_update(self, request, *args, **kwargs):
        """
        Define partial_update method to get correct response data structure.
        """
        return super().partial_update(request, *args, **kwargs)

    @cache_response(prefix='operators-list')
    def list(self, request, *args, **kwargs):
        """
        Cached list
        """
        return super().list(request, *args, **kwargs)


# ------------------------------------------Marketers----------------------------------------


class MarketerViewSet(BaseViewMethodsMixin, ListSerializerResponseMixin, viewsets.ModelViewSet):
    """
    A view set that provides CRUD actions for marketers.
    """
    serializer_class = MarketerListSerializer
    permission_classes = [(IsSuperUserOrHOMorMarketer | IsBusinessDevOrHOBD) & WriteOnlyForHOMRole]
    action_serializers = {
        'list': MarketerListSerializer,
        'retrieve': MarketerListSerializer,
        'create': MarketerCreateSerializer,
        'update': MarketerUpdateSerializer,
        'partial_update': MarketerUpdateSerializer,
    }

    def get_queryset(self):
        filter_role_name = 'marketer'

        # special for business_devs and hobd
        if self.request.user.role and self.request.user.role.name in ('hobd', 'business_dev'):
            filter_role_name = 'business_dev'

        return get_user_model().objects.filter(
            role__name=filter_role_name
        ).prefetch_related(
            'marketer_models'
        ).select_related(
            'role', 'parent'
        )

    @extend_schema(
        request=MarketerCreateSerializer,
        responses=MarketerListSerializer
    )
    def create(self, request, *args, **kwargs):
        """
        Rewrite create method to get correct response schema.
        """

        return super().create(request, *args, **kwargs)

    @extend_schema(
        request=MarketerUpdateSerializer,
        responses=MarketerListSerializer
    )
    def update(self, request, *args, **kwargs):
        """
        Rewrite update method to get correct response schema.
        """

        return super().update(request, *args, **kwargs)

    @extend_schema(
        request=MarketerUpdateSerializer,
        responses=MarketerListSerializer
    )
    def partial_update(self, request, *args, **kwargs):
        """
        Rewrite partial_update method to get correct response schema.
        """
        return super().partial_update(request, *args, **kwargs)

    @cache_response(prefix='marketers-list')
    def list(self, request, *args, **kwargs):
        """
        Cached list
        """
        return super().list(request, *args, **kwargs)


# ------------------------------------------Client Managers----------------------------------------
class ClientManagerViewSet(BaseViewMethodsMixin, ListSerializerResponseMixin, viewsets.ModelViewSet):
    """
    A view set that provides CRUD actions for client managers.
    """
    queryset = (
        get_user_model().objects.
        filter(role__name='client_manager').
        prefetch_related('client_manager_models').
        select_related('role')
    )
    serializer_class = ClientManagerListSerializer
    permission_classes = [IsHOCM]
    action_serializers = {
        'list': ClientManagerListSerializer,
        'retrieve': ClientManagerListSerializer,
        'create': ClientManagerCreateSerializer,
        'update': ClientManagerUpdateSerializer,
        'partial_update': ClientManagerUpdateSerializer
    }
    action_permissions = {
        'list': [IsClientManagerOrHOCM],
        'retrieve': [IsHOCM],
        'create': [IsHOCM],
        'update': [IsHOCM],
        'partial_update': [IsHOCM],
        'destroy': [IsHOCM],
    }

    @extend_schema(
        request=ClientManagerCreateSerializer,
        responses=ClientManagerListSerializer
    )
    def create(self, request, *args, **kwargs):
        """
        Rewrite create method to get correct response schema.
        """

        return super().create(request, *args, **kwargs)

    @extend_schema(
        request=ClientManagerUpdateSerializer,
        responses=ClientManagerListSerializer
    )
    def update(self, request, *args, **kwargs):
        """
        Rewrite update method to get correct response schema.
        """

        return super().update(request, *args, **kwargs)

    @extend_schema(
        request=ClientManagerUpdateSerializer,
        responses=ClientManagerListSerializer
    )
    def partial_update(self, request, *args, **kwargs):
        """
        Rewrite partial_update method to get correct response schema.
        """

        return super().partial_update(request, *args, **kwargs)

    def list(self, request, *args, **kwargs):
        """
        Cached list
        """
        return super().list(request, *args, **kwargs)


# ---------------------------------------------Business Units----------------------------------------
class BusinessUnitViewSet(mixins.ListModelMixin, viewsets.GenericViewSet):
    """
    A view set that provides CRUD actions for business units.
    """
    queryset = get_user_model().objects.filter(role__name='business_unit')
    serializer_class = PureUserSerializer
    permission_classes = [HasAPIKey]

    @cache_response(prefix='business-units-list')
    def list(self, request, *args, **kwargs):
        """
        Cached list
        """
        return super().list(request, *args, **kwargs)


# ---------------------------------------------Business Units----------------------------------------
class TeamLeadViewSet(mixins.ListModelMixin, viewsets.GenericViewSet):
    """
    A view set that provides CRUD actions for team leads.
    """
    queryset = get_user_model().objects.filter(role__name='team_lead')
    serializer_class = PureUserSerializer
    permission_classes = [HasAPIKey]
    filterset_fields = ['parent']

    @cache_response(prefix='team-leads-list')
    def list(self, request, *args, **kwargs):
        """
        Cached list
        """
        return super().list(request, *args, **kwargs)


# ---------------------------------------------HOM----------------------------------------
class HOMViewSet(mixins.ListModelMixin, viewsets.GenericViewSet):
    """
    A view set that provides CRUD actions for HOMs.
    """
    queryset = get_user_model().objects.filter(role__name='hom')
    serializer_class = PureUserSerializer
    permission_classes = [IsHOM]

    @cache_response(prefix='hom-list')
    def list(self, request, *args, **kwargs):
        """
        Cached list
        """
        return super().list(request, *args, **kwargs)
