from django.contrib.auth.models import (
    AbstractBaseUser,
    BaseUserManager,
    PermissionsMixin,
)
from django.core.exceptions import ValidationError
from django.db import models
from django.db.models import Q
from django.utils.translation import gettext_lazy as _

from base.models import TimeStampedUUIDModel
from base.proxy_aware_historical_records import ProxyAwareHistoricalRecords


class UserAccountManager(BaseUserManager):
    """
    Custom user account manager to define `User` model
    """

    def create_superuser(self, email, password, **extra_fields):
        """
        Creates superuser instance

        Arguments:
            email -- superuser email
            password -- superuser password

        Returns:
            Created superuseruser instance
        """
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        user = self.create_user(email=email, password=password, **extra_fields)
        return user

    def create_user(self, email, password, **extra_fields):
        """
        Creates user instance

        Arguments:
            email -- user email
            password -- user password

        Returns:
            Created user instance
        """
        if email:
            email = self.normalize_email(email)
        if password:
            user = self.model(email=email, **extra_fields)
            user.set_password(password)
            user.save()
        else:
            user = self.model(email=email, **extra_fields)
            user.set_unusable_password()
            user.save()

        return user


class UserRole(TimeStampedUUIDModel):
    """
    User role model
    """
    name = models.CharField(
        help_text=_('User role name'),
        unique=True,
        max_length=128
    )
    description = models.CharField(
        help_text=_('User role description'),
        unique=True,
        max_length=128
    )

    def __str__(self):
        return self.name


class User(AbstractBaseUser, PermissionsMixin, TimeStampedUUIDModel):
    """
    Custom user model
    """
    email = models.EmailField(
        help_text=_('Email address'),
        unique=True,
        max_length=128
    )
    first_name = models.CharField(
        help_text=_('First name'),
        max_length=128,
        null=True,
        blank=True
    )
    last_name = models.CharField(
        help_text=_('Last name'),
        max_length=128,
        null=True,
        blank=True
    )

    is_staff = models.BooleanField(
        help_text=_('Designates that this user has staff permissions'),
        default=False
    )
    is_active = models.BooleanField(
        help_text=_('Designates that user is active'),
        default=True
    )
    role = models.ForeignKey(
        'accounts.UserRole',
        on_delete=models.SET_NULL,
        help_text=_('User Role'),
        related_name='role_users',
        blank=True,
        null=True
    )
    role_label = models.CharField(
        max_length=128,
        help_text=_('Role label'),
        blank=True,
        null=True
    )
    telegram_id = models.BigIntegerField(
        help_text=_('Telegram id'),
        blank=True,
        null=True,
    )
    parent = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        related_name='childrens',
        limit_choices_to=(
                Q(role__name='team_lead')
                | Q(role__name='hom')
                | Q(role__name='hof')
                | Q(role__name='hocm')
                | Q(role__name='hobd')
                | Q(role__name='business_unit')
        ),
        blank=True,
        null=True
    )
    external = models.BooleanField(default=False)
    history = ProxyAwareHistoricalRecords(inherit=True)

    objects = UserAccountManager()

    USERNAME_FIELD = 'email'

    def __str__(self) -> str:
        return self.full_name

    @property
    def full_name(self) -> str:
        """
        Get user full name using first and last name

        Returns:
            User full name
        """
        first_name = self.first_name if self.first_name is not None else ''
        last_name = self.last_name if self.last_name is not None else ''

        return f'{first_name} {last_name}'.strip()

    def clean(self):
        cleaned_data = super().clean()
        if not self.parent:
            return cleaned_data
        if self.parent.id == self.id:
            raise ValidationError(_('User cannot be a parent of himself'))

    def save(self, *args, **kwargs):
        if not self.role_label and not self.is_superuser:
            if not self.role:
                raise ValidationError(_('Role is required'))

            self.role_label = self.role.description
        super().save(*args, **kwargs)


class UserOperatorProxy(User):
    """
    Operator Proxy model for admin panel
    """
    class Meta:
        proxy = True


class UserTeamLeadProxy(User):
    """
    Team Lead Proxy model for admin panel
    """
    class Meta:
        proxy = True


class UserSeniorOperatorProxy(User):
    """
    Senior Operator Proxy model for admin panel
    """
    class Meta:
        proxy = True


class UserSuperUserProxy(User):
    """
    Super User Proxy model for admin panel
    """
    class Meta:
        proxy = True


class UserMarketerProxy(User):
    """
    Marketer Proxy model for admin panel
    """
    class Meta:
        proxy = True


class UserHOMProxy(User):
    """
    Head of marketing Proxy model for admin panel
    """
    class Meta:
        proxy = True


class UserHOFProxy(User):
    """
    Head of marketing Proxy model for admin panel
    """
    class Meta:
        proxy = True


class UserFinancierProxy(User):
    """
    Financier Proxy model for admin panel
    """
    class Meta:
        proxy = True


class UserSMMProxy(User):
    """
    SMM Specialist Proxy model for admin panel
    """
    class Meta:
        proxy = True


class UserHOCMProxy(User):
    """
    Head of Client Management Proxy model for admin panel
    """
    class Meta:
        proxy = True


class UserClientManagerProxy(User):
    """
    Client Manager Proxy model for admin panel
    """
    class Meta:
        proxy = True


class UserHOBDProxy(User):
    """
    Head of Business Development Proxy model for admin panel
    """
    class Meta:
        proxy = True


class UserBusinessDevProxy(User):
    """
    Business Developer Proxy model for admin panel
    """
    class Meta:
        proxy = True


class UserBusinessUnitProxy(User):
    """
    Business Unit Proxy model for admin panel
    """
    class Meta:
        proxy = True
