from django.contrib import admin
from django.contrib.auth.hashers import make_password
from simple_history.admin import SimpleHistoryAdmin

from accounts.models import (
    User,
    UserBusinessDevProxy,
    UserBusinessUnitProxy,
    UserClientManagerProxy,
    UserFinancierProxy,
    UserHOBDProxy,
    UserHOCMProxy,
    UserHOFProxy,
    UserHOMProxy,
    UserMarketerProxy,
    UserOperatorProxy,
    UserRole,
    UserSeniorOperatorProxy,
    UserSMMProxy,
    UserSuperUserProxy,
    UserTeamLeadProxy,
)
from only_fans_models.admin import OnlyFansModelInline


@admin.register(UserRole)
class UserRoleAdmin(admin.ModelAdmin):
    """
    Admin class for managing User Roles.
    """
    pass


class ChildUserInline(admin.TabularInline):
    """
    Inline admin class for displaying child users in the UserAdmin.
    """
    model = User
    fk_name = 'parent'
    fields = ['email', 'first_name', 'last_name']
    extra = 0
    show_change_link = True

    def has_add_permission(self, request, obj):
        """
        Determines whether the 'Add' action is permitted for child users.

        Args:
        - request: The current request object.
        - obj: The parent User object.

        Returns:
        - bool: True if 'Add' action is permitted, False otherwise.
        """
        return False

    def has_change_permission(self, request, obj=None):
        """
        Determines whether the 'Change' action is permitted for child users.

        Args:
        - request: The current request object.
        - obj: The parent User object.

        Returns:
        - bool: True if 'Change' action is permitted, False otherwise.
        """
        return False


class AbstractUserAdmin(SimpleHistoryAdmin, admin.ModelAdmin):
    """
    Abstract admin class for User model.
    """
    readonly_fields = ('role_name',)
    list_display = ('email', 'first_name', 'last_name', 'role_name')
    search_fields = ('email', 'first_name', 'last_name')
    exclude = ('is_staff', 'is_active', 'groups', 'username', 'user_permissions', 'is_superuser')

    def role_name(self, obj) -> str:
        """
        Returns the string representation of a user's role.

        Args:
        - obj: User object

        Returns:
        - str: String representation of the user's role. Possible values:
            - 'admin': If the user is a superuser
            - Role name: If the user has a role with a name
            - 'no role': If the user has no roles
        """
        if obj.is_superuser:
            return 'admin'
        if hasattr(obj.role, 'name'):
            return obj.role.name
        return 'no role'

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        return queryset.select_related('role')

    def save_model(self, request, obj, form, change):
        """
        Saves the model instance with optional password encryption.

        Args:
            request: The current HTTP request.
            obj: The model instance being saved.
            form: The form used for validation and data input.
            change: A boolean indicating whether the model instance is being changed or created.

        Returns:
            None

        Raises:
            None
        """
        if not obj.pk or 'password' in form.changed_data:
            obj.password = make_password(obj.password)
        super().save_model(request, obj, form, change)


class AbstractUserAdminProxy(AbstractUserAdmin):
    """
    Abstract admin class for User proxy models.
    """
    exclude = (
        'is_staff',
        'is_active',
        'groups',
        'username',
        'user_permissions',
        'is_superuser',
    )


@admin.register(User)
class UserAdmin(AbstractUserAdmin):
    """
    Admin class for managing User model.
    """
    list_filter = ['role__name']


@admin.register(UserOperatorProxy)
class UserOperatorProxyAdmin(AbstractUserAdminProxy):
    """
    Admin class for managing UserOperatorProxy model.
    """

    def get_queryset(self, request):
        """
        Returns the queryset for UserOperatorProxy model.

        Args:
        - request: The current request object.

        Returns:
        - QuerySet: The queryset for UserOperatorProxy model.
        """
        return super().get_queryset(request).filter(role__name='operator')

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """
        Returns the form field for the specified database field.
        """
        if db_field.name == 'parent':
            kwargs['queryset'] = User.objects.filter(role__name='team_lead')

        return super().formfield_for_foreignkey(db_field, request, **kwargs)

    def get_changeform_initial_data(self, request):
        """
        Returns the initial data for the change form.

        Args:
        - request: The current request object.

        Returns:
        - dict: The initial data for the change form.
        """
        initial_data = super().get_changeform_initial_data(request)
        initial_data['role'] = UserRole.objects.get(name='operator')

        return initial_data


@admin.register(UserTeamLeadProxy)
class UserTeamLeadProxyAdmin(AbstractUserAdminProxy):
    """
    Admin class for managing UserTeamLeadProxy model.
    """
    inlines = [ChildUserInline, OnlyFansModelInline]

    def get_queryset(self, request):
        """
        Returns the queryset for UserTeamLeadProxy model.

        Args:
        - request: The current request object.

        Returns:
        - QuerySet: The queryset for UserTeamLeadProxy model.
        """
        return super().get_queryset(request).filter(role__name='team_lead')

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """
        Returns the form field for the specified database field.
        """
        if db_field.name == 'role':
            kwargs['queryset'] = UserRole.objects.filter(name='team_lead')

        if db_field.name == 'parent':
            kwargs['queryset'] = User.objects.filter(role__name='business_unit')

        return super().formfield_for_foreignkey(db_field, request, **kwargs)

    def get_changeform_initial_data(self, request):
        """
        Returns the initial data for the change form.

        Args:
        - request: The current request object.

        Returns:
        - dict: The initial data for the change form.
        """
        initial_data = super().get_changeform_initial_data(request)
        initial_data['role'] = UserRole.objects.get(name='team_lead')

        return initial_data


@admin.register(UserSeniorOperatorProxy)
class UserSeniorOperatorProxyAdmin(AbstractUserAdminProxy):
    """
    Admin class for managing UserSeniorOperatorProxy model.
    """

    def get_queryset(self, request):
        """
        Returns the queryset for UserSeniorOperatorProxy model.

        Args:
        - request: The current request object.

        Returns:
        - QuerySet: The queryset for UserSeniorOperatorProxy model.
        """
        return super().get_queryset(request).filter(role__name='senior_operator')

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """
        Returns the form field for the specified database field.
        """
        if db_field.name == 'parent':
            kwargs['queryset'] = User.objects.filter(role__name='team_lead')

        if db_field.name == 'role':
            kwargs['queryset'] = UserRole.objects.filter(name='senior_operator')

        return super().formfield_for_foreignkey(db_field, request, **kwargs)

    def get_changeform_initial_data(self, request):
        """
        Returns the initial data for the change form.

        Args:
        - request: The current request object.

        Returns:
        - dict: The initial data for the change form.
        """
        initial_data = super().get_changeform_initial_data(request)
        initial_data['role'] = UserRole.objects.get(name='senior_operator')

        return initial_data


@admin.register(UserSuperUserProxy)
class UserSuperUserProxyProxy(AbstractUserAdminProxy):
    """
    Admin class for managing UserSuperUserProxy model.
    """

    def get_queryset(self, request):
        """
        Returns the queryset for UserSuperUserProxy model.

        Args:
        - request: The current request object.

        Returns:
        - QuerySet: The queryset for UserSuperUserProxy model.
        """
        return super().get_queryset(request).filter(role__name='superuser')


@admin.register(UserMarketerProxy)
class UserMarketerProxyAdmin(AbstractUserAdminProxy):
    """
    Admin class for managing UserMarketerProxy model.
    """
    def get_queryset(self, request):
        """
        Returns the queryset for UserMarketerProxy model.

        Args:
        - request: The current request object.

        Returns:
        - QuerySet: The queryset for UserMarketerProxy model.
        """
        return super().get_queryset(request).filter(role__name='marketer')

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """
        Returns the form field for the specified database field.
        """
        if db_field.name == 'parent':
            kwargs['queryset'] = User.objects.filter(role__name='hom')

        if db_field.name == 'role':
            kwargs['queryset'] = UserRole.objects.filter(name='marketer')

        return super().formfield_for_foreignkey(db_field, request, **kwargs)

    def get_changeform_initial_data(self, request):
        """
        Change initial data in fields
        """
        initial_data = super().get_changeform_initial_data(request)
        initial_data['role'] = UserRole.objects.get(name='marketer')

        return initial_data


@admin.register(UserHOMProxy)
class UserHOMProxyAdmin(AbstractUserAdminProxy):
    """
    Admin class for managing UserHOMProxy model.
    """
    def get_queryset(self, request):
        """
        Returns the queryset for UserHOMProxy model.

        Args:
        - request: The current request object.

        Returns:
        - QuerySet: The queryset for UserHOMProxy model.
        """
        return super().get_queryset(request).filter(role__name='hom')

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """
        Returns the form field for the specified database field.
        """
        if db_field.name == 'role':
            kwargs['queryset'] = UserRole.objects.filter(name='hom')

        return super().formfield_for_foreignkey(db_field, request, **kwargs)

    def get_changeform_initial_data(self, request):
        """
        Change initial data in fields
        """
        initial_data = super().get_changeform_initial_data(request)
        initial_data['role'] = UserRole.objects.get(name='hom')

        return initial_data


@admin.register(UserFinancierProxy)
class UserFinancierProxyAdmin(AbstractUserAdminProxy):
    """
    Admin class for managing UserFinancierProxy model.
    """
    def get_queryset(self, request):
        """
        Returns the queryset for UserFinancierProxy model.

        Args:
        - request: The current request object.

        Returns:
        - QuerySet: The queryset for UserFinancierProxy model.
        """
        return super().get_queryset(request).filter(role__name='financier')

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """
        Returns the form field for the specified database field.
        """
        if db_field.name == 'parent':
            kwargs['queryset'] = User.objects.filter(role__name='hof')

        if db_field.name == 'role':
            kwargs['queryset'] = UserRole.objects.filter(name='financier')

        return super().formfield_for_foreignkey(db_field, request, **kwargs)

    def get_changeform_initial_data(self, request):
        """
        Change initial data in fields
        """
        initial_data = super().get_changeform_initial_data(request)
        initial_data['role'] = UserRole.objects.get(name='financier')

        return initial_data


@admin.register(UserHOFProxy)
class UserHOFProxyAdmin(AbstractUserAdminProxy):
    """
    Admin class for managing UserHOFProxy model.
    """
    def get_queryset(self, request):
        """
        Returns the queryset for UserHOFProxy model.

        Args:
        - request: The current request object.

        Returns:
        - QuerySet: The queryset for UserHOFProxy model.
        """
        return super().get_queryset(request).filter(role__name='hof')

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """
        Returns the form field for the specified database field.
        """
        if db_field.name == 'role':
            kwargs['queryset'] = UserRole.objects.filter(name='hof')

        return super().formfield_for_foreignkey(db_field, request, **kwargs)

    def get_changeform_initial_data(self, request):
        """
        Change initial data in fields
        """
        initial_data = super().get_changeform_initial_data(request)
        initial_data['role'] = UserRole.objects.get(name='hof')

        return initial_data


@admin.register(UserSMMProxy)
class UserSMMProxyAdmin(AbstractUserAdminProxy):
    """
    Admin class for managing UserSMMProxy model.
    """
    def get_queryset(self, request):
        """
        Returns the queryset for UserSMMProxy model.

        Args:
        - request: The current request object.

        Returns:
        - QuerySet: The queryset for UserSMMProxy model.
        """
        return super().get_queryset(request).filter(role__name='smm')

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """
        Returns the form field for the specified database field.
        """
        if db_field.name == 'parent':
            kwargs['queryset'] = User.objects.filter(role__name='hom')

        if db_field.name == 'role':
            kwargs['queryset'] = UserRole.objects.filter(name='smm')

        return super().formfield_for_foreignkey(db_field, request, **kwargs)

    def get_changeform_initial_data(self, request):
        """
        Change initial data in fields
        """
        initial_data = super().get_changeform_initial_data(request)
        initial_data['role'] = UserRole.objects.get(name='smm')

        return initial_data


@admin.register(UserHOCMProxy)
class UserHOCMProxyAdmin(AbstractUserAdminProxy):
    """
    Admin class for managing UserHOCMProxy model.
    """
    def get_queryset(self, request):
        """
        Returns the queryset for UserHOCMProxy model.

        Args:
        - request: The current request object.

        Returns:
        - QuerySet: The queryset for UserHOCMProxy model.
        """
        return super().get_queryset(request).filter(role__name='hocm')

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """
        Returns the form field for the specified database field.
        """
        if db_field.name == 'role':
            kwargs['queryset'] = UserRole.objects.filter(name='hocm')

        return super().formfield_for_foreignkey(db_field, request, **kwargs)

    def get_changeform_initial_data(self, request):
        """
        Change initial data in fields
        """
        initial_data = super().get_changeform_initial_data(request)
        initial_data['role'] = UserRole.objects.get(name='hocm')

        return initial_data


@admin.register(UserClientManagerProxy)
class UserClientManagerProxyAdmin(AbstractUserAdminProxy):
    """
    Admin class for managing UserClientManagerProxy model.
    """
    def get_queryset(self, request):
        """
        Returns the queryset for UserClientManagerProxy model.

        Args:
        - request: The current request object.

        Returns:
        - QuerySet: The queryset for UserClientManagerProxy model.
        """
        return super().get_queryset(request).filter(role__name='client_manager')

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """
        Returns the form field for the specified database field.
        """
        if db_field.name == 'parent':
            kwargs['queryset'] = User.objects.filter(role__name='hocm')

        if db_field.name == 'role':
            kwargs['queryset'] = UserRole.objects.filter(name='client_manager')

        return super().formfield_for_foreignkey(db_field, request, **kwargs)

    def get_changeform_initial_data(self, request):
        """
        Change initial data in fields
        """
        initial_data = super().get_changeform_initial_data(request)
        initial_data['role'] = UserRole.objects.get(name='client_manager')

        return initial_data


@admin.register(UserHOBDProxy)
class UserHOBDProxy(AbstractUserAdminProxy):
    """
    Admin class for managing UserHOBDProxy model.
    """
    def get_queryset(self, request):
        """
        Returns the queryset for UserHOBDProxy model.

        Args:
        - request: The current request object.

        Returns:
        - QuerySet: The queryset for UserHOBDProxy model.
        """
        return super().get_queryset(request).filter(role__name='hobd')

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """
        Returns the form field for the specified database field.
        """
        if db_field.name == 'role':
            kwargs['queryset'] = UserRole.objects.filter(name='hobd')

        return super().formfield_for_foreignkey(db_field, request, **kwargs)

    def get_changeform_initial_data(self, request):
        """
        Change initial data in fields
        """
        initial_data = super().get_changeform_initial_data(request)
        initial_data['role'] = UserRole.objects.get(name='hobd')

        return initial_data


@admin.register(UserBusinessDevProxy)
class UserBusinessDevProxyProxyAdmin(AbstractUserAdminProxy):
    """
    Admin class for managing BusinessDevProxy model.
    """
    def get_queryset(self, request):
        """
        Returns the queryset for BusinessDevProxy model.

        Args:
        - request: The current request object.

        Returns:
        - QuerySet: The queryset for UBusinessDevProxy model.
        """
        return super().get_queryset(request).filter(role__name='business_dev')

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """
        Returns the form field for the specified database field.
        """
        if db_field.name == 'parent':
            kwargs['queryset'] = User.objects.filter(role__name='hobd')

        if db_field.name == 'role':
            kwargs['queryset'] = UserRole.objects.filter(name='business_dev')

        return super().formfield_for_foreignkey(db_field, request, **kwargs)

    def get_changeform_initial_data(self, request):
        """
        Change initial data in fields
        """
        initial_data = super().get_changeform_initial_data(request)
        initial_data['role'] = UserRole.objects.get(name='business_dev')

        return initial_data


@admin.register(UserBusinessUnitProxy)
class UserBusinessUnitProxyAdmin(AbstractUserAdminProxy):
    """
    Admin class for managing UserBusinessUnitProxy model.
    """
    def get_queryset(self, request):
        """
        Returns the queryset for UserBusinessUnitProxy model.

        Args:
        - request: The current request object.

        Returns:
        - QuerySet: The queryset for UserBusinessUnitProxy model.
        """
        return super().get_queryset(request).filter(role__name='business_unit')

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """
        Returns the form field for the specified database field.
        """
        if db_field.name == 'role':
            kwargs['queryset'] = UserRole.objects.filter(name='business_unit')

        return super().formfield_for_foreignkey(db_field, request, **kwargs)

    def get_changeform_initial_data(self, request):
        """
        Change initial data in fields
        """
        initial_data = super().get_changeform_initial_data(request)
        initial_data['role'] = UserRole.objects.get(name='business_unit')

        return initial_data
