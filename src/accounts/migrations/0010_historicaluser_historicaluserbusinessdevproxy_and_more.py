# Generated by Django 4.2.2 on 2025-05-20 14:13

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import simple_history.models
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("accounts", "0009_userbusinessdevproxy_userhobdproxy_alter_user_parent"),
    ]

    operations = [
        migrations.CreateModel(
            name="HistoricalUser",
            fields=[
                ("password", models.CharField(max_length=128, verbose_name="password")),
                (
                    "last_login",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="last login"
                    ),
                ),
                (
                    "is_superuser",
                    models.BooleanField(
                        default=False,
                        help_text="Designates that this user has all permissions without explicitly assigning them.",
                        verbose_name="superuser status",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(blank=True, editable=False, null=True),
                ),
                (
                    "id",
                    models.UUIDField(db_index=True, default=uuid.uuid4, editable=False),
                ),
                (
                    "email",
                    models.EmailField(
                        db_index=True, help_text="Email address", max_length=128
                    ),
                ),
                (
                    "first_name",
                    models.CharField(
                        blank=True, help_text="First name", max_length=128, null=True
                    ),
                ),
                (
                    "last_name",
                    models.CharField(
                        blank=True, help_text="Last name", max_length=128, null=True
                    ),
                ),
                (
                    "is_staff",
                    models.BooleanField(
                        default=False,
                        help_text="Designates that this user has staff permissions",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True, help_text="Designates that user is active"
                    ),
                ),
                (
                    "role_label",
                    models.CharField(
                        blank=True, help_text="Role label", max_length=128, null=True
                    ),
                ),
                (
                    "telegram_id",
                    models.BigIntegerField(
                        blank=True, help_text="Telegram id", null=True
                    ),
                ),
                ("external", models.BooleanField(default=False)),
                ("history_id", models.AutoField(primary_key=True, serialize=False)),
                ("history_date", models.DateTimeField(db_index=True)),
                ("history_change_reason", models.CharField(max_length=100, null=True)),
                (
                    "history_type",
                    models.CharField(
                        choices=[("+", "Created"), ("~", "Changed"), ("-", "Deleted")],
                        max_length=1,
                    ),
                ),
                (
                    "history_user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "parent",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        limit_choices_to=models.Q(
                            ("role__name", "team_lead"),
                            ("role__name", "hom"),
                            ("role__name", "hof"),
                            ("role__name", "hocm"),
                            ("role__name", "hobd"),
                            _connector="OR",
                        ),
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "role",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        help_text="User Role",
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="accounts.userrole",
                    ),
                ),
            ],
            options={
                "verbose_name": "historical user",
                "verbose_name_plural": "historical users",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name="HistoricalUserBusinessDevProxy",
            fields=[],
            options={
                "verbose_name": "historical user business dev proxy",
                "verbose_name_plural": "historical user business dev proxys",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
                "proxy": True,
                "indexes": [],
                "constraints": [],
            },
            bases=("accounts.historicaluser",),
        ),
        migrations.CreateModel(
            name="HistoricalUserClientManagerProxy",
            fields=[],
            options={
                "verbose_name": "historical user client manager proxy",
                "verbose_name_plural": "historical user client manager proxys",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
                "proxy": True,
                "indexes": [],
                "constraints": [],
            },
            bases=("accounts.historicaluser",),
        ),
        migrations.CreateModel(
            name="HistoricalUserFinancierProxy",
            fields=[],
            options={
                "verbose_name": "historical user financier proxy",
                "verbose_name_plural": "historical user financier proxys",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
                "proxy": True,
                "indexes": [],
                "constraints": [],
            },
            bases=("accounts.historicaluser",),
        ),
        migrations.CreateModel(
            name="HistoricalUserHOBDProxy",
            fields=[],
            options={
                "verbose_name": "historical user hobd proxy",
                "verbose_name_plural": "historical user hobd proxys",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
                "proxy": True,
                "indexes": [],
                "constraints": [],
            },
            bases=("accounts.historicaluser",),
        ),
        migrations.CreateModel(
            name="HistoricalUserHOCMProxy",
            fields=[],
            options={
                "verbose_name": "historical user hocm proxy",
                "verbose_name_plural": "historical user hocm proxys",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
                "proxy": True,
                "indexes": [],
                "constraints": [],
            },
            bases=("accounts.historicaluser",),
        ),
        migrations.CreateModel(
            name="HistoricalUserHOFProxy",
            fields=[],
            options={
                "verbose_name": "historical user hof proxy",
                "verbose_name_plural": "historical user hof proxys",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
                "proxy": True,
                "indexes": [],
                "constraints": [],
            },
            bases=("accounts.historicaluser",),
        ),
        migrations.CreateModel(
            name="HistoricalUserHOMProxy",
            fields=[],
            options={
                "verbose_name": "historical user hom proxy",
                "verbose_name_plural": "historical user hom proxys",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
                "proxy": True,
                "indexes": [],
                "constraints": [],
            },
            bases=("accounts.historicaluser",),
        ),
        migrations.CreateModel(
            name="HistoricalUserMarketerProxy",
            fields=[],
            options={
                "verbose_name": "historical user marketer proxy",
                "verbose_name_plural": "historical user marketer proxys",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
                "proxy": True,
                "indexes": [],
                "constraints": [],
            },
            bases=("accounts.historicaluser",),
        ),
        migrations.CreateModel(
            name="HistoricalUserOperatorProxy",
            fields=[],
            options={
                "verbose_name": "historical user operator proxy",
                "verbose_name_plural": "historical user operator proxys",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
                "proxy": True,
                "indexes": [],
                "constraints": [],
            },
            bases=("accounts.historicaluser",),
        ),
        migrations.CreateModel(
            name="HistoricalUserSeniorOperatorProxy",
            fields=[],
            options={
                "verbose_name": "historical user senior operator proxy",
                "verbose_name_plural": "historical user senior operator proxys",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
                "proxy": True,
                "indexes": [],
                "constraints": [],
            },
            bases=("accounts.historicaluser",),
        ),
        migrations.CreateModel(
            name="HistoricalUserSMMProxy",
            fields=[],
            options={
                "verbose_name": "historical user smm proxy",
                "verbose_name_plural": "historical user smm proxys",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
                "proxy": True,
                "indexes": [],
                "constraints": [],
            },
            bases=("accounts.historicaluser",),
        ),
        migrations.CreateModel(
            name="HistoricalUserSuperUserProxy",
            fields=[],
            options={
                "verbose_name": "historical user super user proxy",
                "verbose_name_plural": "historical user super user proxys",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
                "proxy": True,
                "indexes": [],
                "constraints": [],
            },
            bases=("accounts.historicaluser",),
        ),
        migrations.CreateModel(
            name="HistoricalUserTeamLeadProxy",
            fields=[],
            options={
                "verbose_name": "historical user team lead proxy",
                "verbose_name_plural": "historical user team lead proxys",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
                "proxy": True,
                "indexes": [],
                "constraints": [],
            },
            bases=("accounts.historicaluser",),
        ),
    ]
