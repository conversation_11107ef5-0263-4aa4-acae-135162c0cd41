# Generated by Django 4.2.2 on 2023-06-18 12:21

import uuid

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models

import base.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserRole',
            fields=[
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, editable=False)),
                ('modified_at', models.DateTimeField(auto_now=True, null=True)),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(help_text='User role name', max_length=128, unique=True)),
                ('description', models.CharField(help_text='User role description', max_length=128, unique=True)),
            ],
            options={
                'abstract': False,
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.CreateModel(
            name='User',
            fields=[
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, editable=False)),
                ('modified_at', models.DateTimeField(auto_now=True, null=True)),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('email', models.EmailField(help_text='Email address', max_length=128, unique=True)),
                ('first_name', models.CharField(blank=True, help_text='First name', max_length=128, null=True)),
                ('last_name', models.CharField(blank=True, help_text='Last name', max_length=128, null=True)),
                ('is_staff', models.BooleanField(default=False, help_text='Designates that this user has staff permissions')),
                ('is_active', models.BooleanField(default=True, help_text='Designates that user is active')),
                ('role_label', models.CharField(blank=True, help_text='Role label', null=True)),
                ('telegram_id', models.BigIntegerField(blank=True, help_text='Telegram id', null=True)),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('parent', models.ForeignKey(blank=True, limit_choices_to={'role__name': 'team_lead'}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='childrens', to=settings.AUTH_USER_MODEL)),
                ('role', models.ForeignKey(blank=True, help_text='User Role', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='role_users', to='accounts.userrole')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'abstract': False,
            },
            bases=(base.models.CachedPropertyMixin, models.Model),
        ),
        migrations.CreateModel(
            name='UserOperatorProxy',
            fields=[
            ],
            options={
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('accounts.user',),
        ),
        migrations.CreateModel(
            name='UserSeniorOperatorProxy',
            fields=[
            ],
            options={
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('accounts.user',),
        ),
        migrations.CreateModel(
            name='UserSuperUserProxy',
            fields=[
            ],
            options={
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('accounts.user',),
        ),
        migrations.CreateModel(
            name='UserTeamLeadProxy',
            fields=[
            ],
            options={
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('accounts.user',),
        ),
    ]
