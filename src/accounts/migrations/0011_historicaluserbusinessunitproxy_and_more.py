# Generated by Django 4.2.2 on 2025-06-11 17:24

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("accounts", "0010_historicaluser_historicaluserbusinessdevproxy_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="HistoricalUserBusinessUnitProxy",
            fields=[],
            options={
                "verbose_name": "historical user business unit proxy",
                "verbose_name_plural": "historical user business unit proxys",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
                "proxy": True,
                "indexes": [],
                "constraints": [],
            },
            bases=("accounts.historicaluser",),
        ),
        migrations.CreateModel(
            name="UserBusinessUnitProxy",
            fields=[],
            options={
                "proxy": True,
                "indexes": [],
                "constraints": [],
            },
            bases=("accounts.user",),
        ),
        migrations.AlterField(
            model_name="historicaluser",
            name="parent",
            field=models.ForeignKey(
                blank=True,
                db_constraint=False,
                limit_choices_to=models.Q(
                    ("role__name", "team_lead"),
                    ("role__name", "hom"),
                    ("role__name", "hof"),
                    ("role__name", "hocm"),
                    ("role__name", "hobd"),
                    ("role__name", "business_unit"),
                    _connector="OR",
                ),
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="user",
            name="parent",
            field=models.ForeignKey(
                blank=True,
                limit_choices_to=models.Q(
                    ("role__name", "team_lead"),
                    ("role__name", "hom"),
                    ("role__name", "hof"),
                    ("role__name", "hocm"),
                    ("role__name", "hobd"),
                    ("role__name", "business_unit"),
                    _connector="OR",
                ),
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="childrens",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
