# Generated by Django 4.2.2 on 2024-04-04 16:19

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("accounts", "0007_usersmmproxy"),
    ]

    operations = [
        migrations.CreateModel(
            name="UserClientManagerProxy",
            fields=[],
            options={
                "proxy": True,
                "indexes": [],
                "constraints": [],
            },
            bases=("accounts.user",),
        ),
        migrations.CreateModel(
            name="UserHOCMProxy",
            fields=[],
            options={
                "proxy": True,
                "indexes": [],
                "constraints": [],
            },
            bases=("accounts.user",),
        ),
        migrations.AlterField(
            model_name="user",
            name="parent",
            field=models.ForeignKey(
                blank=True,
                limit_choices_to=models.Q(
                    ("role__name", "team_lead"),
                    ("role__name", "hom"),
                    ("role__name", "hof"),
                    ("role__name", "hocm"),
                    _connector="OR",
                ),
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="childrens",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
