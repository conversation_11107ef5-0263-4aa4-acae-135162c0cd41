# Generated by Django 4.2.2 on 2023-09-08 12:14

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("accounts", "0003_alter_user_role_label"),
    ]

    operations = [
        migrations.CreateModel(
            name="UserHOMProxy",
            fields=[],
            options={
                "proxy": True,
                "indexes": [],
                "constraints": [],
            },
            bases=("accounts.user",),
        ),
        migrations.CreateModel(
            name="UserMarketerProxy",
            fields=[],
            options={
                "proxy": True,
                "indexes": [],
                "constraints": [],
            },
            bases=("accounts.user",),
        ),
        migrations.AlterField(
            model_name="user",
            name="parent",
            field=models.ForeignKey(
                blank=True,
                limit_choices_to=models.Q(
                    ("role__name", "team_lead"), ("role__name", "hom"), _connector="OR"
                ),
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="childrens",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
