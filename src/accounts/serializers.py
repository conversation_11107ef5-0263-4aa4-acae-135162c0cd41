from django.conf import settings
from django.db import transaction
from rest_framework import serializers

from accounts.models import User, UserRole
from accounts.services import (
    ClientManagerService,
    MarketerService,
    OperatorService,
)
from accounts.validators import OperatorValidator
from only_fans_models.models import OnlyFansModel
from only_fans_models.serializers import OnlyFansModelsSerializer
from shifts.serializers import ShiftNumberSerializer


class NestedUserSerializer(serializers.ModelSerializer):
    """
    Serializer for the User model with nested relationship fields.
    """

    role = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ['id', 'email', 'full_name', 'role']

    def get_role(self, obj):
        """
        Retrieve the name of the user's role.
        """
        if obj.role:
            return obj.role.name


class UserSerializer(NestedUserSerializer):
    """
    Serializer for the User model with nested relationship fields for parent and children.
    """

    parent = NestedUserSerializer()
    time_zone = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ['id', 'email', 'full_name', 'parent', 'childrens', 'role', 'time_zone', 'role_label']

    def get_time_zone(self, instance: User) -> str:
        """
        Get the time zone associated with the given instance.

        Parameters:
            instance: The instance for which the time zone is retrieved.

        Returns:
            str: The time zone string in the format of 'America/New_York'.
        """
        return settings.TIME_ZONE


class PureUserSerializer(serializers.ModelSerializer):
    """
    Pure Serializer for the User model.
    """

    class Meta:
        model = User
        fields = ['id', 'full_name']


class TeamLeadSerializer(serializers.ModelSerializer):
    """
    Serializer for the TeamLead model.
    """
    shift_numbers = ShiftNumberSerializer(many=True, read_only=True)
    operators = serializers.SerializerMethodField()
    models = OnlyFansModelsSerializer(many=True, read_only=True, source='only_fans_models')
    is_owner = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ['id', 'full_name', 'operators', 'models', 'is_owner', 'shift_numbers']

    def get_is_owner(self, obj) -> bool:
        """
        Method to determine if the requesting user is the owner of the TeamLead object.

        Args:
            obj: The TeamLead object being serialized.

        Returns:
            bool: True if the requesting user is the owner, False otherwise.
        """
        user = self.context['request'].user
        if user.role is None:
            return False
        if user == obj:
            return True
        if user.role.name == 'senior_operator' and user.parent == obj:
            return True
        return False

    def get_operators(self, obj):
        """
        Method to get all operators under a team lead.

        Args:
            obj: The TeamLead object being serialized.

        Returns:
            list: Serialized data of operator users.
        """
        operators = obj.childrens.filter(role__name='operator')

        return PureUserSerializer(operators, many=True).data


class UserNickNameSerializer(serializers.ModelSerializer):
    """
    Serializer for the User model for third party data
    """

    class Meta:
        model = User
        fields = ['full_name', 'telegram_id']


class TeamLeadNicknameSerializer(serializers.ModelSerializer):
    """
    Serializer for the TeamLead model for third party data
    """
    senior_operators = UserNickNameSerializer(many=True, read_only=True, source='childrens')

    class Meta:
        model = User
        fields = ['full_name', 'telegram_id', 'senior_operators']


class TelegramUserSerializer(serializers.ModelSerializer):
    """
    Serializer for the User model with nested relationship fields for parent and children.
    """
    role_name = serializers.CharField(source='role.name', allow_null=True)

    class Meta:
        model = User
        fields = ['full_name', 'telegram_id', 'role_name']
        read_only_fields = fields


# --------------------------------------------Operators Serializers---------------------------------------------------
class OperatorListSerializer(serializers.ModelSerializer):
    """
    Serializer for the list of operators.
    """
    parent = NestedUserSerializer()
    role = serializers.StringRelatedField()

    class Meta:
        model = User
        fields = [
            'id',
            'first_name',
            'last_name',
            'email',
            'is_active',
            'parent',
            'role_label',
            'telegram_id',
            'role',
            'full_name'
        ]


class OperatorCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating an operator.
    """
    parent = serializers.PrimaryKeyRelatedField(
        required=False,
        queryset=User.objects.filter(role__name='team_lead')
    )

    class Meta:
        model = User
        fields = [
            'email',
            'password',
            'first_name',
            'last_name',
            'telegram_id',
            'parent'
        ]
        extra_kwargs = {
            'password': {'write_only': True}
        }

    def validate(self, attrs):
        """
        Validate the data for creating an operator.

        Args:
            attrs: The data to validate.
        """
        OperatorValidator(self.instance).validate_create_operator(attrs, self.context['request'])

        return attrs

    def create(self, validated_data):
        """
        Create an operator.

        Args:
            validated_data: The validated data to create an operator.
        """
        return OperatorService.create(validated_data, request=self.context['request'])


class OperatorUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for updating an operator.
    """
    role = serializers.SlugRelatedField(
        required=False,
        slug_field="name",
        queryset=UserRole.objects.filter(name__in=['operator', 'senior_operator'])
    )
    parent = serializers.PrimaryKeyRelatedField(
        required=False,
        queryset=User.objects.filter(role__name='team_lead')
    )
    password = serializers.CharField(required=False, write_only=True)

    class Meta:
        model = User
        fields = [
            'password',
            'email',
            'role',
            'telegram_id',
            'role_label',
            'parent',
            'is_active',
            'first_name',
            'last_name'
        ]
        extra_kwargs = {
            'password': {'write_only': True}
        }

    def validate(self, attrs):
        """
        Validate the data for updating an operator.

        Args:
            attrs: The data to validate.
        """
        OperatorValidator(self.instance).validate_update_operator(attrs)

        return attrs

    def update(self, instance, validated_data):
        """
        Update an operator.

        Args:
            instance: The operator instance to update.
            validated_data: The validated data to update the operator.
        """
        with transaction.atomic():
            OperatorService(instance).update(validated_data)

            return super().update(instance, validated_data)


# --------------------------------------------Marketers Serializers---------------------------------------------------
class MarketerListSerializer(serializers.ModelSerializer):
    """
    Serializer for the list of marketers.
    """
    role = serializers.StringRelatedField()
    only_fans_models = OnlyFansModelsSerializer(
        many=True,
        read_only=True,
        source='marketer_models'
    )
    parent = PureUserSerializer()

    class Meta:
        model = User
        fields = [
            'id',
            'first_name',
            'last_name',
            'full_name',
            'email',
            'is_active',
            'role_label',
            'telegram_id',
            'role',
            'only_fans_models',
            'parent'
        ]


class MarketerCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating a Marketer.
    """
    only_fans_models = serializers.PrimaryKeyRelatedField(
        required=False,
        queryset=OnlyFansModel.objects.all(),
        many=True,
        allow_empty=True
    )
    password = serializers.CharField(write_only=True, min_length=6)
    parent = serializers.PrimaryKeyRelatedField(
        required=False,
        queryset=User.objects.filter(role__name='hom'),
        allow_null=True,
    )

    class Meta:
        model = User
        fields = [
            'email',
            'password',
            'first_name',
            'last_name',
            'telegram_id',
            'only_fans_models',
            'is_active',
            'parent'
        ]

    def validate_only_fans_models(self, value):
        if isinstance(value, list):
            for model in value:
                if model.marketer is not None and model.marketer != self.instance:
                    raise serializers.ValidationError(
                        {
                            'only_fans_models': (
                                f"Model {model.nickname} is already assigned to another marketer: {model.marketer}"
                            )
                        }
                    )

        return value

    def create(self, validated_data):
        if not validated_data.get('parent'):
            validated_data['parent'] = self.context['request'].user

        return MarketerService.create(validated_data)


class MarketerUpdateSerializer(MarketerCreateSerializer):
    """
    Serializer for updating the Marketer.
    """
    password = serializers.CharField(required=False, write_only=True, min_length=6)

    def update(self, instance, validated_data):
        return MarketerService(instance).update(validated_data)


# --------------------------------------Client Manager Serializers--------------------------------------------


class ClientManagerListSerializer(MarketerListSerializer):
    """
    Serializer for the list of client managers.
    """
    only_fans_models = OnlyFansModelsSerializer(
        many=True,
        read_only=True,
        source='client_manager_models'
    )


class ClientManagerCreateSerializer(MarketerCreateSerializer):
    """
    Serializer for creating a client manager.
    """
    def validate_only_fans_models(self, value):
        errors = []
        if isinstance(value, list):
            for model in value:
                if model.client_manager is not None and model.client_manager != self.instance:
                    errors.append(
                        f"Model {model.nickname} is already assigned to another client manager: {model.client_manager}"
                    )

        if errors:
            raise serializers.ValidationError(errors)

        return value

    def create(self, validated_data):
        return ClientManagerService.create(validated_data)


class ClientManagerUpdateSerializer(ClientManagerCreateSerializer):
    """
    Serializer for updating the client manager.
    """
    password = serializers.CharField(required=False, write_only=True)

    def update(self, instance, validated_data):
        return ClientManagerService(instance).update(validated_data)
