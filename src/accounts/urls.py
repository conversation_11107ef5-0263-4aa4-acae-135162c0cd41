from rest_framework import routers

from accounts.views import (
    BusinessUnitViewSet,
    ClientManagerViewSet,
    HOMViewSet,
    MarketerViewSet,
    OperatorViewSet,
    TeamLeadViewSet,
    UsersViewSet,
)

router = routers.DefaultRouter()
router.register(r'users', UsersViewSet, basename='users')
router.register(r'operators', OperatorViewSet, basename='operators')
router.register(r'marketers', MarketerViewSet, basename='marketers')
router.register(r'client-managers', ClientManagerViewSet, basename='client-managers')
router.register(r'business-units', BusinessUnitViewSet, basename='business-units')
router.register(r'team-leads', TeamLeadViewSet, basename='team-leads')
router.register(r'heads-of-marketing', HOMViewSet, basename='homs')

urlpatterns = router.urls
