import datetime

from django.contrib.auth import get_user_model
from django.db.models import Q
from django.utils.translation import gettext_lazy as _
from rest_framework import serializers
from rest_framework.request import Request

from base.validators import SerializerAttrsValidator


class OperatorValidator:
    """
    Operator Validator
    """
    def __init__(self, instance: get_user_model()) -> None:
        self.instance = instance

    def validate_operator_is_not_busy_for_shift(
            self,
            start_time: datetime.datetime,
            end_time: datetime.datetime
    ) -> None:
        """
        Validate that operator is not busy in shift time range

        Args:
            start_time (datetime.datetime): shift start time
            end_time (datetime.datetime): shift end time
        """
        if self.instance.operator_shifts.filter(
            (Q(shift_start__gte=start_time) & Q(shift_start__lt=end_time)) |
            (Q(shift_end__gt=start_time) & Q(shift_end__lte=end_time)),
            ~Q(status='end')
        ).exists():
            raise serializers.ValidationError({
                'operator':
                _(f'Operator {self.instance} is busy in this shift time range {start_time} - {end_time}')
            })

    def validate_operator_without_progress_shifts(self) -> None:
        """
        Validate that operator does not have progress shifts
        """
        if self.instance.operator_shifts.filter(status='progress').exists():
            raise serializers.ValidationError({
                'operator': _(f'Operator {self.instance} already has shift in progress')
            })

    @classmethod
    def validate_create_operator(
            cls,
            attrs: dict,
            request: Request
    ) -> dict:
        """
        Validate operator can be created

        Args:
            attrs (dict): validated data
            request (Request): request object
        """
        SerializerAttrsValidator(attrs).validate_len_of_string("password", min_len=6)

        if request.user.role.name == 'superuser' and not attrs.get('parent'):
            raise serializers.ValidationError({
                'parent': _('This field is required')
            })

        return attrs

    def validate_update_operator(
            self,
            attrs: dict
    ) -> dict:
        """
        Validate operator can be updated

        Args:
            attrs (dict): validated data
        """
        if attrs.get('password'):
            SerializerAttrsValidator(attrs).validate_len_of_string("password", min_len=6)

        return attrs
