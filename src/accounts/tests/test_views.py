import datetime

from django.urls import reverse
from rest_framework import status

from accounts.models import User
from base.tests.mixins import BaseCRMTest
from only_fans_models.models import OnlyFansModel


class UserApiTestCase(BaseCRMTest):
    """
    Test case for testing user API endpoints.
    """

    def test_user_me_login(self):
        response = self.client.get(reverse('users-me'))
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_user_me_logout(self):
        self.client.logout()
        response = self.client.get(reverse('users-me'))
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_user_my_group_operator(self):
        self.client.force_authenticate(user=self.user_operator)
        response = self.client.get(reverse('users-my-group'))
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_user_my_group_logout(self):
        self.client.logout()
        response = self.client.get(reverse('users-my-group'))
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_user_my_group_superuser(self):
        self.client.force_authenticate(user=self.user_superuser)
        response = self.client.get(reverse('users-my-group'))
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_user_my_group_team_lead(self):
        self.client.force_authenticate(user=self.user_team_lead)
        response = self.client.get(reverse('users-my-group'))
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_user_my_group_senior_operator(self):
        self.client.force_authenticate(user=self.user_senior_operator)
        response = self.client.get(reverse('users-my-group'))
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_user_my_group_cache(self):
        self.client.force_authenticate(user=self.user_team_lead)
        cache_key = f'my_group_{self.user_team_lead.id}_'
        self.assertIsNone(self.cache_service.get_cache_data(cache_key))
        response = self.client.get(reverse('users-my-group'))
        self.assertIsNotNone(self.cache_service.get_cache_data(cache_key))
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIsNotNone(self.cache_service.get_cache_data(cache_key))

    def test_signal_invalidate_cache_my_group(self):
        self.client.force_authenticate(user=self.user_team_lead)
        cache_key = f'my_group_{self.user_team_lead.id}_'
        self.client.get(reverse('users-my-group'))
        self.assertIsNotNone(self.cache_service.get_cache_data(cache_key))
        new_user = User.objects.create(
            email='<EMAIL>',
            first_name='first',
            last_name='last',
            role=self.operator_role
        )
        self.assertIsNone(self.cache_service.get_cache_data(cache_key))
        self.client.get(reverse('users-my-group'))
        self.assertIsNotNone(self.cache_service.get_cache_data(cache_key))
        new_user.delete()
        self.assertIsNone(self.cache_service.get_cache_data(cache_key))
        self.client.get(reverse('users-my-group'))
        self.assertIsNotNone(self.cache_service.get_cache_data(cache_key))
        model = OnlyFansModel.objects.create(
            model_id=343434,
            nickname='test',
            username_of='test2',
        )
        self.assertIsNone(self.cache_service.get_cache_data(cache_key))
        self.client.get(reverse('users-my-group'))
        self.assertIsNotNone(self.cache_service.get_cache_data(cache_key))
        model.delete()
        self.assertIsNone(self.cache_service.get_cache_data(cache_key))
        date_time_start = datetime.datetime.now()
        date_time_end = datetime.datetime.now() + datetime.timedelta(hours=1)
        self.client.get(reverse('users-my-group'))
        self.assertIsNotNone(self.cache_service.get_cache_data(cache_key))
        shift_number_instance = self.create_shift_number(
            shift_number=1,
            time_start=date_time_start.time(),
            time_end=date_time_end.time(),
        )
        self.assertIsNone(self.cache_service.get_cache_data(cache_key))
        self.client.get(reverse('users-my-group'))
        self.assertIsNotNone(self.cache_service.get_cache_data(cache_key))
        shift_number_instance.team_leads.add(self.user_team_lead)
        self.assertIsNone(self.cache_service.get_cache_data(cache_key))

    def test_operator_list_permission(self):
        self.client.force_authenticate(user=self.user_operator)
        response = self.client.get(reverse('operators-list'))
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        for user in [self.user_team_lead, self.user_senior_operator, self.user_superuser]:
            self.client.force_authenticate(user=user)
            response = self.client.get(reverse('operators-list'))
            self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_operator_can_be_created(self):
        self.client.force_authenticate(user=self.user_team_lead)
        data = {
            'email': '<EMAIL>',
            'first_name': 'test_first_name',
            'last_name': 'test_last_name',
            'password': 'test_password',
        }
        response = self.client.post(reverse('operators-list'), data=data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

    def test_operator_cannot_be_created_with_short_password(self):
        self.client.force_authenticate(user=self.user_team_lead)
        data = {
            'email': self.user_operator.email,
            'first_name': 'test_first_name',
            'last_name': 'test_last_name',
            'password': '1234'
        }
        response = self.client.post(reverse('operators-list'), data=data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_operator_can_be_deleted(self):
        self.client.force_authenticate(user=self.user_team_lead)
        response = self.client.delete(reverse('operators-detail', args=(self.user_operator.id,)))
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

    def test_operator_can_be_updated(self):
        self.client.force_authenticate(user=self.user_team_lead)
        new_team_lead = User.objects.create(
            email='<EMAIL>',
            first_name='teamlead',
            last_name='teamleadovich',
            role=self.team_lead_role
        )

        data = {
            'email': '<EMAIL>',
            'role_label': 'operator_new_lable',
            'role': 'senior_operator',
            'telegram_id': 88888888888,
            'password': 'changed_password',
            'parent': new_team_lead.id,
        }
        response = self.client.patch(reverse('operators-detail', args=(self.user_operator.id,)), data=data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['email'], data['email'])
        self.assertEqual(response.data['role_label'], data['role_label'])
        self.assertEqual(response.data['telegram_id'], data['telegram_id'])
        self.assertEqual(response.data['parent']['id'], str(data['parent']))
        self.assertEqual(response.data['role'], data['role'])

        self.client.logout()
        response = self.client.login(email=data['email'], password=data['password'])
        self.assertTrue(response)

    def test_user_team_leads(self):
        self.client.force_authenticate(user=self.user_superuser)
        response = self.client.get(reverse('users-team-leads'))
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_user_team_leads_team_lead(self):
        self.client.force_authenticate(user=self.user_team_lead)
        response = self.client.get(reverse('users-team-leads'))
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_user_team_leads_operator(self):
        self.client.force_authenticate(user=self.user_operator)
        response = self.client.get(reverse('users-team-leads'))
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_user_team_leads_so(self):
        self.client.force_authenticate(user=self.user_senior_operator)
        response = self.client.get(reverse('users-team-leads'))
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_user_team_leads_un_login(self):
        self.client.logout()
        response = self.client.get(reverse('users-team-leads'))
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_operator_first_name_last_name_can_be_updated(self):
        data = {
            'first_name': 'changed_first_name',
            'last_name': 'changed_last_name',
        }
        self.client.force_authenticate(user=self.user_team_lead)
        response = self.client.patch(reverse('operators-detail', args=(self.user_operator.id,)), data=data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['first_name'], data['first_name'])
        self.assertEqual(response.data['last_name'], data['last_name'])

    def test_marketer_list_permissions(self):
        for user in [self.user_team_lead, self.user_senior_operator, self.user_operator]:
            self.client.force_authenticate(user=user)
            response = self.client.get(reverse('marketers-list'))
            self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        for user in [self.user_marketer, self.user_hom, self.user_superuser]:
            self.client.force_authenticate(user=user)
            response = self.client.get(reverse('marketers-list'))
            self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_user_team_leads_cache(self):
        self.client.force_authenticate(user=self.user_superuser)
        cache_key = f'team-leads_{self.user_superuser.id}_'
        self.assertIsNone(self.cache_service.get_cache_data(cache_key))
        response = self.client.get(reverse('users-team-leads'))
        self.assertIsNotNone(self.cache_service.get_cache_data(cache_key))
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_signal_invalidate_cache_team_leads(self):
        self.client.force_authenticate(user=self.user_superuser)
        cache_key = f'team-leads_{self.user_superuser.id}_'
        self.client.get(reverse('users-team-leads'))
        self.assertIsNotNone(self.cache_service.get_cache_data(cache_key))
        new_user = User.objects.create(
            email='<EMAIL>',
            first_name='first',
            last_name='last',
            role=self.team_lead_role
        )
        self.assertIsNone(self.cache_service.get_cache_data(cache_key))
        self.client.get(reverse('users-team-leads'))
        self.assertIsNotNone(self.cache_service.get_cache_data(cache_key))
        new_user.delete()
        self.assertIsNone(self.cache_service.get_cache_data(cache_key))
        self.client.get(reverse('users-team-leads'))
        self.assertIsNotNone(self.cache_service.get_cache_data(cache_key))

    def test_user_operators_cache(self):
        self.client.force_authenticate(user=self.user_superuser)
        cache_key = f'operators-list_{self.user_superuser.id}_'
        self.assertIsNone(self.cache_service.get_cache_data(cache_key))
        response = self.client.get(reverse('operators-list'))
        self.assertIsNotNone(self.cache_service.get_cache_data(cache_key))
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_invalidate_operators_cache(self):
        self.client.force_authenticate(user=self.user_superuser)
        cache_key = f'operators-list_{self.user_superuser.id}_'
        self.client.get(reverse('operators-list'))
        self.assertIsNotNone(self.cache_service.get_cache_data(cache_key))
        new_user = User.objects.create(
            email='<EMAIL>',
            first_name='first',
            last_name='last',
            role=self.operator_role
        )
        self.assertIsNone(self.cache_service.get_cache_data(cache_key))
        self.client.get(reverse('operators-list'))
        self.assertIsNotNone(self.cache_service.get_cache_data(cache_key))
        new_user.delete()
        self.assertIsNone(self.cache_service.get_cache_data(cache_key))
        self.client.get(reverse('operators-list'))
        self.assertIsNotNone(self.cache_service.get_cache_data(cache_key))

    def test_marketer_write_permissions(self):
        data = {
            'email': '<EMAIL>',
            'first_name': 'test_first_name',
            'last_name': 'test_last_name',
            'password': 'test_password',
        }

        for user in [
            self.user_team_lead,
            self.user_senior_operator,
            self.user_superuser,
            self.user_operator,
            self.user_marketer,
        ]:
            self.client.force_authenticate(user=user)
            response = self.client.post(reverse('marketers-list'),  data=data)
            self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        for user in [self.user_hom]:
            self.client.force_authenticate(user=user)
            response = self.client.post(reverse('marketers-list'), data=data)
            self.assertEqual(response.status_code, status.HTTP_201_CREATED)

    def test_marketer_models_could_be_updated(self):
        new_marketer = User.objects.create_user(
            email='<EMAIL>',
            password='XXXX_password',
            role=self.marketer_role,
            first_name='test_new_marketer_first_name',
            last_name='test_new_marketer_last_name',
        )

        first_model = self.create_only_fans_model(1)
        second_model = self.create_only_fans_model(2)
        third_model = self.create_only_fans_model(3)

        self.client.force_authenticate(user=self.user_hom)

        data_new_marketer = {
            'only_fans_models': [first_model.id]
        }
        response = self.client.patch(
            reverse('marketers-detail', args=(new_marketer.id,)), data=data_new_marketer
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data_old_marketer = {
            'only_fans_models': [second_model.id]
        }
        response = self.client.patch(
            reverse('marketers-detail', args=(self.user_marketer.id,)), data=data_old_marketer
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data_new_marketer = {
            'only_fans_models': [first_model.id, third_model.id]
        }
        response = self.client.patch(
            reverse('marketers-detail', args=(new_marketer.id,)), data=data_new_marketer
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data_old_marketer = {
            'only_fans_models': [second_model.id, third_model.id]
        }
        response = self.client.patch(
            reverse('marketers-detail', args=(self.user_marketer.id,)), data=data_old_marketer
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_invalidate_marketers_cache(self):
        self.client.force_authenticate(user=self.user_hom)
        cache_key = f'marketers-list_{self.user_hom.id}_'
        self.client.get(reverse('marketers-list'))
        self.assertIsNotNone(self.cache_service.get_cache_data(cache_key))
        new_user = User.objects.create(
            email='<EMAIL>',
            first_name='first',
            last_name='last',
            role=self.marketer_role
        )
        self.assertIsNone(self.cache_service.get_cache_data(cache_key))
        self.client.get(reverse('marketers-list'))
        self.assertIsNotNone(self.cache_service.get_cache_data(cache_key))
        new_user.delete()
        self.assertIsNone(self.cache_service.get_cache_data(cache_key))
        self.client.get(reverse('marketers-list'))
        self.assertIsNotNone(self.cache_service.get_cache_data(cache_key))

        new_model = self.create_only_fans_model(1)
        self.assertIsNone(self.cache_service.get_cache_data(cache_key))
        self.client.get(reverse('marketers-list'))
        self.assertIsNotNone(self.cache_service.get_cache_data(cache_key))
        new_model.delete()
        self.assertIsNone(self.cache_service.get_cache_data(cache_key))

    def test_client_managers_write_permissions(self):
        data = {
            'email': '<EMAIL>',
            'first_name': 'test_first_name',
            'last_name': 'test_last_name',
            'password': 'test_password',
        }

        for user in self.users:
            if user.role.name == 'hocm':
                continue

            self.client.force_authenticate(user=user)
            response = self.client.post(reverse('client-managers-list'), data=data)
            self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        self.client.force_authenticate(user=self.user_hocm)
        response = self.client.post(reverse('client-managers-list'), data=data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

    def test_client_manager_only_fans_models_validation(self):
        self.client.force_authenticate(user=self.user_hocm)
        of_model = self.create_only_fans_model(1)
        of_model.client_manager = self.user_client_manager
        of_model.save()

        data = {
            'only_fans_models': [of_model.id],
            'email': '<EMAIL>',
            'first_name': 'test_first_name',
            'last_name': 'test_last_name',
            'password': 'test_password',
        }
        self.client.force_authenticate(user=self.user_hocm)
        response = self.client.post(reverse('client-managers-list'), data=data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertTrue('only_fans_models' in response.data)

    def test_telegram_users_permissions(self):
        self.client.force_authenticate(user=self.user_superuser)

        response = self.client.get(reverse('users-telegram-users'))
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        response = self.client.get(reverse('users-telegram-users'), headers=self.api_key_headers)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_marketer_parent_is_set(self):
        self.client.force_authenticate(user=self.user_hom)

        new_parent = User.objects.create(
            email='<EMAIL>',
            first_name='first',
            last_name='last',
            role=self.hom_role
        )
        data_with_parent = {
            'email': '<EMAIL>',
            'first_name': 'test_first_name',
            'last_name': 'test_last_name',
            'password': 'test_password',
            'parent': str(new_parent.id),
        }
        response = self.client.post(reverse('marketers-list'),  data=data_with_parent)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        user = User.objects.get(email=data_with_parent['email'])
        self.assertEqual(user.parent, new_parent)

        data_without_parent = {
            'email': '<EMAIL>',
            'first_name': 'test_first_name',
            'last_name': 'test_last_name',
            'password': 'test_password',
        }
        response = self.client.post(reverse('marketers-list'),  data=data_without_parent)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        user = User.objects.get(email=data_without_parent['email'])
        self.assertEqual(user.parent, self.user_hom)
