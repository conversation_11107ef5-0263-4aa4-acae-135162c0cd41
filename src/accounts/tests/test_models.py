from accounts.models import User, UserRole
from base.tests.mixins import BaseCRMTest


class UserModelsTestCase(BaseCRMTest):
    """
    Test case for testing user models
    """
    def setUp(self):
        super().setUp()
        self.count_users = User.objects.count()

    def test_create_operator(self):
        role_operator = UserRole.objects.get(name='operator')
        User.objects.create(
            first_name='test',
            last_name='test2',
            email='<EMAIL>',
            password='fdfdf4fdfdfE',
            role=role_operator
        )
        self.assertEqual(User.objects.count(), self.count_users + 1)

    def test_create_senior_operator(self):
        role_senior_operator = UserRole.objects.get(name='operator')
        User.objects.create(
            first_name='test',
            last_name='test2',
            email='<EMAIL>',
            password='fdfdf4fdfdfE',
            role=role_senior_operator
        )
        self.assertEqual(User.objects.count(), self.count_users + 1)

    def test_create_team_lead(self):
        role_team_lead = UserRole.objects.get(name='operator')
        User.objects.create(
            first_name='test',
            last_name='test2',
            email='<EMAIL>',
            password='fdfdf4fdfdfE',
            role=role_team_lead
        )
        self.assertEqual(User.objects.count(), self.count_users + 1)

    def test_create_superuser(self):
        role_superuser = UserRole.objects.get(name='superuser')
        User.objects.create(
            first_name='test',
            last_name='test2',
            email='<EMAIL>',
            password='fdfdf4fdfdfE',
            role=role_superuser
        )
        self.assertEqual(User.objects.count(), self.count_users + 1)

    def test_create_marketer(self):
        role_marketer = UserRole.objects.get(name='marketer')
        User.objects.create(
            first_name='first_marketer',
            last_name='last_marketer',
            email='<EMAIL>',
            password='fdfdf4fdfdfE',
            role=role_marketer
        )
        self.assertTrue(
            User.objects.filter(
                role__name='marketer',
                first_name='first_marketer',
                last_name='last_marketer'
            ).exists()
        )

    def test_create_hom(self):
        role_hom = UserRole.objects.get(name='hom')
        User.objects.create(
            first_name='first_hom',
            last_name='last_hom',
            email='<EMAIL>',
            password='fdfdf4fdfdfE',
            role=role_hom
        )
        self.assertTrue(
            User.objects.filter(
                role__name='hom',
                first_name='first_hom',
                last_name='last_hom'
            ).exists()
        )

    def test_create_hof(self):
        role_hof = UserRole.objects.get(name='hof')
        User.objects.create(
            first_name='hoff',
            last_name='hofff',
            email='<EMAIL>',
            password='fdfdf4fdfdfE',
            role=role_hof
        )
        self.assertTrue(
            User.objects.filter(
                role__name='hof',
                first_name='hoff',
                last_name='hofff'
            ).exists()
        )

    def test_create_financier(self):
        role_financier = UserRole.objects.get(name='financier')
        User.objects.create(
            first_name='financierrr',
            last_name='financierrr',
            email='<EMAIL>',
            password='fdfdf4fdfdfE',
            role=role_financier
        )
        self.assertTrue(
            User.objects.filter(
                role__name='financier',
                first_name='financierrr',
                last_name='financierrr',
            ).exists()
        )
