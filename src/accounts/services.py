from django.contrib.auth import get_user_model
from django.db import transaction
from rest_framework.request import Request

from accounts.models import UserRole


class UserService:
    _model = get_user_model()
    role_name = None
    only_fans_models_related_name = None  # optional

    def __init__(self, instance: _model):
        self.instance = instance

    @classmethod
    def create(cls, validated_data: dict, **kwargs) -> _model:
        """
        Create a user with a specific role. Subclasses should define `role_name`.
        `_models_attr_name` is optional and only required if the user has related models to set.
        """
        assert cls.role_name is not None, 'Subclasses must define role_name'

        if 'only_fans_models' in validated_data:
            assert (
                cls.only_fans_models_related_name is not None
            ), 'Subclasses must define only_fans_models_related_name if only_fans_models in validated_data'

        with transaction.atomic():
            only_fans_models_to_set = (
                validated_data.pop('only_fans_models', None)
                if cls.only_fans_models_related_name
                else None
            )
            user = cls._model.objects.create_user(
                **validated_data, role=UserRole.objects.get(name=cls.role_name)
            )

            if only_fans_models_to_set is not None:
                getattr(user, cls.only_fans_models_related_name).set(
                    only_fans_models_to_set
                )

        return user

    def update(self, validated_data: dict) -> _model:
        """
        Update a user. `models_attr_name` is optional.
        """
        if 'only_fans_models' in validated_data:
            assert (
                self.only_fans_models_related_name is not None
            ), 'Subclasses must define only_fans_models_related_name if only_fans_models in validated_data'

        with transaction.atomic():
            only_fans_models_to_set = (
                validated_data.pop('only_fans_models', None)
                if self.only_fans_models_related_name
                else None
            )
            password = validated_data.pop('password', None)

            for key, value in validated_data.items():
                setattr(self.instance, key, value)

            if password:
                self.instance.set_password(password)

            if only_fans_models_to_set is not None:
                getattr(self.instance, self.only_fans_models_related_name).set(
                    only_fans_models_to_set
                )

            self.instance.save()

        return self.instance


class OperatorService(UserService):
    """
    Service for working with operators.
    """

    role_name = 'operator'

    @classmethod
    def create(cls, validated_data: dict, **kwargs) -> UserService._model:
        """
        Create an operator.

        Args:
            validated_data: The validated data to create an operator.
            **kwargs: includes request.
        """
        request = kwargs.get('request')

        assert request is not None, 'request keyword argument is required'
        assert isinstance(
            request, Request
        ), 'request must be a rest_framework.request.Request'

        if request.user.role.name == 'team_lead':
            validated_data['parent'] = request.user
        elif request.user.role.name == 'senior_operator':
            validated_data['parent'] = request.user.parent

        return super().create(validated_data)

    def update(self, validated_data: dict) -> None:
        """
        Set the password for the operator if it is provided.

        Args:
            validated_data: The validated data to update the operator.
        """
        password = validated_data.pop('password', None)

        if password:
            self.instance.set_password(password)

        need_update_name_in_shifts = False
        first_name = validated_data.pop('first_name', None)
        last_name = validated_data.pop('last_name', None)

        if first_name is not None and first_name != self.instance.first_name:
            self.instance.first_name = first_name
            need_update_name_in_shifts = True

        if last_name is not None and last_name != self.instance.last_name:
            self.instance.last_name = last_name
            need_update_name_in_shifts = True

        if need_update_name_in_shifts:
            self.instance.operator_shifts.select_for_update().update(
                operator_name=self.instance.full_name
            )


class MarketerService(UserService):
    """
    Marketer service
    """

    role_name = 'marketer'
    only_fans_models_related_name = 'marketer_models'


class ClientManagerService(UserService):
    """
    Client Manager Service
    """

    role_name = 'client_manager'
    only_fans_models_related_name = 'client_manager_models'
