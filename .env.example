POSTGRES_DB=
POSTGRES_USER=
POSTGRES_PASSWORD=
POSTGRES_HOST=
POSTGRES_PORT=5432

POSTGRES_TEST_DB=postgres
POSTGRES_TEST_USER=postgres
POSTGRES_TEST_PASSWORD=postgres
POSTGRES_TEST_HOST=db

REDIS_PASSWORD=
REDIS_HOST_NAME=redis
REDIS_PORT=6379
REDIS_CACHE_DB=5

CELERY_BROKER_URL=
CELERY_RESULT_BACKEND=

ALLOWED_HOSTS=
ALLOWED_CIDR_NETS=
CORS_ALLOWED_ORIGINS=
CSRF_TRUSTED_ORIGINS=

SECRET_KEY=
DEBUG=False

TZ=Europe/Kiev

MVP_URL=http:
MVP_API_KEY=

OF_DB_SYNCHRO_URL=
OF_DB_SYNCHRO_TOKEN=