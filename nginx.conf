server {
    listen 80;
    server_name 37.1.192.172;
    access_log  off;
    error_log   on;

    location / {
        root /home/<USER>/dist;
        try_files $uri  index.html;
    }
    location /static/ {
        alias /opt/app/staticfiles/;
        autoindex off;
    }
    location /media/ {
        alias /opt/app/media/;
        autoindex off;
    }
    location /api {
        try_files $uri @proxy_api;
    }
    location /admin {
        try_files $uri @proxy_api;
    }
    location @proxy_api {
        proxy_set_header X-Forwarded-Proto https;
        proxy_set_header X-Url-Scheme $scheme;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $http_host;
        proxy_redirect off;
        proxy_pass http://0.0.0.0:8000;
    }
}

